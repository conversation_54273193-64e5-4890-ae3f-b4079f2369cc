# 生产环境Docker Compose配置
# 用于OAuth2集成的生产部署

version: '3.8'

services:
  # 用户管理服务
  user-management:
    build:
      context: ./services/user-management
      dockerfile: Dockerfile.prod
    image: aipm/user-management:${DOCKER_IMAGE_TAG:-latest}
    container_name: aipm-user-management-prod
    restart: unless-stopped
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - APP_BASE_URL=${APP_BASE_URL}
      - FRONTEND_URL=${FRONTEND_URL}
      - DATABASE_URL=${DATABASE_URL}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - CUSTOM_CLIENT_ID=${CUSTOM_CLIENT_ID}
      - CUSTOM_CLIENT_SECRET=${CUSTOM_CLIENT_SECRET}
      - CUSTOM_AUTHORIZATION_URI=${CUSTOM_AUTHORIZATION_URI}
      - CUSTOM_TOKEN_URI=${CUSTOM_TOKEN_URI}
      - CUSTOM_USER_INFO_URI=${CUSTOM_USER_INFO_URI}
      - CUSTOM_JWK_SET_URI=${CUSTOM_JWK_SET_URI}
      - LOG_FILE_PATH=/app/logs/user-management.log
    ports:
      - "${SERVER_PORT:-8080}:8080"
    volumes:
      - user-management-logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    networks:
      - aipm-network
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-1G}
          cpus: ${CPU_LIMIT:-1.0}
        reservations:
          memory: ${MEMORY_REQUEST:-512M}
          cpus: ${CPU_REQUEST:-0.5}

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: aipm-postgres-prod
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-aipm_prod}
      - POSTGRES_USER=${DATABASE_USERNAME}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - postgres-backups:/backups
      - ./scripts/db/init-prod.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - aipm-network
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME} -d ${POSTGRES_DB:-aipm_prod}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: aipm-redis-prod
    restart: unless-stopped
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
      - /etc/localtime:/etc/localtime:ro
    networks:
      - aipm-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: aipm-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
      - /etc/localtime:/etc/localtime:ro
    networks:
      - aipm-network
    depends_on:
      - user-management
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      args:
        - REACT_APP_API_BASE_URL=${APP_BASE_URL}
        - REACT_APP_OAUTH2_REDIRECT_URI=${FRONTEND_URL}/oauth2/redirect
    image: aipm/frontend:${DOCKER_IMAGE_TAG:-latest}
    container_name: aipm-frontend-prod
    restart: unless-stopped
    networks:
      - aipm-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: aipm-prometheus-prod
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
      - /etc/localtime:/etc/localtime:ro
    networks:
      - aipm-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: aipm-grafana-prod
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - aipm-network
    depends_on:
      - prometheus

# 网络配置
networks:
  aipm-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/postgres
  postgres-backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${BACKUP_PATH:-./backups}/postgres
  redis-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/redis
  user-management-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-./logs}/user-management
  nginx-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-./logs}/nginx
  prometheus-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/prometheus
  grafana-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/grafana
