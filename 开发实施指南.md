# AI项目管理平台开发实施指南

## 1. 开发环境搭建

### 1.1 基础环境要求

#### 1.1.1 开发工具和版本要求
```bash
# 必需的开发工具版本
Java: OpenJDK 17+
Python: 3.11+
Node.js: 18+
Go: 1.20+
Docker: 24.0+
Kubernetes: 1.27+
Git: 2.40+

# 推荐的IDE
IntelliJ IDEA Ultimate 2023.2+ (Java服务)
PyCharm Professional 2023.2+ (Python服务)
VS Code 1.80+ (前端和通用开发)
```

#### 1.1.2 本地开发环境配置
```bash
# 1. 克隆项目仓库
git clone https://github.com/your-org/ai-pm-platform.git
cd ai-pm-platform

# 2. 安装开发依赖
make install-dev-deps

# 3. 启动本地基础设施
docker-compose -f docker-compose.dev.yml up -d

# 4. 初始化数据库
make init-db

# 5. 启动所有服务
make dev-start
```

#### 1.1.3 开发环境Docker Compose配置
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: aipm_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  # MongoDB文档数据库
  mongodb:
    image: mongo:6
    environment:
      MONGO_INITDB_ROOT_USERNAME: dev_user
      MONGO_INITDB_ROOT_PASSWORD: dev_password
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  # Neo4j图数据库
  neo4j:
    image: neo4j:5
    environment:
      NEO4J_AUTH: neo4j/dev_password
      NEO4J_PLUGINS: '["apoc"]'
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data

  # Elasticsearch搜索引擎
  elasticsearch:
    image: elasticsearch:8.9.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  # Kafka消息队列
  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

volumes:
  postgres_data:
  redis_data:
  mongodb_data:
  neo4j_data:
  elasticsearch_data:
```

### 1.2 项目结构和代码组织

#### 1.2.1 整体项目结构
```
ai-pm-platform/
├── services/                    # 微服务目录
│   ├── project-management/      # 项目管理服务
│   ├── ai-analysis/            # AI分析服务
│   ├── integration/            # 集成服务
│   ├── notification/           # 通知服务
│   └── user-management/        # 用户管理服务
├── frontend/                   # 前端应用
│   ├── web/                    # Web前端
│   └── mobile/                 # 移动端
├── shared/                     # 共享代码
│   ├── proto/                  # gRPC协议定义
│   ├── events/                 # 事件定义
│   └── utils/                  # 工具库
├── infrastructure/             # 基础设施代码
│   ├── k8s/                    # Kubernetes配置
│   ├── terraform/              # Terraform配置
│   └── monitoring/             # 监控配置
├── docs/                       # 文档
├── scripts/                    # 脚本文件
├── tests/                      # 集成测试
├── docker-compose.dev.yml      # 开发环境配置
├── docker-compose.prod.yml     # 生产环境配置
├── Makefile                    # 构建脚本
└── README.md                   # 项目说明
```

#### 1.2.2 Java服务代码结构（以项目管理服务为例）
```
services/project-management/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/aipm/project/
│   │   │       ├── ProjectManagementApplication.java
│   │   │       ├── config/              # 配置类
│   │   │       │   ├── DatabaseConfig.java
│   │   │       │   ├── RedisConfig.java
│   │   │       │   └── SecurityConfig.java
│   │   │       ├── controller/          # 控制器
│   │   │       │   ├── ProjectController.java
│   │   │       │   ├── TaskController.java
│   │   │       │   └── MilestoneController.java
│   │   │       ├── service/             # 业务服务
│   │   │       │   ├── ProjectService.java
│   │   │       │   ├── TaskService.java
│   │   │       │   └── HealthService.java
│   │   │       ├── repository/          # 数据访问层
│   │   │       │   ├── ProjectRepository.java
│   │   │       │   └── TaskRepository.java
│   │   │       ├── entity/              # 实体类
│   │   │       │   ├── Project.java
│   │   │       │   ├── Task.java
│   │   │       │   └── Milestone.java
│   │   │       ├── dto/                 # 数据传输对象
│   │   │       │   ├── ProjectDto.java
│   │   │       │   └── TaskDto.java
│   │   │       ├── event/               # 事件处理
│   │   │       │   ├── ProjectEventPublisher.java
│   │   │       │   └── TaskEventListener.java
│   │   │       └── exception/           # 异常处理
│   │   │           ├── GlobalExceptionHandler.java
│   │   │           └── ProjectNotFoundException.java
│   │   └── resources/
│   │       ├── application.yml
│   │       ├── application-dev.yml
│   │       ├── application-prod.yml
│   │       └── db/migration/            # 数据库迁移脚本
│   └── test/
│       └── java/
│           └── com/aipm/project/
│               ├── controller/          # 控制器测试
│               ├── service/             # 服务测试
│               └── repository/          # 仓库测试
├── Dockerfile
├── pom.xml
└── README.md
```

#### 1.2.3 Python服务代码结构（以AI分析服务为例）
```
services/ai-analysis/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config/                 # 配置模块
│   │   ├── __init__.py
│   │   ├── settings.py
│   │   └── database.py
│   ├── api/                    # API路由
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── analysis.py
│   │   │   ├── prediction.py
│   │   │   └── models.py
│   │   └── dependencies.py
│   ├── core/                   # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── ai_engine.py
│   │   ├── feature_engineering.py
│   │   ├── model_manager.py
│   │   └── prediction_service.py
│   ├── models/                 # 数据模型
│   │   ├── __init__.py
│   │   ├── project.py
│   │   ├── analysis.py
│   │   └── prediction.py
│   ├── services/               # 业务服务
│   │   ├── __init__.py
│   │   ├── analysis_service.py
│   │   ├── data_service.py
│   │   └── ml_service.py
│   ├── utils/                  # 工具函数
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   ├── cache.py
│   │   └── metrics.py
│   └── ml/                     # 机器学习模块
│       ├── __init__.py
│       ├── models/             # ML模型
│       │   ├── progress_predictor.py
│       │   ├── risk_assessor.py
│       │   └── quality_analyzer.py
│       ├── features/           # 特征工程
│       │   ├── extractors.py
│       │   └── transformers.py
│       └── training/           # 模型训练
│           ├── trainer.py
│           └── evaluator.py
├── tests/
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_api/
│   ├── test_core/
│   └── test_ml/
├── requirements.txt
├── requirements-dev.txt
├── Dockerfile
├── pyproject.toml
└── README.md
```

## 2. 开发规范和最佳实践

### 2.1 代码规范

#### 2.1.1 Java代码规范
```java
// 1. 类和方法命名规范
@Service
@Transactional
public class ProjectService {
    
    private static final Logger logger = LoggerFactory.getLogger(ProjectService.class);
    
    private final ProjectRepository projectRepository;
    private final TaskRepository taskRepository;
    private final ProjectEventPublisher eventPublisher;
    
    // 构造函数注入（推荐）
    public ProjectService(ProjectRepository projectRepository,
                         TaskRepository taskRepository,
                         ProjectEventPublisher eventPublisher) {
        this.projectRepository = projectRepository;
        this.taskRepository = taskRepository;
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * 创建新项目
     * 
     * @param request 项目创建请求
     * @return 创建的项目DTO
     * @throws ProjectValidationException 当项目数据无效时抛出
     */
    public ProjectDto createProject(CreateProjectRequest request) {
        // 参数验证
        validateProjectRequest(request);
        
        // 业务逻辑
        Project project = Project.builder()
            .name(request.getName())
            .description(request.getDescription())
            .startDate(request.getStartDate())
            .endDate(request.getEndDate())
            .status(ProjectStatus.PLANNING)
            .createdBy(getCurrentUserId())
            .build();
        
        // 保存项目
        Project savedProject = projectRepository.save(project);
        
        // 发布事件
        eventPublisher.publishProjectCreated(savedProject);
        
        // 记录日志
        logger.info("Project created successfully: id={}, name={}", 
                   savedProject.getId(), savedProject.getName());
        
        return ProjectDto.fromEntity(savedProject);
    }
    
    private void validateProjectRequest(CreateProjectRequest request) {
        if (StringUtils.isBlank(request.getName())) {
            throw new ProjectValidationException("Project name cannot be empty");
        }
        
        if (request.getEndDate().isBefore(request.getStartDate())) {
            throw new ProjectValidationException("End date cannot be before start date");
        }
    }
    
    private String getCurrentUserId() {
        // 从安全上下文获取当前用户ID
        return SecurityContextHolder.getContext()
            .getAuthentication()
            .getName();
    }
}
```

#### 2.1.2 Python代码规范
```python
# ai_analysis/app/services/analysis_service.py
"""
项目分析服务模块

提供项目进度、风险、质量等多维度分析功能
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta

from app.core.ai_engine import AIEngine
from app.models.analysis import AnalysisRequest, AnalysisResult
from app.models.project import Project
from app.utils.cache import cache_result
from app.utils.metrics import track_performance

logger = logging.getLogger(__name__)


class AnalysisService:
    """项目分析服务类"""
    
    def __init__(self, ai_engine: AIEngine):
        """
        初始化分析服务
        
        Args:
            ai_engine: AI引擎实例
        """
        self.ai_engine = ai_engine
        self._cache_ttl = 3600  # 缓存1小时
    
    @track_performance
    @cache_result(ttl=3600)
    async def analyze_project_progress(
        self, 
        project_id: str, 
        time_range: Optional[str] = "30d"
    ) -> AnalysisResult:
        """
        分析项目进度
        
        Args:
            project_id: 项目ID
            time_range: 时间范围，默认30天
            
        Returns:
            分析结果对象
            
        Raises:
            ProjectNotFoundException: 项目不存在时抛出
            AnalysisException: 分析失败时抛出
        """
        try:
            # 获取项目数据
            project_data = await self._get_project_data(project_id, time_range)
            
            # 验证数据完整性
            self._validate_project_data(project_data)
            
            # 执行AI分析
            analysis_result = await self.ai_engine.analyze_progress(project_data)
            
            # 记录分析日志
            logger.info(
                "Progress analysis completed",
                extra={
                    "project_id": project_id,
                    "confidence": analysis_result.confidence,
                    "predicted_completion": analysis_result.predicted_completion_date
                }
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error(
                "Progress analysis failed",
                extra={"project_id": project_id, "error": str(e)},
                exc_info=True
            )
            raise AnalysisException(f"Failed to analyze project progress: {str(e)}")
    
    async def _get_project_data(self, project_id: str, time_range: str) -> Dict:
        """获取项目数据"""
        # 实现数据获取逻辑
        pass
    
    def _validate_project_data(self, project_data: Dict) -> None:
        """验证项目数据完整性"""
        required_fields = ["tasks", "commits", "team_members"]
        
        for field in required_fields:
            if field not in project_data:
                raise ValueError(f"Missing required field: {field}")
        
        if not project_data["tasks"]:
            raise ValueError("Project must have at least one task")


class AnalysisException(Exception):
    """分析异常类"""
    pass
```

#### 2.1.3 前端代码规范（TypeScript + React）
```typescript
// frontend/web/src/components/ProjectDashboard/ProjectDashboard.tsx
import React, { useEffect, useState, useCallback } from 'react';
import { Card, Row, Col, Spin, Alert, Button } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';

import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { fetchProjectData, selectProject, selectProjectLoading } from '@/store/slices/projectSlice';
import { ProjectHealthCard } from './components/ProjectHealthCard';
import { ProgressChart } from './components/ProgressChart';
import { TeamEfficiencyCard } from './components/TeamEfficiencyCard';
import { AIInsightsPanel } from './components/AIInsightsPanel';

import type { Project } from '@/types/project';

interface ProjectDashboardProps {
  className?: string;
}

/**
 * 项目仪表板组件
 * 
 * 显示项目整体状态、进度、团队效率和AI洞察
 */
export const ProjectDashboard: React.FC<ProjectDashboardProps> = ({ 
  className 
}) => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // Redux状态
  const project = useAppSelector(selectProject);
  const loading = useAppSelector(selectProjectLoading);
  
  // 本地状态
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 获取项目数据
  const loadProjectData = useCallback(async () => {
    if (!projectId) {
      setError('项目ID不能为空');
      return;
    }
    
    try {
      setError(null);
      await dispatch(fetchProjectData(projectId)).unwrap();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载项目数据失败';
      setError(errorMessage);
    }
  }, [dispatch, projectId]);
  
  // 刷新数据
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadProjectData();
    } finally {
      setRefreshing(false);
    }
  }, [loadProjectData]);
  
  // 组件挂载时加载数据
  useEffect(() => {
    loadProjectData();
  }, [loadProjectData]);
  
  // 错误处理
  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={handleRefresh}>
            重试
          </Button>
        }
      />
    );
  }
  
  // 加载状态
  if (loading && !project) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" tip="加载项目数据中..." />
      </div>
    );
  }
  
  // 项目不存在
  if (!project) {
    return (
      <Alert
        message="项目不存在"
        description="请检查项目ID是否正确"
        type="warning"
        showIcon
      />
    );
  }
  
  return (
    <div className={`project-dashboard ${className || ''}`}>
      {/* 页面头部 */}
      <div className="dashboard-header mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {project.name}
            </h1>
            <p className="text-gray-600 mt-1">
              {project.description}
            </p>
          </div>
          <Button 
            type="primary" 
            loading={refreshing}
            onClick={handleRefresh}
          >
            刷新数据
          </Button>
        </div>
      </div>
      
      {/* 仪表板内容 */}
      <Row gutter={[16, 16]}>
        {/* 项目健康度 */}
        <Col xs={24} sm={12} lg={6}>
          <ProjectHealthCard 
            project={project}
            loading={refreshing}
          />
        </Col>
        
        {/* 团队效率 */}
        <Col xs={24} sm={12} lg={6}>
          <TeamEfficiencyCard 
            projectId={project.id}
            loading={refreshing}
          />
        </Col>
        
        {/* 进度图表 */}
        <Col xs={24} lg={12}>
          <Card title="项目进度" loading={refreshing}>
            <ProgressChart projectId={project.id} />
          </Card>
        </Col>
        
        {/* AI洞察面板 */}
        <Col xs={24}>
          <AIInsightsPanel 
            projectId={project.id}
            loading={refreshing}
          />
        </Col>
      </Row>
    </div>
  );
};

export default ProjectDashboard;
```

### 2.2 测试策略

#### 2.2.1 单元测试规范
```java
// Java单元测试示例
@ExtendWith(MockitoExtension.class)
class ProjectServiceTest {
    
    @Mock
    private ProjectRepository projectRepository;
    
    @Mock
    private TaskRepository taskRepository;
    
    @Mock
    private ProjectEventPublisher eventPublisher;
    
    @InjectMocks
    private ProjectService projectService;
    
    @Test
    @DisplayName("应该成功创建项目")
    void shouldCreateProjectSuccessfully() {
        // Given
        CreateProjectRequest request = CreateProjectRequest.builder()
            .name("测试项目")
            .description("这是一个测试项目")
            .startDate(LocalDateTime.now())
            .endDate(LocalDateTime.now().plusDays(30))
            .build();
        
        Project savedProject = Project.builder()
            .id("project-123")
            .name(request.getName())
            .description(request.getDescription())
            .status(ProjectStatus.PLANNING)
            .build();
        
        when(projectRepository.save(any(Project.class))).thenReturn(savedProject);
        
        // When
        ProjectDto result = projectService.createProject(request);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("测试项目");
        assertThat(result.getStatus()).isEqualTo(ProjectStatus.PLANNING);
        
        verify(projectRepository).save(any(Project.class));
        verify(eventPublisher).publishProjectCreated(savedProject);
    }
    
    @Test
    @DisplayName("当项目名称为空时应该抛出异常")
    void shouldThrowExceptionWhenProjectNameIsEmpty() {
        // Given
        CreateProjectRequest request = CreateProjectRequest.builder()
            .name("")
            .description("描述")
            .startDate(LocalDateTime.now())
            .endDate(LocalDateTime.now().plusDays(30))
            .build();
        
        // When & Then
        assertThatThrownBy(() -> projectService.createProject(request))
            .isInstanceOf(ProjectValidationException.class)
            .hasMessage("Project name cannot be empty");
        
        verify(projectRepository, never()).save(any(Project.class));
        verify(eventPublisher, never()).publishProjectCreated(any(Project.class));
    }
}
```

```python
# Python单元测试示例
import pytest
from unittest.mock import AsyncMock, Mock
from datetime import datetime

from app.services.analysis_service import AnalysisService, AnalysisException
from app.models.analysis import AnalysisResult
from app.core.ai_engine import AIEngine


class TestAnalysisService:
    """分析服务测试类"""
    
    @pytest.fixture
    def mock_ai_engine(self):
        """模拟AI引擎"""
        return Mock(spec=AIEngine)
    
    @pytest.fixture
    def analysis_service(self, mock_ai_engine):
        """分析服务实例"""
        return AnalysisService(mock_ai_engine)
    
    @pytest.mark.asyncio
    async def test_analyze_project_progress_success(
        self, 
        analysis_service, 
        mock_ai_engine
    ):
        """测试项目进度分析成功场景"""
        # Given
        project_id = "project-123"
        expected_result = AnalysisResult(
            confidence=0.85,
            predicted_completion_date=datetime.now(),
            factors={"velocity": 0.8, "quality": 0.9}
        )
        
        # 模拟AI引擎返回结果
        mock_ai_engine.analyze_progress = AsyncMock(return_value=expected_result)
        
        # 模拟数据获取
        analysis_service._get_project_data = AsyncMock(return_value={
            "tasks": [{"id": "task-1", "status": "done"}],
            "commits": [{"sha": "abc123", "date": "2025-01-01"}],
            "team_members": ["user-1", "user-2"]
        })
        
        # When
        result = await analysis_service.analyze_project_progress(project_id)
        
        # Then
        assert result == expected_result
        assert result.confidence == 0.85
        mock_ai_engine.analyze_progress.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_project_progress_invalid_data(
        self, 
        analysis_service
    ):
        """测试无效数据时抛出异常"""
        # Given
        project_id = "project-123"
        
        # 模拟返回无效数据
        analysis_service._get_project_data = AsyncMock(return_value={
            "tasks": [],  # 空任务列表
            "commits": [],
            "team_members": []
        })
        
        # When & Then
        with pytest.raises(AnalysisException) as exc_info:
            await analysis_service.analyze_project_progress(project_id)
        
        assert "Failed to analyze project progress" in str(exc_info.value)
```

#### 2.2.2 集成测试规范
```java
// Spring Boot集成测试
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
@Transactional
class ProjectControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private ProjectRepository projectRepository;
    
    @Test
    void shouldCreateAndRetrieveProject() {
        // Given
        CreateProjectRequest request = CreateProjectRequest.builder()
            .name("集成测试项目")
            .description("这是一个集成测试项目")
            .startDate(LocalDateTime.now())
            .endDate(LocalDateTime.now().plusDays(30))
            .build();
        
        // When - 创建项目
        ResponseEntity<ProjectDto> createResponse = restTemplate.postForEntity(
            "/api/v1/projects", 
            request, 
            ProjectDto.class
        );
        
        // Then - 验证创建结果
        assertThat(createResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(createResponse.getBody()).isNotNull();
        
        String projectId = createResponse.getBody().getId();
        
        // When - 获取项目
        ResponseEntity<ProjectDto> getResponse = restTemplate.getForEntity(
            "/api/v1/projects/" + projectId, 
            ProjectDto.class
        );
        
        // Then - 验证获取结果
        assertThat(getResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(getResponse.getBody()).isNotNull();
        assertThat(getResponse.getBody().getName()).isEqualTo("集成测试项目");
        
        // 验证数据库中的数据
        Optional<Project> savedProject = projectRepository.findById(projectId);
        assertThat(savedProject).isPresent();
        assertThat(savedProject.get().getName()).isEqualTo("集成测试项目");
    }
}
```

## 3. AI模型开发指南

### 3.1 机器学习模型开发流程

#### 3.1.1 数据准备和特征工程
```python
# app/ml/features/extractors.py
"""
特征提取器模块

从项目数据中提取用于机器学习的特征
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any
from datetime import datetime, timedelta

class ProjectFeatureExtractor:
    """项目特征提取器"""

    def __init__(self):
        self.feature_config = {
            'time_features': ['project_duration', 'days_since_start', 'days_to_deadline'],
            'task_features': ['total_tasks', 'completed_tasks', 'completion_rate', 'avg_task_duration'],
            'team_features': ['team_size', 'avg_experience', 'team_velocity'],
            'code_features': ['commits_count', 'lines_changed', 'code_quality_score'],
            'communication_features': ['meeting_frequency', 'message_count', 'response_time']
        }

    def extract_features(self, project_data: Dict[str, Any]) -> pd.DataFrame:
        """
        从项目数据中提取特征

        Args:
            project_data: 项目原始数据

        Returns:
            特征DataFrame
        """
        features = {}

        # 时间特征
        features.update(self._extract_time_features(project_data))

        # 任务特征
        features.update(self._extract_task_features(project_data))

        # 团队特征
        features.update(self._extract_team_features(project_data))

        # 代码特征
        features.update(self._extract_code_features(project_data))

        # 沟通特征
        features.update(self._extract_communication_features(project_data))

        return pd.DataFrame([features])

    def _extract_time_features(self, project_data: Dict) -> Dict[str, float]:
        """提取时间相关特征"""
        start_date = datetime.fromisoformat(project_data['start_date'])
        end_date = datetime.fromisoformat(project_data['end_date'])
        current_date = datetime.now()

        return {
            'project_duration': (end_date - start_date).days,
            'days_since_start': (current_date - start_date).days,
            'days_to_deadline': (end_date - current_date).days,
            'progress_ratio': min(1.0, (current_date - start_date).days / (end_date - start_date).days)
        }

    def _extract_task_features(self, project_data: Dict) -> Dict[str, float]:
        """提取任务相关特征"""
        tasks = project_data.get('tasks', [])

        if not tasks:
            return {
                'total_tasks': 0,
                'completed_tasks': 0,
                'completion_rate': 0,
                'avg_task_duration': 0,
                'overdue_tasks': 0
            }

        completed_tasks = [t for t in tasks if t['status'] == 'done']
        overdue_tasks = [t for t in tasks if self._is_task_overdue(t)]

        # 计算平均任务持续时间
        task_durations = []
        for task in completed_tasks:
            if task.get('completed_at') and task.get('created_at'):
                duration = (
                    datetime.fromisoformat(task['completed_at']) -
                    datetime.fromisoformat(task['created_at'])
                ).days
                task_durations.append(duration)

        return {
            'total_tasks': len(tasks),
            'completed_tasks': len(completed_tasks),
            'completion_rate': len(completed_tasks) / len(tasks),
            'avg_task_duration': np.mean(task_durations) if task_durations else 0,
            'overdue_tasks': len(overdue_tasks),
            'overdue_rate': len(overdue_tasks) / len(tasks)
        }

    def _extract_team_features(self, project_data: Dict) -> Dict[str, float]:
        """提取团队相关特征"""
        team_members = project_data.get('team_members', [])

        if not team_members:
            return {
                'team_size': 0,
                'avg_experience': 0,
                'team_velocity': 0,
                'team_diversity': 0
            }

        # 计算团队经验
        experiences = [member.get('experience_years', 0) for member in team_members]

        # 计算团队速度（最近30天完成的任务数）
        recent_tasks = self._get_recent_completed_tasks(project_data, days=30)
        team_velocity = len(recent_tasks) / len(team_members) if team_members else 0

        # 计算团队多样性（技能种类数）
        all_skills = set()
        for member in team_members:
            all_skills.update(member.get('skills', []))

        return {
            'team_size': len(team_members),
            'avg_experience': np.mean(experiences),
            'team_velocity': team_velocity,
            'team_diversity': len(all_skills)
        }

    def _extract_code_features(self, project_data: Dict) -> Dict[str, float]:
        """提取代码相关特征"""
        commits = project_data.get('commits', [])

        if not commits:
            return {
                'commits_count': 0,
                'avg_commits_per_day': 0,
                'lines_added': 0,
                'lines_deleted': 0,
                'code_quality_score': 0
            }

        # 最近30天的提交
        recent_commits = self._get_recent_commits(commits, days=30)

        total_lines_added = sum(commit.get('lines_added', 0) for commit in commits)
        total_lines_deleted = sum(commit.get('lines_deleted', 0) for commit in commits)

        # 代码质量评分（基于代码审查、测试覆盖率等）
        quality_metrics = project_data.get('quality_metrics', {})
        code_quality_score = (
            quality_metrics.get('test_coverage', 0) * 0.4 +
            quality_metrics.get('code_review_score', 0) * 0.3 +
            quality_metrics.get('static_analysis_score', 0) * 0.3
        )

        return {
            'commits_count': len(commits),
            'recent_commits_count': len(recent_commits),
            'avg_commits_per_day': len(recent_commits) / 30,
            'lines_added': total_lines_added,
            'lines_deleted': total_lines_deleted,
            'code_churn': total_lines_added + total_lines_deleted,
            'code_quality_score': code_quality_score
        }

    def _extract_communication_features(self, project_data: Dict) -> Dict[str, float]:
        """提取沟通相关特征"""
        meetings = project_data.get('meetings', [])
        messages = project_data.get('messages', [])

        # 最近30天的会议和消息
        recent_meetings = self._get_recent_meetings(meetings, days=30)
        recent_messages = self._get_recent_messages(messages, days=30)

        # 计算平均响应时间
        response_times = []
        for message in recent_messages:
            if message.get('response_time'):
                response_times.append(message['response_time'])

        return {
            'meeting_frequency': len(recent_meetings) / 30,
            'message_count': len(recent_messages),
            'avg_response_time': np.mean(response_times) if response_times else 0,
            'communication_activity': (len(recent_meetings) + len(recent_messages)) / 30
        }

    def _is_task_overdue(self, task: Dict) -> bool:
        """判断任务是否逾期"""
        if not task.get('due_date') or task['status'] == 'done':
            return False

        due_date = datetime.fromisoformat(task['due_date'])
        return datetime.now() > due_date

    def _get_recent_completed_tasks(self, project_data: Dict, days: int) -> List[Dict]:
        """获取最近完成的任务"""
        cutoff_date = datetime.now() - timedelta(days=days)
        tasks = project_data.get('tasks', [])

        recent_tasks = []
        for task in tasks:
            if (task['status'] == 'done' and
                task.get('completed_at') and
                datetime.fromisoformat(task['completed_at']) > cutoff_date):
                recent_tasks.append(task)

        return recent_tasks

    def _get_recent_commits(self, commits: List[Dict], days: int) -> List[Dict]:
        """获取最近的提交"""
        cutoff_date = datetime.now() - timedelta(days=days)

        recent_commits = []
        for commit in commits:
            if (commit.get('date') and
                datetime.fromisoformat(commit['date']) > cutoff_date):
                recent_commits.append(commit)

        return recent_commits

    def _get_recent_meetings(self, meetings: List[Dict], days: int) -> List[Dict]:
        """获取最近的会议"""
        cutoff_date = datetime.now() - timedelta(days=days)

        recent_meetings = []
        for meeting in meetings:
            if (meeting.get('date') and
                datetime.fromisoformat(meeting['date']) > cutoff_date):
                recent_meetings.append(meeting)

        return recent_meetings

    def _get_recent_messages(self, messages: List[Dict], days: int) -> List[Dict]:
        """获取最近的消息"""
        cutoff_date = datetime.now() - timedelta(days=days)

        recent_messages = []
        for message in messages:
            if (message.get('timestamp') and
                datetime.fromisoformat(message['timestamp']) > cutoff_date):
                recent_messages.append(message)

        return recent_messages
```

#### 3.1.2 模型训练和评估
```python
# app/ml/training/trainer.py
"""
模型训练器模块

负责机器学习模型的训练、验证和优化
"""

import joblib
import mlflow
import mlflow.sklearn
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
from typing import Dict, Tuple, Any
import logging

logger = logging.getLogger(__name__)


class ProjectProgressPredictor:
    """项目进度预测模型训练器"""

    def __init__(self, model_name: str = "project_progress_predictor"):
        self.model_name = model_name
        self.model = None
        self.scaler = StandardScaler()
        self.feature_importance = None

        # 模型候选
        self.model_candidates = {
            'random_forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                random_state=42
            ),
            'linear_regression': LinearRegression()
        }

    def train(self,
              features: pd.DataFrame,
              targets: pd.Series,
              test_size: float = 0.2,
              cv_folds: int = 5) -> Dict[str, Any]:
        """
        训练模型

        Args:
            features: 特征数据
            targets: 目标变量（项目完成百分比）
            test_size: 测试集比例
            cv_folds: 交叉验证折数

        Returns:
            训练结果字典
        """
        logger.info(f"开始训练模型 {self.model_name}")

        # 数据预处理
        X_scaled = self.scaler.fit_transform(features)

        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, targets, test_size=test_size, random_state=42
        )

        # 模型选择和训练
        best_model, best_score = self._select_best_model(
            X_train, y_train, cv_folds
        )

        # 在最佳模型上进行超参数调优
        best_model = self._tune_hyperparameters(best_model, X_train, y_train)

        # 训练最终模型
        best_model.fit(X_train, y_train)
        self.model = best_model

        # 评估模型
        train_metrics = self._evaluate_model(best_model, X_train, y_train)
        test_metrics = self._evaluate_model(best_model, X_test, y_test)

        # 特征重要性
        if hasattr(best_model, 'feature_importances_'):
            self.feature_importance = dict(zip(
                features.columns,
                best_model.feature_importances_
            ))

        # 记录实验结果
        training_results = {
            'model_type': type(best_model).__name__,
            'train_metrics': train_metrics,
            'test_metrics': test_metrics,
            'feature_importance': self.feature_importance,
            'cv_score': best_score
        }

        # MLflow记录
        self._log_to_mlflow(training_results, best_model)

        logger.info(f"模型训练完成，测试集R²: {test_metrics['r2']:.4f}")

        return training_results

    def _select_best_model(self,
                          X_train: np.ndarray,
                          y_train: np.ndarray,
                          cv_folds: int) -> Tuple[Any, float]:
        """选择最佳模型"""
        best_model = None
        best_score = -np.inf

        for model_name, model in self.model_candidates.items():
            # 交叉验证
            cv_scores = cross_val_score(
                model, X_train, y_train,
                cv=cv_folds, scoring='r2'
            )
            mean_score = cv_scores.mean()

            logger.info(f"{model_name} CV R²: {mean_score:.4f} (+/- {cv_scores.std() * 2:.4f})")

            if mean_score > best_score:
                best_score = mean_score
                best_model = model

        return best_model, best_score

    def _tune_hyperparameters(self, model: Any, X_train: np.ndarray, y_train: np.ndarray) -> Any:
        """超参数调优"""
        if isinstance(model, RandomForestRegressor):
            param_grid = {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, 15, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            }
        elif isinstance(model, GradientBoostingRegressor):
            param_grid = {
                'n_estimators': [50, 100, 200],
                'max_depth': [3, 6, 9],
                'learning_rate': [0.01, 0.1, 0.2],
                'subsample': [0.8, 0.9, 1.0]
            }
        else:
            # 线性回归不需要调优
            return model

        grid_search = GridSearchCV(
            model, param_grid, cv=3, scoring='r2', n_jobs=-1
        )
        grid_search.fit(X_train, y_train)

        logger.info(f"最佳参数: {grid_search.best_params_}")

        return grid_search.best_estimator_

    def _evaluate_model(self, model: Any, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """评估模型性能"""
        y_pred = model.predict(X)

        return {
            'mse': mean_squared_error(y, y_pred),
            'mae': mean_absolute_error(y, y_pred),
            'rmse': np.sqrt(mean_squared_error(y, y_pred)),
            'r2': r2_score(y, y_pred)
        }

    def _log_to_mlflow(self, results: Dict[str, Any], model: Any) -> None:
        """记录到MLflow"""
        with mlflow.start_run():
            # 记录参数
            mlflow.log_param("model_type", results['model_type'])

            # 记录指标
            for metric_name, metric_value in results['test_metrics'].items():
                mlflow.log_metric(f"test_{metric_name}", metric_value)

            for metric_name, metric_value in results['train_metrics'].items():
                mlflow.log_metric(f"train_{metric_name}", metric_value)

            mlflow.log_metric("cv_score", results['cv_score'])

            # 记录模型
            mlflow.sklearn.log_model(model, "model")

            # 记录特征重要性
            if results['feature_importance']:
                for feature, importance in results['feature_importance'].items():
                    mlflow.log_metric(f"feature_importance_{feature}", importance)

    def predict(self, features: pd.DataFrame) -> np.ndarray:
        """预测"""
        if self.model is None:
            raise ValueError("模型尚未训练")

        X_scaled = self.scaler.transform(features)
        return self.model.predict(X_scaled)

    def save_model(self, filepath: str) -> None:
        """保存模型"""
        if self.model is None:
            raise ValueError("模型尚未训练")

        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'feature_importance': self.feature_importance
        }

        joblib.dump(model_data, filepath)
        logger.info(f"模型已保存到 {filepath}")

    def load_model(self, filepath: str) -> None:
        """加载模型"""
        model_data = joblib.load(filepath)

        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.feature_importance = model_data['feature_importance']

        logger.info(f"模型已从 {filepath} 加载")


class RiskAssessmentModel:
    """风险评估模型"""

    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.risk_categories = ['low', 'medium', 'high', 'critical']

    def train(self, features: pd.DataFrame, risk_labels: pd.Series) -> Dict[str, Any]:
        """训练风险评估模型"""
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import classification_report, confusion_matrix

        # 数据预处理
        X_scaled = self.scaler.fit_transform(features)

        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, risk_labels, test_size=0.2, random_state=42, stratify=risk_labels
        )

        # 训练模型
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )

        self.model.fit(X_train, y_train)

        # 评估
        y_pred = self.model.predict(X_test)

        results = {
            'classification_report': classification_report(y_test, y_pred),
            'confusion_matrix': confusion_matrix(y_test, y_pred).tolist(),
            'feature_importance': dict(zip(features.columns, self.model.feature_importances_))
        }

        return results

    def predict_risk(self, features: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """预测风险等级和概率"""
        if self.model is None:
            raise ValueError("模型尚未训练")

        X_scaled = self.scaler.transform(features)
        predictions = self.model.predict(X_scaled)
        probabilities = self.model.predict_proba(X_scaled)

        return predictions, probabilities
```

### 3.2 模型部署和服务化

#### 3.2.1 模型服务API
```python
# app/api/v1/models.py
"""
模型服务API

提供机器学习模型的预测接口
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Dict, Optional
import numpy as np
import pandas as pd
from datetime import datetime

from app.core.model_manager import ModelManager
from app.services.data_service import DataService
from app.utils.cache import cache_result
from app.utils.metrics import track_prediction_latency

router = APIRouter(prefix="/models", tags=["models"])


class PredictionRequest(BaseModel):
    """预测请求模型"""
    project_id: str = Field(..., description="项目ID")
    model_type: str = Field(..., description="模型类型: progress, risk, quality")
    features: Optional[Dict] = Field(None, description="自定义特征，如果不提供则自动提取")


class PredictionResponse(BaseModel):
    """预测响应模型"""
    project_id: str
    model_type: str
    prediction: float
    confidence: float
    factors: Dict[str, float]
    recommendations: List[str]
    timestamp: datetime


class ModelInfo(BaseModel):
    """模型信息"""
    model_name: str
    model_type: str
    version: str
    accuracy: float
    last_trained: datetime
    feature_count: int


@router.post("/predict", response_model=PredictionResponse)
@track_prediction_latency
async def predict(
    request: PredictionRequest,
    background_tasks: BackgroundTasks,
    model_manager: ModelManager = Depends(),
    data_service: DataService = Depends()
):
    """
    执行模型预测

    Args:
        request: 预测请求
        background_tasks: 后台任务
        model_manager: 模型管理器
        data_service: 数据服务

    Returns:
        预测结果
    """
    try:
        # 获取或提取特征
        if request.features:
            features_df = pd.DataFrame([request.features])
        else:
            # 自动提取特征
            project_data = await data_service.get_project_data(request.project_id)
            features_df = await data_service.extract_features(project_data)

        # 获取模型
        model = model_manager.get_model(request.model_type)
        if not model:
            raise HTTPException(
                status_code=404,
                detail=f"Model type '{request.model_type}' not found"
            )

        # 执行预测
        prediction_result = model.predict(features_df)

        # 生成建议
        recommendations = await _generate_recommendations(
            request.model_type,
            prediction_result,
            features_df
        )

        # 构建响应
        response = PredictionResponse(
            project_id=request.project_id,
            model_type=request.model_type,
            prediction=float(prediction_result['value']),
            confidence=float(prediction_result['confidence']),
            factors=prediction_result['factors'],
            recommendations=recommendations,
            timestamp=datetime.now()
        )

        # 异步记录预测日志
        background_tasks.add_task(
            _log_prediction,
            request.project_id,
            request.model_type,
            response
        )

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")


@router.get("/info", response_model=List[ModelInfo])
async def get_models_info(model_manager: ModelManager = Depends()):
    """获取所有模型信息"""
    models_info = []

    for model_type, model in model_manager.get_all_models().items():
        info = ModelInfo(
            model_name=model.name,
            model_type=model_type,
            version=model.version,
            accuracy=model.metrics.get('accuracy', 0.0),
            last_trained=model.last_trained,
            feature_count=len(model.feature_names)
        )
        models_info.append(info)

    return models_info


@router.post("/retrain/{model_type}")
async def retrain_model(
    model_type: str,
    background_tasks: BackgroundTasks,
    model_manager: ModelManager = Depends()
):
    """触发模型重训练"""
    if model_type not in ['progress', 'risk', 'quality']:
        raise HTTPException(
            status_code=400,
            detail="Invalid model type"
        )

    # 异步执行重训练
    background_tasks.add_task(
        _retrain_model_async,
        model_type,
        model_manager
    )

    return {"message": f"Model {model_type} retraining started"}


async def _generate_recommendations(
    model_type: str,
    prediction_result: Dict,
    features_df: pd.DataFrame
) -> List[str]:
    """生成基于预测结果的建议"""
    recommendations = []

    if model_type == 'progress':
        completion_rate = prediction_result['value']

        if completion_rate < 0.5:
            recommendations.append("项目进度较慢，建议增加资源投入或调整计划")

        if prediction_result['factors'].get('team_velocity', 0) < 0.5:
            recommendations.append("团队速度偏低，建议优化工作流程或提供技能培训")

        if prediction_result['factors'].get('code_quality_score', 0) < 0.7:
            recommendations.append("代码质量需要改进，建议加强代码审查和测试")

    elif model_type == 'risk':
        risk_level = prediction_result['value']

        if risk_level > 0.7:
            recommendations.append("项目风险较高，建议立即制定风险缓解计划")
            recommendations.append("增加项目监控频率，及时识别和处理问题")

        if prediction_result['factors'].get('communication_activity', 0) < 0.3:
            recommendations.append("团队沟通不足，建议增加会议频率和沟通渠道")

    elif model_type == 'quality':
        quality_score = prediction_result['value']

        if quality_score < 0.6:
            recommendations.append("代码质量偏低，建议加强静态代码分析和单元测试")
            recommendations.append("实施更严格的代码审查流程")

    return recommendations


async def _log_prediction(
    project_id: str,
    model_type: str,
    response: PredictionResponse
) -> None:
    """记录预测日志"""
    # 实现预测日志记录逻辑
    pass


async def _retrain_model_async(model_type: str, model_manager: ModelManager) -> None:
    """异步重训练模型"""
    try:
        await model_manager.retrain_model(model_type)
    except Exception as e:
        # 记录重训练失败日志
        pass
```

---

**文档版本：** 1.0
**创建日期：** 2025-08-15
**最后更新：** 2025-08-15
**文档状态：** 开发指南详细完成
