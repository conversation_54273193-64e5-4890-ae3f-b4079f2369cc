#!/bin/bash

# OAuth2生产环境部署脚本
# 
# 该脚本用于部署OAuth2集成到生产环境
# 
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-19

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_TYPE="${1:-docker}"  # docker 或 k8s
ENVIRONMENT="${2:-production}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    case $DEPLOYMENT_TYPE in
        "docker")
            if ! command -v docker &> /dev/null; then
                log_error "Docker未安装或不在PATH中"
                exit 1
            fi
            
            if ! command -v docker-compose &> /dev/null; then
                log_error "Docker Compose未安装或不在PATH中"
                exit 1
            fi
            ;;
        "k8s")
            if ! command -v kubectl &> /dev/null; then
                log_error "kubectl未安装或不在PATH中"
                exit 1
            fi
            
            if ! command -v helm &> /dev/null; then
                log_warning "Helm未安装，某些功能可能不可用"
            fi
            ;;
        *)
            log_error "不支持的部署类型: $DEPLOYMENT_TYPE"
            exit 1
            ;;
    esac
    
    log_success "依赖检查通过"
}

# 验证环境配置
validate_environment() {
    log_info "验证环境配置..."
    
    # 检查环境变量文件
    ENV_FILE="$PROJECT_ROOT/.env.$ENVIRONMENT"
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境配置文件不存在: $ENV_FILE"
        log_info "请复制 .env.production.template 并填入实际配置"
        exit 1
    fi
    
    # 加载环境变量
    source "$ENV_FILE"
    
    # 验证必需的OAuth2环境变量
    required_vars=(
        "APP_BASE_URL"
        "FRONTEND_URL"
        "JWT_SECRET"
        "DATABASE_PASSWORD"
        "GOOGLE_CLIENT_ID"
        "GOOGLE_CLIENT_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "必需的环境变量未设置: $var"
            exit 1
        fi
    done
    
    # 验证URL格式
    if [[ ! $APP_BASE_URL =~ ^https:// ]]; then
        log_error "APP_BASE_URL必须使用HTTPS: $APP_BASE_URL"
        exit 1
    fi
    
    if [[ ! $FRONTEND_URL =~ ^https:// ]]; then
        log_error "FRONTEND_URL必须使用HTTPS: $FRONTEND_URL"
        exit 1
    fi
    
    log_success "环境配置验证通过"
}

# 构建Docker镜像
build_docker_images() {
    log_info "构建Docker镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 构建用户管理服务镜像
    log_info "构建用户管理服务镜像..."
    docker build -t aipm/user-management:${DOCKER_IMAGE_TAG:-latest} \
        -f services/user-management/Dockerfile.prod \
        services/user-management/
    
    # 构建前端应用镜像
    log_info "构建前端应用镜像..."
    docker build -t aipm/frontend:${DOCKER_IMAGE_TAG:-latest} \
        --build-arg REACT_APP_API_BASE_URL="$APP_BASE_URL" \
        --build-arg REACT_APP_OAUTH2_REDIRECT_URI="$FRONTEND_URL/oauth2/redirect" \
        -f frontend/Dockerfile.prod \
        frontend/
    
    log_success "Docker镜像构建完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local data_path="${DATA_PATH:-$PROJECT_ROOT/data}"
    local log_path="${LOG_PATH:-$PROJECT_ROOT/logs}"
    local backup_path="${BACKUP_PATH:-$PROJECT_ROOT/backups}"
    
    mkdir -p "$data_path"/{postgres,redis,prometheus,grafana}
    mkdir -p "$log_path"/{user-management,nginx}
    mkdir -p "$backup_path"/postgres
    
    # 设置权限
    chmod 755 "$data_path" "$log_path" "$backup_path"
    
    log_success "目录创建完成"
}

# 生成SSL证书（自签名，生产环境应使用真实证书）
generate_ssl_certificates() {
    log_info "生成SSL证书..."
    
    local ssl_dir="$PROJECT_ROOT/nginx/ssl"
    mkdir -p "$ssl_dir"
    
    # 检查是否已有证书
    if [ -f "$ssl_dir/api.your-domain.com.crt" ] && [ -f "$ssl_dir/your-domain.com.crt" ]; then
        log_info "SSL证书已存在，跳过生成"
        return
    fi
    
    log_warning "生成自签名证书（仅用于测试，生产环境请使用真实证书）"
    
    # 生成API域名证书
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$ssl_dir/api.your-domain.com.key" \
        -out "$ssl_dir/api.your-domain.com.crt" \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=AIPM/CN=api.your-domain.com"
    
    # 生成前端域名证书
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$ssl_dir/your-domain.com.key" \
        -out "$ssl_dir/your-domain.com.crt" \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=AIPM/CN=your-domain.com"
    
    log_success "SSL证书生成完成"
}

# Docker部署
deploy_docker() {
    log_info "使用Docker Compose部署..."
    
    cd "$PROJECT_ROOT"
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose -f docker-compose.prod.yml down --remove-orphans
    
    # 启动服务
    log_info "启动生产服务..."
    docker-compose -f docker-compose.prod.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose -f docker-compose.prod.yml ps
    
    log_success "Docker部署完成"
}

# Kubernetes部署
deploy_kubernetes() {
    log_info "使用Kubernetes部署..."
    
    cd "$PROJECT_ROOT"
    
    # 创建命名空间
    kubectl apply -f k8s/production/oauth2-deployment.yaml
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl rollout status deployment/user-management-oauth2 -n aipm-prod --timeout=300s
    
    # 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n aipm-prod -l app=user-management
    
    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get services -n aipm-prod
    
    log_success "Kubernetes部署完成"
}

# 运行数据库迁移
run_database_migration() {
    log_info "运行数据库迁移..."
    
    case $DEPLOYMENT_TYPE in
        "docker")
            docker-compose -f docker-compose.prod.yml exec user-management \
                java -jar app.jar --spring.profiles.active=prod \
                --spring.flyway.migrate=true
            ;;
        "k8s")
            kubectl exec -n aipm-prod deployment/user-management-oauth2 -- \
                java -jar app.jar --spring.profiles.active=prod \
                --spring.flyway.migrate=true
            ;;
    esac
    
    log_success "数据库迁移完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务完全启动
    sleep 60
    
    # 检查健康状态
    local health_url="$APP_BASE_URL/actuator/health"
    log_info "检查健康状态: $health_url"
    
    if curl -f -s "$health_url" > /dev/null; then
        log_success "健康检查通过"
    else
        log_error "健康检查失败"
        exit 1
    fi
    
    # 检查OAuth2提供商端点
    local oauth2_url="$APP_BASE_URL/api/v1/oauth2/providers"
    log_info "检查OAuth2端点: $oauth2_url"
    
    if curl -f -s "$oauth2_url" > /dev/null; then
        log_success "OAuth2端点检查通过"
    else
        log_error "OAuth2端点检查失败"
        exit 1
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_info "部署信息:"
    echo "=================================="
    echo "应用URL: $APP_BASE_URL"
    echo "前端URL: $FRONTEND_URL"
    echo "OAuth2授权端点:"
    echo "  - Google: $APP_BASE_URL/oauth2/authorization/google"
    echo "  - GitHub: $APP_BASE_URL/oauth2/authorization/github"
    echo "  - 自定义: $APP_BASE_URL/oauth2/authorization/custom"
    echo "OAuth2 API端点: $APP_BASE_URL/api/v1/oauth2/providers"
    echo "健康检查: $APP_BASE_URL/actuator/health"
    echo "=================================="
    
    log_warning "请确保在OAuth2提供商中配置正确的回调URL:"
    echo "  - Google: $APP_BASE_URL/oauth2/callback/google"
    echo "  - GitHub: $APP_BASE_URL/oauth2/callback/github"
    echo "  - 自定义: $APP_BASE_URL/oauth2/callback/custom"
}

# 主函数
main() {
    log_info "开始OAuth2生产环境部署..."
    log_info "部署类型: $DEPLOYMENT_TYPE"
    log_info "环境: $ENVIRONMENT"
    
    # 检查是否在项目根目录
    if [ ! -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_dependencies
    validate_environment
    create_directories
    generate_ssl_certificates
    
    if [ "$DEPLOYMENT_TYPE" = "docker" ]; then
        build_docker_images
        deploy_docker
    elif [ "$DEPLOYMENT_TYPE" = "k8s" ]; then
        deploy_kubernetes
    fi
    
    run_database_migration
    verify_deployment
    show_deployment_info
    
    log_success "OAuth2生产环境部署完成！"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [部署类型] [环境]"
    echo ""
    echo "部署类型:"
    echo "  docker    - 使用Docker Compose部署（默认）"
    echo "  k8s       - 使用Kubernetes部署"
    echo ""
    echo "环境:"
    echo "  production - 生产环境（默认）"
    echo "  staging    - 预发布环境"
    echo ""
    echo "示例:"
    echo "  $0 docker production"
    echo "  $0 k8s staging"
}

# 处理命令行参数
case "${1:-}" in
    "-h"|"--help"|"help")
        show_usage
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
