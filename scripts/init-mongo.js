// AI项目管理平台 MongoDB 初始化脚本
// 创建开发环境所需的数据库、集合和索引

// 切换到应用数据库
db = db.getSiblingDB('aipm_dev');

// ============================================================================
// 创建应用用户
// ============================================================================

// 创建应用用户（如果不存在）
try {
    db.createUser({
        user: "aipm_app_user",
        pwd: "aipm_app_password_123",
        roles: [
            {
                role: "readWrite",
                db: "aipm_dev"
            }
        ]
    });
    print("✓ 应用用户创建成功");
} catch (e) {
    if (e.code === 51003) {
        print("✓ 应用用户已存在");
    } else {
        print("✗ 创建应用用户失败:", e.message);
    }
}

// ============================================================================
// 创建集合和文档结构
// ============================================================================

// AI分析结果集合
db.createCollection("analysis_results", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["projectId", "analysisType", "result", "createdAt"],
            properties: {
                projectId: {
                    bsonType: "string",
                    description: "项目ID，必须是字符串"
                },
                analysisType: {
                    bsonType: "string",
                    enum: ["progress", "risk", "quality", "efficiency"],
                    description: "分析类型，必须是指定的枚举值"
                },
                result: {
                    bsonType: "object",
                    description: "分析结果对象"
                },
                confidence: {
                    bsonType: "double",
                    minimum: 0,
                    maximum: 1,
                    description: "置信度，0-1之间的数值"
                },
                metadata: {
                    bsonType: "object",
                    description: "元数据对象"
                },
                createdAt: {
                    bsonType: "date",
                    description: "创建时间"
                },
                updatedAt: {
                    bsonType: "date",
                    description: "更新时间"
                }
            }
        }
    }
});

// 项目文档集合
db.createCollection("project_documents", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["projectId", "title", "content", "type", "createdAt"],
            properties: {
                projectId: {
                    bsonType: "string",
                    description: "项目ID"
                },
                title: {
                    bsonType: "string",
                    description: "文档标题"
                },
                content: {
                    bsonType: "string",
                    description: "文档内容"
                },
                type: {
                    bsonType: "string",
                    enum: ["requirement", "design", "api", "user_manual", "technical"],
                    description: "文档类型"
                },
                version: {
                    bsonType: "string",
                    description: "文档版本"
                },
                tags: {
                    bsonType: "array",
                    items: {
                        bsonType: "string"
                    },
                    description: "文档标签"
                },
                authorId: {
                    bsonType: "string",
                    description: "作者ID"
                },
                status: {
                    bsonType: "string",
                    enum: ["draft", "review", "approved", "archived"],
                    description: "文档状态"
                },
                createdAt: {
                    bsonType: "date",
                    description: "创建时间"
                },
                updatedAt: {
                    bsonType: "date",
                    description: "更新时间"
                }
            }
        }
    }
});

// 集成配置集合
db.createCollection("integration_configs", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["projectId", "integrationType", "config", "createdAt"],
            properties: {
                projectId: {
                    bsonType: "string",
                    description: "项目ID"
                },
                integrationType: {
                    bsonType: "string",
                    enum: ["github", "gitlab", "jira", "slack", "dingtalk", "wework"],
                    description: "集成类型"
                },
                config: {
                    bsonType: "object",
                    description: "集成配置对象"
                },
                status: {
                    bsonType: "string",
                    enum: ["active", "inactive", "error"],
                    description: "集成状态"
                },
                lastSyncAt: {
                    bsonType: "date",
                    description: "最后同步时间"
                },
                createdAt: {
                    bsonType: "date",
                    description: "创建时间"
                },
                updatedAt: {
                    bsonType: "date",
                    description: "更新时间"
                }
            }
        }
    }
});

// 事件日志集合
db.createCollection("event_logs", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["eventType", "entityType", "entityId", "timestamp"],
            properties: {
                eventType: {
                    bsonType: "string",
                    description: "事件类型"
                },
                entityType: {
                    bsonType: "string",
                    enum: ["project", "task", "user", "organization"],
                    description: "实体类型"
                },
                entityId: {
                    bsonType: "string",
                    description: "实体ID"
                },
                userId: {
                    bsonType: "string",
                    description: "操作用户ID"
                },
                data: {
                    bsonType: "object",
                    description: "事件数据"
                },
                timestamp: {
                    bsonType: "date",
                    description: "事件时间戳"
                },
                source: {
                    bsonType: "string",
                    description: "事件来源"
                }
            }
        }
    }
});

// 通知消息集合
db.createCollection("notifications", {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["userId", "title", "content", "type", "createdAt"],
            properties: {
                userId: {
                    bsonType: "string",
                    description: "用户ID"
                },
                title: {
                    bsonType: "string",
                    description: "通知标题"
                },
                content: {
                    bsonType: "string",
                    description: "通知内容"
                },
                type: {
                    bsonType: "string",
                    enum: ["info", "warning", "error", "success"],
                    description: "通知类型"
                },
                category: {
                    bsonType: "string",
                    enum: ["system", "project", "task", "mention"],
                    description: "通知分类"
                },
                isRead: {
                    bsonType: "bool",
                    description: "是否已读"
                },
                readAt: {
                    bsonType: "date",
                    description: "阅读时间"
                },
                data: {
                    bsonType: "object",
                    description: "通知相关数据"
                },
                createdAt: {
                    bsonType: "date",
                    description: "创建时间"
                }
            }
        }
    }
});

print("✓ 集合创建完成");

// ============================================================================
// 创建索引
// ============================================================================

// analysis_results 集合索引
db.analysis_results.createIndex({ "projectId": 1, "analysisType": 1 });
db.analysis_results.createIndex({ "createdAt": -1 });
db.analysis_results.createIndex({ "projectId": 1, "createdAt": -1 });

// project_documents 集合索引
db.project_documents.createIndex({ "projectId": 1 });
db.project_documents.createIndex({ "type": 1 });
db.project_documents.createIndex({ "authorId": 1 });
db.project_documents.createIndex({ "title": "text", "content": "text" }); // 全文搜索索引
db.project_documents.createIndex({ "tags": 1 });
db.project_documents.createIndex({ "createdAt": -1 });

// integration_configs 集合索引
db.integration_configs.createIndex({ "projectId": 1, "integrationType": 1 }, { unique: true });
db.integration_configs.createIndex({ "status": 1 });
db.integration_configs.createIndex({ "lastSyncAt": -1 });

// event_logs 集合索引
db.event_logs.createIndex({ "entityType": 1, "entityId": 1 });
db.event_logs.createIndex({ "eventType": 1 });
db.event_logs.createIndex({ "userId": 1 });
db.event_logs.createIndex({ "timestamp": -1 });
db.event_logs.createIndex({ "entityType": 1, "entityId": 1, "timestamp": -1 });

// notifications 集合索引
db.notifications.createIndex({ "userId": 1, "isRead": 1 });
db.notifications.createIndex({ "userId": 1, "createdAt": -1 });
db.notifications.createIndex({ "type": 1 });
db.notifications.createIndex({ "category": 1 });

print("✓ 索引创建完成");

// ============================================================================
// 插入初始数据
// ============================================================================

// 插入示例分析结果
db.analysis_results.insertOne({
    projectId: "sample-project-id",
    analysisType: "progress",
    result: {
        completionRate: 0.65,
        predictedCompletionDate: new Date("2025-12-31"),
        riskLevel: "medium",
        recommendations: [
            "建议增加测试资源投入",
            "关注关键路径任务进度"
        ]
    },
    confidence: 0.85,
    metadata: {
        modelVersion: "v1.0",
        dataPoints: 150,
        analysisDate: new Date()
    },
    createdAt: new Date(),
    updatedAt: new Date()
});

// 插入示例项目文档
db.project_documents.insertOne({
    projectId: "sample-project-id",
    title: "项目需求规格说明书",
    content: "这是一个示例项目需求文档...",
    type: "requirement",
    version: "1.0",
    tags: ["需求", "规格", "v1.0"],
    authorId: "admin-user-id",
    status: "approved",
    createdAt: new Date(),
    updatedAt: new Date()
});

// 插入示例集成配置
db.integration_configs.insertOne({
    projectId: "sample-project-id",
    integrationType: "github",
    config: {
        repositoryUrl: "https://github.com/example/project",
        accessToken: "encrypted_token",
        webhookUrl: "https://api.aipm.dev/webhooks/github",
        syncEnabled: true
    },
    status: "active",
    lastSyncAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
});

print("✓ 初始数据插入完成");

// ============================================================================
// 创建视图
// ============================================================================

// 创建项目分析摘要视图
db.createView("project_analysis_summary", "analysis_results", [
    {
        $group: {
            _id: "$projectId",
            latestProgressAnalysis: {
                $last: {
                    $cond: [
                        { $eq: ["$analysisType", "progress"] },
                        "$$ROOT",
                        null
                    ]
                }
            },
            latestRiskAnalysis: {
                $last: {
                    $cond: [
                        { $eq: ["$analysisType", "risk"] },
                        "$$ROOT",
                        null
                    ]
                }
            },
            totalAnalyses: { $sum: 1 },
            lastAnalysisDate: { $max: "$createdAt" }
        }
    },
    {
        $project: {
            projectId: "$_id",
            latestProgressAnalysis: 1,
            latestRiskAnalysis: 1,
            totalAnalyses: 1,
            lastAnalysisDate: 1,
            _id: 0
        }
    }
]);

print("✓ 视图创建完成");

print("🎉 MongoDB 初始化完成！");
print("数据库: aipm_dev");
print("用户: aipm_app_user");
print("集合数量:", db.getCollectionNames().length);
