#!/bin/bash

# OAuth2集成测试脚本
# 
# 该脚本用于运行OAuth2相关的所有测试，包括后端和前端测试
# 
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-19

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装或不在PATH中"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装或不在PATH中"
        exit 1
    fi
    
    log_success "所有依赖检查通过"
}

# 设置测试环境变量
setup_test_env() {
    log_info "设置测试环境变量..."
    
    export SPRING_PROFILES_ACTIVE=test
    export GOOGLE_CLIENT_ID=test-google-client-id
    export GOOGLE_CLIENT_SECRET=test-google-client-secret
    export GITHUB_CLIENT_ID=test-github-client-id
    export GITHUB_CLIENT_SECRET=test-github-client-secret
    export CUSTOM_CLIENT_ID=test-custom-client-id
    export CUSTOM_CLIENT_SECRET=test-custom-client-secret
    export CUSTOM_AUTHORIZATION_URI=https://test-provider.com/oauth2/authorize
    export CUSTOM_TOKEN_URI=https://test-provider.com/oauth2/token
    export CUSTOM_USER_INFO_URI=https://test-provider.com/oauth2/userinfo
    
    log_success "测试环境变量设置完成"
}

# 运行后端测试
run_backend_tests() {
    log_info "运行后端OAuth2测试..."
    
    cd services/user-management
    
    # 运行OAuth2相关的单元测试
    log_info "运行OAuth2单元测试..."
    mvn test -Dtest="*OAuth2*Test" -Dspring.profiles.active=test
    
    if [ $? -eq 0 ]; then
        log_success "后端OAuth2测试通过"
    else
        log_error "后端OAuth2测试失败"
        exit 1
    fi
    
    cd ../..
}

# 运行前端测试
run_frontend_tests() {
    log_info "运行前端OAuth2测试..."
    
    cd frontend
    
    # 安装依赖（如果需要）
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    # 运行OAuth2相关的测试
    log_info "运行OAuth2前端测试..."
    npm test -- --run --reporter=verbose oauth2
    
    if [ $? -eq 0 ]; then
        log_success "前端OAuth2测试通过"
    else
        log_error "前端OAuth2测试失败"
        exit 1
    fi
    
    cd ..
}

# 运行集成测试
run_integration_tests() {
    log_info "运行OAuth2集成测试..."
    
    cd services/user-management
    
    # 启动测试数据库（如果使用Docker）
    if command -v docker &> /dev/null; then
        log_info "启动测试数据库..."
        docker-compose -f ../../docker-compose.test.yml up -d postgres redis
        sleep 10
    fi
    
    # 运行集成测试
    log_info "运行OAuth2集成测试..."
    mvn test -Dtest="*OAuth2*IntegrationTest,*OAuth2*EndToEndTest" -Dspring.profiles.active=test
    
    if [ $? -eq 0 ]; then
        log_success "OAuth2集成测试通过"
    else
        log_error "OAuth2集成测试失败"
        exit 1
    fi
    
    # 停止测试数据库
    if command -v docker &> /dev/null; then
        log_info "停止测试数据库..."
        docker-compose -f ../../docker-compose.test.yml down
    fi
    
    cd ../..
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    cd services/user-management
    
    # 生成测试覆盖率报告
    mvn jacoco:report
    
    if [ -f "target/site/jacoco/index.html" ]; then
        log_success "测试覆盖率报告已生成: services/user-management/target/site/jacoco/index.html"
    fi
    
    cd ../..
    
    # 前端测试报告
    cd frontend
    
    if [ -d "coverage" ]; then
        log_success "前端测试覆盖率报告已生成: frontend/coverage/index.html"
    fi
    
    cd ..
}

# 验证OAuth2配置
verify_oauth2_config() {
    log_info "验证OAuth2配置..."
    
    # 检查配置文件是否存在
    config_files=(
        "services/user-management/src/main/resources/application-oauth2.yml"
        "services/user-management/src/main/java/com/aipm/usermanagement/config/OAuth2Config.java"
        "services/user-management/src/main/java/com/aipm/usermanagement/security/oauth2/CustomOAuth2UserService.java"
    )
    
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "配置文件存在: $file"
        else
            log_error "配置文件缺失: $file"
            exit 1
        fi
    done
    
    # 检查前端组件
    frontend_files=(
        "frontend/src/services/oauth2Service.ts"
        "frontend/src/components/OAuth2Login.tsx"
        "frontend/src/pages/OAuth2Redirect.tsx"
    )
    
    for file in "${frontend_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "前端文件存在: $file"
        else
            log_error "前端文件缺失: $file"
            exit 1
        fi
    done
}

# 主函数
main() {
    log_info "开始OAuth2集成测试..."
    
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行测试步骤
    check_dependencies
    verify_oauth2_config
    setup_test_env
    
    # 根据参数决定运行哪些测试
    case "${1:-all}" in
        "backend")
            run_backend_tests
            ;;
        "frontend")
            run_frontend_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "all")
            run_backend_tests
            run_frontend_tests
            run_integration_tests
            ;;
        *)
            log_error "无效的测试类型: $1"
            log_info "用法: $0 [backend|frontend|integration|all]"
            exit 1
            ;;
    esac
    
    generate_test_report
    
    log_success "OAuth2集成测试完成！"
}

# 运行主函数
main "$@"
