# AI项目管理平台 Makefile
# 提供统一的开发、测试、构建和部署命令

.PHONY: help install-dev-deps dev-start dev-stop test build deploy clean

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

# 项目配置
PROJECT_NAME := ai-pm-platform
DOCKER_REGISTRY := ghcr.io/your-org
VERSION := $(shell git describe --tags --always --dirty)
SERVICES := project-management ai-analysis integration notification user-management

help: ## 显示帮助信息
	@echo "$(BLUE)AI项目管理平台开发工具$(RESET)"
	@echo ""
	@echo "$(GREEN)可用命令:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# ============================================================================
# 开发环境设置
# ============================================================================

install-dev-deps: ## 安装开发依赖
	@echo "$(GREEN)安装开发依赖...$(RESET)"
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)错误: Docker 未安装$(RESET)"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "$(RED)错误: Docker Compose 未安装$(RESET)"; exit 1; }
	@command -v java >/dev/null 2>&1 || { echo "$(RED)错误: Java 未安装$(RESET)"; exit 1; }
	@command -v python3 >/dev/null 2>&1 || { echo "$(RED)错误: Python 3 未安装$(RESET)"; exit 1; }
	@command -v node >/dev/null 2>&1 || { echo "$(RED)错误: Node.js 未安装$(RESET)"; exit 1; }
	@command -v go >/dev/null 2>&1 || { echo "$(RED)错误: Go 未安装$(RESET)"; exit 1; }
	@echo "$(GREEN)✓ 所有依赖已安装$(RESET)"

init-db: ## 初始化数据库
	@echo "$(GREEN)初始化数据库...$(RESET)"
	@docker-compose -f docker-compose.dev.yml exec postgres psql -U dev_user -d aipm_dev -f /docker-entrypoint-initdb.d/init.sql
	@echo "$(GREEN)✓ 数据库初始化完成$(RESET)"

dev-start: ## 启动开发环境
	@echo "$(GREEN)启动开发环境...$(RESET)"
	@docker-compose -f docker-compose.dev.yml up -d
	@echo "$(YELLOW)等待服务启动...$(RESET)"
	@sleep 10
	@$(MAKE) start-services
	@echo "$(GREEN)✓ 开发环境已启动$(RESET)"
	@echo "$(BLUE)访问地址:$(RESET)"
	@echo "  - Web前端: http://localhost:3000"
	@echo "  - API文档: http://localhost:8080/swagger-ui.html"
	@echo "  - AI服务: http://localhost:8081/docs"

dev-stop: ## 停止开发环境
	@echo "$(GREEN)停止开发环境...$(RESET)"
	@$(MAKE) stop-services
	@docker-compose -f docker-compose.dev.yml down
	@echo "$(GREEN)✓ 开发环境已停止$(RESET)"

start-services: ## 启动所有微服务
	@echo "$(GREEN)启动微服务...$(RESET)"
	@cd services/project-management && ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev &
	@cd services/ai-analysis && python -m uvicorn app.main:app --host 0.0.0.0 --port 8081 --reload &
	@cd services/integration && npm run dev &
	@cd services/notification && go run main.go &
	@cd services/user-management && ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev &
	@cd frontend/web && npm start &
	@echo "$(GREEN)✓ 所有服务已启动$(RESET)"

stop-services: ## 停止所有微服务
	@echo "$(GREEN)停止微服务...$(RESET)"
	@pkill -f "spring-boot:run" || true
	@pkill -f "uvicorn" || true
	@pkill -f "npm run dev" || true
	@pkill -f "go run main.go" || true
	@pkill -f "npm start" || true
	@echo "$(GREEN)✓ 所有服务已停止$(RESET)"

# ============================================================================
# 测试
# ============================================================================

test: ## 运行所有测试
	@echo "$(GREEN)运行所有测试...$(RESET)"
	@$(MAKE) test-java
	@$(MAKE) test-python
	@$(MAKE) test-node
	@$(MAKE) test-go
	@$(MAKE) test-frontend
	@echo "$(GREEN)✓ 所有测试完成$(RESET)"

test-java: ## 运行Java服务测试
	@echo "$(GREEN)运行Java服务测试...$(RESET)"
	@cd services/project-management && ./mvnw test
	@cd services/user-management && ./mvnw test

test-python: ## 运行Python服务测试
	@echo "$(GREEN)运行Python服务测试...$(RESET)"
	@cd services/ai-analysis && python -m pytest tests/ -v --cov=app --cov-report=xml

test-node: ## 运行Node.js服务测试
	@echo "$(GREEN)运行Node.js服务测试...$(RESET)"
	@cd services/integration && npm test

test-go: ## 运行Go服务测试
	@echo "$(GREEN)运行Go服务测试...$(RESET)"
	@cd services/notification && go test ./... -v -cover

test-frontend: ## 运行前端测试
	@echo "$(GREEN)运行前端测试...$(RESET)"
	@cd frontend/web && npm test -- --coverage --watchAll=false

test-integration: ## 运行集成测试
	@echo "$(GREEN)运行集成测试...$(RESET)"
	@cd tests && python -m pytest integration/ -v

# ============================================================================
# 代码质量
# ============================================================================

lint: ## 运行代码检查
	@echo "$(GREEN)运行代码检查...$(RESET)"
	@$(MAKE) lint-java
	@$(MAKE) lint-python
	@$(MAKE) lint-node
	@$(MAKE) lint-go
	@$(MAKE) lint-frontend

lint-java: ## Java代码检查
	@cd services/project-management && ./mvnw checkstyle:check
	@cd services/user-management && ./mvnw checkstyle:check

lint-python: ## Python代码检查
	@cd services/ai-analysis && python -m flake8 app/ tests/
	@cd services/ai-analysis && python -m black --check app/ tests/
	@cd services/ai-analysis && python -m isort --check-only app/ tests/

lint-node: ## Node.js代码检查
	@cd services/integration && npm run lint
	@cd frontend/web && npm run lint

lint-go: ## Go代码检查
	@cd services/notification && go fmt ./...
	@cd services/notification && go vet ./...
	@cd services/notification && golangci-lint run

lint-frontend: ## 前端代码检查
	@cd frontend/web && npm run lint

format: ## 格式化代码
	@echo "$(GREEN)格式化代码...$(RESET)"
	@cd services/ai-analysis && python -m black app/ tests/
	@cd services/ai-analysis && python -m isort app/ tests/
	@cd services/integration && npm run format
	@cd frontend/web && npm run format
	@cd services/notification && go fmt ./...

# ============================================================================
# 构建
# ============================================================================

build: ## 构建所有服务
	@echo "$(GREEN)构建所有服务...$(RESET)"
	@for service in $(SERVICES); do \
		echo "$(YELLOW)构建 $$service...$(RESET)"; \
		$(MAKE) build-$$service; \
	done
	@$(MAKE) build-frontend
	@echo "$(GREEN)✓ 所有服务构建完成$(RESET)"

build-project-management: ## 构建项目管理服务
	@cd services/project-management && ./mvnw clean package -DskipTests
	@docker build -t $(DOCKER_REGISTRY)/project-management:$(VERSION) services/project-management/

build-ai-analysis: ## 构建AI分析服务
	@docker build -t $(DOCKER_REGISTRY)/ai-analysis:$(VERSION) services/ai-analysis/

build-integration: ## 构建集成服务
	@cd services/integration && npm run build
	@docker build -t $(DOCKER_REGISTRY)/integration:$(VERSION) services/integration/

build-notification: ## 构建通知服务
	@cd services/notification && go build -o bin/notification main.go
	@docker build -t $(DOCKER_REGISTRY)/notification:$(VERSION) services/notification/

build-user-management: ## 构建用户管理服务
	@cd services/user-management && ./mvnw clean package -DskipTests
	@docker build -t $(DOCKER_REGISTRY)/user-management:$(VERSION) services/user-management/

build-frontend: ## 构建前端
	@cd frontend/web && npm run build
	@docker build -t $(DOCKER_REGISTRY)/frontend:$(VERSION) frontend/web/

# ============================================================================
# 部署
# ============================================================================

deploy-dev: ## 部署到开发环境
	@echo "$(GREEN)部署到开发环境...$(RESET)"
	@kubectl apply -f infrastructure/k8s/dev/
	@echo "$(GREEN)✓ 开发环境部署完成$(RESET)"

deploy-staging: ## 部署到预发布环境
	@echo "$(GREEN)部署到预发布环境...$(RESET)"
	@kubectl apply -f infrastructure/k8s/staging/
	@echo "$(GREEN)✓ 预发布环境部署完成$(RESET)"

deploy-prod: ## 部署到生产环境
	@echo "$(RED)部署到生产环境...$(RESET)"
	@read -p "确认部署到生产环境? [y/N] " confirm && [ "$$confirm" = "y" ]
	@kubectl apply -f infrastructure/k8s/prod/
	@echo "$(GREEN)✓ 生产环境部署完成$(RESET)"

push-images: ## 推送Docker镜像
	@echo "$(GREEN)推送Docker镜像...$(RESET)"
	@for service in $(SERVICES) frontend; do \
		echo "$(YELLOW)推送 $$service:$(VERSION)...$(RESET)"; \
		docker push $(DOCKER_REGISTRY)/$$service:$(VERSION); \
	done
	@echo "$(GREEN)✓ 所有镜像推送完成$(RESET)"

# ============================================================================
# 数据库管理
# ============================================================================

db-migrate: ## 运行数据库迁移
	@echo "$(GREEN)运行数据库迁移...$(RESET)"
	@cd services/project-management && ./mvnw flyway:migrate
	@cd services/user-management && ./mvnw flyway:migrate

db-reset: ## 重置数据库
	@echo "$(RED)重置数据库...$(RESET)"
	@read -p "确认重置数据库? 这将删除所有数据! [y/N] " confirm && [ "$$confirm" = "y" ]
	@docker-compose -f docker-compose.dev.yml exec postgres psql -U dev_user -d aipm_dev -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
	@$(MAKE) db-migrate
	@$(MAKE) init-db

# ============================================================================
# 监控和日志
# ============================================================================

logs: ## 查看所有服务日志
	@docker-compose -f docker-compose.dev.yml logs -f

logs-service: ## 查看特定服务日志 (使用: make logs-service SERVICE=project-management)
	@docker-compose -f docker-compose.dev.yml logs -f $(SERVICE)

monitor: ## 启动监控服务
	@echo "$(GREEN)启动监控服务...$(RESET)"
	@docker-compose -f infrastructure/monitoring/docker-compose.yml up -d
	@echo "$(BLUE)监控地址:$(RESET)"
	@echo "  - Prometheus: http://localhost:9090"
	@echo "  - Grafana: http://localhost:3001 (admin/admin)"

# ============================================================================
# 清理
# ============================================================================

clean: ## 清理构建文件和Docker资源
	@echo "$(GREEN)清理构建文件...$(RESET)"
	@cd services/project-management && ./mvnw clean
	@cd services/user-management && ./mvnw clean
	@cd services/ai-analysis && find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@cd services/integration && rm -rf node_modules dist
	@cd services/notification && rm -rf bin/
	@cd frontend/web && rm -rf node_modules build
	@docker system prune -f
	@echo "$(GREEN)✓ 清理完成$(RESET)"

clean-docker: ## 清理Docker资源
	@echo "$(GREEN)清理Docker资源...$(RESET)"
	@docker container prune -f
	@docker image prune -f
	@docker volume prune -f
	@docker network prune -f

# ============================================================================
# 工具
# ============================================================================

generate-docs: ## 生成API文档
	@echo "$(GREEN)生成API文档...$(RESET)"
	@cd services/project-management && ./mvnw spring-boot:run -Dspring-boot.run.arguments="--spring.profiles.active=docs" &
	@sleep 30
	@curl http://localhost:8080/v3/api-docs > docs/api/project-management.json
	@pkill -f "spring-boot:run"

security-scan: ## 运行安全扫描
	@echo "$(GREEN)运行安全扫描...$(RESET)"
	@cd services/ai-analysis && python -m safety check
	@cd services/integration && npm audit
	@cd frontend/web && npm audit

performance-test: ## 运行性能测试
	@echo "$(GREEN)运行性能测试...$(RESET)"
	@cd tests/performance && k6 run load-test.js

backup-db: ## 备份数据库
	@echo "$(GREEN)备份数据库...$(RESET)"
	@mkdir -p backups
	@docker-compose -f docker-compose.dev.yml exec postgres pg_dump -U dev_user aipm_dev > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✓ 数据库备份完成$(RESET)"

# ============================================================================
# 版本管理
# ============================================================================

version: ## 显示当前版本
	@echo "$(BLUE)当前版本: $(VERSION)$(RESET)"

tag: ## 创建新版本标签 (使用: make tag VERSION=v1.0.0)
	@git tag -a $(VERSION) -m "Release $(VERSION)"
	@git push origin $(VERSION)
	@echo "$(GREEN)✓ 版本标签 $(VERSION) 已创建$(RESET)"
