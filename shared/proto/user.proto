// AI项目管理平台 - 用户服务 gRPC 协议定义
// 定义用户管理相关的服务接口和数据结构

syntax = "proto3";

package aipm.user.v1;

option go_package = "github.com/aipm/shared/proto/user/v1;userv1";
option java_package = "com.aipm.proto.user.v1";
option java_multiple_files = true;
option java_outer_classname = "UserProto";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// ============================================================================
// 用户服务接口定义
// ============================================================================

// 用户管理服务
service UserService {
  // 用户认证
  rpc Authenticate(AuthenticateRequest) returns (AuthenticateResponse);
  
  // 刷新令牌
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  
  // 用户注册
  rpc Register(RegisterRequest) returns (RegisterResponse);
  
  // 获取用户信息
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  
  // 更新用户信息
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
  
  // 删除用户
  rpc DeleteUser(DeleteUserRequest) returns (google.protobuf.Empty);
  
  // 获取用户列表
  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse);
  
  // 验证用户权限
  rpc CheckPermission(CheckPermissionRequest) returns (CheckPermissionResponse);
  
  // 获取用户角色
  rpc GetUserRoles(GetUserRolesRequest) returns (GetUserRolesResponse);
  
  // 分配用户角色
  rpc AssignRole(AssignRoleRequest) returns (AssignRoleResponse);
}

// ============================================================================
// 数据结构定义
// ============================================================================

// 用户信息
message User {
  string id = 1;                                    // 用户ID
  string username = 2;                              // 用户名
  string email = 3;                                 // 邮箱
  string full_name = 4;                             // 全名
  string avatar_url = 5;                            // 头像URL
  string phone = 6;                                 // 电话号码
  UserStatus status = 7;                            // 用户状态
  UserRole role = 8;                                // 用户角色
  google.protobuf.Timestamp last_login_at = 9;      // 最后登录时间
  google.protobuf.Timestamp created_at = 10;        // 创建时间
  google.protobuf.Timestamp updated_at = 11;        // 更新时间
  string created_by = 12;                           // 创建者ID
  string updated_by = 13;                           // 更新者ID
}

// 用户状态枚举
enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  USER_STATUS_ACTIVE = 1;                           // 活跃
  USER_STATUS_INACTIVE = 2;                         // 非活跃
  USER_STATUS_SUSPENDED = 3;                        // 暂停
}

// 用户角色枚举
enum UserRole {
  USER_ROLE_UNSPECIFIED = 0;
  USER_ROLE_ADMIN = 1;                              // 管理员
  USER_ROLE_MANAGER = 2;                            // 经理
  USER_ROLE_USER = 3;                               // 普通用户
  USER_ROLE_VIEWER = 4;                             // 查看者
}

// 权限信息
message Permission {
  string resource = 1;                              // 资源名称
  repeated string actions = 2;                      // 允许的操作
}

// ============================================================================
// 请求和响应消息定义
// ============================================================================

// 认证请求
message AuthenticateRequest {
  string username = 1;                              // 用户名或邮箱
  string password = 2;                              // 密码
}

// 认证响应
message AuthenticateResponse {
  bool success = 1;                                 // 认证是否成功
  string message = 2;                               // 响应消息
  string access_token = 3;                          // 访问令牌
  string refresh_token = 4;                         // 刷新令牌
  User user = 5;                                    // 用户信息
  google.protobuf.Timestamp expires_at = 6;        // 令牌过期时间
}

// 刷新令牌请求
message RefreshTokenRequest {
  string refresh_token = 1;                         // 刷新令牌
}

// 刷新令牌响应
message RefreshTokenResponse {
  bool success = 1;                                 // 是否成功
  string message = 2;                               // 响应消息
  string access_token = 3;                          // 新的访问令牌
  google.protobuf.Timestamp expires_at = 4;        // 令牌过期时间
}

// 注册请求
message RegisterRequest {
  string username = 1;                              // 用户名
  string email = 2;                                 // 邮箱
  string password = 3;                              // 密码
  string full_name = 4;                             // 全名
  string phone = 5;                                 // 电话号码
}

// 注册响应
message RegisterResponse {
  bool success = 1;                                 // 注册是否成功
  string message = 2;                               // 响应消息
  User user = 3;                                    // 用户信息
}

// 获取用户请求
message GetUserRequest {
  string user_id = 1;                               // 用户ID
}

// 获取用户响应
message GetUserResponse {
  User user = 1;                                    // 用户信息
}

// 更新用户请求
message UpdateUserRequest {
  string user_id = 1;                               // 用户ID
  string full_name = 2;                             // 全名
  string avatar_url = 3;                            // 头像URL
  string phone = 4;                                 // 电话号码
  UserStatus status = 5;                            // 用户状态
}

// 更新用户响应
message UpdateUserResponse {
  bool success = 1;                                 // 更新是否成功
  string message = 2;                               // 响应消息
  User user = 3;                                    // 更新后的用户信息
}

// 删除用户请求
message DeleteUserRequest {
  string user_id = 1;                               // 用户ID
}

// 获取用户列表请求
message ListUsersRequest {
  int32 page = 1;                                   // 页码
  int32 page_size = 2;                              // 每页大小
  string search = 3;                                // 搜索关键词
  UserStatus status = 4;                            // 状态过滤
  UserRole role = 5;                                // 角色过滤
}

// 获取用户列表响应
message ListUsersResponse {
  repeated User users = 1;                          // 用户列表
  int32 total = 2;                                  // 总数
  int32 page = 3;                                   // 当前页码
  int32 page_size = 4;                              // 每页大小
}

// 检查权限请求
message CheckPermissionRequest {
  string user_id = 1;                               // 用户ID
  string resource = 2;                              // 资源名称
  string action = 3;                                // 操作名称
}

// 检查权限响应
message CheckPermissionResponse {
  bool allowed = 1;                                 // 是否允许
  string reason = 2;                                // 原因说明
}

// 获取用户角色请求
message GetUserRolesRequest {
  string user_id = 1;                               // 用户ID
}

// 获取用户角色响应
message GetUserRolesResponse {
  repeated string roles = 1;                        // 角色列表
  repeated Permission permissions = 2;              // 权限列表
}

// 分配角色请求
message AssignRoleRequest {
  string user_id = 1;                               // 用户ID
  string role = 2;                                  // 角色名称
  string assigned_by = 3;                           // 分配者ID
}

// 分配角色响应
message AssignRoleResponse {
  bool success = 1;                                 // 分配是否成功
  string message = 2;                               // 响应消息
}
