// AI项目管理平台 - 项目服务 gRPC 协议定义
// 定义项目管理相关的服务接口和数据结构

syntax = "proto3";

package aipm.project.v1;

option go_package = "github.com/aipm/shared/proto/project/v1;projectv1";
option java_package = "com.aipm.proto.project.v1";
option java_multiple_files = true;
option java_outer_classname = "ProjectProto";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// ============================================================================
// 项目服务接口定义
// ============================================================================

// 项目管理服务
service ProjectService {
  // 创建项目
  rpc CreateProject(CreateProjectRequest) returns (CreateProjectResponse);
  
  // 获取项目信息
  rpc GetProject(GetProjectRequest) returns (GetProjectResponse);
  
  // 更新项目信息
  rpc UpdateProject(UpdateProjectRequest) returns (UpdateProjectResponse);
  
  // 删除项目
  rpc DeleteProject(DeleteProjectRequest) returns (google.protobuf.Empty);
  
  // 获取项目列表
  rpc ListProjects(ListProjectsRequest) returns (ListProjectsResponse);
  
  // 添加项目成员
  rpc AddProjectMember(AddProjectMemberRequest) returns (AddProjectMemberResponse);
  
  // 移除项目成员
  rpc RemoveProjectMember(RemoveProjectMemberRequest) returns (google.protobuf.Empty);
  
  // 获取项目成员列表
  rpc ListProjectMembers(ListProjectMembersRequest) returns (ListProjectMembersResponse);
  
  // 更新项目状态
  rpc UpdateProjectStatus(UpdateProjectStatusRequest) returns (UpdateProjectStatusResponse);
}

// 任务管理服务
service TaskService {
  // 创建任务
  rpc CreateTask(CreateTaskRequest) returns (CreateTaskResponse);
  
  // 获取任务信息
  rpc GetTask(GetTaskRequest) returns (GetTaskResponse);
  
  // 更新任务信息
  rpc UpdateTask(UpdateTaskRequest) returns (UpdateTaskResponse);
  
  // 删除任务
  rpc DeleteTask(DeleteTaskRequest) returns (google.protobuf.Empty);
  
  // 获取任务列表
  rpc ListTasks(ListTasksRequest) returns (ListTasksResponse);
  
  // 分配任务
  rpc AssignTask(AssignTaskRequest) returns (AssignTaskResponse);
  
  // 更新任务状态
  rpc UpdateTaskStatus(UpdateTaskStatusRequest) returns (UpdateTaskStatusResponse);
}

// ============================================================================
// 数据结构定义
// ============================================================================

// 项目信息
message Project {
  string id = 1;                                    // 项目ID
  string name = 2;                                  // 项目名称
  string description = 3;                           // 项目描述
  string key = 4;                                   // 项目简称
  string organization_id = 5;                       // 组织ID
  string owner_id = 6;                              // 项目负责人ID
  ProjectStatus status = 7;                         // 项目状态
  ProjectPriority priority = 8;                     // 项目优先级
  google.protobuf.Timestamp start_date = 9;        // 开始日期
  google.protobuf.Timestamp end_date = 10;         // 结束日期
  int32 estimated_hours = 11;                       // 预估工时
  int32 actual_hours = 12;                          // 实际工时
  double budget = 13;                               // 预算
  double spent_budget = 14;                         // 已花费预算
  int32 progress = 15;                              // 进度百分比
  ProjectVisibility visibility = 16;                // 可见性
  ProjectMethodology methodology = 17;              // 项目方法论
  google.protobuf.Timestamp created_at = 18;       // 创建时间
  google.protobuf.Timestamp updated_at = 19;       // 更新时间
  string created_by = 20;                           // 创建者ID
  string updated_by = 21;                           // 更新者ID
}

// 项目状态枚举
enum ProjectStatus {
  PROJECT_STATUS_UNSPECIFIED = 0;
  PROJECT_STATUS_PLANNING = 1;                      // 规划中
  PROJECT_STATUS_ACTIVE = 2;                        // 进行中
  PROJECT_STATUS_ON_HOLD = 3;                       // 暂停
  PROJECT_STATUS_COMPLETED = 4;                     // 已完成
  PROJECT_STATUS_CANCELLED = 5;                     // 已取消
}

// 项目优先级枚举
enum ProjectPriority {
  PROJECT_PRIORITY_UNSPECIFIED = 0;
  PROJECT_PRIORITY_LOW = 1;                         // 低优先级
  PROJECT_PRIORITY_MEDIUM = 2;                      // 中优先级
  PROJECT_PRIORITY_HIGH = 3;                        // 高优先级
  PROJECT_PRIORITY_CRITICAL = 4;                    // 紧急
}

// 项目可见性枚举
enum ProjectVisibility {
  PROJECT_VISIBILITY_UNSPECIFIED = 0;
  PROJECT_VISIBILITY_PUBLIC = 1;                    // 公开
  PROJECT_VISIBILITY_PRIVATE = 2;                   // 私有
  PROJECT_VISIBILITY_INTERNAL = 3;                  // 内部
}

// 项目方法论枚举
enum ProjectMethodology {
  PROJECT_METHODOLOGY_UNSPECIFIED = 0;
  PROJECT_METHODOLOGY_AGILE = 1;                    // 敏捷
  PROJECT_METHODOLOGY_WATERFALL = 2;               // 瀑布
  PROJECT_METHODOLOGY_KANBAN = 3;                   // 看板
  PROJECT_METHODOLOGY_SCRUM = 4;                    // Scrum
}

// 项目成员信息
message ProjectMember {
  string id = 1;                                    // 成员ID
  string project_id = 2;                            // 项目ID
  string user_id = 3;                               // 用户ID
  ProjectMemberRole role = 4;                       // 成员角色
  ProjectMemberStatus status = 5;                   // 成员状态
  double hourly_rate = 6;                           // 小时费率
  google.protobuf.Timestamp joined_at = 7;         // 加入时间
  google.protobuf.Timestamp left_at = 8;           // 离开时间
}

// 项目成员角色枚举
enum ProjectMemberRole {
  PROJECT_MEMBER_ROLE_UNSPECIFIED = 0;
  PROJECT_MEMBER_ROLE_OWNER = 1;                    // 项目负责人
  PROJECT_MEMBER_ROLE_MANAGER = 2;                  // 项目经理
  PROJECT_MEMBER_ROLE_DEVELOPER = 3;                // 开发者
  PROJECT_MEMBER_ROLE_TESTER = 4;                   // 测试者
  PROJECT_MEMBER_ROLE_VIEWER = 5;                   // 查看者
}

// 项目成员状态枚举
enum ProjectMemberStatus {
  PROJECT_MEMBER_STATUS_UNSPECIFIED = 0;
  PROJECT_MEMBER_STATUS_ACTIVE = 1;                 // 活跃
  PROJECT_MEMBER_STATUS_INACTIVE = 2;               // 非活跃
}

// 任务信息
message Task {
  string id = 1;                                    // 任务ID
  string title = 2;                                 // 任务标题
  string description = 3;                           // 任务描述
  string project_id = 4;                            // 项目ID
  string parent_task_id = 5;                        // 父任务ID
  string assignee_id = 6;                           // 分配给用户ID
  string reporter_id = 7;                           // 报告者ID
  TaskStatus status = 8;                            // 任务状态
  TaskPriority priority = 9;                        // 任务优先级
  TaskType type = 10;                               // 任务类型
  double estimated_hours = 11;                      // 预估工时
  double actual_hours = 12;                         // 实际工时
  int32 story_points = 13;                          // 故事点
  google.protobuf.Timestamp due_date = 14;         // 截止日期
  google.protobuf.Timestamp start_date = 15;       // 开始日期
  google.protobuf.Timestamp completed_at = 16;     // 完成时间
  repeated string labels = 17;                      // 标签列表
  google.protobuf.Timestamp created_at = 18;       // 创建时间
  google.protobuf.Timestamp updated_at = 19;       // 更新时间
  string created_by = 20;                           // 创建者ID
  string updated_by = 21;                           // 更新者ID
}

// 任务状态枚举
enum TaskStatus {
  TASK_STATUS_UNSPECIFIED = 0;
  TASK_STATUS_TODO = 1;                             // 待办
  TASK_STATUS_IN_PROGRESS = 2;                      // 进行中
  TASK_STATUS_IN_REVIEW = 3;                        // 审查中
  TASK_STATUS_DONE = 4;                             // 已完成
  TASK_STATUS_CANCELLED = 5;                        // 已取消
}

// 任务优先级枚举
enum TaskPriority {
  TASK_PRIORITY_UNSPECIFIED = 0;
  TASK_PRIORITY_LOW = 1;                            // 低优先级
  TASK_PRIORITY_MEDIUM = 2;                         // 中优先级
  TASK_PRIORITY_HIGH = 3;                           // 高优先级
  TASK_PRIORITY_CRITICAL = 4;                       // 紧急
}

// 任务类型枚举
enum TaskType {
  TASK_TYPE_UNSPECIFIED = 0;
  TASK_TYPE_EPIC = 1;                               // 史诗
  TASK_TYPE_STORY = 2;                              // 用户故事
  TASK_TYPE_TASK = 3;                               // 任务
  TASK_TYPE_BUG = 4;                                // 缺陷
  TASK_TYPE_SUBTASK = 5;                            // 子任务
}

// ============================================================================
// 请求和响应消息定义
// ============================================================================

// 创建项目请求
message CreateProjectRequest {
  string name = 1;                                  // 项目名称
  string description = 2;                           // 项目描述
  string key = 3;                                   // 项目简称
  string organization_id = 4;                       // 组织ID
  ProjectPriority priority = 5;                     // 项目优先级
  google.protobuf.Timestamp start_date = 6;        // 开始日期
  google.protobuf.Timestamp end_date = 7;          // 结束日期
  int32 estimated_hours = 8;                        // 预估工时
  double budget = 9;                                // 预算
  ProjectVisibility visibility = 10;                // 可见性
  ProjectMethodology methodology = 11;              // 项目方法论
}

// 创建项目响应
message CreateProjectResponse {
  bool success = 1;                                 // 创建是否成功
  string message = 2;                               // 响应消息
  Project project = 3;                              // 项目信息
}

// 获取项目请求
message GetProjectRequest {
  string project_id = 1;                            // 项目ID
}

// 获取项目响应
message GetProjectResponse {
  Project project = 1;                              // 项目信息
}

// 更新项目请求
message UpdateProjectRequest {
  string project_id = 1;                            // 项目ID
  string name = 2;                                  // 项目名称
  string description = 3;                           // 项目描述
  ProjectPriority priority = 4;                     // 项目优先级
  google.protobuf.Timestamp start_date = 5;        // 开始日期
  google.protobuf.Timestamp end_date = 6;          // 结束日期
  int32 estimated_hours = 7;                        // 预估工时
  double budget = 8;                                // 预算
  ProjectVisibility visibility = 9;                 // 可见性
  ProjectMethodology methodology = 10;              // 项目方法论
}

// 更新项目响应
message UpdateProjectResponse {
  bool success = 1;                                 // 更新是否成功
  string message = 2;                               // 响应消息
  Project project = 3;                              // 更新后的项目信息
}

// 删除项目请求
message DeleteProjectRequest {
  string project_id = 1;                            // 项目ID
}

// 获取项目列表请求
message ListProjectsRequest {
  string organization_id = 1;                       // 组织ID
  int32 page = 2;                                   // 页码
  int32 page_size = 3;                              // 每页大小
  string search = 4;                                // 搜索关键词
  ProjectStatus status = 5;                         // 状态过滤
  ProjectPriority priority = 6;                     // 优先级过滤
}

// 获取项目列表响应
message ListProjectsResponse {
  repeated Project projects = 1;                    // 项目列表
  int32 total = 2;                                  // 总数
  int32 page = 3;                                   // 当前页码
  int32 page_size = 4;                              // 每页大小
}

// 添加项目成员请求
message AddProjectMemberRequest {
  string project_id = 1;                            // 项目ID
  string user_id = 2;                               // 用户ID
  ProjectMemberRole role = 3;                       // 成员角色
  double hourly_rate = 4;                           // 小时费率
}

// 添加项目成员响应
message AddProjectMemberResponse {
  bool success = 1;                                 // 添加是否成功
  string message = 2;                               // 响应消息
  ProjectMember member = 3;                         // 成员信息
}

// 移除项目成员请求
message RemoveProjectMemberRequest {
  string project_id = 1;                            // 项目ID
  string user_id = 2;                               // 用户ID
}

// 获取项目成员列表请求
message ListProjectMembersRequest {
  string project_id = 1;                            // 项目ID
  ProjectMemberRole role = 2;                       // 角色过滤
  ProjectMemberStatus status = 3;                   // 状态过滤
}

// 获取项目成员列表响应
message ListProjectMembersResponse {
  repeated ProjectMember members = 1;               // 成员列表
}

// 更新项目状态请求
message UpdateProjectStatusRequest {
  string project_id = 1;                            // 项目ID
  ProjectStatus status = 2;                         // 新状态
}

// 更新项目状态响应
message UpdateProjectStatusResponse {
  bool success = 1;                                 // 更新是否成功
  string message = 2;                               // 响应消息
  Project project = 3;                              // 更新后的项目信息
}

// 创建任务请求
message CreateTaskRequest {
  string title = 1;                                 // 任务标题
  string description = 2;                           // 任务描述
  string project_id = 3;                            // 项目ID
  string parent_task_id = 4;                        // 父任务ID
  string assignee_id = 5;                           // 分配给用户ID
  TaskPriority priority = 6;                        // 任务优先级
  TaskType type = 7;                                // 任务类型
  double estimated_hours = 8;                       // 预估工时
  int32 story_points = 9;                           // 故事点
  google.protobuf.Timestamp due_date = 10;         // 截止日期
  repeated string labels = 11;                      // 标签列表
}

// 创建任务响应
message CreateTaskResponse {
  bool success = 1;                                 // 创建是否成功
  string message = 2;                               // 响应消息
  Task task = 3;                                    // 任务信息
}

// 获取任务请求
message GetTaskRequest {
  string task_id = 1;                               // 任务ID
}

// 获取任务响应
message GetTaskResponse {
  Task task = 1;                                    // 任务信息
}

// 更新任务请求
message UpdateTaskRequest {
  string task_id = 1;                               // 任务ID
  string title = 2;                                 // 任务标题
  string description = 3;                           // 任务描述
  TaskPriority priority = 4;                        // 任务优先级
  double estimated_hours = 5;                       // 预估工时
  int32 story_points = 6;                           // 故事点
  google.protobuf.Timestamp due_date = 7;          // 截止日期
  repeated string labels = 8;                       // 标签列表
}

// 更新任务响应
message UpdateTaskResponse {
  bool success = 1;                                 // 更新是否成功
  string message = 2;                               // 响应消息
  Task task = 3;                                    // 更新后的任务信息
}

// 删除任务请求
message DeleteTaskRequest {
  string task_id = 1;                               // 任务ID
}

// 获取任务列表请求
message ListTasksRequest {
  string project_id = 1;                            // 项目ID
  string assignee_id = 2;                           // 分配给用户ID
  TaskStatus status = 3;                            // 状态过滤
  TaskPriority priority = 4;                        // 优先级过滤
  TaskType type = 5;                                // 类型过滤
  int32 page = 6;                                   // 页码
  int32 page_size = 7;                              // 每页大小
  string search = 8;                                // 搜索关键词
}

// 获取任务列表响应
message ListTasksResponse {
  repeated Task tasks = 1;                          // 任务列表
  int32 total = 2;                                  // 总数
  int32 page = 3;                                   // 当前页码
  int32 page_size = 4;                              // 每页大小
}

// 分配任务请求
message AssignTaskRequest {
  string task_id = 1;                               // 任务ID
  string assignee_id = 2;                           // 分配给用户ID
}

// 分配任务响应
message AssignTaskResponse {
  bool success = 1;                                 // 分配是否成功
  string message = 2;                               // 响应消息
  Task task = 3;                                    // 更新后的任务信息
}

// 更新任务状态请求
message UpdateTaskStatusRequest {
  string task_id = 1;                               // 任务ID
  TaskStatus status = 2;                            // 新状态
}

// 更新任务状态响应
message UpdateTaskStatusResponse {
  bool success = 1;                                 // 更新是否成功
  string message = 2;                               // 响应消息
  Task task = 3;                                    // 更新后的任务信息
}
