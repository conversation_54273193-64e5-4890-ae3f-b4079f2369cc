// AI项目管理平台 - 事件定义
// 定义系统中的各种事件类型和数据结构

package events

import (
	"encoding/json"
	"time"
)

// ============================================================================
// 基础事件结构
// ============================================================================

// Event 基础事件接口
type Event interface {
	GetEventID() string
	GetEventType() string
	GetEntityType() string
	GetEntityID() string
	GetUserID() string
	GetTimestamp() time.Time
	GetData() interface{}
	GetSource() string
	ToJSON() ([]byte, error)
}

// BaseEvent 基础事件结构
type BaseEvent struct {
	EventID    string                 `json:"event_id"`    // 事件ID
	EventType  string                 `json:"event_type"`  // 事件类型
	EntityType string                 `json:"entity_type"` // 实体类型
	EntityID   string                 `json:"entity_id"`   // 实体ID
	UserID     string                 `json:"user_id"`     // 操作用户ID
	Timestamp  time.Time              `json:"timestamp"`   // 事件时间戳
	Data       map[string]interface{} `json:"data"`        // 事件数据
	Source     string                 `json:"source"`      // 事件来源
}

// 实现Event接口
func (e *BaseEvent) GetEventID() string     { return e.EventID }
func (e *BaseEvent) GetEventType() string   { return e.EventType }
func (e *BaseEvent) GetEntityType() string  { return e.EntityType }
func (e *BaseEvent) GetEntityID() string    { return e.EntityID }
func (e *BaseEvent) GetUserID() string      { return e.UserID }
func (e *BaseEvent) GetTimestamp() time.Time { return e.Timestamp }
func (e *BaseEvent) GetData() interface{}   { return e.Data }
func (e *BaseEvent) GetSource() string      { return e.Source }

// ToJSON 将事件转换为JSON格式
func (e *BaseEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// ============================================================================
// 事件类型常量
// ============================================================================

// 用户相关事件
const (
	UserCreated     = "user.created"      // 用户创建
	UserUpdated     = "user.updated"      // 用户更新
	UserDeleted     = "user.deleted"      // 用户删除
	UserLoggedIn    = "user.logged_in"    // 用户登录
	UserLoggedOut   = "user.logged_out"   // 用户登出
	UserActivated   = "user.activated"    // 用户激活
	UserDeactivated = "user.deactivated"  // 用户停用
)

// 项目相关事件
const (
	ProjectCreated       = "project.created"        // 项目创建
	ProjectUpdated       = "project.updated"        // 项目更新
	ProjectDeleted       = "project.deleted"        // 项目删除
	ProjectStatusChanged = "project.status_changed" // 项目状态变更
	ProjectMemberAdded   = "project.member_added"   // 项目成员添加
	ProjectMemberRemoved = "project.member_removed" // 项目成员移除
	ProjectMemberUpdated = "project.member_updated" // 项目成员更新
)

// 任务相关事件
const (
	TaskCreated       = "task.created"        // 任务创建
	TaskUpdated       = "task.updated"        // 任务更新
	TaskDeleted       = "task.deleted"        // 任务删除
	TaskAssigned      = "task.assigned"       // 任务分配
	TaskUnassigned    = "task.unassigned"     // 任务取消分配
	TaskStatusChanged = "task.status_changed" // 任务状态变更
	TaskCompleted     = "task.completed"      // 任务完成
)

// AI分析相关事件
const (
	AnalysisStarted   = "analysis.started"   // 分析开始
	AnalysisCompleted = "analysis.completed" // 分析完成
	AnalysisFailed    = "analysis.failed"    // 分析失败
	PredictionMade    = "prediction.made"    // 预测生成
	RiskDetected      = "risk.detected"      // 风险检测
	AlertTriggered    = "alert.triggered"    // 告警触发
)

// 集成相关事件
const (
	IntegrationConnected    = "integration.connected"    // 集成连接
	IntegrationDisconnected = "integration.disconnected" // 集成断开
	IntegrationSyncStarted  = "integration.sync_started" // 同步开始
	IntegrationSyncCompleted = "integration.sync_completed" // 同步完成
	IntegrationSyncFailed   = "integration.sync_failed"  // 同步失败
	WebhookReceived         = "webhook.received"         // Webhook接收
)

// 通知相关事件
const (
	NotificationSent   = "notification.sent"   // 通知发送
	NotificationFailed = "notification.failed" // 通知失败
	NotificationRead   = "notification.read"   // 通知已读
	EmailSent          = "email.sent"          // 邮件发送
	SMSSent            = "sms.sent"            // 短信发送
)

// ============================================================================
// 实体类型常量
// ============================================================================

const (
	EntityTypeUser         = "user"         // 用户
	EntityTypeOrganization = "organization" // 组织
	EntityTypeProject      = "project"      // 项目
	EntityTypeTask         = "task"         // 任务
	EntityTypeMilestone    = "milestone"    // 里程碑
	EntityTypeDocument     = "document"     // 文档
	EntityTypeIntegration  = "integration"  // 集成
	EntityTypeNotification = "notification" // 通知
)

// ============================================================================
// 具体事件结构定义
// ============================================================================

// UserEvent 用户事件
type UserEvent struct {
	BaseEvent
	UserData UserEventData `json:"user_data"`
}

// UserEventData 用户事件数据
type UserEventData struct {
	Username  string `json:"username"`
	Email     string `json:"email"`
	FullName  string `json:"full_name"`
	Role      string `json:"role"`
	Status    string `json:"status"`
	IPAddress string `json:"ip_address,omitempty"`
}

// ProjectEvent 项目事件
type ProjectEvent struct {
	BaseEvent
	ProjectData ProjectEventData `json:"project_data"`
}

// ProjectEventData 项目事件数据
type ProjectEventData struct {
	Name           string    `json:"name"`
	Key            string    `json:"key"`
	OrganizationID string    `json:"organization_id"`
	OwnerID        string    `json:"owner_id"`
	Status         string    `json:"status"`
	Priority       string    `json:"priority"`
	StartDate      time.Time `json:"start_date,omitempty"`
	EndDate        time.Time `json:"end_date,omitempty"`
	Progress       int       `json:"progress"`
}

// TaskEvent 任务事件
type TaskEvent struct {
	BaseEvent
	TaskData TaskEventData `json:"task_data"`
}

// TaskEventData 任务事件数据
type TaskEventData struct {
	Title       string    `json:"title"`
	ProjectID   string    `json:"project_id"`
	AssigneeID  string    `json:"assignee_id,omitempty"`
	ReporterID  string    `json:"reporter_id"`
	Status      string    `json:"status"`
	Priority    string    `json:"priority"`
	Type        string    `json:"type"`
	DueDate     time.Time `json:"due_date,omitempty"`
	StoryPoints int       `json:"story_points,omitempty"`
}

// AnalysisEvent AI分析事件
type AnalysisEvent struct {
	BaseEvent
	AnalysisData AnalysisEventData `json:"analysis_data"`
}

// AnalysisEventData AI分析事件数据
type AnalysisEventData struct {
	ProjectID      string                 `json:"project_id"`
	AnalysisType   string                 `json:"analysis_type"`
	ModelVersion   string                 `json:"model_version"`
	Confidence     float64                `json:"confidence"`
	Result         map[string]interface{} `json:"result"`
	Duration       int64                  `json:"duration_ms"`
	ErrorMessage   string                 `json:"error_message,omitempty"`
}

// IntegrationEvent 集成事件
type IntegrationEvent struct {
	BaseEvent
	IntegrationData IntegrationEventData `json:"integration_data"`
}

// IntegrationEventData 集成事件数据
type IntegrationEventData struct {
	ProjectID        string                 `json:"project_id"`
	IntegrationType  string                 `json:"integration_type"`
	Status           string                 `json:"status"`
	SyncedRecords    int                    `json:"synced_records,omitempty"`
	ErrorMessage     string                 `json:"error_message,omitempty"`
	WebhookPayload   map[string]interface{} `json:"webhook_payload,omitempty"`
}

// NotificationEvent 通知事件
type NotificationEvent struct {
	BaseEvent
	NotificationData NotificationEventData `json:"notification_data"`
}

// NotificationEventData 通知事件数据
type NotificationEventData struct {
	RecipientID   string `json:"recipient_id"`
	Title         string `json:"title"`
	Content       string `json:"content"`
	Type          string `json:"type"`
	Category      string `json:"category"`
	Channel       string `json:"channel"`
	DeliveryID    string `json:"delivery_id,omitempty"`
	ErrorMessage  string `json:"error_message,omitempty"`
}

// ============================================================================
// 事件构造函数
// ============================================================================

// NewUserEvent 创建用户事件
func NewUserEvent(eventType, userID string, userData UserEventData) *UserEvent {
	return &UserEvent{
		BaseEvent: BaseEvent{
			EventID:    generateEventID(),
			EventType:  eventType,
			EntityType: EntityTypeUser,
			EntityID:   userID,
			UserID:     userID,
			Timestamp:  time.Now(),
			Source:     "user-service",
		},
		UserData: userData,
	}
}

// NewProjectEvent 创建项目事件
func NewProjectEvent(eventType, projectID, userID string, projectData ProjectEventData) *ProjectEvent {
	return &ProjectEvent{
		BaseEvent: BaseEvent{
			EventID:    generateEventID(),
			EventType:  eventType,
			EntityType: EntityTypeProject,
			EntityID:   projectID,
			UserID:     userID,
			Timestamp:  time.Now(),
			Source:     "project-service",
		},
		ProjectData: projectData,
	}
}

// NewTaskEvent 创建任务事件
func NewTaskEvent(eventType, taskID, userID string, taskData TaskEventData) *TaskEvent {
	return &TaskEvent{
		BaseEvent: BaseEvent{
			EventID:    generateEventID(),
			EventType:  eventType,
			EntityType: EntityTypeTask,
			EntityID:   taskID,
			UserID:     userID,
			Timestamp:  time.Now(),
			Source:     "project-service",
		},
		TaskData: taskData,
	}
}

// NewAnalysisEvent 创建AI分析事件
func NewAnalysisEvent(eventType, projectID, userID string, analysisData AnalysisEventData) *AnalysisEvent {
	return &AnalysisEvent{
		BaseEvent: BaseEvent{
			EventID:    generateEventID(),
			EventType:  eventType,
			EntityType: EntityTypeProject,
			EntityID:   projectID,
			UserID:     userID,
			Timestamp:  time.Now(),
			Source:     "ai-service",
		},
		AnalysisData: analysisData,
	}
}

// NewIntegrationEvent 创建集成事件
func NewIntegrationEvent(eventType, projectID, userID string, integrationData IntegrationEventData) *IntegrationEvent {
	return &IntegrationEvent{
		BaseEvent: BaseEvent{
			EventID:    generateEventID(),
			EventType:  eventType,
			EntityType: EntityTypeIntegration,
			EntityID:   projectID,
			UserID:     userID,
			Timestamp:  time.Now(),
			Source:     "integration-service",
		},
		IntegrationData: integrationData,
	}
}

// NewNotificationEvent 创建通知事件
func NewNotificationEvent(eventType, recipientID, userID string, notificationData NotificationEventData) *NotificationEvent {
	return &NotificationEvent{
		BaseEvent: BaseEvent{
			EventID:    generateEventID(),
			EventType:  eventType,
			EntityType: EntityTypeNotification,
			EntityID:   recipientID,
			UserID:     userID,
			Timestamp:  time.Now(),
			Source:     "notification-service",
		},
		NotificationData: notificationData,
	}
}

// ============================================================================
// 工具函数
// ============================================================================

// generateEventID 生成事件ID
func generateEventID() string {
	// 这里可以使用UUID或其他唯一ID生成方法
	// 为了简化，这里使用时间戳
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
