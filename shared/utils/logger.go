// AI项目管理平台 - 日志工具库
// 提供统一的日志记录功能

package utils

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// Logger 日志记录器接口
type Logger interface {
	Debug(args ...interface{})
	Debugf(format string, args ...interface{})
	Info(args ...interface{})
	Infof(format string, args ...interface{})
	Warn(args ...interface{})
	Warnf(format string, args ...interface{})
	Error(args ...interface{})
	Errorf(format string, args ...interface{})
	Fatal(args ...interface{})
	Fatalf(format string, args ...interface{})
	WithField(key string, value interface{}) Logger
	WithFields(fields map[string]interface{}) Logger
	WithContext(ctx context.Context) Logger
}

// AppLogger 应用日志记录器
type AppLogger struct {
	logger *logrus.Logger
	entry  *logrus.Entry
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `json:"level"`       // 日志级别
	Format     string `json:"format"`      // 日志格式 (json, text)
	Output     string `json:"output"`      // 输出目标 (stdout, file)
	Filename   string `json:"filename"`    // 日志文件名
	MaxSize    int    `json:"max_size"`    // 最大文件大小(MB)
	MaxBackups int    `json:"max_backups"` // 最大备份文件数
	MaxAge     int    `json:"max_age"`     // 最大保存天数
	Compress   bool   `json:"compress"`    // 是否压缩
}

// DefaultLogConfig 默认日志配置
var DefaultLogConfig = LogConfig{
	Level:      "info",
	Format:     "json",
	Output:     "stdout",
	Filename:   "app.log",
	MaxSize:    100,
	MaxBackups: 3,
	MaxAge:     28,
	Compress:   true,
}

// NewLogger 创建新的日志记录器
func NewLogger(config LogConfig) Logger {
	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	if config.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "function",
				logrus.FieldKeyFile:  "file",
			},
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FullTimestamp:   true,
			ForceColors:     true,
		})
	}

	// 设置输出目标
	if config.Output == "file" {
		file, err := os.OpenFile(config.Filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			logger.SetOutput(os.Stdout)
			logger.Warnf("无法打开日志文件 %s，使用标准输出: %v", config.Filename, err)
		} else {
			logger.SetOutput(file)
		}
	} else {
		logger.SetOutput(os.Stdout)
	}

	// 启用调用者信息
	logger.SetReportCaller(true)

	return &AppLogger{
		logger: logger,
		entry:  logger.WithFields(logrus.Fields{}),
	}
}

// GetDefaultLogger 获取默认日志记录器
func GetDefaultLogger() Logger {
	return NewLogger(DefaultLogConfig)
}

// Debug 记录调试级别日志
func (l *AppLogger) Debug(args ...interface{}) {
	l.entry.Debug(args...)
}

// Debugf 记录格式化调试级别日志
func (l *AppLogger) Debugf(format string, args ...interface{}) {
	l.entry.Debugf(format, args...)
}

// Info 记录信息级别日志
func (l *AppLogger) Info(args ...interface{}) {
	l.entry.Info(args...)
}

// Infof 记录格式化信息级别日志
func (l *AppLogger) Infof(format string, args ...interface{}) {
	l.entry.Infof(format, args...)
}

// Warn 记录警告级别日志
func (l *AppLogger) Warn(args ...interface{}) {
	l.entry.Warn(args...)
}

// Warnf 记录格式化警告级别日志
func (l *AppLogger) Warnf(format string, args ...interface{}) {
	l.entry.Warnf(format, args...)
}

// Error 记录错误级别日志
func (l *AppLogger) Error(args ...interface{}) {
	l.entry.Error(args...)
}

// Errorf 记录格式化错误级别日志
func (l *AppLogger) Errorf(format string, args ...interface{}) {
	l.entry.Errorf(format, args...)
}

// Fatal 记录致命错误级别日志
func (l *AppLogger) Fatal(args ...interface{}) {
	l.entry.Fatal(args...)
}

// Fatalf 记录格式化致命错误级别日志
func (l *AppLogger) Fatalf(format string, args ...interface{}) {
	l.entry.Fatalf(format, args...)
}

// WithField 添加单个字段
func (l *AppLogger) WithField(key string, value interface{}) Logger {
	return &AppLogger{
		logger: l.logger,
		entry:  l.entry.WithField(key, value),
	}
}

// WithFields 添加多个字段
func (l *AppLogger) WithFields(fields map[string]interface{}) Logger {
	return &AppLogger{
		logger: l.logger,
		entry:  l.entry.WithFields(fields),
	}
}

// WithContext 添加上下文信息
func (l *AppLogger) WithContext(ctx context.Context) Logger {
	entry := l.entry

	// 从上下文中提取请求ID
	if requestID := ctx.Value("request_id"); requestID != nil {
		entry = entry.WithField("request_id", requestID)
	}

	// 从上下文中提取用户ID
	if userID := ctx.Value("user_id"); userID != nil {
		entry = entry.WithField("user_id", userID)
	}

	// 从上下文中提取跟踪ID
	if traceID := ctx.Value("trace_id"); traceID != nil {
		entry = entry.WithField("trace_id", traceID)
	}

	return &AppLogger{
		logger: l.logger,
		entry:  entry,
	}
}

// ============================================================================
// 日志中间件和工具函数
// ============================================================================

// LoggerMiddleware HTTP日志中间件
type LoggerMiddleware struct {
	logger Logger
}

// NewLoggerMiddleware 创建日志中间件
func NewLoggerMiddleware(logger Logger) *LoggerMiddleware {
	return &LoggerMiddleware{logger: logger}
}

// LogRequest 记录HTTP请求日志
func (m *LoggerMiddleware) LogRequest(method, path, userAgent, clientIP string, statusCode int, duration time.Duration) {
	m.logger.WithFields(map[string]interface{}{
		"method":      method,
		"path":        path,
		"user_agent":  userAgent,
		"client_ip":   clientIP,
		"status_code": statusCode,
		"duration_ms": duration.Milliseconds(),
	}).Info("HTTP请求处理完成")
}

// LogError 记录错误日志
func (m *LoggerMiddleware) LogError(err error, context map[string]interface{}) {
	fields := map[string]interface{}{
		"error": err.Error(),
	}

	// 添加调用栈信息
	if pc, file, line, ok := runtime.Caller(1); ok {
		function := runtime.FuncForPC(pc).Name()
		fields["function"] = function
		fields["file"] = fmt.Sprintf("%s:%d", file, line)
	}

	// 合并上下文信息
	for k, v := range context {
		fields[k] = v
	}

	m.logger.WithFields(fields).Error("发生错误")
}

// ============================================================================
// 结构化日志工具函数
// ============================================================================

// LogUserAction 记录用户操作日志
func LogUserAction(logger Logger, userID, action, resource string, details map[string]interface{}) {
	fields := map[string]interface{}{
		"user_id":  userID,
		"action":   action,
		"resource": resource,
	}

	// 合并详细信息
	for k, v := range details {
		fields[k] = v
	}

	logger.WithFields(fields).Info("用户操作记录")
}

// LogBusinessEvent 记录业务事件日志
func LogBusinessEvent(logger Logger, eventType, entityType, entityID string, data map[string]interface{}) {
	fields := map[string]interface{}{
		"event_type":  eventType,
		"entity_type": entityType,
		"entity_id":   entityID,
	}

	// 合并事件数据
	for k, v := range data {
		fields[k] = v
	}

	logger.WithFields(fields).Info("业务事件记录")
}

// LogPerformance 记录性能日志
func LogPerformance(logger Logger, operation string, duration time.Duration, metadata map[string]interface{}) {
	fields := map[string]interface{}{
		"operation":   operation,
		"duration_ms": duration.Milliseconds(),
	}

	// 合并元数据
	for k, v := range metadata {
		fields[k] = v
	}

	// 根据耗时判断日志级别
	if duration > 5*time.Second {
		logger.WithFields(fields).Warn("操作耗时较长")
	} else if duration > 1*time.Second {
		logger.WithFields(fields).Info("操作完成")
	} else {
		logger.WithFields(fields).Debug("操作完成")
	}
}

// LogSecurityEvent 记录安全事件日志
func LogSecurityEvent(logger Logger, eventType, userID, clientIP string, details map[string]interface{}) {
	fields := map[string]interface{}{
		"security_event": eventType,
		"user_id":        userID,
		"client_ip":      clientIP,
	}

	// 合并详细信息
	for k, v := range details {
		fields[k] = v
	}

	logger.WithFields(fields).Warn("安全事件记录")
}

// ============================================================================
// 日志格式化工具
// ============================================================================

// SanitizeLogData 清理敏感数据
func SanitizeLogData(data map[string]interface{}) map[string]interface{} {
	sensitiveFields := []string{"password", "token", "secret", "key", "credential"}
	sanitized := make(map[string]interface{})

	for k, v := range data {
		key := strings.ToLower(k)
		isSensitive := false

		for _, field := range sensitiveFields {
			if strings.Contains(key, field) {
				isSensitive = true
				break
			}
		}

		if isSensitive {
			sanitized[k] = "***"
		} else {
			sanitized[k] = v
		}
	}

	return sanitized
}

// TruncateString 截断长字符串
func TruncateString(s string, maxLength int) string {
	if len(s) <= maxLength {
		return s
	}
	return s[:maxLength] + "..."
}
