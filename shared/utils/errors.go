// AI项目管理平台 - 错误处理工具库
// 提供统一的错误定义和处理功能

package utils

import (
	"fmt"
	"net/http"
	"runtime"
	"time"
)

// ============================================================================
// 错误类型定义
// ============================================================================

// ErrorCode 错误代码类型
type ErrorCode string

// 通用错误代码
const (
	ErrCodeUnknown          ErrorCode = "UNKNOWN"           // 未知错误
	ErrCodeInvalidRequest   ErrorCode = "INVALID_REQUEST"   // 无效请求
	ErrCodeUnauthorized     ErrorCode = "UNAUTHORIZED"      // 未授权
	ErrCodeForbidden        ErrorCode = "FORBIDDEN"         // 禁止访问
	ErrCodeNotFound         ErrorCode = "NOT_FOUND"         // 资源不存在
	ErrCodeConflict         ErrorCode = "CONFLICT"          // 资源冲突
	ErrCodeValidationFailed ErrorCode = "VALIDATION_FAILED" // 验证失败
	ErrCodeInternalError    ErrorCode = "INTERNAL_ERROR"    // 内部错误
	ErrCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE" // 服务不可用
)

// 业务错误代码
const (
	ErrCodeUserNotFound      ErrorCode = "USER_NOT_FOUND"       // 用户不存在
	ErrCodeUserAlreadyExists ErrorCode = "USER_ALREADY_EXISTS"  // 用户已存在
	ErrCodeInvalidCredentials ErrorCode = "INVALID_CREDENTIALS" // 无效凭证
	ErrCodeTokenExpired      ErrorCode = "TOKEN_EXPIRED"        // 令牌过期
	ErrCodeTokenInvalid      ErrorCode = "TOKEN_INVALID"        // 无效令牌
	
	ErrCodeProjectNotFound      ErrorCode = "PROJECT_NOT_FOUND"       // 项目不存在
	ErrCodeProjectAlreadyExists ErrorCode = "PROJECT_ALREADY_EXISTS"  // 项目已存在
	ErrCodeProjectAccessDenied  ErrorCode = "PROJECT_ACCESS_DENIED"   // 项目访问被拒绝
	
	ErrCodeTaskNotFound      ErrorCode = "TASK_NOT_FOUND"       // 任务不存在
	ErrCodeTaskAlreadyExists ErrorCode = "TASK_ALREADY_EXISTS"  // 任务已存在
	ErrCodeTaskAssignFailed  ErrorCode = "TASK_ASSIGN_FAILED"   // 任务分配失败
	
	ErrCodeAnalysisFailed    ErrorCode = "ANALYSIS_FAILED"      // 分析失败
	ErrCodeModelNotFound     ErrorCode = "MODEL_NOT_FOUND"      // 模型不存在
	ErrCodePredictionFailed  ErrorCode = "PREDICTION_FAILED"    // 预测失败
	
	ErrCodeIntegrationFailed ErrorCode = "INTEGRATION_FAILED"   // 集成失败
	ErrCodeSyncFailed        ErrorCode = "SYNC_FAILED"          // 同步失败
	ErrCodeWebhookFailed     ErrorCode = "WEBHOOK_FAILED"       // Webhook失败
)

// AppError 应用错误结构
type AppError struct {
	Code       ErrorCode              `json:"code"`        // 错误代码
	Message    string                 `json:"message"`     // 错误消息
	Details    string                 `json:"details"`     // 错误详情
	HTTPStatus int                    `json:"http_status"` // HTTP状态码
	Metadata   map[string]interface{} `json:"metadata"`    // 元数据
	Cause      error                  `json:"-"`           // 原始错误
	Stack      string                 `json:"stack"`       // 调用栈
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Unwrap 返回原始错误
func (e *AppError) Unwrap() error {
	return e.Cause
}

// WithMetadata 添加元数据
func (e *AppError) WithMetadata(key string, value interface{}) *AppError {
	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}
	e.Metadata[key] = value
	return e
}

// WithCause 设置原始错误
func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

// ============================================================================
// 错误构造函数
// ============================================================================

// NewError 创建新的应用错误
func NewError(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatusByCode(code),
		Stack:      getStack(),
	}
}

// NewErrorWithDetails 创建带详情的应用错误
func NewErrorWithDetails(code ErrorCode, message, details string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Details:    details,
		HTTPStatus: getHTTPStatusByCode(code),
		Stack:      getStack(),
	}
}

// WrapError 包装现有错误
func WrapError(err error, code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Details:    err.Error(),
		HTTPStatus: getHTTPStatusByCode(code),
		Cause:      err,
		Stack:      getStack(),
	}
}

// ============================================================================
// 预定义错误构造函数
// ============================================================================

// NewValidationError 创建验证错误
func NewValidationError(field, message string) *AppError {
	return NewErrorWithDetails(
		ErrCodeValidationFailed,
		"数据验证失败",
		fmt.Sprintf("字段 '%s': %s", field, message),
	).WithMetadata("field", field)
}

// NewNotFoundError 创建资源不存在错误
func NewNotFoundError(resource, id string) *AppError {
	return NewErrorWithDetails(
		ErrCodeNotFound,
		"资源不存在",
		fmt.Sprintf("%s (ID: %s) 不存在", resource, id),
	).WithMetadata("resource", resource).WithMetadata("id", id)
}

// NewUnauthorizedError 创建未授权错误
func NewUnauthorizedError(message string) *AppError {
	if message == "" {
		message = "用户未授权访问此资源"
	}
	return NewError(ErrCodeUnauthorized, message)
}

// NewForbiddenError 创建禁止访问错误
func NewForbiddenError(message string) *AppError {
	if message == "" {
		message = "用户无权限执行此操作"
	}
	return NewError(ErrCodeForbidden, message)
}

// NewConflictError 创建资源冲突错误
func NewConflictError(resource, message string) *AppError {
	return NewErrorWithDetails(
		ErrCodeConflict,
		"资源冲突",
		fmt.Sprintf("%s: %s", resource, message),
	).WithMetadata("resource", resource)
}

// NewInternalError 创建内部错误
func NewInternalError(message string) *AppError {
	if message == "" {
		message = "服务器内部错误"
	}
	return NewError(ErrCodeInternalError, message)
}

// NewServiceUnavailableError 创建服务不可用错误
func NewServiceUnavailableError(service string) *AppError {
	return NewErrorWithDetails(
		ErrCodeServiceUnavailable,
		"服务不可用",
		fmt.Sprintf("服务 '%s' 当前不可用", service),
	).WithMetadata("service", service)
}

// ============================================================================
// 业务错误构造函数
// ============================================================================

// NewUserNotFoundError 创建用户不存在错误
func NewUserNotFoundError(userID string) *AppError {
	return NewErrorWithDetails(
		ErrCodeUserNotFound,
		"用户不存在",
		fmt.Sprintf("用户 (ID: %s) 不存在", userID),
	).WithMetadata("user_id", userID)
}

// NewProjectNotFoundError 创建项目不存在错误
func NewProjectNotFoundError(projectID string) *AppError {
	return NewErrorWithDetails(
		ErrCodeProjectNotFound,
		"项目不存在",
		fmt.Sprintf("项目 (ID: %s) 不存在", projectID),
	).WithMetadata("project_id", projectID)
}

// NewTaskNotFoundError 创建任务不存在错误
func NewTaskNotFoundError(taskID string) *AppError {
	return NewErrorWithDetails(
		ErrCodeTaskNotFound,
		"任务不存在",
		fmt.Sprintf("任务 (ID: %s) 不存在", taskID),
	).WithMetadata("task_id", taskID)
}

// NewAnalysisFailedError 创建分析失败错误
func NewAnalysisFailedError(reason string) *AppError {
	return NewErrorWithDetails(
		ErrCodeAnalysisFailed,
		"AI分析失败",
		reason,
	)
}

// NewIntegrationFailedError 创建集成失败错误
func NewIntegrationFailedError(integrationType, reason string) *AppError {
	return NewErrorWithDetails(
		ErrCodeIntegrationFailed,
		"集成失败",
		fmt.Sprintf("%s 集成失败: %s", integrationType, reason),
	).WithMetadata("integration_type", integrationType)
}

// ============================================================================
// 工具函数
// ============================================================================

// getHTTPStatusByCode 根据错误代码获取HTTP状态码
func getHTTPStatusByCode(code ErrorCode) int {
	switch code {
	case ErrCodeInvalidRequest, ErrCodeValidationFailed:
		return http.StatusBadRequest
	case ErrCodeUnauthorized, ErrCodeInvalidCredentials, ErrCodeTokenExpired, ErrCodeTokenInvalid:
		return http.StatusUnauthorized
	case ErrCodeForbidden, ErrCodeProjectAccessDenied:
		return http.StatusForbidden
	case ErrCodeNotFound, ErrCodeUserNotFound, ErrCodeProjectNotFound, ErrCodeTaskNotFound, ErrCodeModelNotFound:
		return http.StatusNotFound
	case ErrCodeConflict, ErrCodeUserAlreadyExists, ErrCodeProjectAlreadyExists, ErrCodeTaskAlreadyExists:
		return http.StatusConflict
	case ErrCodeServiceUnavailable:
		return http.StatusServiceUnavailable
	default:
		return http.StatusInternalServerError
	}
}

// getStack 获取调用栈信息
func getStack() string {
	buf := make([]byte, 1024)
	for {
		n := runtime.Stack(buf, false)
		if n < len(buf) {
			return string(buf[:n])
		}
		buf = make([]byte, 2*len(buf))
	}
}

// IsAppError 检查是否为应用错误
func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

// GetAppError 获取应用错误
func GetAppError(err error) *AppError {
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}
	return nil
}

// HasErrorCode 检查错误是否包含指定代码
func HasErrorCode(err error, code ErrorCode) bool {
	if appErr := GetAppError(err); appErr != nil {
		return appErr.Code == code
	}
	return false
}

// ============================================================================
// 错误响应结构
// ============================================================================

// ErrorResponse API错误响应结构
type ErrorResponse struct {
	Success   bool                   `json:"success"`             // 请求是否成功
	Error     ErrorInfo              `json:"error"`               // 错误信息
	RequestID string                 `json:"request_id,omitempty"` // 请求ID
	Timestamp string                 `json:"timestamp"`           // 时间戳
}

// ErrorInfo 错误信息结构
type ErrorInfo struct {
	Code     ErrorCode              `json:"code"`               // 错误代码
	Message  string                 `json:"message"`            // 错误消息
	Details  string                 `json:"details,omitempty"`  // 错误详情
	Metadata map[string]interface{} `json:"metadata,omitempty"` // 元数据
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(err *AppError, requestID string) *ErrorResponse {
	return &ErrorResponse{
		Success: false,
		Error: ErrorInfo{
			Code:     err.Code,
			Message:  err.Message,
			Details:  err.Details,
			Metadata: err.Metadata,
		},
		RequestID: requestID,
		Timestamp: fmt.Sprintf("%d", time.Now().Unix()),
	}
}
