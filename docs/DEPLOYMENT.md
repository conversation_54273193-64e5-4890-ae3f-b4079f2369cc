# AI项目管理平台部署指南

本文档详细介绍了AI项目管理平台的部署流程和配置说明。

## 📋 部署前准备

### 系统要求

**最低配置**
- CPU: 4核心
- 内存: 8GB
- 存储: 100GB SSD
- 网络: 100Mbps

**推荐配置**
- CPU: 8核心
- 内存: 16GB
- 存储: 200GB SSD
- 网络: 1Gbps

### 软件依赖

- Docker 20.10+
- Docker Compose 2.0+
- Kubernetes 1.24+ (生产环境)
- Helm 3.8+ (可选)

## 🚀 快速部署

### 1. 使用Docker Compose部署

```bash
# 克隆项目
git clone https://github.com/your-org/ai-pm.git
cd ai-pm

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置必要的环境变量

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 2. 初始化数据库

```bash
# 等待数据库启动
docker-compose exec postgres pg_isready -U ai_pm_user -d ai_pm_db

# 运行数据库迁移
docker-compose exec backend python -m alembic upgrade head

# 导入初始数据
docker-compose exec backend python scripts/seed_data.py
```

### 3. 验证部署

访问以下地址验证服务是否正常运行：

- 前端应用: http://localhost:3000
- 后端API文档: http://localhost:8000/docs
- AI服务API: http://localhost:8001/docs
- 监控面板: http://localhost:3001 (Grafana)
- 日志查看: http://localhost:5601 (Kibana)

## 🏗️ 生产环境部署

### 1. Kubernetes集群部署

#### 准备工作

```bash
# 创建命名空间
kubectl create namespace ai-pm

# 创建密钥
kubectl create secret generic ai-pm-secrets \
  --from-literal=database-url="************************************/ai_pm_db" \
  --from-literal=redis-url="redis://redis:6379/0" \
  --from-literal=jwt-secret="your-jwt-secret" \
  -n ai-pm

# 应用配置文件
kubectl apply -f k8s/configmaps/ -n ai-pm
```

#### 部署数据库

```bash
# 部署PostgreSQL
kubectl apply -f k8s/deployments/postgres-deployment.yaml

# 部署Redis
kubectl apply -f k8s/deployments/redis-deployment.yaml

# 等待数据库就绪
kubectl wait --for=condition=ready pod -l app=postgres -n ai-pm --timeout=300s
```

#### 部署应用服务

```bash
# 部署后端服务
kubectl apply -f k8s/deployments/backend-deployment.yaml

# 部署AI服务
kubectl apply -f k8s/deployments/ai-service-deployment.yaml

# 部署通知服务
kubectl apply -f k8s/deployments/notification-deployment.yaml

# 部署集成服务
kubectl apply -f k8s/deployments/integration-deployment.yaml

# 部署前端应用
kubectl apply -f k8s/deployments/frontend-deployment.yaml
```

#### 配置Ingress

```bash
# 安装Nginx Ingress Controller
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml

# 应用Ingress配置
kubectl apply -f k8s/ingress/ai-pm-ingress.yaml
```

### 2. 监控和日志

#### Prometheus监控

```bash
# 安装Prometheus Operator
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --values monitoring/prometheus-values.yaml
```

#### 日志收集

```bash
# 安装ELK Stack
helm repo add elastic https://helm.elastic.co
helm repo update

# 安装Elasticsearch
helm install elasticsearch elastic/elasticsearch \
  --namespace logging \
  --create-namespace \
  --values monitoring/elasticsearch-values.yaml

# 安装Kibana
helm install kibana elastic/kibana \
  --namespace logging \
  --values monitoring/kibana-values.yaml

# 安装Filebeat
helm install filebeat elastic/filebeat \
  --namespace logging \
  --values monitoring/filebeat-values.yaml
```

## 🔧 配置说明

### 环境变量配置

创建 `.env` 文件并配置以下变量：

```env
# 基础配置
ENVIRONMENT=production
DEBUG=false

# 数据库配置
DATABASE_URL=**********************************************/ai_pm_db
REDIS_URL=redis://redis:6379/0

# 安全配置
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# 服务URL配置
AI_SERVICE_URL=http://ai-service:8001
NOTIFICATION_SERVICE_URL=http://notification-service:8002
INTEGRATION_SERVICE_URL=http://integration-service:8003

# 第三方集成
JIRA_BASE_URL=https://your-domain.atlassian.net
JIRA_USERNAME=your-jira-username
JIRA_API_TOKEN=your-jira-api-token

DINGTALK_APP_KEY=your-dingtalk-app-key
DINGTALK_APP_SECRET=your-dingtalk-app-secret

SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# 文件存储
FILE_STORAGE_TYPE=local
FILE_STORAGE_PATH=/app/uploads
MAX_FILE_SIZE=10485760

# AI模型配置
MODEL_PATH=/app/models
TENSORFLOW_SERVING_URL=http://tensorflow-serving:8501

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
SENTRY_DSN=your-sentry-dsn
```

### Nginx配置

```nginx
upstream backend {
    server backend:8000;
}

upstream ai-service {
    server ai-service:8001;
}

upstream notification-service {
    server notification-service:8002;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # 前端静态文件
    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # API路由
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket连接
    location /ws {
        proxy_pass http://notification-service;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready
   
   # 查看数据库日志
   docker-compose logs postgres
   ```

2. **服务启动失败**
   ```bash
   # 查看服务日志
   docker-compose logs [service-name]
   
   # 检查服务健康状态
   docker-compose ps
   ```

3. **内存不足**
   ```bash
   # 调整服务资源限制
   # 编辑 docker-compose.yml 中的 deploy.resources 配置
   ```

### 性能优化

1. **数据库优化**
   - 配置连接池
   - 添加适当索引
   - 定期清理日志

2. **缓存优化**
   - 配置Redis持久化
   - 调整缓存过期策略
   - 监控缓存命中率

3. **应用优化**
   - 启用Gzip压缩
   - 配置CDN
   - 优化静态资源

## 📊 监控和维护

### 健康检查

```bash
# 检查所有服务状态
curl http://localhost:8000/health
curl http://localhost:8001/health
curl http://localhost:8002/health

# 检查数据库连接
docker-compose exec backend python -c "from database import engine; print(engine.execute('SELECT 1').scalar())"
```

### 备份策略

```bash
# 数据库备份
docker-compose exec postgres pg_dump -U ai_pm_user ai_pm_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Redis备份
docker-compose exec redis redis-cli BGSAVE
```

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f --tail=100 backend

# 清理旧日志
docker system prune -f
```

## 🔐 安全配置

### SSL/TLS配置

1. 获取SSL证书（Let's Encrypt推荐）
2. 配置Nginx SSL
3. 强制HTTPS重定向
4. 配置HSTS头

### 防火墙配置

```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 定期安全更新

```bash
# 更新系统包
apt update && apt upgrade -y

# 更新Docker镜像
docker-compose pull
docker-compose up -d
```

---

如需更多帮助，请参考项目文档或联系技术支持团队。
