# AI项目管理平台系统管理员指南

本指南面向系统管理员，提供平台部署、配置、维护和故障排查的详细说明。

## 📋 目录

1. [系统架构概述](#系统架构概述)
2. [部署和安装](#部署和安装)
3. [系统配置](#系统配置)
4. [用户和权限管理](#用户和权限管理)
5. [监控和日志](#监控和日志)
6. [备份和恢复](#备份和恢复)
7. [故障排查](#故障排查)
8. [性能优化](#性能优化)

## 🏗️ 系统架构概述

### 整体架构

AI项目管理平台采用微服务架构，主要组件包括：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API网关       │    │   负载均衡器    │
│   (React)       │◄──►│   (Nginx)       │◄──►│   (HAProxy)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ 用户管理服务 │ │ 项目管理服务│ │ AI分析服务 │
        │ (Spring Boot)│ │(Spring Boot)│ │ (FastAPI)  │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼───────────────▼───────────────▼──────┐
        │              数据层                          │
        │  ┌──────────┐  ┌──────────┐  ┌──────────┐   │
        │  │PostgreSQL│  │   Redis  │  │ MinIO    │   │
        │  │   数据库 │  │   缓存   │  │ 对象存储 │   │
        │  └──────────┘  └──────────┘  └──────────┘   │
        └─────────────────────────────────────────────┘
```

### 技术栈

- **前端**: React 18 + TypeScript + Ant Design
- **后端**: Spring Boot 3.x + FastAPI
- **数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **消息队列**: RabbitMQ
- **容器化**: Docker + Kubernetes
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 🚀 部署和安装

### 环境要求

#### 最低配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 8核心
- **内存**: 16GB RAM
- **存储**: 500GB SSD
- **网络**: 1Gbps

### 使用Docker Compose部署（开发/测试环境）

1. **克隆代码仓库**:
```bash
git clone https://github.com/your-org/ai-pm.git
cd ai-pm
```

2. **配置环境变量**:
```bash
cp .env.example .env
# 编辑.env文件，配置数据库密码、API密钥等
```

3. **启动服务**:
```bash
docker-compose up -d
```

4. **初始化数据库**:
```bash
docker-compose exec backend python manage.py migrate
docker-compose exec backend python manage.py createsuperuser
```

### 使用Kubernetes部署（生产环境）

1. **准备Kubernetes集群**:
```bash
# 确保kubectl已配置并能访问集群
kubectl cluster-info
```

2. **创建命名空间**:
```bash
kubectl create namespace ai-pm-prod
```

3. **配置密钥**:
```bash
# 创建数据库密钥
kubectl create secret generic database-secret \
  --from-literal=url="********************************/dbname" \
  --namespace=ai-pm-prod

# 创建应用密钥
kubectl create secret generic app-secret \
  --from-literal=jwt-secret="your-jwt-secret" \
  --from-literal=secret-key="your-secret-key" \
  --namespace=ai-pm-prod
```

4. **使用Helm部署**:
```bash
# 添加Helm仓库
helm repo add ai-pm https://charts.ai-pm.com
helm repo update

# 部署应用
helm install ai-pm ai-pm/ai-pm \
  --namespace=ai-pm-prod \
  --values=values-prod.yaml
```

5. **验证部署**:
```bash
# 检查Pod状态
kubectl get pods -n ai-pm-prod

# 检查服务状态
kubectl get services -n ai-pm-prod

# 查看应用日志
kubectl logs -f deployment/backend -n ai-pm-prod
```

## ⚙️ 系统配置

### 数据库配置

#### PostgreSQL优化

1. **连接池配置**:
```properties
# application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

2. **数据库参数调优**:
```sql
-- postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
```

#### Redis配置

```redis
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 应用配置

#### 后端服务配置

```yaml
# application-prod.yml
server:
  port: 8000
  compression:
    enabled: true
  
spring:
  profiles:
    active: prod
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        use_sql_comments: false
  
logging:
  level:
    com.aipm: INFO
    org.springframework.security: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/ai-pm/application.log
    max-size: 100MB
    max-history: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
```

#### AI服务配置

```python
# config/settings.py
import os

# 基础配置
DEBUG = False
ENVIRONMENT = "production"

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL")
REDIS_URL = os.getenv("REDIS_URL")

# AI模型配置
AI_MODEL_PATH = "/app/models"
MODEL_CACHE_SIZE = 1000
PREDICTION_TIMEOUT = 30

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/ai-pm/ai-service.log',
            'maxBytes': 100*1024*1024,  # 100MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
    },
    'loggers': {
        '': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 👥 用户和权限管理

### 用户角色定义

| 角色 | 权限描述 | 主要功能 |
|------|----------|----------|
| 超级管理员 | 系统完全控制权限 | 系统配置、用户管理、数据管理 |
| 组织管理员 | 组织内完全管理权限 | 项目管理、成员管理、报表查看 |
| 项目经理 | 项目完全管理权限 | 项目规划、任务分配、进度跟踪 |
| 开发者 | 开发相关权限 | 任务执行、代码提交、测试 |
| 测试员 | 测试相关权限 | 测试执行、缺陷管理、质量报告 |
| 查看者 | 只读权限 | 查看项目信息、报表 |

### 权限管理命令

```bash
# 创建超级管理员
kubectl exec -it deployment/backend -n ai-pm-prod -- \
  python manage.py createsuperuser

# 批量导入用户
kubectl exec -it deployment/backend -n ai-pm-prod -- \
  python manage.py import_users users.csv

# 重置用户密码
kubectl exec -it deployment/backend -n ai-pm-prod -- \
  python manage.py reset_password username

# 禁用用户账户
kubectl exec -it deployment/backend -n ai-pm-prod -- \
  python manage.py disable_user username
```

### LDAP集成配置

```yaml
# application.yml
spring:
  ldap:
    urls: ldap://ldap.company.com:389
    base: dc=company,dc=com
    username: cn=admin,dc=company,dc=com
    password: ${LDAP_PASSWORD}
    
security:
  ldap:
    enabled: true
    user-search-base: ou=users
    user-search-filter: (uid={0})
    group-search-base: ou=groups
    group-search-filter: (member={0})
```

## 📊 监控和日志

### Prometheus监控配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'ai-pm-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  - job_name: 'ai-pm-ai-service'
    static_configs:
      - targets: ['ai-service:8001']
    metrics_path: '/metrics'
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 关键监控指标

1. **应用指标**:
   - API响应时间
   - 请求成功率
   - 并发用户数
   - 任务处理速度

2. **系统指标**:
   - CPU使用率
   - 内存使用率
   - 磁盘I/O
   - 网络流量

3. **业务指标**:
   - 活跃用户数
   - 项目创建数
   - 任务完成率
   - AI预测准确率

### 日志管理

#### 日志收集配置

```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/ai-pm/*.log
  fields:
    service: ai-pm
    environment: production
  fields_under_root: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "ai-pm-logs-%{+yyyy.MM.dd}"

setup.template.name: "ai-pm"
setup.template.pattern: "ai-pm-*"
```

#### 日志查询示例

```bash
# 查看错误日志
curl -X GET "elasticsearch:9200/ai-pm-logs-*/_search" -H 'Content-Type: application/json' -d'
{
  "query": {
    "bool": {
      "must": [
        {"match": {"level": "ERROR"}},
        {"range": {"@timestamp": {"gte": "now-1h"}}}
      ]
    }
  }
}'

# 查看特定服务日志
kubectl logs -f deployment/backend -n ai-pm-prod --tail=100

# 查看所有Pod日志
kubectl logs -f -l app=ai-pm -n ai-pm-prod --tail=100
```

## 💾 备份和恢复

### 数据库备份

#### 自动备份脚本

```bash
#!/bin/bash
# backup-database.sh

BACKUP_DIR="/backup/database"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="ai_pm_prod"
DB_USER="ai_pm_user"
DB_HOST="postgres-primary"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行数据库备份
kubectl exec -n ai-pm-prod deployment/postgres -- \
  pg_dump -U $DB_USER -h $DB_HOST $DB_NAME | \
  gzip > $BACKUP_DIR/ai_pm_backup_$DATE.sql.gz

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: ai_pm_backup_$DATE.sql.gz"
```

#### 数据库恢复

```bash
#!/bin/bash
# restore-database.sh

BACKUP_FILE=$1
DB_NAME="ai_pm_prod"
DB_USER="ai_pm_user"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file>"
    exit 1
fi

# 停止应用服务
kubectl scale deployment backend --replicas=0 -n ai-pm-prod

# 恢复数据库
gunzip -c $BACKUP_FILE | \
kubectl exec -i -n ai-pm-prod deployment/postgres -- \
  psql -U $DB_USER -d $DB_NAME

# 重启应用服务
kubectl scale deployment backend --replicas=3 -n ai-pm-prod

echo "数据库恢复完成"
```

### 文件备份

```bash
#!/bin/bash
# backup-files.sh

BACKUP_DIR="/backup/files"
DATE=$(date +%Y%m%d_%H%M%S)

# 备份上传文件
kubectl exec -n ai-pm-prod deployment/backend -- \
  tar czf - /app/uploads | \
  cat > $BACKUP_DIR/uploads_backup_$DATE.tar.gz

# 备份配置文件
kubectl get configmaps -n ai-pm-prod -o yaml > \
  $BACKUP_DIR/configmaps_backup_$DATE.yaml

kubectl get secrets -n ai-pm-prod -o yaml > \
  $BACKUP_DIR/secrets_backup_$DATE.yaml

echo "文件备份完成"
```

## 🔧 故障排查

### 常见问题和解决方案

#### 1. 应用无法启动

**症状**: Pod处于CrashLoopBackOff状态

**排查步骤**:
```bash
# 查看Pod状态
kubectl get pods -n ai-pm-prod

# 查看Pod详细信息
kubectl describe pod <pod-name> -n ai-pm-prod

# 查看应用日志
kubectl logs <pod-name> -n ai-pm-prod --previous
```

**常见原因**:
- 数据库连接失败
- 配置文件错误
- 资源不足
- 镜像拉取失败

#### 2. 数据库连接问题

**症状**: 应用报数据库连接错误

**排查步骤**:
```bash
# 检查数据库Pod状态
kubectl get pods -l app=postgres -n ai-pm-prod

# 测试数据库连接
kubectl exec -it deployment/postgres -n ai-pm-prod -- \
  psql -U ai_pm_user -d ai_pm_prod -c "SELECT 1;"

# 检查数据库配置
kubectl get secret database-secret -n ai-pm-prod -o yaml
```

#### 3. 性能问题

**症状**: 应用响应缓慢

**排查步骤**:
```bash
# 检查资源使用情况
kubectl top pods -n ai-pm-prod
kubectl top nodes

# 查看应用指标
curl http://backend:8000/actuator/metrics

# 检查数据库性能
kubectl exec -it deployment/postgres -n ai-pm-prod -- \
  psql -U ai_pm_user -d ai_pm_prod -c "
  SELECT query, calls, total_time, mean_time 
  FROM pg_stat_statements 
  ORDER BY total_time DESC LIMIT 10;"
```

### 故障排查工具

#### 1. 健康检查脚本

```bash
#!/bin/bash
# health-check.sh

echo "=== AI项目管理平台健康检查 ==="

# 检查Pod状态
echo "1. 检查Pod状态..."
kubectl get pods -n ai-pm-prod

# 检查服务状态
echo "2. 检查服务状态..."
kubectl get services -n ai-pm-prod

# 检查Ingress状态
echo "3. 检查Ingress状态..."
kubectl get ingress -n ai-pm-prod

# 检查存储状态
echo "4. 检查存储状态..."
kubectl get pvc -n ai-pm-prod

# 测试API健康状态
echo "5. 测试API健康状态..."
curl -f http://backend.ai-pm-prod.svc.cluster.local:8000/health

echo "健康检查完成"
```

#### 2. 性能诊断脚本

```bash
#!/bin/bash
# performance-diagnosis.sh

echo "=== 性能诊断报告 ==="

# CPU和内存使用情况
echo "1. 资源使用情况:"
kubectl top pods -n ai-pm-prod

# 网络连接状态
echo "2. 网络连接状态:"
kubectl exec -it deployment/backend -n ai-pm-prod -- netstat -an

# 数据库连接池状态
echo "3. 数据库连接池状态:"
curl -s http://backend:8000/actuator/metrics/hikaricp.connections.active

# JVM内存使用情况
echo "4. JVM内存使用情况:"
curl -s http://backend:8000/actuator/metrics/jvm.memory.used

echo "性能诊断完成"
```

## ⚡ 性能优化

### 应用层优化

1. **JVM参数调优**:
```bash
JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication"
```

2. **数据库连接池优化**:
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
```

3. **缓存策略优化**:
```java
@Cacheable(value = "projects", key = "#projectId")
public Project getProject(String projectId) {
    return projectRepository.findById(projectId);
}

@CacheEvict(value = "projects", key = "#project.id")
public Project updateProject(Project project) {
    return projectRepository.save(project);
}
```

### 数据库优化

1. **索引优化**:
```sql
-- 创建复合索引
CREATE INDEX idx_tasks_project_status_assignee 
ON tasks (project_id, status, assignee_id);

-- 创建部分索引
CREATE INDEX idx_active_projects 
ON projects (created_at) 
WHERE status = 'active';
```

2. **查询优化**:
```sql
-- 使用EXPLAIN分析查询计划
EXPLAIN ANALYZE 
SELECT * FROM tasks 
WHERE project_id = 'xxx' AND status = 'active';

-- 优化分页查询
SELECT * FROM tasks 
WHERE project_id = 'xxx' 
ORDER BY created_at DESC 
LIMIT 20 OFFSET 0;
```

### 系统级优化

1. **Kubernetes资源配置**:
```yaml
resources:
  requests:
    cpu: 500m
    memory: 1Gi
  limits:
    cpu: 2000m
    memory: 4Gi
```

2. **水平扩缩容配置**:
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

---

**系统管理员指南完成** ✅

如需更多技术支持，请联系开发团队或查阅详细的API文档。
