# OAuth2集成指南

## 概述

本文档详细说明了如何在AI项目管理平台中集成OAuth2认证，支持多种ID Provider，包括Google、GitHub和自定义OAuth2提供商。

## 功能特性

- ✅ 支持多种OAuth2提供商（Google、GitHub、自定义）
- ✅ 与现有JWT认证系统兼容
- ✅ 自动用户注册和信息同步
- ✅ 安全的令牌管理
- ✅ 前端组件化集成
- ✅ 完整的错误处理

## 架构设计

### 后端组件

1. **OAuth2Config** - OAuth2配置类
2. **CustomOAuth2UserService** - 自定义用户服务
3. **OAuth2UserInfo** - 用户信息抽象类
4. **OAuth2UserInfoFactory** - 用户信息工厂
5. **OAuth2AuthenticationSuccessHandler** - 认证成功处理器
6. **OAuth2AuthenticationFailureHandler** - 认证失败处理器
7. **OAuth2Controller** - OAuth2 API控制器

### 前端组件

1. **OAuth2Service** - OAuth2服务类
2. **OAuth2Login** - OAuth2登录组件
3. **OAuth2Redirect** - OAuth2回调处理页面

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中配置OAuth2提供商信息：

```bash
# Google OAuth2
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth2
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# 自定义OAuth2提供商
CUSTOM_CLIENT_ID=your-custom-client-id
CUSTOM_CLIENT_SECRET=your-custom-client-secret
CUSTOM_AUTHORIZATION_URI=https://your-provider.com/oauth2/authorize
CUSTOM_TOKEN_URI=https://your-provider.com/oauth2/token
CUSTOM_USER_INFO_URI=https://your-provider.com/oauth2/userinfo
CUSTOM_USER_NAME_ATTRIBUTE=sub
CUSTOM_JWK_SET_URI=https://your-provider.com/.well-known/jwks.json
```

### 2. 应用配置

在 `application.yml` 中启用OAuth2配置：

```yaml
spring:
  profiles:
    include: oauth2
```

### 3. 自定义ID Provider配置

对于自定义OAuth2提供商，需要确保以下端点可用：

- **授权端点**: `/oauth2/authorize`
- **令牌端点**: `/oauth2/token`
- **用户信息端点**: `/oauth2/userinfo`
- **JWKS端点**: `/.well-known/jwks.json`

## 使用指南

### 1. 后端集成

OAuth2认证已集成到Spring Security配置中，支持以下端点：

- `GET /oauth2/authorization/{provider}` - 启动OAuth2认证
- `GET /oauth2/callback/{provider}` - OAuth2回调处理
- `GET /api/v1/oauth2/providers` - 获取可用提供商
- `GET /api/v1/oauth2/status` - 检查认证状态

### 2. 前端集成

在登录页面中使用OAuth2Login组件：

```tsx
import OAuth2Login from '@/components/OAuth2Login'

const LoginPage = () => {
  return (
    <div>
      {/* 传统登录表单 */}
      <LoginForm />
      
      {/* OAuth2登录选项 */}
      <OAuth2Login
        onSuccess={(token, refreshToken) => {
          // 处理登录成功
        }}
        onError={(error, message) => {
          // 处理登录失败
        }}
      />
    </div>
  )
}
```

### 3. 路由配置

在路由中添加OAuth2回调页面：

```tsx
import OAuth2Redirect from '@/pages/OAuth2Redirect'

const routes = [
  // 其他路由...
  {
    path: '/oauth2/redirect',
    component: OAuth2Redirect
  }
]
```

## 自定义ID Provider实现

### 1. 服务端要求

自定义OAuth2提供商需要实现标准的OAuth2.0授权码流程：

```
1. 用户点击登录 → 重定向到授权端点
2. 用户授权 → 重定向回应用并携带授权码
3. 应用使用授权码换取访问令牌
4. 使用访问令牌获取用户信息
```

### 2. 用户信息端点响应格式

用户信息端点应返回JSON格式的用户数据：

```json
{
  "id": "user-unique-id",
  "name": "用户姓名",
  "email": "<EMAIL>",
  "avatar": "https://example.com/avatar.jpg"
}
```

### 3. 字段映射配置

在 `application-oauth2.yml` 中配置字段映射：

```yaml
app:
  oauth2:
    user-info-mapping:
      custom:
        id-attribute: user_id        # 如果ID字段不是"id"
        name-attribute: full_name    # 如果姓名字段不是"name"
        email-attribute: email_addr  # 如果邮箱字段不是"email"
        picture-attribute: photo_url # 如果头像字段不是"avatar"
```

## 安全考虑

### 1. 重定向URI验证

系统会验证OAuth2回调的重定向URI是否在授权列表中：

```yaml
app:
  oauth2:
    authorized-redirect-uris:
      - http://localhost:3000/oauth2/redirect
      - https://your-domain.com/oauth2/redirect
```

### 2. 令牌安全

- 访问令牌和刷新令牌使用JWT格式
- 刷新令牌存储在Redis中，支持撤销
- 令牌包含用户权限信息

### 3. 用户数据同步

- OAuth2用户首次登录时自动创建账户
- 后续登录时同步用户信息（姓名、头像等）
- 邮箱作为用户唯一标识，支持多种登录方式

## 故障排除

### 1. 常见问题

**问题**: OAuth2认证失败
**解决**: 检查客户端ID、密钥和重定向URI配置

**问题**: 用户信息获取失败
**解决**: 检查用户信息端点URL和字段映射配置

**问题**: 重定向URI不匹配
**解决**: 确保重定向URI在OAuth2提供商和应用配置中一致

### 2. 调试方法

启用OAuth2调试日志：

```yaml
logging:
  level:
    org.springframework.security.oauth2: DEBUG
    com.aipm.usermanagement.security.oauth2: DEBUG
```

### 3. 测试工具

使用以下端点测试OAuth2集成：

- `GET /api/v1/oauth2/providers` - 验证提供商配置
- `GET /api/v1/oauth2/status` - 检查认证状态

## 部署注意事项

1. **环境变量**: 确保生产环境中正确配置所有OAuth2相关环境变量
2. **HTTPS**: 生产环境必须使用HTTPS
3. **域名配置**: 在OAuth2提供商中正确配置回调域名
4. **数据库迁移**: 运行V2迁移脚本添加OAuth2字段

## 扩展支持

要添加新的OAuth2提供商：

1. 在 `OAuth2UserInfoFactory` 中添加新的用户信息实现
2. 在 `application-oauth2.yml` 中添加提供商配置
3. 在前端 `OAuth2Login` 组件中添加图标和样式
4. 更新环境变量配置

## 总结

OAuth2集成为AI项目管理平台提供了灵活的第三方认证支持，用户可以使用Google、GitHub或自定义ID Provider登录，同时保持与现有JWT认证系统的兼容性。
