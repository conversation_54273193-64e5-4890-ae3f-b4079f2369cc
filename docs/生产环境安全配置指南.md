# 生产环境安全配置指南

## 概述

本文档详细说明了AI项目管理平台OAuth2集成在生产环境中的安全配置要求和最佳实践。

## 1. OAuth2提供商配置

### 1.1 Google OAuth2配置

1. **创建Google Cloud项目**
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 创建新项目或选择现有项目
   - 启用Google+ API和Google Identity API

2. **配置OAuth2客户端**
   ```
   应用类型: Web应用
   授权重定向URI:
   - https://api.your-domain.com/oauth2/callback/google
   - https://your-domain.com/oauth2/redirect
   
   授权JavaScript源:
   - https://your-domain.com
   ```

3. **安全设置**
   - 限制API密钥使用范围
   - 定期轮换客户端密钥
   - 监控API使用情况

### 1.2 GitHub OAuth2配置

1. **创建GitHub OAuth应用**
   - 访问 GitHub Settings > Developer settings > OAuth Apps
   - 点击 "New OAuth App"

2. **配置应用信息**
   ```
   应用名称: AI项目管理平台
   主页URL: https://your-domain.com
   授权回调URL: https://api.your-domain.com/oauth2/callback/github
   ```

3. **安全设置**
   - 启用双因素认证
   - 定期审查授权的应用
   - 监控异常登录活动

### 1.3 自定义OAuth2提供商配置

1. **端点安全要求**
   - 所有端点必须使用HTTPS
   - 实施速率限制
   - 启用CORS保护

2. **令牌安全**
   - 使用强随机数生成器
   - 实施令牌过期机制
   - 支持令牌撤销

## 2. 应用安全配置

### 2.1 HTTPS配置

1. **SSL/TLS证书**
   ```bash
   # 使用Let's Encrypt获取免费证书
   certbot certonly --webroot -w /var/www/html -d your-domain.com -d api.your-domain.com
   ```

2. **Nginx SSL配置**
   ```nginx
   ssl_protocols TLSv1.2 TLSv1.3;
   ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
   ssl_prefer_server_ciphers off;
   ssl_session_cache shared:SSL:10m;
   ssl_session_timeout 10m;
   
   # HSTS
   add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
   ```

### 2.2 环境变量安全

1. **敏感信息管理**
   ```bash
   # 使用强密钥
   JWT_SECRET=$(openssl rand -base64 64)
   
   # 数据库密码
   DATABASE_PASSWORD=$(openssl rand -base64 32)
   
   # Redis密码
   REDIS_PASSWORD=$(openssl rand -base64 32)
   ```

2. **密钥轮换策略**
   - JWT密钥: 每90天轮换
   - 数据库密码: 每180天轮换
   - OAuth2客户端密钥: 每年轮换

### 2.3 网络安全

1. **防火墙配置**
   ```bash
   # 只允许必要的端口
   ufw allow 22/tcp    # SSH
   ufw allow 80/tcp    # HTTP (重定向到HTTPS)
   ufw allow 443/tcp   # HTTPS
   ufw deny 8080/tcp   # 应用端口（通过Nginx代理）
   ```

2. **IP白名单**
   ```nginx
   # 管理接口访问控制
   location /actuator/ {
       allow 10.0.0.0/8;
       allow **********/12;
       allow ***********/16;
       deny all;
   }
   ```

## 3. 数据库安全

### 3.1 PostgreSQL安全配置

1. **连接安全**
   ```postgresql
   # postgresql.conf
   ssl = on
   ssl_cert_file = 'server.crt'
   ssl_key_file = 'server.key'
   ssl_ca_file = 'ca.crt'
   
   # pg_hba.conf
   hostssl all all 0.0.0.0/0 md5
   ```

2. **用户权限**
   ```sql
   -- 创建专用用户
   CREATE USER aipm_user WITH PASSWORD 'strong_password';
   
   -- 授予最小权限
   GRANT CONNECT ON DATABASE aipm_prod TO aipm_user;
   GRANT USAGE ON SCHEMA public TO aipm_user;
   GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO aipm_user;
   ```

### 3.2 Redis安全配置

1. **认证配置**
   ```redis
   # redis.conf
   requirepass your_strong_redis_password
   
   # 禁用危险命令
   rename-command FLUSHDB ""
   rename-command FLUSHALL ""
   rename-command DEBUG ""
   ```

2. **网络安全**
   ```redis
   # 绑定到内网地址
   bind 127.0.0.1 **********
   
   # 禁用保护模式（在有密码的情况下）
   protected-mode yes
   ```

## 4. 应用安全

### 4.1 Spring Security配置

1. **CSRF保护**
   ```java
   // 对于API端点禁用CSRF，但启用其他保护
   http.csrf().disable()
       .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
   ```

2. **安全头配置**
   ```java
   http.headers()
       .frameOptions().deny()
       .contentTypeOptions().and()
       .httpStrictTransportSecurity(hstsConfig -> hstsConfig
           .maxAgeInSeconds(31536000)
           .includeSubdomains(true));
   ```

### 4.2 JWT安全

1. **令牌配置**
   ```yaml
   security:
     jwt:
       secret: ${JWT_SECRET}  # 至少256位
       access-token-expiration: 3600000   # 1小时
       refresh-token-expiration: 604800000 # 7天
   ```

2. **令牌验证**
   ```java
   // 验证令牌签名
   // 检查令牌过期时间
   // 验证令牌类型
   // 检查黑名单
   ```

## 5. 监控和审计

### 5.1 安全监控

1. **关键指标监控**
   - OAuth2认证失败率
   - 异常登录模式
   - API调用频率
   - 错误率趋势

2. **告警配置**
   ```yaml
   # Prometheus告警规则
   - alert: SuspiciousOAuth2Activity
     expr: rate(oauth2_failed_attempts[5m]) > 10
     for: 2m
     labels:
       severity: warning
   ```

### 5.2 审计日志

1. **日志配置**
   ```yaml
   logging:
     level:
       org.springframework.security: INFO
       com.aipm.usermanagement.security: INFO
     pattern:
       file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
   ```

2. **关键事件记录**
   - 用户登录/登出
   - OAuth2认证成功/失败
   - 权限变更
   - 敏感操作

## 6. 备份和恢复

### 6.1 数据备份

1. **数据库备份**
   ```bash
   # 每日备份脚本
   #!/bin/bash
   pg_dump -h localhost -U aipm_user aipm_prod | gzip > /backups/aipm_$(date +%Y%m%d).sql.gz
   ```

2. **配置备份**
   ```bash
   # 备份配置文件
   tar -czf /backups/config_$(date +%Y%m%d).tar.gz /app/config/
   ```

### 6.2 灾难恢复

1. **恢复计划**
   - RTO (恢复时间目标): 4小时
   - RPO (恢复点目标): 1小时
   - 备份保留期: 30天

2. **恢复测试**
   - 每月进行恢复演练
   - 验证备份完整性
   - 测试恢复流程

## 7. 合规性要求

### 7.1 数据保护

1. **GDPR合规**
   - 用户数据最小化
   - 数据删除权
   - 数据可移植性

2. **数据加密**
   - 传输加密: TLS 1.2+
   - 存储加密: AES-256
   - 密钥管理: HSM或云KMS

### 7.2 访问控制

1. **最小权限原则**
   - 基于角色的访问控制
   - 定期权限审查
   - 访问日志记录

2. **身份验证**
   - 多因素认证
   - 密码策略
   - 会话管理

## 8. 安全检查清单

### 8.1 部署前检查

- [ ] SSL证书已配置且有效
- [ ] 所有密钥已生成并安全存储
- [ ] 防火墙规则已配置
- [ ] 数据库访问权限已限制
- [ ] OAuth2提供商配置已验证
- [ ] 监控和告警已配置
- [ ] 备份策略已实施

### 8.2 定期安全检查

- [ ] 依赖项安全扫描
- [ ] 渗透测试
- [ ] 访问日志审查
- [ ] 权限审查
- [ ] 密钥轮换
- [ ] 备份验证
- [ ] 安全培训

## 9. 应急响应

### 9.1 安全事件响应

1. **事件分类**
   - P0: 数据泄露
   - P1: 服务中断
   - P2: 安全漏洞
   - P3: 异常活动

2. **响应流程**
   - 事件检测
   - 影响评估
   - 遏制措施
   - 根因分析
   - 恢复操作
   - 事后总结

### 9.2 联系信息

- 安全团队: <EMAIL>
- 运维团队: <EMAIL>
- 管理层: <EMAIL>

## 10. 总结

生产环境的安全配置是一个持续的过程，需要定期审查和更新。遵循本指南的建议，可以确保OAuth2集成在生产环境中的安全性和可靠性。
