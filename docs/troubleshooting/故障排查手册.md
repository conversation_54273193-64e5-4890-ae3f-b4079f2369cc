# AI项目管理平台故障排查手册

本手册提供常见故障的快速诊断和解决方案，帮助运维人员快速定位和解决问题。

## 🚨 紧急故障处理流程

### 故障等级定义

| 等级 | 描述 | 响应时间 | 解决时间 |
|------|------|----------|----------|
| P0 - 严重 | 系统完全不可用 | 15分钟 | 2小时 |
| P1 - 高 | 核心功能不可用 | 30分钟 | 4小时 |
| P2 - 中 | 部分功能异常 | 2小时 | 24小时 |
| P3 - 低 | 性能问题或小功能异常 | 24小时 | 72小时 |

### 紧急联系方式

```
🔥 P0/P1故障紧急联系：
- 技术负责人：张三 (13800138000)
- 运维负责人：李四 (13800138001)
- 开发负责人：王五 (13800138002)

📧 故障报告邮箱：<EMAIL>
💬 紧急沟通群：AI-PM紧急响应群
```

## 🔍 快速诊断工具

### 一键健康检查脚本

```bash
#!/bin/bash
# quick-health-check.sh

echo "🏥 AI项目管理平台快速健康检查"
echo "检查时间: $(date)"
echo "=========================================="

# 1. 检查Kubernetes集群状态
echo "1️⃣ 检查Kubernetes集群状态..."
kubectl cluster-info --request-timeout=10s
if [ $? -eq 0 ]; then
    echo "✅ Kubernetes集群正常"
else
    echo "❌ Kubernetes集群异常"
    exit 1
fi

# 2. 检查命名空间和Pod状态
echo -e "\n2️⃣ 检查Pod状态..."
kubectl get pods -n ai-pm-prod --no-headers | while read line; do
    pod_name=$(echo $line | awk '{print $1}')
    pod_status=$(echo $line | awk '{print $3}')
    
    if [[ "$pod_status" == "Running" ]]; then
        echo "✅ $pod_name: $pod_status"
    else
        echo "❌ $pod_name: $pod_status"
    fi
done

# 3. 检查服务可达性
echo -e "\n3️⃣ 检查服务可达性..."
services=("frontend:80" "backend:8000" "ai-service:8001")
for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    service_port=$(echo $service | cut -d: -f2)
    
    kubectl exec -n ai-pm-prod deployment/backend -- \
        curl -f -m 5 http://$service_name:$service_port/health >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ $service_name 服务正常"
    else
        echo "❌ $service_name 服务异常"
    fi
done

# 4. 检查数据库连接
echo -e "\n4️⃣ 检查数据库连接..."
kubectl exec -n ai-pm-prod deployment/postgres -- \
    pg_isready -U ai_pm_user >/dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL数据库正常"
else
    echo "❌ PostgreSQL数据库异常"
fi

# 5. 检查Redis连接
echo -e "\n5️⃣ 检查Redis连接..."
kubectl exec -n ai-pm-prod deployment/redis -- \
    redis-cli ping >/dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Redis缓存正常"
else
    echo "❌ Redis缓存异常"
fi

# 6. 检查存储状态
echo -e "\n6️⃣ 检查存储状态..."
kubectl get pvc -n ai-pm-prod --no-headers | while read line; do
    pvc_name=$(echo $line | awk '{print $1}')
    pvc_status=$(echo $line | awk '{print $2}')
    
    if [[ "$pvc_status" == "Bound" ]]; then
        echo "✅ $pvc_name: $pvc_status"
    else
        echo "❌ $pvc_name: $pvc_status"
    fi
done

echo -e "\n=========================================="
echo "🏥 健康检查完成"
```

### 性能监控脚本

```bash
#!/bin/bash
# performance-monitor.sh

echo "📊 AI项目管理平台性能监控"
echo "监控时间: $(date)"
echo "=========================================="

# 1. 检查资源使用情况
echo "1️⃣ 资源使用情况:"
kubectl top pods -n ai-pm-prod --sort-by=cpu

# 2. 检查API响应时间
echo -e "\n2️⃣ API响应时间测试:"
apis=("/health" "/api/v1/projects" "/api/v1/dashboard")
for api in "${apis[@]}"; do
    response_time=$(kubectl exec -n ai-pm-prod deployment/backend -- \
        curl -o /dev/null -s -w "%{time_total}" http://backend:8000$api)
    
    if (( $(echo "$response_time < 1.0" | bc -l) )); then
        echo "✅ $api: ${response_time}s"
    else
        echo "⚠️ $api: ${response_time}s (慢)"
    fi
done

# 3. 检查数据库性能
echo -e "\n3️⃣ 数据库性能指标:"
kubectl exec -n ai-pm-prod deployment/postgres -- \
    psql -U ai_pm_user -d ai_pm_prod -c "
    SELECT 
        'Active Connections' as metric,
        count(*) as value
    FROM pg_stat_activity 
    WHERE state = 'active'
    UNION ALL
    SELECT 
        'Database Size' as metric,
        pg_size_pretty(pg_database_size('ai_pm_prod')) as value;"

echo -e "\n=========================================="
echo "📊 性能监控完成"
```

## 🔧 常见故障及解决方案

### 1. 应用无法启动

#### 故障现象
- Pod状态为`CrashLoopBackOff`
- 应用日志显示启动错误
- 健康检查失败

#### 诊断步骤

```bash
# 1. 查看Pod状态和事件
kubectl get pods -n ai-pm-prod
kubectl describe pod <pod-name> -n ai-pm-prod

# 2. 查看应用日志
kubectl logs <pod-name> -n ai-pm-prod --previous
kubectl logs <pod-name> -n ai-pm-prod --tail=100

# 3. 检查配置和密钥
kubectl get configmaps -n ai-pm-prod
kubectl get secrets -n ai-pm-prod
```

#### 常见原因及解决方案

**原因1: 数据库连接失败**
```bash
# 检查数据库密钥
kubectl get secret database-secret -n ai-pm-prod -o yaml

# 测试数据库连接
kubectl exec -it deployment/postgres -n ai-pm-prod -- \
    psql -U ai_pm_user -d ai_pm_prod -c "SELECT 1;"

# 解决方案：更新数据库密钥
kubectl create secret generic database-secret \
    --from-literal=url="********************************/dbname" \
    --namespace=ai-pm-prod \
    --dry-run=client -o yaml | kubectl apply -f -
```

**原因2: 内存不足**
```bash
# 检查资源限制
kubectl describe pod <pod-name> -n ai-pm-prod | grep -A 5 "Limits"

# 解决方案：增加内存限制
kubectl patch deployment backend -n ai-pm-prod -p '
{
    "spec": {
        "template": {
            "spec": {
                "containers": [{
                    "name": "backend",
                    "resources": {
                        "limits": {"memory": "2Gi"},
                        "requests": {"memory": "1Gi"}
                    }
                }]
            }
        }
    }
}'
```

**原因3: 镜像拉取失败**
```bash
# 检查镜像拉取状态
kubectl describe pod <pod-name> -n ai-pm-prod | grep -A 10 "Events"

# 解决方案：检查镜像仓库访问权限
kubectl create secret docker-registry regcred \
    --docker-server=ghcr.io \
    --docker-username=<username> \
    --docker-password=<token> \
    --namespace=ai-pm-prod
```

### 2. 数据库性能问题

#### 故障现象
- API响应时间过长
- 数据库连接池耗尽
- 查询超时错误

#### 诊断步骤

```bash
# 1. 检查数据库连接数
kubectl exec -it deployment/postgres -n ai-pm-prod -- \
    psql -U ai_pm_user -d ai_pm_prod -c "
    SELECT count(*) as active_connections 
    FROM pg_stat_activity 
    WHERE state = 'active';"

# 2. 查看慢查询
kubectl exec -it deployment/postgres -n ai-pm-prod -- \
    psql -U ai_pm_user -d ai_pm_prod -c "
    SELECT query, calls, total_time, mean_time 
    FROM pg_stat_statements 
    ORDER BY total_time DESC LIMIT 10;"

# 3. 检查锁等待
kubectl exec -it deployment/postgres -n ai-pm-prod -- \
    psql -U ai_pm_user -d ai_pm_prod -c "
    SELECT blocked_locks.pid AS blocked_pid,
           blocked_activity.usename AS blocked_user,
           blocking_locks.pid AS blocking_pid,
           blocking_activity.usename AS blocking_user,
           blocked_activity.query AS blocked_statement
    FROM pg_catalog.pg_locks blocked_locks
    JOIN pg_catalog.pg_stat_activity blocked_activity 
         ON blocked_activity.pid = blocked_locks.pid
    JOIN pg_catalog.pg_locks blocking_locks 
         ON blocking_locks.locktype = blocked_locks.locktype
    JOIN pg_catalog.pg_stat_activity blocking_activity 
         ON blocking_activity.pid = blocking_locks.pid
    WHERE NOT blocked_locks.granted;"
```

#### 解决方案

**方案1: 优化连接池配置**
```yaml
# 更新应用配置
spring:
  datasource:
    hikari:
      maximum-pool-size: 30
      minimum-idle: 10
      connection-timeout: 20000
      idle-timeout: 300000
```

**方案2: 添加数据库索引**
```sql
-- 分析缺失的索引
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE schemaname = 'public' 
ORDER BY n_distinct DESC;

-- 创建必要的索引
CREATE INDEX CONCURRENTLY idx_tasks_project_status 
ON tasks (project_id, status);
```

**方案3: 清理长时间运行的查询**
```bash
# 终止长时间运行的查询
kubectl exec -it deployment/postgres -n ai-pm-prod -- \
    psql -U ai_pm_user -d ai_pm_prod -c "
    SELECT pg_terminate_backend(pid) 
    FROM pg_stat_activity 
    WHERE state = 'active' 
    AND query_start < now() - interval '5 minutes'
    AND query NOT LIKE '%pg_stat_activity%';"
```

### 3. 内存泄漏问题

#### 故障现象
- Pod内存使用持续增长
- 频繁的OOMKilled事件
- 应用响应变慢

#### 诊断步骤

```bash
# 1. 监控内存使用趋势
kubectl top pods -n ai-pm-prod --sort-by=memory

# 2. 查看JVM内存使用情况
kubectl exec -n ai-pm-prod deployment/backend -- \
    curl -s http://localhost:8000/actuator/metrics/jvm.memory.used

# 3. 生成堆转储文件
kubectl exec -n ai-pm-prod deployment/backend -- \
    jcmd 1 GC.run_finalization
kubectl exec -n ai-pm-prod deployment/backend -- \
    jcmd 1 VM.gc
```

#### 解决方案

**方案1: 调整JVM参数**
```bash
# 更新JVM配置
JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 
           -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heapdump.hprof"
```

**方案2: 重启有问题的Pod**
```bash
# 重启特定Pod
kubectl delete pod <pod-name> -n ai-pm-prod

# 滚动重启Deployment
kubectl rollout restart deployment/backend -n ai-pm-prod
```

### 4. 网络连接问题

#### 故障现象
- 服务间调用失败
- 外部API访问超时
- DNS解析错误

#### 诊断步骤

```bash
# 1. 测试服务间连通性
kubectl exec -n ai-pm-prod deployment/backend -- \
    curl -v http://ai-service:8001/health

# 2. 检查DNS解析
kubectl exec -n ai-pm-prod deployment/backend -- \
    nslookup ai-service.ai-pm-prod.svc.cluster.local

# 3. 检查网络策略
kubectl get networkpolicies -n ai-pm-prod
```

#### 解决方案

**方案1: 重启CoreDNS**
```bash
kubectl delete pods -n kube-system -l k8s-app=kube-dns
```

**方案2: 检查Service配置**
```bash
# 验证Service端点
kubectl get endpoints -n ai-pm-prod

# 重新创建Service
kubectl delete service ai-service -n ai-pm-prod
kubectl apply -f ai-service-service.yaml
```

### 5. 存储空间不足

#### 故障现象
- Pod无法启动
- 数据库写入失败
- 日志文件无法创建

#### 诊断步骤

```bash
# 1. 检查PVC使用情况
kubectl get pvc -n ai-pm-prod
kubectl describe pvc <pvc-name> -n ai-pm-prod

# 2. 检查节点磁盘空间
kubectl get nodes
kubectl describe node <node-name>

# 3. 查看存储使用详情
kubectl exec -n ai-pm-prod deployment/backend -- df -h
```

#### 解决方案

**方案1: 扩容PVC**
```bash
# 编辑PVC增加存储空间
kubectl patch pvc data-postgres-0 -n ai-pm-prod -p '
{
    "spec": {
        "resources": {
            "requests": {
                "storage": "100Gi"
            }
        }
    }
}'
```

**方案2: 清理日志文件**
```bash
# 清理应用日志
kubectl exec -n ai-pm-prod deployment/backend -- \
    find /var/log -name "*.log" -mtime +7 -delete

# 清理Docker镜像
docker system prune -f
```

## 📞 故障上报流程

### 故障报告模板

```
故障报告 - [故障等级] [故障标题]

基本信息:
- 发现时间: YYYY-MM-DD HH:MM:SS
- 影响范围: [用户数/功能模块]
- 故障等级: P0/P1/P2/P3
- 报告人: [姓名]

故障描述:
[详细描述故障现象和影响]

初步诊断:
[已执行的诊断步骤和发现]

临时措施:
[已采取的临时解决措施]

需要支持:
[需要哪些团队或人员的支持]
```

### 故障处理记录

```
故障处理记录 - [故障ID]

处理时间线:
- HH:MM 故障发现
- HH:MM 开始诊断
- HH:MM 确定根因
- HH:MM 实施修复
- HH:MM 验证恢复
- HH:MM 故障关闭

根本原因:
[详细的根本原因分析]

解决方案:
[具体的解决步骤]

预防措施:
[避免类似故障的改进措施]

经验教训:
[从此次故障中学到的经验]
```

## 🔄 故障恢复验证清单

### 系统恢复验证

- [ ] 所有Pod状态为Running
- [ ] 所有Service可正常访问
- [ ] 数据库连接正常
- [ ] 缓存服务正常
- [ ] API健康检查通过
- [ ] 前端页面可正常加载
- [ ] 用户登录功能正常
- [ ] 核心业务功能正常

### 性能验证

- [ ] API响应时间 < 200ms
- [ ] 数据库查询时间正常
- [ ] 内存使用率 < 80%
- [ ] CPU使用率 < 70%
- [ ] 磁盘使用率 < 85%

### 监控验证

- [ ] 监控指标恢复正常
- [ ] 告警规则正常工作
- [ ] 日志收集正常
- [ ] 备份任务正常执行

---

**故障排查手册完成** ✅

紧急情况请立即联系技术支持团队！
