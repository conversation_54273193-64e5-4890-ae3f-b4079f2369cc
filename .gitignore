# AI项目管理平台 .gitignore 配置文件

# ============================================================================
# 操作系统相关文件
# ============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ============================================================================
# IDE和编辑器配置文件
# ============================================================================
.vscode/
.idea/
*.swp
*.swo
*~

# ============================================================================
# Java相关文件
# ============================================================================
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
target/
.mvn/wrapper/maven-wrapper.jar
.mvn/wrapper/maven-wrapper.properties

# ============================================================================
# Python相关文件
# ============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# ============================================================================
# Node.js相关文件
# ============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.next
.nuxt
dist/

# ============================================================================
# Go相关文件
# ============================================================================
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# ============================================================================
# Docker相关文件
# ============================================================================
.dockerignore

# ============================================================================
# 数据库文件
# ============================================================================
*.db
*.sqlite
*.sqlite3

# ============================================================================
# 日志文件
# ============================================================================
*.log
logs/

# ============================================================================
# 临时文件和缓存
# ============================================================================
tmp/
temp/
.tmp/
.cache/
.sass-cache/

# ============================================================================
# 环境配置文件
# ============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ============================================================================
# 备份文件
# ============================================================================
*.bak
*.backup
*.old

# ============================================================================
# 机器学习模型文件
# ============================================================================
*.pkl
*.pickle
*.h5
*.pb
models/
checkpoints/
mlruns/

# ============================================================================
# 测试覆盖率报告
# ============================================================================
coverage/
.nyc_output/

# ============================================================================
# 构建输出
# ============================================================================
out/
build/
dist/

# ============================================================================
# 密钥和证书文件
# ============================================================================
*.pem
*.key
*.crt
*.p12
secrets/

# ============================================================================
# 本地配置文件
# ============================================================================
config/local.yml
config/local.json
local.properties

# ============================================================================
# 数据文件
# ============================================================================
data/
*.csv
*.json
*.xml
!config/*.json
!docs/*.json

# ============================================================================
# Kubernetes相关
# ============================================================================
*.kubeconfig
kustomization.yaml

# ============================================================================
# Terraform相关
# ============================================================================
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# ============================================================================
# 监控和日志
# ============================================================================
prometheus/
grafana/
elasticsearch/
kibana/
