/**
 * 端到端用户工作流测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

const { test, expect } = require('@playwright/test')

// 测试配置
const BASE_URL = process.env.E2E_BASE_URL || 'http://localhost:3000'
const API_BASE_URL = process.env.E2E_API_URL || 'http://localhost:8000'

// 测试用户数据
const testUser = {
  username: 'e2etest',
  email: '<EMAIL>',
  password: 'testpassword123',
  fullName: '端到端测试用户'
}

const testProject = {
  name: '端到端测试项目',
  description: '这是一个端到端测试项目',
  startDate: '2025-01-01',
  endDate: '2025-12-31',
  budget: 100000
}

const testTask = {
  title: '端到端测试任务',
  description: '这是一个端到端测试任务',
  priority: 'high',
  dueDate: '2025-08-30'
}

test.describe('用户认证流程', () => {
  test('用户注册、登录和登出流程', async ({ page }) => {
    // 访问首页
    await page.goto(BASE_URL)
    
    // 点击注册按钮
    await page.click('text=注册')
    
    // 填写注册表单
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.fill('[data-testid="fullname-input"]', testUser.fullName)
    
    // 提交注册表单
    await page.click('[data-testid="register-submit"]')
    
    // 验证注册成功
    await expect(page.locator('text=注册成功')).toBeVisible()
    
    // 登录
    await page.click('text=登录')
    await page.fill('[data-testid="login-username"]', testUser.username)
    await page.fill('[data-testid="login-password"]', testUser.password)
    await page.click('[data-testid="login-submit"]')
    
    // 验证登录成功，应该跳转到仪表板
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible()
    await expect(page.locator(`text=欢迎, ${testUser.fullName}`)).toBeVisible()
    
    // 登出
    await page.click('[data-testid="user-menu"]')
    await page.click('text=登出')
    
    // 验证登出成功，应该回到登录页面
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible()
  })
  
  test('无效登录处理', async ({ page }) => {
    await page.goto(BASE_URL)
    
    // 尝试使用无效凭据登录
    await page.click('text=登录')
    await page.fill('[data-testid="login-username"]', 'invaliduser')
    await page.fill('[data-testid="login-password"]', 'wrongpassword')
    await page.click('[data-testid="login-submit"]')
    
    // 验证错误消息
    await expect(page.locator('text=用户名或密码错误')).toBeVisible()
  })
})

test.describe('项目管理流程', () => {
  test.beforeEach(async ({ page }) => {
    // 每个测试前先登录
    await page.goto(BASE_URL)
    await page.click('text=登录')
    await page.fill('[data-testid="login-username"]', testUser.username)
    await page.fill('[data-testid="login-password"]', testUser.password)
    await page.click('[data-testid="login-submit"]')
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible()
  })
  
  test('创建、编辑和删除项目', async ({ page }) => {
    // 导航到项目页面
    await page.click('[data-testid="nav-projects"]')
    await expect(page.locator('[data-testid="projects-page"]')).toBeVisible()
    
    // 创建新项目
    await page.click('[data-testid="create-project-btn"]')
    await page.fill('[data-testid="project-name"]', testProject.name)
    await page.fill('[data-testid="project-description"]', testProject.description)
    await page.fill('[data-testid="project-start-date"]', testProject.startDate)
    await page.fill('[data-testid="project-end-date"]', testProject.endDate)
    await page.fill('[data-testid="project-budget"]', testProject.budget.toString())
    await page.click('[data-testid="project-submit"]')
    
    // 验证项目创建成功
    await expect(page.locator('text=项目创建成功')).toBeVisible()
    await expect(page.locator(`text=${testProject.name}`)).toBeVisible()
    
    // 编辑项目
    await page.click(`[data-testid="project-${testProject.name}"] [data-testid="edit-btn"]`)
    const updatedName = testProject.name + ' (已更新)'
    await page.fill('[data-testid="project-name"]', updatedName)
    await page.click('[data-testid="project-submit"]')
    
    // 验证项目更新成功
    await expect(page.locator('text=项目更新成功')).toBeVisible()
    await expect(page.locator(`text=${updatedName}`)).toBeVisible()
    
    // 删除项目
    await page.click(`[data-testid="project-${updatedName}"] [data-testid="delete-btn"]`)
    await page.click('[data-testid="confirm-delete"]')
    
    // 验证项目删除成功
    await expect(page.locator('text=项目删除成功')).toBeVisible()
    await expect(page.locator(`text=${updatedName}`)).not.toBeVisible()
  })
  
  test('项目详情页面功能', async ({ page }) => {
    // 先创建一个项目
    await page.click('[data-testid="nav-projects"]')
    await page.click('[data-testid="create-project-btn"]')
    await page.fill('[data-testid="project-name"]', testProject.name)
    await page.fill('[data-testid="project-description"]', testProject.description)
    await page.fill('[data-testid="project-start-date"]', testProject.startDate)
    await page.fill('[data-testid="project-end-date"]', testProject.endDate)
    await page.click('[data-testid="project-submit"]')
    
    // 进入项目详情页面
    await page.click(`[data-testid="project-${testProject.name}"]`)
    await expect(page.locator('[data-testid="project-details"]')).toBeVisible()
    
    // 验证项目信息显示正确
    await expect(page.locator(`text=${testProject.name}`)).toBeVisible()
    await expect(page.locator(`text=${testProject.description}`)).toBeVisible()
    
    // 测试项目统计信息
    await expect(page.locator('[data-testid="project-stats"]')).toBeVisible()
    await expect(page.locator('[data-testid="task-count"]')).toBeVisible()
    await expect(page.locator('[data-testid="progress-chart"]')).toBeVisible()
  })
})

test.describe('任务管理流程', () => {
  test.beforeEach(async ({ page }) => {
    // 登录并创建项目
    await page.goto(BASE_URL)
    await page.click('text=登录')
    await page.fill('[data-testid="login-username"]', testUser.username)
    await page.fill('[data-testid="login-password"]', testUser.password)
    await page.click('[data-testid="login-submit"]')
    
    // 创建测试项目
    await page.click('[data-testid="nav-projects"]')
    await page.click('[data-testid="create-project-btn"]')
    await page.fill('[data-testid="project-name"]', testProject.name)
    await page.fill('[data-testid="project-description"]', testProject.description)
    await page.fill('[data-testid="project-start-date"]', testProject.startDate)
    await page.fill('[data-testid="project-end-date"]', testProject.endDate)
    await page.click('[data-testid="project-submit"]')
    
    // 进入项目详情
    await page.click(`[data-testid="project-${testProject.name}"]`)
  })
  
  test('创建和管理任务', async ({ page }) => {
    // 创建新任务
    await page.click('[data-testid="create-task-btn"]')
    await page.fill('[data-testid="task-title"]', testTask.title)
    await page.fill('[data-testid="task-description"]', testTask.description)
    await page.selectOption('[data-testid="task-priority"]', testTask.priority)
    await page.fill('[data-testid="task-due-date"]', testTask.dueDate)
    await page.click('[data-testid="task-submit"]')
    
    // 验证任务创建成功
    await expect(page.locator('text=任务创建成功')).toBeVisible()
    await expect(page.locator(`text=${testTask.title}`)).toBeVisible()
    
    // 更新任务状态
    await page.click(`[data-testid="task-${testTask.title}"] [data-testid="status-dropdown"]`)
    await page.click('text=进行中')
    
    // 验证状态更新
    await expect(page.locator(`[data-testid="task-${testTask.title}"] text=进行中`)).toBeVisible()
    
    // 分配任务
    await page.click(`[data-testid="task-${testTask.title}"] [data-testid="assign-btn"]`)
    await page.selectOption('[data-testid="assignee-select"]', testUser.username)
    await page.click('[data-testid="assign-submit"]')
    
    // 验证任务分配
    await expect(page.locator(`[data-testid="task-${testTask.title}"] text=${testUser.fullName}`)).toBeVisible()
  })
  
  test('任务拖拽排序', async ({ page }) => {
    // 创建多个任务
    const tasks = ['任务1', '任务2', '任务3']
    
    for (const taskTitle of tasks) {
      await page.click('[data-testid="create-task-btn"]')
      await page.fill('[data-testid="task-title"]', taskTitle)
      await page.fill('[data-testid="task-description"]', `${taskTitle}的描述`)
      await page.click('[data-testid="task-submit"]')
    }
    
    // 测试拖拽功能
    const firstTask = page.locator(`[data-testid="task-任务1"]`)
    const thirdTask = page.locator(`[data-testid="task-任务3"]`)
    
    await firstTask.dragTo(thirdTask)
    
    // 验证任务顺序改变
    // 这里需要根据实际的拖拽实现来验证
  })
})

test.describe('通知系统测试', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto(BASE_URL)
    await page.click('text=登录')
    await page.fill('[data-testid="login-username"]', testUser.username)
    await page.fill('[data-testid="login-password"]', testUser.password)
    await page.click('[data-testid="login-submit"]')
  })
  
  test('通知中心功能', async ({ page }) => {
    // 打开通知中心
    await page.click('[data-testid="notification-bell"]')
    await expect(page.locator('[data-testid="notification-center"]')).toBeVisible()
    
    // 测试通知列表
    await expect(page.locator('[data-testid="notification-list"]')).toBeVisible()
    
    // 测试标记已读
    const firstNotification = page.locator('[data-testid="notification-item"]').first()
    if (await firstNotification.isVisible()) {
      await firstNotification.click('[data-testid="mark-read-btn"]')
      await expect(page.locator('text=已标记为已读')).toBeVisible()
    }
    
    // 测试通知偏好设置
    await page.click('[data-testid="notification-settings"]')
    await expect(page.locator('[data-testid="notification-preferences"]')).toBeVisible()
    
    // 切换邮件通知
    await page.click('[data-testid="email-notification-toggle"]')
    await expect(page.locator('text=设置已更新')).toBeVisible()
  })
})

test.describe('第三方集成测试', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto(BASE_URL)
    await page.click('text=登录')
    await page.fill('[data-testid="login-username"]', testUser.username)
    await page.fill('[data-testid="login-password"]', testUser.password)
    await page.click('[data-testid="login-submit"]')
  })
  
  test('集成配置管理', async ({ page }) => {
    // 导航到集成页面
    await page.click('[data-testid="nav-integrations"]')
    await expect(page.locator('[data-testid="integrations-page"]')).toBeVisible()
    
    // 添加新集成
    await page.click('[data-testid="add-integration-btn"]')
    await page.selectOption('[data-testid="integration-type"]', 'jira')
    await page.fill('[data-testid="integration-name"]', '测试Jira集成')
    await page.fill('[data-testid="jira-server-url"]', 'https://test.atlassian.net')
    await page.fill('[data-testid="jira-username"]', '<EMAIL>')
    await page.fill('[data-testid="jira-api-token"]', 'test-token')
    await page.click('[data-testid="integration-submit"]')
    
    // 验证集成添加成功
    await expect(page.locator('text=集成配置保存成功')).toBeVisible()
    await expect(page.locator('text=测试Jira集成')).toBeVisible()
    
    // 测试连接
    await page.click('[data-testid="test-connection-btn"]')
    // 由于是测试环境，连接可能失败，但应该有响应
    await expect(page.locator('[data-testid="connection-result"]')).toBeVisible()
  })
})

test.describe('数据可视化测试', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto(BASE_URL)
    await page.click('text=登录')
    await page.fill('[data-testid="login-username"]', testUser.username)
    await page.fill('[data-testid="login-password"]', testUser.password)
    await page.click('[data-testid="login-submit"]')
  })
  
  test('仪表板图表显示', async ({ page }) => {
    // 验证仪表板图表
    await expect(page.locator('[data-testid="dashboard-charts"]')).toBeVisible()
    await expect(page.locator('[data-testid="project-progress-chart"]')).toBeVisible()
    await expect(page.locator('[data-testid="task-status-chart"]')).toBeVisible()
    await expect(page.locator('[data-testid="team-performance-chart"]')).toBeVisible()
    
    // 测试图表交互
    await page.click('[data-testid="chart-filter-dropdown"]')
    await page.click('text=最近7天')
    
    // 验证图表更新
    await page.waitForTimeout(1000) // 等待图表重新渲染
    await expect(page.locator('[data-testid="chart-loading"]')).not.toBeVisible()
  })
  
  test('报告页面功能', async ({ page }) => {
    // 导航到报告页面
    await page.click('[data-testid="nav-reports"]')
    await expect(page.locator('[data-testid="reports-page"]')).toBeVisible()
    
    // 生成报告
    await page.click('[data-testid="generate-report-btn"]')
    await page.selectOption('[data-testid="report-type"]', 'project-summary')
    await page.fill('[data-testid="report-start-date"]', '2025-01-01')
    await page.fill('[data-testid="report-end-date"]', '2025-08-16')
    await page.click('[data-testid="report-submit"]')
    
    // 验证报告生成
    await expect(page.locator('text=报告生成成功')).toBeVisible()
    await expect(page.locator('[data-testid="report-content"]')).toBeVisible()
    
    // 测试报告导出
    await page.click('[data-testid="export-report-btn"]')
    await page.click('text=导出PDF')
    
    // 验证下载开始
    const downloadPromise = page.waitForEvent('download')
    const download = await downloadPromise
    expect(download.suggestedFilename()).toContain('.pdf')
  })
})

test.describe('性能和可用性测试', () => {
  test('页面加载性能', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto(BASE_URL)
    await page.waitForLoadState('networkidle')
    
    const loadTime = Date.now() - startTime
    
    // 验证页面在3秒内加载完成
    expect(loadTime).toBeLessThan(3000)
  })
  
  test('响应式设计', async ({ page }) => {
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto(BASE_URL)
    
    // 验证移动端导航
    await expect(page.locator('[data-testid="mobile-menu-btn"]')).toBeVisible()
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.reload()
    
    // 验证平板端布局
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible()
    
    // 测试桌面端视图
    await page.setViewportSize({ width: 1920, height: 1080 })
    await page.reload()
    
    // 验证桌面端完整布局
    await expect(page.locator('[data-testid="full-sidebar"]')).toBeVisible()
  })
})

// 测试清理
test.afterAll(async () => {
  // 清理测试数据
  // 这里可以调用API删除测试过程中创建的数据
})
