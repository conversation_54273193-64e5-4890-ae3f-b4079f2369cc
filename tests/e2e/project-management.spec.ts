/**
 * 项目管理端到端测试
 * 测试完整的项目管理工作流程
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import { test, expect, Page } from '@playwright/test';

// 测试数据
const testProject = {
  name: 'E2E测试项目',
  description: '这是一个端到端测试项目，用于验证项目管理功能',
  startDate: '2025-08-01',
  endDate: '2025-12-31',
  budget: '500000'
};

const testTask = {
  title: 'E2E测试任务',
  description: '这是一个端到端测试任务',
  priority: 'high',
  dueDate: '2025-09-30'
};

// 辅助函数
async function login(page: Page) {
  await page.goto('/auth/login');
  await page.fill('[data-testid="username-input"]', 'demo_manager');
  await page.fill('[data-testid="password-input"]', 'password123');
  await page.click('[data-testid="login-button"]');
  await page.waitForURL('/dashboard');
}

async function createProject(page: Page, project = testProject) {
  await page.goto('/projects');
  await page.click('[data-testid="create-project-button"]');
  
  // 填写项目信息
  await page.fill('[data-testid="project-name-input"]', project.name);
  await page.fill('[data-testid="project-description-input"]', project.description);
  await page.fill('[data-testid="project-start-date"]', project.startDate);
  await page.fill('[data-testid="project-end-date"]', project.endDate);
  await page.fill('[data-testid="project-budget"]', project.budget);
  
  // 提交表单
  await page.click('[data-testid="submit-project-button"]');
  await page.waitForSelector('[data-testid="success-message"]');
}

test.describe('项目管理工作流程', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
  });

  test('完整的项目创建流程', async ({ page }) => {
    // 导航到项目页面
    await page.goto('/projects');
    await expect(page).toHaveTitle(/项目管理/);

    // 点击创建项目按钮
    await page.click('[data-testid="create-project-button"]');
    await expect(page.locator('[data-testid="project-form-modal"]')).toBeVisible();

    // 填写项目基本信息
    await page.fill('[data-testid="project-name-input"]', testProject.name);
    await page.fill('[data-testid="project-description-input"]', testProject.description);
    
    // 选择项目状态
    await page.click('[data-testid="project-status-select"]');
    await page.click('[data-testid="status-option-planning"]');
    
    // 选择优先级
    await page.click('[data-testid="project-priority-select"]');
    await page.click('[data-testid="priority-option-high"]');
    
    // 设置日期
    await page.fill('[data-testid="project-start-date"]', testProject.startDate);
    await page.fill('[data-testid="project-end-date"]', testProject.endDate);
    
    // 设置预算
    await page.fill('[data-testid="project-budget"]', testProject.budget);
    
    // 提交表单
    await page.click('[data-testid="submit-project-button"]');
    
    // 验证成功消息
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('项目创建成功');
    
    // 验证项目出现在列表中
    await expect(page.locator(`[data-testid="project-item-${testProject.name}"]`)).toBeVisible();
  });

  test('项目详情页面功能', async ({ page }) => {
    // 先创建一个项目
    await createProject(page);
    
    // 点击项目进入详情页
    await page.click(`[data-testid="project-item-${testProject.name}"]`);
    await page.waitForURL(/\/projects\/[a-f0-9-]+/);
    
    // 验证项目详情信息
    await expect(page.locator('[data-testid="project-title"]')).toContainText(testProject.name);
    await expect(page.locator('[data-testid="project-description"]')).toContainText(testProject.description);
    
    // 验证项目统计信息
    await expect(page.locator('[data-testid="project-stats"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-tasks-count"]')).toBeVisible();
    await expect(page.locator('[data-testid="completed-tasks-count"]')).toBeVisible();
    await expect(page.locator('[data-testid="team-members-count"]')).toBeVisible();
    
    // 验证项目进度条
    await expect(page.locator('[data-testid="project-progress-bar"]')).toBeVisible();
  });

  test('任务创建和管理', async ({ page }) => {
    // 先创建一个项目
    await createProject(page);
    
    // 进入项目详情页
    await page.click(`[data-testid="project-item-${testProject.name}"]`);
    await page.waitForURL(/\/projects\/[a-f0-9-]+/);
    
    // 创建任务
    await page.click('[data-testid="create-task-button"]');
    await expect(page.locator('[data-testid="task-form-modal"]')).toBeVisible();
    
    // 填写任务信息
    await page.fill('[data-testid="task-title-input"]', testTask.title);
    await page.fill('[data-testid="task-description-input"]', testTask.description);
    
    // 设置优先级
    await page.click('[data-testid="task-priority-select"]');
    await page.click(`[data-testid="priority-option-${testTask.priority}"]`);
    
    // 设置截止日期
    await page.fill('[data-testid="task-due-date"]', testTask.dueDate);
    
    // 分配任务
    await page.click('[data-testid="task-assignee-select"]');
    await page.click('[data-testid="assignee-option-current-user"]');
    
    // 提交任务
    await page.click('[data-testid="submit-task-button"]');
    
    // 验证任务创建成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator(`[data-testid="task-item-${testTask.title}"]`)).toBeVisible();
    
    // 测试任务状态更新
    await page.click(`[data-testid="task-item-${testTask.title}"]`);
    await page.click('[data-testid="task-status-select"]');
    await page.click('[data-testid="status-option-in-progress"]');
    await page.click('[data-testid="update-task-status-button"]');
    
    // 验证状态更新
    await expect(page.locator('[data-testid="task-status-badge"]')).toContainText('进行中');
  });

  test('团队成员管理', async ({ page }) => {
    // 先创建一个项目
    await createProject(page);
    
    // 进入项目详情页
    await page.click(`[data-testid="project-item-${testProject.name}"]`);
    await page.waitForURL(/\/projects\/[a-f0-9-]+/);
    
    // 切换到团队标签页
    await page.click('[data-testid="team-tab"]');
    
    // 添加团队成员
    await page.click('[data-testid="add-member-button"]');
    await expect(page.locator('[data-testid="add-member-modal"]')).toBeVisible();
    
    // 搜索并选择用户
    await page.fill('[data-testid="user-search-input"]', 'demo_dev1');
    await page.click('[data-testid="user-search-button"]');
    await page.click('[data-testid="user-result-demo_dev1"]');
    
    // 设置角色
    await page.click('[data-testid="member-role-select"]');
    await page.click('[data-testid="role-option-member"]');
    
    // 添加成员
    await page.click('[data-testid="add-member-submit-button"]');
    
    // 验证成员添加成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="member-item-demo_dev1"]')).toBeVisible();
  });

  test('项目搜索和过滤', async ({ page }) => {
    // 创建多个测试项目
    await createProject(page, { ...testProject, name: '搜索测试项目1' });
    await createProject(page, { ...testProject, name: '搜索测试项目2' });
    
    // 导航到项目列表
    await page.goto('/projects');
    
    // 测试搜索功能
    await page.fill('[data-testid="project-search-input"]', '搜索测试');
    await page.click('[data-testid="search-button"]');
    
    // 验证搜索结果
    await expect(page.locator('[data-testid="project-list"]')).toBeVisible();
    const projectItems = page.locator('[data-testid^="project-item-"]');
    await expect(projectItems).toHaveCount(2);
    
    // 测试状态过滤
    await page.click('[data-testid="status-filter-select"]');
    await page.click('[data-testid="filter-option-planning"]');
    await page.click('[data-testid="apply-filters-button"]');
    
    // 验证过滤结果
    const filteredItems = page.locator('[data-testid^="project-item-"]');
    await expect(filteredItems.first()).toBeVisible();
  });

  test('项目进度跟踪', async ({ page }) => {
    // 创建项目并添加任务
    await createProject(page);
    await page.click(`[data-testid="project-item-${testProject.name}"]`);
    
    // 创建多个任务
    const tasks = ['任务1', '任务2', '任务3'];
    for (const taskTitle of tasks) {
      await page.click('[data-testid="create-task-button"]');
      await page.fill('[data-testid="task-title-input"]', taskTitle);
      await page.fill('[data-testid="task-description-input"]', `${taskTitle}的描述`);
      await page.click('[data-testid="submit-task-button"]');
      await page.waitForSelector('[data-testid="success-message"]');
    }
    
    // 完成一个任务
    await page.click('[data-testid="task-item-任务1"]');
    await page.click('[data-testid="task-status-select"]');
    await page.click('[data-testid="status-option-done"]');
    await page.click('[data-testid="update-task-status-button"]');
    
    // 验证项目进度更新
    await page.reload();
    const progressText = await page.locator('[data-testid="project-progress-text"]').textContent();
    expect(progressText).toContain('33%'); // 1/3 任务完成
  });

  test('响应式设计测试', async ({ page }) => {
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/projects');
    
    // 验证移动端导航
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-navigation"]')).toBeVisible();
    
    // 验证项目卡片在移动端的显示
    await expect(page.locator('[data-testid="project-grid"]')).toHaveCSS('grid-template-columns', /1fr/);
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    
    // 验证平板端布局
    await expect(page.locator('[data-testid="project-grid"]')).toHaveCSS('grid-template-columns', /repeat\(2/);
  });

  test('错误处理和用户反馈', async ({ page }) => {
    await page.goto('/projects');
    
    // 测试表单验证错误
    await page.click('[data-testid="create-project-button"]');
    await page.click('[data-testid="submit-project-button"]'); // 不填写任何信息直接提交
    
    // 验证错误消息
    await expect(page.locator('[data-testid="name-error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="name-error-message"]')).toContainText('项目名称不能为空');
    
    // 测试网络错误处理
    await page.route('**/api/v1/projects', route => route.abort());
    await page.fill('[data-testid="project-name-input"]', '网络错误测试');
    await page.click('[data-testid="submit-project-button"]');
    
    // 验证网络错误提示
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText('网络错误');
  });
});
