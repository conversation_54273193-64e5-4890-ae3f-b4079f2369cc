"""
风险预测模型单元测试
测试AI风险预测功能的准确性和可靠性

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
import asyncio
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../services/ai-service/src'))

from models.risk_predictor import RiskPredictor


class TestRiskPredictor:
    """风险预测器测试类"""

    @pytest.fixture
    def risk_predictor(self):
        """创建风险预测器实例"""
        return RiskPredictor()

    @pytest.fixture
    def sample_training_data(self):
        """生成样本训练数据"""
        np.random.seed(42)
        n_samples = 100
        
        # 生成特征数据
        X = np.random.rand(n_samples, 8)
        # 生成标签数据
        y = np.random.choice([0, 1, 2], n_samples, p=[0.6, 0.3, 0.1])
        
        return X, y

    @pytest.fixture
    def sample_project_data(self):
        """生成样本项目数据"""
        return {
            'duration_days': 90,
            'team_size': 8,
            'complexity_score': 7.5,
            'budget_amount': 500000,
            'requirements_count': 25,
            'dependencies_count': 5,
            'experience_score': 6.5,
            'technology_risk': 6.0
        }

    @pytest.mark.asyncio
    async def test_risk_predictor_initialization(self, risk_predictor):
        """测试风险预测器初始化"""
        assert risk_predictor.model is None
        assert risk_predictor.scaler is not None
        assert len(risk_predictor.feature_names) == 8
        assert len(risk_predictor.risk_levels) == 3
        assert not risk_predictor.is_trained

    @pytest.mark.asyncio
    async def test_train_model_success(self, risk_predictor, sample_training_data):
        """测试模型训练成功"""
        X, y = sample_training_data
        
        result = await risk_predictor.train(X, y)
        
        assert risk_predictor.is_trained
        assert risk_predictor.model is not None
        assert 'accuracy' in result
        assert 'feature_importance' in result
        assert 'classification_report' in result
        assert result['accuracy'] > 0
        assert len(result['feature_importance']) == 8

    @pytest.mark.asyncio
    async def test_train_model_with_invalid_data(self, risk_predictor):
        """测试使用无效数据训练模型"""
        # 空数据
        X_empty = np.array([])
        y_empty = np.array([])
        
        with pytest.raises(Exception):
            await risk_predictor.train(X_empty, y_empty)

    @pytest.mark.asyncio
    async def test_predict_with_trained_model(self, risk_predictor, sample_training_data, sample_project_data):
        """测试使用已训练模型进行预测"""
        X, y = sample_training_data
        await risk_predictor.train(X, y)
        
        result = await risk_predictor.predict(sample_project_data)
        
        assert 'risk_level' in result
        assert 'risk_label' in result
        assert 'risk_score' in result
        assert 'probabilities' in result
        assert 'risk_factors' in result
        assert 'recommendations' in result
        assert 'confidence' in result
        
        assert result['risk_level'] in [0, 1, 2]
        assert result['risk_label'] in ['低风险', '中风险', '高风险']
        assert 0 <= result['risk_score'] <= 1
        assert 0 <= result['confidence'] <= 1
        assert isinstance(result['risk_factors'], list)
        assert isinstance(result['recommendations'], list)

    @pytest.mark.asyncio
    async def test_predict_without_trained_model(self, risk_predictor, sample_project_data):
        """测试未训练模型时的预测（应使用规则基础预测）"""
        result = await risk_predictor.predict(sample_project_data)
        
        assert 'risk_level' in result
        assert 'risk_label' in result
        assert 'risk_score' in result
        assert 'risk_factors' in result
        assert 'recommendations' in result
        assert 'confidence' in result
        assert 'method' in result
        assert result['method'] == 'rule_based'

    @pytest.mark.asyncio
    async def test_rule_based_prediction_logic(self, risk_predictor):
        """测试规则基础预测逻辑"""
        # 高风险项目数据
        high_risk_data = {
            'duration_days': 365,  # 持续时间过长
            'team_size': 20,       # 团队规模过大
            'complexity_score': 9, # 复杂度很高
            'budget_amount': 1000000,
            'requirements_count': 50,
            'dependencies_count': 10,
            'experience_score': 3, # 经验不足
            'technology_risk': 9   # 技术风险很高
        }
        
        result = await risk_predictor.predict(high_risk_data)
        
        assert result['risk_level'] == 2  # 高风险
        assert result['risk_label'] == '高风险'
        assert len(result['risk_factors']) > 0
        assert len(result['recommendations']) > 0

    @pytest.mark.asyncio
    async def test_rule_based_prediction_low_risk(self, risk_predictor):
        """测试规则基础预测 - 低风险场景"""
        low_risk_data = {
            'duration_days': 30,   # 持续时间短
            'team_size': 5,        # 团队规模适中
            'complexity_score': 3, # 复杂度低
            'budget_amount': 100000,
            'requirements_count': 10,
            'dependencies_count': 2,
            'experience_score': 8, # 经验丰富
            'technology_risk': 3   # 技术风险低
        }
        
        result = await risk_predictor.predict(low_risk_data)
        
        assert result['risk_level'] == 0  # 低风险
        assert result['risk_label'] == '低风险'

    def test_extract_features(self, risk_predictor, sample_project_data):
        """测试特征提取功能"""
        features = risk_predictor._extract_features(sample_project_data)
        
        assert len(features) == 8
        assert all(isinstance(f, float) for f in features)
        assert features[0] == sample_project_data['duration_days']
        assert features[1] == sample_project_data['team_size']
        assert features[2] == sample_project_data['complexity_score']

    def test_extract_features_with_missing_data(self, risk_predictor):
        """测试缺少数据时的特征提取"""
        incomplete_data = {
            'duration_days': 60,
            'team_size': 4
            # 缺少其他字段
        }
        
        features = risk_predictor._extract_features(incomplete_data)
        
        assert len(features) == 8
        # 应该使用默认值填充缺失字段
        assert features[0] == 60
        assert features[1] == 4
        assert features[2] == 5  # 默认复杂度评分

    def test_analyze_risk_factors(self, risk_predictor):
        """测试风险因子分析"""
        project_data = {
            'duration_days': 200,
            'team_size': 15,
            'complexity_score': 8,
            'budget_amount': 800000,
            'requirements_count': 40,
            'dependencies_count': 8,
            'experience_score': 4,
            'technology_risk': 7
        }
        
        features = risk_predictor._extract_features(project_data)
        risk_factors = risk_predictor._analyze_risk_factors(project_data, features)
        
        assert isinstance(risk_factors, list)
        assert len(risk_factors) > 0
        # 应该识别出一些风险因子
        assert any('持续时间' in factor for factor in risk_factors)
        assert any('复杂度' in factor for factor in risk_factors)

    def test_generate_recommendations(self, risk_predictor):
        """测试建议生成"""
        risk_factors = [
            '项目持续时间过长，可能导致需求变更',
            '团队经验不足，可能影响项目质量',
            '技术风险较高，需要充分验证'
        ]
        
        recommendations = risk_predictor._generate_recommendations(2, risk_factors)
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        # 高风险项目应该有相应的建议
        assert any('检查点' in rec or '监控' in rec for rec in recommendations)
        assert any('培训' in rec or '专家' in rec for rec in recommendations)

    @pytest.mark.asyncio
    async def test_prediction_consistency(self, risk_predictor, sample_training_data):
        """测试预测一致性"""
        X, y = sample_training_data
        await risk_predictor.train(X, y)
        
        project_data = {
            'duration_days': 90,
            'team_size': 8,
            'complexity_score': 7.5,
            'budget_amount': 500000,
            'requirements_count': 25,
            'dependencies_count': 5,
            'experience_score': 6.5,
            'technology_risk': 6.0
        }
        
        # 多次预测同一项目，结果应该一致
        result1 = await risk_predictor.predict(project_data)
        result2 = await risk_predictor.predict(project_data)
        
        assert result1['risk_level'] == result2['risk_level']
        assert result1['risk_score'] == result2['risk_score']
        assert result1['confidence'] == result2['confidence']

    @pytest.mark.asyncio
    async def test_edge_cases(self, risk_predictor):
        """测试边界情况"""
        # 极端值测试
        extreme_data = {
            'duration_days': 1,
            'team_size': 1,
            'complexity_score': 1,
            'budget_amount': 1,
            'requirements_count': 1,
            'dependencies_count': 0,
            'experience_score': 1,
            'technology_risk': 1
        }
        
        result = await risk_predictor.predict(extreme_data)
        
        assert 'risk_level' in result
        assert result['risk_level'] in [0, 1, 2]
        assert 0 <= result['risk_score'] <= 1

    @pytest.mark.asyncio
    async def test_feature_importance_analysis(self, risk_predictor, sample_training_data):
        """测试特征重要性分析"""
        X, y = sample_training_data
        result = await risk_predictor.train(X, y)
        
        feature_importance = result['feature_importance']
        
        assert len(feature_importance) == 8
        assert all(importance >= 0 for importance in feature_importance.values())
        # 重要性之和应该接近1
        total_importance = sum(feature_importance.values())
        assert abs(total_importance - 1.0) < 0.1

    @pytest.mark.asyncio
    async def test_model_performance_metrics(self, risk_predictor, sample_training_data):
        """测试模型性能指标"""
        X, y = sample_training_data
        result = await risk_predictor.train(X, y)
        
        assert 'classification_report' in result
        classification_report = result['classification_report']
        
        assert 'accuracy' in classification_report
        assert 'macro avg' in classification_report
        assert 'weighted avg' in classification_report
        
        # 准确率应该在合理范围内
        assert 0.3 <= result['accuracy'] <= 1.0
