"""
AI分析服务真实算法测试
测试新实现的真实数据分析功能

<AUTHOR>
@version 1.0.0
@since 2025-08-28
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
import json

# 假设的导入路径，需要根据实际项目结构调整
from services.ai_service.src.api.analysis_router import (
    get_project_data,
    calculate_team_velocity,
    calculate_quality_score,
    analyze_project_trends,
    generate_intelligent_insights,
    calculate_confidence_level
)


class TestRealAnalytics:
    """真实AI分析功能测试"""

    @pytest.fixture
    def mock_project_data(self):
        """模拟项目数据"""
        return {
            "project_id": "test-project-001",
            "total_tasks": 50,
            "completed_tasks": 35,
            "team_members": 5,
            "recent_activity": True,
            "historical_data": True,
            "created_at": "2025-01-01T00:00:00Z",
            "status": "active"
        }

    @pytest.fixture
    def mock_quality_data(self):
        """模拟代码质量数据"""
        return {
            "test_coverage": 85,
            "code_review_rate": 90,
            "bugs_per_kloc": 3,
            "technical_debt_ratio": 0.15
        }

    @pytest.fixture
    def mock_historical_data(self):
        """模拟历史数据"""
        return {
            "velocity": [8.5, 9.2, 10.1, 11.3, 12.5],
            "quality": [0.75, 0.78, 0.82, 0.85, 0.87],
            "completion_rate": [0.65, 0.72, 0.78, 0.83, 0.88],
            "workload": [0.8, 0.75, 0.82, 0.79, 0.76]
        }

    @pytest.mark.asyncio
    async def test_get_project_data_success(self, mock_project_data):
        """测试成功获取项目数据"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_project_data
            
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            result = await get_project_data("test-project-001")
            
            assert result is not None
            assert result["project_id"] == "test-project-001"
            assert result["total_tasks"] == 50
            assert result["completed_tasks"] == 35

    @pytest.mark.asyncio
    async def test_get_project_data_not_found(self):
        """测试项目不存在的情况"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 404
            
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            result = await get_project_data("non-existent-project")
            
            assert result is None

    @pytest.mark.asyncio
    async def test_get_project_data_api_error(self):
        """测试API错误情况"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 500
            
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            result = await get_project_data("test-project-001")
            
            assert result == {}

    @pytest.mark.asyncio
    async def test_calculate_team_velocity_success(self):
        """测试团队速度计算成功"""
        mock_response_data = {"count": 15}
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            velocity = await calculate_team_velocity("test-project-001", days=30)
            
            assert velocity == 0.5  # 15 tasks / 30 days

    @pytest.mark.asyncio
    async def test_calculate_team_velocity_no_data(self):
        """测试无数据情况下的团队速度计算"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 404
            
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            velocity = await calculate_team_velocity("test-project-001", days=30)
            
            assert velocity == 0.0

    @pytest.mark.asyncio
    async def test_calculate_quality_score_success(self, mock_quality_data):
        """测试质量分数计算成功"""
        with patch('services.ai_service.src.api.analysis_router.get_code_quality_data') as mock_get_quality:
            mock_get_quality.return_value = mock_quality_data
            
            result = await calculate_quality_score("test-project-001")
            
            assert "overall_score" in result
            assert 0 <= result["overall_score"] <= 1
            assert result["test_coverage"] == 0.85
            assert result["code_review_rate"] == 0.90
            assert result["bug_density"] == 0.7  # 1 - (3/10)

    @pytest.mark.asyncio
    async def test_calculate_quality_score_no_data(self):
        """测试无质量数据情况"""
        with patch('services.ai_service.src.api.analysis_router.get_code_quality_data') as mock_get_quality:
            mock_get_quality.return_value = {}
            
            result = await calculate_quality_score("test-project-001")
            
            assert result["overall_score"] == 0.5  # 默认值

    @pytest.mark.asyncio
    async def test_analyze_project_trends_success(self, mock_historical_data):
        """测试项目趋势分析成功"""
        with patch('services.ai_service.src.api.analysis_router.get_historical_metrics') as mock_get_historical:
            mock_get_historical.return_value = mock_historical_data
            
            result = await analyze_project_trends("test-project-001")
            
            assert "velocity_trend" in result
            assert "quality_trend" in result
            assert "team_satisfaction" in result
            
            # 基于模拟数据，速度应该是上升趋势
            assert result["velocity_trend"] == "increasing"
            assert result["quality_trend"] == "improving"

    @pytest.mark.asyncio
    async def test_analyze_project_trends_no_data(self):
        """测试无历史数据情况"""
        with patch('services.ai_service.src.api.analysis_router.get_historical_metrics') as mock_get_historical:
            mock_get_historical.return_value = {}
            
            result = await analyze_project_trends("test-project-001")
            
            assert result["velocity_trend"] == "stable"
            assert result["quality_trend"] == "stable"
            assert result["team_satisfaction"] == "medium"

    def test_analyze_velocity_trend_increasing(self):
        """测试速度上升趋势分析"""
        from services.ai_service.src.api.analysis_router import analyze_velocity_trend
        
        velocity_data = [8.0, 9.0, 10.0, 11.0, 12.0]
        result = analyze_velocity_trend(velocity_data)
        
        assert result == "increasing"

    def test_analyze_velocity_trend_decreasing(self):
        """测试速度下降趋势分析"""
        from services.ai_service.src.api.analysis_router import analyze_velocity_trend
        
        velocity_data = [12.0, 11.0, 10.0, 9.0, 8.0]
        result = analyze_velocity_trend(velocity_data)
        
        assert result == "decreasing"

    def test_analyze_velocity_trend_stable(self):
        """测试速度稳定趋势分析"""
        from services.ai_service.src.api.analysis_router import analyze_velocity_trend
        
        velocity_data = [10.0, 10.1, 9.9, 10.2, 9.8]
        result = analyze_velocity_trend(velocity_data)
        
        assert result == "stable"

    def test_analyze_velocity_trend_insufficient_data(self):
        """测试数据不足情况"""
        from services.ai_service.src.api.analysis_router import analyze_velocity_trend
        
        velocity_data = [10.0]
        result = analyze_velocity_trend(velocity_data)
        
        assert result == "stable"

    def test_analyze_quality_trend_improving(self):
        """测试质量改进趋势分析"""
        from services.ai_service.src.api.analysis_router import analyze_quality_trend
        
        quality_data = [0.70, 0.75, 0.80, 0.85, 0.90]
        result = analyze_quality_trend(quality_data)
        
        assert result == "improving"

    def test_analyze_quality_trend_declining(self):
        """测试质量下降趋势分析"""
        from services.ai_service.src.api.analysis_router import analyze_quality_trend
        
        quality_data = [0.90, 0.85, 0.80, 0.75, 0.70]
        result = analyze_quality_trend(quality_data)
        
        assert result == "declining"

    def test_analyze_team_satisfaction_high(self):
        """测试高团队满意度分析"""
        from services.ai_service.src.api.analysis_router import analyze_team_satisfaction
        
        historical_data = {
            "completion_rate": [0.90, 0.92, 0.88, 0.91, 0.89],
            "workload": [0.7, 0.8, 0.75, 0.72, 0.78]
        }
        
        result = analyze_team_satisfaction(historical_data)
        
        assert result == "high"

    def test_analyze_team_satisfaction_low(self):
        """测试低团队满意度分析"""
        from services.ai_service.src.api.analysis_router import analyze_team_satisfaction
        
        historical_data = {
            "completion_rate": [0.50, 0.45, 0.52, 0.48, 0.51],
            "workload": [0.9, 0.95, 0.92, 0.88, 0.94]
        }
        
        result = analyze_team_satisfaction(historical_data)
        
        assert result == "low"

    @pytest.mark.asyncio
    async def test_generate_intelligent_insights_high_performance(self):
        """测试高性能项目的智能洞察生成"""
        completion_rate = 0.95
        velocity = 15.0
        quality_metrics = {"overall_score": 0.90}
        trends = {
            "velocity_trend": "increasing",
            "quality_trend": "improving",
            "team_satisfaction": "high"
        }
        
        insights = await generate_intelligent_insights(
            completion_rate, velocity, quality_metrics, trends
        )
        
        assert len(insights) > 0
        assert any("优秀" in insight for insight in insights)
        assert any("持续提升" in insight for insight in insights)

    @pytest.mark.asyncio
    async def test_generate_intelligent_insights_low_performance(self):
        """测试低性能项目的智能洞察生成"""
        completion_rate = 0.45
        velocity = 3.0
        quality_metrics = {"overall_score": 0.40}
        trends = {
            "velocity_trend": "decreasing",
            "quality_trend": "declining",
            "team_satisfaction": "low"
        }
        
        insights = await generate_intelligent_insights(
            completion_rate, velocity, quality_metrics, trends
        )
        
        assert len(insights) > 0
        assert any("需要关注" in insight or "建议" in insight for insight in insights)

    def test_calculate_confidence_level_high_data_quality(self, mock_project_data):
        """测试高数据质量的置信度计算"""
        result = calculate_confidence_level(mock_project_data)
        
        assert 0 <= result <= 1
        assert result >= 0.8  # 高质量数据应该有高置信度

    def test_calculate_confidence_level_low_data_quality(self):
        """测试低数据质量的置信度计算"""
        poor_data = {
            "total_tasks": 0,
            "team_members": 0,
            "recent_activity": False,
            "historical_data": False
        }
        
        result = calculate_confidence_level(poor_data)
        
        assert 0 <= result <= 1
        assert result <= 0.3  # 低质量数据应该有低置信度

    def test_calculate_confidence_level_empty_data(self):
        """测试空数据的置信度计算"""
        result = calculate_confidence_level({})
        
        assert result == 0.5  # 默认中等置信度

    @pytest.mark.asyncio
    async def test_integration_full_analysis_flow(self, mock_project_data, mock_quality_data, mock_historical_data):
        """测试完整分析流程的集成测试"""
        project_id = "integration-test-project"
        
        # 模拟所有依赖的API调用
        with patch('httpx.AsyncClient') as mock_client:
            # 模拟项目数据获取
            mock_project_response = Mock()
            mock_project_response.status_code = 200
            mock_project_response.json.return_value = mock_project_data
            
            # 模拟任务完成数据获取
            mock_velocity_response = Mock()
            mock_velocity_response.status_code = 200
            mock_velocity_response.json.return_value = {"count": 20}
            
            # 模拟历史数据获取
            mock_historical_response = Mock()
            mock_historical_response.status_code = 200
            mock_historical_response.json.return_value = mock_historical_data
            
            # 配置mock客户端返回不同的响应
            mock_client.return_value.__aenter__.return_value.get.side_effect = [
                mock_project_response,  # get_project_data
                mock_velocity_response,  # calculate_team_velocity
                mock_historical_response  # get_historical_metrics
            ]
            
            # 模拟代码质量数据
            with patch('services.ai_service.src.api.analysis_router.get_code_quality_data') as mock_get_quality:
                mock_get_quality.return_value = mock_quality_data
                
                # 执行完整的分析流程
                project_data = await get_project_data(project_id)
                assert project_data is not None
                
                velocity = await calculate_team_velocity(project_id, days=30)
                assert velocity > 0
                
                quality_metrics = await calculate_quality_score(project_id)
                assert quality_metrics["overall_score"] > 0
                
                trends = await analyze_project_trends(project_id)
                assert all(key in trends for key in ["velocity_trend", "quality_trend", "team_satisfaction"])
                
                # 计算完成率
                completion_rate = project_data["completed_tasks"] / project_data["total_tasks"]
                
                # 生成洞察
                insights = await generate_intelligent_insights(
                    completion_rate, velocity, quality_metrics, trends
                )
                assert len(insights) > 0
                
                # 计算置信度
                confidence = calculate_confidence_level(project_data)
                assert 0 <= confidence <= 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
