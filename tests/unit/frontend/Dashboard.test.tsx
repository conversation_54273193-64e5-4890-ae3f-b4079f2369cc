/**
 * Dashboard组件单元测试
 * 测试仪表板页面的各项功能和交互
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { configureStore } from '@reduxjs/toolkit';

import Dashboard from '../../../frontend/src/pages/Dashboard';
import dashboardReducer from '../../../frontend/src/store/slices/dashboardSlice';
import authReducer from '../../../frontend/src/store/slices/authSlice';

// Mock ECharts
jest.mock('echarts', () => ({
  init: jest.fn(() => ({
    setOption: jest.fn(),
    resize: jest.fn(),
    dispose: jest.fn(),
  })),
  graphic: {
    LinearGradient: jest.fn(),
  },
}));

// Mock API calls
jest.mock('../../../frontend/src/services/api/dashboardApi', () => ({
  dashboardApi: {
    getDashboardData: jest.fn(() => Promise.resolve({
      data: {
        stats: {
          totalProjects: 12,
          activeProjects: 8,
          completedTasks: 156,
          totalTasks: 203,
          teamMembers: 15,
          overdueTasks: 7,
        },
        projectProgress: [
          {
            id: '1',
            name: 'AI推荐系统',
            progress: 85,
            status: 'active',
            startDate: '2025-07-01',
            endDate: '2025-08-30'
          }
        ],
        taskDistribution: [
          { status: '待开始', count: 25, percentage: 12.3 },
          { status: '进行中', count: 45, percentage: 22.2 },
          { status: '已完成', count: 120, percentage: 59.1 }
        ],
        teamPerformance: [],
        riskAnalysis: [],
        recentActivities: [
          {
            id: '1',
            type: 'project',
            title: '创建了新项目',
            description: 'AI推荐系统开发',
            timestamp: '2小时前',
            user: { 
              id: '1',
              name: '张三', 
              avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1' 
            }
          }
        ],
        upcomingDeadlines: [
          {
            id: '1',
            title: 'API接口开发',
            type: 'task',
            deadline: '2025-08-20',
            priority: 'high',
            progress: 75
          }
        ]
      },
      success: true
    })),
    getStats: jest.fn(),
    getProjectProgress: jest.fn(),
    getTeamPerformance: jest.fn(),
    getRiskAnalysis: jest.fn(),
  }
}));

// 创建测试store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardReducer,
      auth: authReducer,
    },
    preloadedState: {
      dashboard: {
        data: null,
        loading: false,
        error: null,
        lastUpdated: null,
        ...initialState.dashboard
      },
      auth: {
        isAuthenticated: true,
        user: { id: '1', username: 'testuser' },
        token: 'test-token',
        isLoading: false,
        error: null,
        ...initialState.auth
      }
    }
  });
};

// 测试包装器组件
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <ConfigProvider locale={zhCN}>
        {children}
      </ConfigProvider>
    </BrowserRouter>
  </Provider>
);

describe('Dashboard组件', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该正确渲染仪表板页面', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    // 检查页面标题
    expect(screen.getByText('项目仪表板')).toBeInTheDocument();
    expect(screen.getByText('欢迎回来！这里是您的项目概览和关键指标。')).toBeInTheDocument();
  });

  test('应该显示加载状态', () => {
    const store = createTestStore({
      dashboard: { loading: true }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText('加载仪表板数据...')).toBeInTheDocument();
  });

  test('应该显示错误状态', () => {
    const store = createTestStore({
      dashboard: { 
        loading: false, 
        error: '网络连接失败' 
      }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText('加载失败')).toBeInTheDocument();
    expect(screen.getByText('网络连接失败')).toBeInTheDocument();
  });

  test('应该显示统计卡片', async () => {
    const store = createTestStore({
      dashboard: {
        data: {
          stats: {
            totalProjects: 12,
            activeProjects: 8,
            completedTasks: 156,
            totalTasks: 203,
            teamMembers: 15,
            overdueTasks: 7,
          }
        }
      }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    // 检查统计数据
    expect(screen.getByText('总项目数')).toBeInTheDocument();
    expect(screen.getByText('12')).toBeInTheDocument();
    expect(screen.getByText('活跃项目')).toBeInTheDocument();
    expect(screen.getByText('8')).toBeInTheDocument();
    expect(screen.getByText('任务完成率')).toBeInTheDocument();
    expect(screen.getByText('逾期任务')).toBeInTheDocument();
    expect(screen.getByText('7')).toBeInTheDocument();
  });

  test('应该显示最近活动列表', async () => {
    const store = createTestStore({
      dashboard: {
        data: {
          recentActivities: [
            {
              id: '1',
              type: 'project',
              title: '创建了新项目',
              description: 'AI推荐系统开发',
              timestamp: '2小时前',
              user: { 
                id: '1',
                name: '张三', 
                avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1' 
              }
            },
            {
              id: '2',
              type: 'task',
              title: '完成了任务',
              description: '用户界面设计',
              timestamp: '4小时前',
              user: { 
                id: '2',
                name: '李四', 
                avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=2' 
              }
            }
          ]
        }
      }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText('最近活动')).toBeInTheDocument();
    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('创建了新项目')).toBeInTheDocument();
    expect(screen.getByText('AI推荐系统开发')).toBeInTheDocument();
    expect(screen.getByText('李四')).toBeInTheDocument();
    expect(screen.getByText('完成了任务')).toBeInTheDocument();
  });

  test('应该显示即将到期的任务', async () => {
    const store = createTestStore({
      dashboard: {
        data: {
          upcomingDeadlines: [
            {
              id: '1',
              title: 'API接口开发',
              type: 'task',
              deadline: '2025-08-20',
              priority: 'high',
              progress: 75
            },
            {
              id: '2',
              title: '用户测试',
              type: 'project',
              deadline: '2025-08-25',
              priority: 'medium',
              progress: 45
            }
          ]
        }
      }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText('即将到期')).toBeInTheDocument();
    expect(screen.getByText('API接口开发')).toBeInTheDocument();
    expect(screen.getByText('2025-08-20')).toBeInTheDocument();
    expect(screen.getByText('高优先级')).toBeInTheDocument();
    expect(screen.getByText('用户测试')).toBeInTheDocument();
    expect(screen.getByText('中优先级')).toBeInTheDocument();
  });

  test('应该正确计算任务完成率', () => {
    const store = createTestStore({
      dashboard: {
        data: {
          stats: {
            completedTasks: 80,
            totalTasks: 100,
          }
        }
      }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText('80.0%')).toBeInTheDocument();
  });

  test('应该处理空数据情况', () => {
    const store = createTestStore({
      dashboard: {
        data: {
          stats: {
            totalProjects: 0,
            activeProjects: 0,
            completedTasks: 0,
            totalTasks: 0,
            teamMembers: 0,
            overdueTasks: 0,
          },
          recentActivities: [],
          upcomingDeadlines: []
        }
      }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText('0')).toBeInTheDocument();
  });

  test('应该响应查看详情按钮点击', async () => {
    const store = createTestStore({
      dashboard: {
        data: {
          stats: {
            totalProjects: 12,
            activeProjects: 8,
            completedTasks: 156,
            totalTasks: 203,
            teamMembers: 15,
            overdueTasks: 7,
          }
        }
      }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    // 查找并点击查看详情按钮
    const detailButtons = screen.getAllByText('查看详情');
    expect(detailButtons.length).toBeGreaterThan(0);
    
    fireEvent.click(detailButtons[0]);
    // 这里可以添加导航验证
  });

  test('应该正确处理优先级颜色', () => {
    const store = createTestStore({
      dashboard: {
        data: {
          upcomingDeadlines: [
            {
              id: '1',
              title: '高优先级任务',
              type: 'task',
              deadline: '2025-08-20',
              priority: 'high',
              progress: 75
            }
          ]
        }
      }
    });

    render(
      <TestWrapper store={store}>
        <Dashboard />
      </TestWrapper>
    );

    const highPriorityTag = screen.getByText('高优先级');
    expect(highPriorityTag).toBeInTheDocument();
    expect(highPriorityTag.closest('.ant-tag')).toHaveClass('ant-tag-red');
  });
});
