"""
项目API集成测试
测试项目管理相关的API端点

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import date, datetime
import uuid

from backend.main import app
from backend.database import get_db
from backend.models.project import Project
from backend.models.user import User
from backend.schemas.project import ProjectCreate, ProjectUpdate
from tests.conftest import override_get_db, test_db


class TestProjectAPI:
    """项目API测试类"""

    @pytest.fixture(autouse=True)
    async def setup(self, test_db: AsyncSession):
        """测试前置设置"""
        self.db = test_db
        app.dependency_overrides[get_db] = lambda: test_db
        
        # 创建测试用户
        self.test_user = User(
            id=str(uuid.uuid4()),
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed_password",
            first_name="Test",
            last_name="User"
        )
        self.db.add(self.test_user)
        await self.db.commit()
        await self.db.refresh(self.test_user)

    @pytest.mark.asyncio
    async def test_create_project_success(self):
        """测试成功创建项目"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            project_data = {
                "name": "测试项目",
                "description": "这是一个测试项目",
                "status": "planning",
                "priority": "medium",
                "start_date": "2025-08-01",
                "end_date": "2025-12-31",
                "budget": 100000.00,
                "owner_id": self.test_user.id
            }
            
            response = await client.post(
                "/api/v1/projects/",
                json=project_data,
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 201
            data = response.json()
            assert data["name"] == project_data["name"]
            assert data["description"] == project_data["description"]
            assert data["status"] == project_data["status"]
            assert data["owner_id"] == project_data["owner_id"]

    @pytest.mark.asyncio
    async def test_create_project_invalid_data(self):
        """测试创建项目时数据验证失败"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 缺少必需字段
            project_data = {
                "description": "缺少名称的项目"
            }
            
            response = await client.post(
                "/api/v1/projects/",
                json=project_data,
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 422
            data = response.json()
            assert "detail" in data

    @pytest.mark.asyncio
    async def test_get_project_success(self):
        """测试成功获取项目详情"""
        # 先创建一个项目
        project = Project(
            id=str(uuid.uuid4()),
            name="获取测试项目",
            description="用于测试获取功能的项目",
            status="active",
            priority="high",
            owner_id=self.test_user.id
        )
        self.db.add(project)
        await self.db.commit()
        await self.db.refresh(project)

        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                f"/api/v1/projects/{project.id}",
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == project.id
            assert data["name"] == project.name
            assert data["description"] == project.description

    @pytest.mark.asyncio
    async def test_get_project_not_found(self):
        """测试获取不存在的项目"""
        non_existent_id = str(uuid.uuid4())
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                f"/api/v1/projects/{non_existent_id}",
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 404
            data = response.json()
            assert "not found" in data["detail"].lower()

    @pytest.mark.asyncio
    async def test_update_project_success(self):
        """测试成功更新项目"""
        # 先创建一个项目
        project = Project(
            id=str(uuid.uuid4()),
            name="更新测试项目",
            description="用于测试更新功能的项目",
            status="planning",
            priority="medium",
            owner_id=self.test_user.id
        )
        self.db.add(project)
        await self.db.commit()
        await self.db.refresh(project)

        update_data = {
            "name": "已更新的项目名称",
            "status": "active",
            "priority": "high"
        }

        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.put(
                f"/api/v1/projects/{project.id}",
                json=update_data,
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["name"] == update_data["name"]
            assert data["status"] == update_data["status"]
            assert data["priority"] == update_data["priority"]

    @pytest.mark.asyncio
    async def test_delete_project_success(self):
        """测试成功删除项目"""
        # 先创建一个项目
        project = Project(
            id=str(uuid.uuid4()),
            name="删除测试项目",
            description="用于测试删除功能的项目",
            status="planning",
            priority="low",
            owner_id=self.test_user.id
        )
        self.db.add(project)
        await self.db.commit()
        await self.db.refresh(project)

        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.delete(
                f"/api/v1/projects/{project.id}",
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 204

            # 验证项目已被删除
            get_response = await client.get(
                f"/api/v1/projects/{project.id}",
                headers={"Authorization": f"Bearer test_token"}
            )
            assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_projects_success(self):
        """测试成功获取项目列表"""
        # 创建多个测试项目
        projects = []
        for i in range(3):
            project = Project(
                id=str(uuid.uuid4()),
                name=f"列表测试项目 {i+1}",
                description=f"第{i+1}个测试项目",
                status="active",
                priority="medium",
                owner_id=self.test_user.id
            )
            projects.append(project)
            self.db.add(project)
        
        await self.db.commit()

        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/projects/",
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "items" in data
            assert "total" in data
            assert "page" in data
            assert "size" in data
            assert len(data["items"]) >= 3

    @pytest.mark.asyncio
    async def test_list_projects_with_filters(self):
        """测试带过滤条件的项目列表"""
        # 创建不同状态的项目
        active_project = Project(
            id=str(uuid.uuid4()),
            name="活跃项目",
            description="活跃状态的项目",
            status="active",
            priority="high",
            owner_id=self.test_user.id
        )
        
        completed_project = Project(
            id=str(uuid.uuid4()),
            name="已完成项目",
            description="已完成的项目",
            status="completed",
            priority="medium",
            owner_id=self.test_user.id
        )
        
        self.db.add_all([active_project, completed_project])
        await self.db.commit()

        async with AsyncClient(app=app, base_url="http://test") as client:
            # 测试状态过滤
            response = await client.get(
                "/api/v1/projects/?status=active",
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert all(item["status"] == "active" for item in data["items"])

    @pytest.mark.asyncio
    async def test_project_statistics(self):
        """测试项目统计信息"""
        # 创建不同状态的项目用于统计
        statuses = ["planning", "active", "completed", "paused"]
        for status in statuses:
            for i in range(2):  # 每种状态创建2个项目
                project = Project(
                    id=str(uuid.uuid4()),
                    name=f"{status}项目{i+1}",
                    description=f"{status}状态的项目",
                    status=status,
                    priority="medium",
                    owner_id=self.test_user.id
                )
                self.db.add(project)
        
        await self.db.commit()

        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                "/api/v1/projects/statistics",
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "total_projects" in data
            assert "status_distribution" in data
            assert "priority_distribution" in data

    @pytest.mark.asyncio
    async def test_unauthorized_access(self):
        """测试未授权访问"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/api/v1/projects/")
            assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_project_validation_rules(self):
        """测试项目数据验证规则"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 测试无效的日期范围
            invalid_project_data = {
                "name": "无效日期项目",
                "description": "结束日期早于开始日期",
                "status": "planning",
                "priority": "medium",
                "start_date": "2025-12-31",
                "end_date": "2025-01-01",  # 结束日期早于开始日期
                "owner_id": self.test_user.id
            }
            
            response = await client.post(
                "/api/v1/projects/",
                json=invalid_project_data,
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_project_progress_calculation(self):
        """测试项目进度计算"""
        # 创建项目
        project = Project(
            id=str(uuid.uuid4()),
            name="进度测试项目",
            description="用于测试进度计算的项目",
            status="active",
            priority="medium",
            owner_id=self.test_user.id
        )
        self.db.add(project)
        await self.db.commit()
        await self.db.refresh(project)

        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get(
                f"/api/v1/projects/{project.id}/progress",
                headers={"Authorization": f"Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "progress_percentage" in data
            assert "total_tasks" in data
            assert "completed_tasks" in data
            assert "remaining_tasks" in data
