"""
API集成测试

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import pytest
import asyncio
import json
from httpx import AsyncClient
from fastapi.testclient import TestClient
from datetime import datetime, timedelta

# 导入应用
from backend.app.main import app
from backend.app.database import get_db
from backend.app.models.user import User
from backend.app.models.project import Project
from backend.app.models.task import Task


class TestAuthAPI:
    """认证API测试"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_user_registration(self, client):
        """测试用户注册"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "测试用户"
        }
        
        response = client.post("/api/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == "testuser"
        assert data["email"] == "<EMAIL>"
        assert "password" not in data  # 确保密码不在响应中
    
    def test_user_login(self, client):
        """测试用户登录"""
        # 首先注册用户
        user_data = {
            "username": "logintest",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "登录测试用户"
        }
        client.post("/api/auth/register", json=user_data)
        
        # 然后测试登录
        login_data = {
            "username": "logintest",
            "password": "testpassword123"
        }
        
        response = client.post("/api/auth/login", data=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_invalid_login(self, client):
        """测试无效登录"""
        login_data = {
            "username": "nonexistent",
            "password": "wrongpassword"
        }
        
        response = client.post("/api/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "Invalid credentials" in response.json()["detail"]
    
    def test_protected_endpoint_without_token(self, client):
        """测试未授权访问受保护端点"""
        response = client.get("/api/projects/")
        
        assert response.status_code == 401
    
    def test_protected_endpoint_with_token(self, client):
        """测试使用令牌访问受保护端点"""
        # 注册并登录获取令牌
        user_data = {
            "username": "protectedtest",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "受保护测试用户"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", data={
            "username": "protectedtest",
            "password": "testpassword123"
        })
        token = login_response.json()["access_token"]
        
        # 使用令牌访问受保护端点
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/projects/", headers=headers)
        
        assert response.status_code == 200


class TestProjectAPI:
    """项目API测试"""
    
    @pytest.fixture
    def authenticated_client(self):
        """创建已认证的测试客户端"""
        client = TestClient(app)
        
        # 注册并登录
        user_data = {
            "username": "projecttest",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "项目测试用户"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", data={
            "username": "projecttest",
            "password": "testpassword123"
        })
        token = login_response.json()["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        return client
    
    def test_create_project(self, authenticated_client):
        """测试创建项目"""
        project_data = {
            "name": "测试项目",
            "description": "这是一个测试项目",
            "start_date": "2025-01-01",
            "end_date": "2025-12-31",
            "budget": 100000
        }
        
        response = authenticated_client.post("/api/projects/", json=project_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "测试项目"
        assert data["description"] == "这是一个测试项目"
        assert "id" in data
    
    def test_get_projects(self, authenticated_client):
        """测试获取项目列表"""
        # 先创建一个项目
        project_data = {
            "name": "列表测试项目",
            "description": "用于测试列表的项目",
            "start_date": "2025-01-01",
            "end_date": "2025-12-31"
        }
        authenticated_client.post("/api/projects/", json=project_data)
        
        # 获取项目列表
        response = authenticated_client.get("/api/projects/")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
    
    def test_get_project_by_id(self, authenticated_client):
        """测试根据ID获取项目"""
        # 创建项目
        project_data = {
            "name": "ID测试项目",
            "description": "用于测试ID获取的项目",
            "start_date": "2025-01-01",
            "end_date": "2025-12-31"
        }
        create_response = authenticated_client.post("/api/projects/", json=project_data)
        project_id = create_response.json()["id"]
        
        # 根据ID获取项目
        response = authenticated_client.get(f"/api/projects/{project_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == project_id
        assert data["name"] == "ID测试项目"
    
    def test_update_project(self, authenticated_client):
        """测试更新项目"""
        # 创建项目
        project_data = {
            "name": "更新测试项目",
            "description": "用于测试更新的项目",
            "start_date": "2025-01-01",
            "end_date": "2025-12-31"
        }
        create_response = authenticated_client.post("/api/projects/", json=project_data)
        project_id = create_response.json()["id"]
        
        # 更新项目
        update_data = {
            "name": "更新后的项目名称",
            "description": "更新后的项目描述"
        }
        response = authenticated_client.put(f"/api/projects/{project_id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "更新后的项目名称"
        assert data["description"] == "更新后的项目描述"
    
    def test_delete_project(self, authenticated_client):
        """测试删除项目"""
        # 创建项目
        project_data = {
            "name": "删除测试项目",
            "description": "用于测试删除的项目",
            "start_date": "2025-01-01",
            "end_date": "2025-12-31"
        }
        create_response = authenticated_client.post("/api/projects/", json=project_data)
        project_id = create_response.json()["id"]
        
        # 删除项目
        response = authenticated_client.delete(f"/api/projects/{project_id}")
        
        assert response.status_code == 204
        
        # 验证项目已删除
        get_response = authenticated_client.get(f"/api/projects/{project_id}")
        assert get_response.status_code == 404


class TestTaskAPI:
    """任务API测试"""
    
    @pytest.fixture
    def authenticated_client_with_project(self):
        """创建已认证的测试客户端并创建项目"""
        client = TestClient(app)
        
        # 注册并登录
        user_data = {
            "username": "tasktest",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "任务测试用户"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", data={
            "username": "tasktest",
            "password": "testpassword123"
        })
        token = login_response.json()["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # 创建项目
        project_data = {
            "name": "任务测试项目",
            "description": "用于任务测试的项目",
            "start_date": "2025-01-01",
            "end_date": "2025-12-31"
        }
        project_response = client.post("/api/projects/", json=project_data)
        project_id = project_response.json()["id"]
        
        client.project_id = project_id
        return client
    
    def test_create_task(self, authenticated_client_with_project):
        """测试创建任务"""
        client = authenticated_client_with_project
        
        task_data = {
            "title": "测试任务",
            "description": "这是一个测试任务",
            "priority": "high",
            "project_id": client.project_id,
            "due_date": "2025-08-30"
        }
        
        response = client.post("/api/tasks/", json=task_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "测试任务"
        assert data["priority"] == "high"
        assert "id" in data
    
    def test_update_task_status(self, authenticated_client_with_project):
        """测试更新任务状态"""
        client = authenticated_client_with_project
        
        # 创建任务
        task_data = {
            "title": "状态测试任务",
            "description": "用于测试状态更新的任务",
            "priority": "medium",
            "project_id": client.project_id,
            "due_date": "2025-08-30"
        }
        create_response = client.post("/api/tasks/", json=task_data)
        task_id = create_response.json()["id"]
        
        # 更新状态
        response = client.patch(f"/api/tasks/{task_id}/status", json={"status": "in_progress"})
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "in_progress"


class TestIntegrationAPI:
    """集成API测试"""
    
    @pytest.fixture
    def authenticated_client(self):
        """创建已认证的测试客户端"""
        client = TestClient(app)
        
        # 注册并登录
        user_data = {
            "username": "integrationtest",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "集成测试用户"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", data={
            "username": "integrationtest",
            "password": "testpassword123"
        })
        token = login_response.json()["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        return client
    
    def test_jira_integration(self, authenticated_client):
        """测试Jira集成"""
        jira_config = {
            "name": "测试Jira集成",
            "type": "jira",
            "config": {
                "serverUrl": "https://test.atlassian.net",
                "username": "<EMAIL>",
                "apiToken": "test-token"
            }
        }
        
        response = authenticated_client.post("/api/integrations/jira/test-connection", json=jira_config)
        
        # 由于是测试环境，连接可能失败，但应该返回适当的响应
        assert response.status_code in [200, 400]
    
    def test_notification_integration(self, authenticated_client):
        """测试通知集成"""
        notification_data = {
            "type": "info",
            "channel": "in_app",
            "title": "测试通知",
            "content": "这是一个测试通知",
            "user_id": "test-user"
        }
        
        response = authenticated_client.post("/api/notifications/send", json=notification_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "通知发送成功"


class TestErrorHandling:
    """错误处理测试"""
    
    def test_404_error(self):
        """测试404错误"""
        client = TestClient(app)
        response = client.get("/api/nonexistent")
        
        assert response.status_code == 404
    
    def test_validation_error(self):
        """测试验证错误"""
        client = TestClient(app)
        
        # 发送无效数据
        invalid_data = {
            "name": "",  # 空名称
            "email": "invalid-email"  # 无效邮箱
        }
        
        response = client.post("/api/auth/register", json=invalid_data)
        
        assert response.status_code == 422
        assert "detail" in response.json()


class TestPerformance:
    """性能测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """测试并发请求"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 创建多个并发请求
            tasks = []
            for i in range(10):
                task = client.get("/api/health")
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            
            # 验证所有请求都成功
            for response in responses:
                assert response.status_code == 200
    
    def test_large_data_handling(self, authenticated_client):
        """测试大数据处理"""
        # 创建大量项目
        projects = []
        for i in range(50):
            project_data = {
                "name": f"批量测试项目{i}",
                "description": f"批量测试项目描述{i}",
                "start_date": "2025-01-01",
                "end_date": "2025-12-31"
            }
            projects.append(project_data)
        
        # 批量创建（如果API支持）
        # 或者逐个创建并测试性能
        start_time = datetime.now()
        
        for project in projects[:5]:  # 限制数量以避免测试时间过长
            response = authenticated_client.post("/api/projects/", json=project)
            assert response.status_code == 201
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 验证性能在可接受范围内（例如每个请求不超过1秒）
        assert duration < 5.0  # 5个请求在5秒内完成


if __name__ == "__main__":
    pytest.main([__file__])
