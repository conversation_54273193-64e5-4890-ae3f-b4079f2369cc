"""
性能和负载测试
使用Locust进行API性能测试

<AUTHOR>
@version 1.0.0
@since 2025-08-28
"""

import random
import json
from locust import HttpUser, task, between
from locust.exception import RescheduleTask


class APIUser(HttpUser):
    """API用户行为模拟"""
    
    wait_time = between(1, 3)  # 用户操作间隔1-3秒
    
    def on_start(self):
        """用户开始时的初始化操作"""
        self.token = None
        self.user_id = None
        self.project_id = None
        self.task_id = None
        
        # 注册并登录用户
        self.register_and_login()
    
    def register_and_login(self):
        """注册并登录用户"""
        # 生成随机用户数据
        user_suffix = random.randint(1000, 9999)
        self.username = f"loadtest_user_{user_suffix}"
        self.email = f"loadtest_{user_suffix}@example.com"
        self.password = "LoadTest123!"
        
        # 注册用户
        register_data = {
            "username": self.username,
            "email": self.email,
            "password": self.password,
            "full_name": f"负载测试用户{user_suffix}"
        }
        
        with self.client.post("/api/v1/auth/register", 
                             json=register_data, 
                             catch_response=True) as response:
            if response.status_code == 201:
                response.success()
            elif response.status_code == 409:
                # 用户已存在，继续登录
                response.success()
            else:
                response.failure(f"注册失败: {response.status_code}")
        
        # 登录获取token
        login_data = {
            "username": self.username,
            "password": self.password
        }
        
        with self.client.post("/api/v1/auth/login", 
                             data=login_data, 
                             catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.user_id = data.get("user_id")
                
                # 设置认证头
                self.client.headers.update({
                    "Authorization": f"Bearer {self.token}"
                })
                response.success()
            else:
                response.failure(f"登录失败: {response.status_code}")
                raise RescheduleTask()

    @task(3)
    def view_dashboard(self):
        """查看仪表板 - 高频操作"""
        with self.client.get("/api/v1/dashboard", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取仪表板失败: {response.status_code}")

    @task(2)
    def list_projects(self):
        """获取项目列表 - 中频操作"""
        with self.client.get("/api/v1/projects", 
                           catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("data") and len(data["data"]) > 0:
                    # 随机选择一个项目用于后续操作
                    self.project_id = random.choice(data["data"])["id"]
                response.success()
            else:
                response.failure(f"获取项目列表失败: {response.status_code}")

    @task(1)
    def create_project(self):
        """创建项目 - 低频操作"""
        project_data = {
            "name": f"负载测试项目_{random.randint(1000, 9999)}",
            "description": "这是一个负载测试创建的项目",
            "start_date": "2025-01-01",
            "end_date": "2025-12-31",
            "priority": random.choice(["LOW", "MEDIUM", "HIGH"]),
            "budget": random.randint(10000, 100000)
        }
        
        with self.client.post("/api/v1/projects", 
                            json=project_data, 
                            catch_response=True) as response:
            if response.status_code == 201:
                data = response.json()
                self.project_id = data.get("id")
                response.success()
            else:
                response.failure(f"创建项目失败: {response.status_code}")

    @task(2)
    def view_project_details(self):
        """查看项目详情"""
        if not self.project_id:
            return
            
        with self.client.get(f"/api/v1/projects/{self.project_id}", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 404:
                # 项目不存在，重置project_id
                self.project_id = None
                response.success()
            else:
                response.failure(f"获取项目详情失败: {response.status_code}")

    @task(2)
    def list_tasks(self):
        """获取任务列表"""
        if not self.project_id:
            return
            
        with self.client.get(f"/api/v1/projects/{self.project_id}/tasks", 
                           catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("data") and len(data["data"]) > 0:
                    self.task_id = random.choice(data["data"])["id"]
                response.success()
            else:
                response.failure(f"获取任务列表失败: {response.status_code}")

    @task(1)
    def create_task(self):
        """创建任务"""
        if not self.project_id:
            return
            
        task_data = {
            "title": f"负载测试任务_{random.randint(1000, 9999)}",
            "description": "这是一个负载测试创建的任务",
            "project_id": self.project_id,
            "priority": random.choice(["LOW", "MEDIUM", "HIGH"]),
            "status": "TODO",
            "due_date": "2025-06-30"
        }
        
        with self.client.post("/api/v1/tasks", 
                            json=task_data, 
                            catch_response=True) as response:
            if response.status_code == 201:
                data = response.json()
                self.task_id = data.get("id")
                response.success()
            else:
                response.failure(f"创建任务失败: {response.status_code}")

    @task(1)
    def update_task_status(self):
        """更新任务状态"""
        if not self.task_id:
            return
            
        new_status = random.choice(["TODO", "IN_PROGRESS", "DONE"])
        update_data = {"status": new_status}
        
        with self.client.patch(f"/api/v1/tasks/{self.task_id}", 
                             json=update_data, 
                             catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 404:
                # 任务不存在，重置task_id
                self.task_id = None
                response.success()
            else:
                response.failure(f"更新任务状态失败: {response.status_code}")

    @task(1)
    def get_ai_insights(self):
        """获取AI洞察"""
        if not self.project_id:
            return
            
        with self.client.get(f"/api/v1/analysis/insights/{self.project_id}", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 404:
                response.success()  # 项目不存在是正常情况
            else:
                response.failure(f"获取AI洞察失败: {response.status_code}")

    @task(1)
    def search_projects(self):
        """搜索项目"""
        search_keywords = ["测试", "项目", "开发", "管理", "系统"]
        keyword = random.choice(search_keywords)
        
        with self.client.get(f"/api/v1/projects/search?q={keyword}", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"搜索项目失败: {response.status_code}")

    @task(1)
    def get_notifications(self):
        """获取通知"""
        with self.client.get("/api/v1/notifications", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取通知失败: {response.status_code}")


class AdminUser(HttpUser):
    """管理员用户行为模拟"""
    
    wait_time = between(2, 5)  # 管理员操作间隔较长
    weight = 1  # 管理员用户权重较低
    
    def on_start(self):
        """管理员用户初始化"""
        self.login_as_admin()
    
    def login_as_admin(self):
        """以管理员身份登录"""
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        with self.client.post("/api/v1/auth/login", 
                             data=login_data, 
                             catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                token = data.get("access_token")
                self.client.headers.update({
                    "Authorization": f"Bearer {token}"
                })
                response.success()
            else:
                response.failure(f"管理员登录失败: {response.status_code}")
                raise RescheduleTask()

    @task(2)
    def view_system_stats(self):
        """查看系统统计"""
        with self.client.get("/api/v1/admin/stats", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取系统统计失败: {response.status_code}")

    @task(1)
    def view_user_list(self):
        """查看用户列表"""
        with self.client.get("/api/v1/admin/users", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取用户列表失败: {response.status_code}")

    @task(1)
    def view_audit_logs(self):
        """查看审计日志"""
        with self.client.get("/api/v1/admin/audit-logs", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取审计日志失败: {response.status_code}")


class ReadOnlyUser(HttpUser):
    """只读用户行为模拟"""
    
    wait_time = between(1, 2)  # 只读用户操作频繁
    weight = 2  # 只读用户权重较高
    
    def on_start(self):
        """只读用户初始化"""
        self.register_and_login_readonly()
    
    def register_and_login_readonly(self):
        """注册并登录只读用户"""
        user_suffix = random.randint(10000, 99999)
        username = f"readonly_user_{user_suffix}"
        email = f"readonly_{user_suffix}@example.com"
        password = "ReadOnly123!"
        
        # 注册
        register_data = {
            "username": username,
            "email": email,
            "password": password,
            "full_name": f"只读用户{user_suffix}",
            "role": "VIEWER"
        }
        
        self.client.post("/api/v1/auth/register", json=register_data)
        
        # 登录
        login_data = {"username": username, "password": password}
        
        with self.client.post("/api/v1/auth/login", 
                             data=login_data, 
                             catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                token = data.get("access_token")
                self.client.headers.update({
                    "Authorization": f"Bearer {token}"
                })
                response.success()
            else:
                response.failure(f"只读用户登录失败: {response.status_code}")
                raise RescheduleTask()

    @task(5)
    def browse_projects(self):
        """浏览项目"""
        with self.client.get("/api/v1/projects", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"浏览项目失败: {response.status_code}")

    @task(3)
    def view_project_analytics(self):
        """查看项目分析"""
        # 随机选择一个项目ID（在实际测试中应该从项目列表获取）
        project_id = f"project-{random.randint(1, 100)}"
        
        with self.client.get(f"/api/v1/analysis/insights/{project_id}", 
                           catch_response=True) as response:
            if response.status_code in [200, 404]:
                response.success()
            else:
                response.failure(f"查看项目分析失败: {response.status_code}")

    @task(2)
    def search_content(self):
        """搜索内容"""
        search_terms = ["项目", "任务", "团队", "进度", "报告"]
        term = random.choice(search_terms)
        
        with self.client.get(f"/api/v1/search?q={term}", 
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"搜索内容失败: {response.status_code}")


# 性能测试配置
class PerformanceTestConfig:
    """性能测试配置"""
    
    # 测试目标
    TARGET_RPS = 100  # 目标每秒请求数
    MAX_RESPONSE_TIME = 200  # 最大响应时间(ms)
    ERROR_RATE_THRESHOLD = 0.01  # 错误率阈值(1%)
    
    # 负载配置
    USER_CLASSES = [
        (APIUser, 10),      # 10个普通用户
        (AdminUser, 1),     # 1个管理员用户
        (ReadOnlyUser, 5)   # 5个只读用户
    ]
    
    # 测试场景
    SCENARIOS = {
        "smoke_test": {
            "users": 5,
            "spawn_rate": 1,
            "duration": "2m"
        },
        "load_test": {
            "users": 50,
            "spawn_rate": 5,
            "duration": "10m"
        },
        "stress_test": {
            "users": 200,
            "spawn_rate": 10,
            "duration": "15m"
        },
        "spike_test": {
            "users": 500,
            "spawn_rate": 50,
            "duration": "5m"
        }
    }


if __name__ == "__main__":
    # 运行性能测试的示例命令：
    # locust -f load_test.py --host=http://localhost:8080 --users=50 --spawn-rate=5 --run-time=10m
    print("性能测试配置已加载")
    print("使用以下命令运行测试：")
    print("locust -f load_test.py --host=http://localhost:8080 --users=50 --spawn-rate=5 --run-time=10m")
