# pytest配置文件
# AI项目管理平台测试配置
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-16

[tool:pytest]
# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 最小版本要求
minversion = 6.0

# 添加选项
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --cov=backend
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    --durations=10

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    fast: 快速测试
    api: API测试
    database: 数据库测试
    auth: 认证测试
    project: 项目相关测试
    task: 任务相关测试
    user: 用户相关测试
    notification: 通知相关测试
    integration_service: 集成服务测试
    ai: AI功能测试
    performance: 性能测试
    security: 安全测试

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 测试发现
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    node_modules
    .venv
    venv

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 覆盖率配置
[coverage:run]
source = backend
omit = 
    */tests/*
    */venv/*
    */migrations/*
    */settings/*
    */manage.py
    */wsgi.py
    */asgi.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
