/**
 * Playwright端到端测试配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

const { defineConfig, devices } = require('@playwright/test')

/**
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  // 测试目录
  testDir: './e2e',
  
  // 全局设置文件
  globalSetup: require.resolve('./setup/global-setup.js'),
  globalTeardown: require.resolve('./setup/global-teardown.js'),
  
  // 每个测试文件的超时时间
  timeout: 30 * 1000,
  
  // 断言超时时间
  expect: {
    timeout: 5000
  },
  
  // 失败时重试次数
  retries: process.env.CI ? 2 : 0,
  
  // 并行工作进程数
  workers: process.env.CI ? 1 : undefined,
  
  // 报告器配置
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }],
    ['line']
  ],
  
  // 全局测试配置
  use: {
    // 基础URL
    baseURL: process.env.E2E_BASE_URL || 'http://localhost:3000',
    
    // 浏览器上下文选项
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // 导航超时
    navigationTimeout: 30 * 1000,
    
    // 操作超时
    actionTimeout: 10 * 1000,
    
    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,
    
    // 用户代理
    userAgent: 'AI-PM-E2E-Tests',
    
    // 视口大小
    viewport: { width: 1280, height: 720 },
    
    // 语言环境
    locale: 'zh-CN',
    
    // 时区
    timezoneId: 'Asia/Shanghai',
    
    // 权限
    permissions: ['notifications'],
    
    // 地理位置
    geolocation: { longitude: 116.4074, latitude: 39.9042 }, // 北京
    
    // 颜色方案
    colorScheme: 'light'
  },

  // 项目配置 - 不同浏览器和设备
  projects: [
    // 桌面浏览器
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    
    // 移动设备
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
    
    // 平板设备
    {
      name: 'Tablet',
      use: { ...devices['iPad Pro'] },
    },
    
    // 特定测试场景
    {
      name: 'authenticated',
      use: { 
        ...devices['Desktop Chrome'],
        storageState: 'tests/auth/user.json'
      },
      dependencies: ['setup']
    },
    
    // 设置项目
    {
      name: 'setup',
      testMatch: /.*\.setup\.js/,
      teardown: 'cleanup'
    },
    
    // 清理项目
    {
      name: 'cleanup',
      testMatch: /.*\.cleanup\.js/
    }
  ],

  // 输出目录
  outputDir: 'test-results/',
  
  // Web服务器配置（用于开发环境）
  webServer: process.env.CI ? undefined : {
    command: 'npm run dev',
    port: 3000,
    cwd: '../frontend',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
    env: {
      NODE_ENV: 'test'
    }
  },
  
  // 测试匹配模式
  testMatch: [
    '**/*.test.js',
    '**/*.spec.js'
  ],
  
  // 忽略的测试文件
  testIgnore: [
    '**/node_modules/**',
    '**/build/**',
    '**/dist/**'
  ],
  
  // 元数据
  metadata: {
    platform: process.platform,
    version: require('../package.json').version,
    environment: process.env.NODE_ENV || 'test'
  },
  
  // 实验性功能
  experimental: {
    // 启用测试生成器
    testGenerator: true
  }
})
