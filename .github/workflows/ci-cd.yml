# AI项目管理平台 CI/CD 流水线
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-16

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, develop ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ai-pm

jobs:
  # 代码质量检查
  code-quality:
    name: Code Quality Check
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
        cache-dependency-path: services/notification-service/go.sum

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Install backend dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Frontend lint and type check
      working-directory: ./frontend
      run: |
        npm run lint
        npm run type-check

    - name: Backend lint and type check
      working-directory: ./backend
      run: |
        flake8 .
        mypy .
        black --check .

    - name: Go lint
      working-directory: ./services/notification-service
      run: |
        go fmt ./...
        go vet ./...
        golangci-lint run

    - name: Security scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif'

  # 单元测试
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: code-quality
    strategy:
      matrix:
        component: [frontend, backend, ai-service, notification-service]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup test environment
      run: |
        case "${{ matrix.component }}" in
          frontend)
            cd frontend
            npm ci
            ;;
          backend|ai-service)
            cd ${{ matrix.component }}
            python -m pip install --upgrade pip
            pip install -r requirements.txt
            pip install pytest pytest-cov
            ;;
          notification-service)
            cd services/notification-service
            go mod download
            ;;
        esac

    - name: Run tests
      run: |
        case "${{ matrix.component }}" in
          frontend)
            cd frontend
            npm run test:coverage
            ;;
          backend|ai-service)
            cd ${{ matrix.component }}
            pytest --cov=. --cov-report=xml
            ;;
          notification-service)
            cd services/notification-service
            go test -v -race -coverprofile=coverage.out ./...
            ;;
        esac

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./${{ matrix.component }}/coverage.xml
        flags: ${{ matrix.component }}
        name: ${{ matrix.component }}-coverage

  # 集成测试
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt
        pip install pytest pytest-asyncio

    - name: Run integration tests
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
      run: |
        cd tests/integration
        pytest -v

  # 端到端测试
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install Playwright
      run: |
        cd tests
        npm install
        npx playwright install --with-deps

    - name: Start services
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30

    - name: Run E2E tests
      run: |
        cd tests
        npx playwright test

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: tests/test-results/

    - name: Stop services
      if: always()
      run: docker-compose -f docker-compose.test.yml down

  # 构建镜像
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: github.event_name == 'push'
    strategy:
      matrix:
        component: [frontend, backend, ai-service, notification-service, integration-service]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.component }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build-images
    if: github.event_name == 'push'
    strategy:
      matrix:
        component: [frontend, backend, ai-service, notification-service, integration-service]
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ github.repository }}/${{ matrix.component }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到开发环境
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [build-images, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: development
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_DEV }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Deploy to development
      run: |
        cd k8s/overlays/dev
        kubectl apply -k .
        kubectl rollout status deployment/frontend -n ai-pm-dev
        kubectl rollout status deployment/backend -n ai-pm-dev

  # 部署到生产环境
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-images, security-scan, e2e-tests]
    if: startsWith(github.ref, 'refs/tags/v')
    environment: production
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_PROD }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Install Helm
      uses: azure/setup-helm@v3
      with:
        version: '3.12.0'

    - name: Create production secrets
      run: |
        # 创建生产环境密钥
        kubectl create secret generic database-secret \
          --from-literal=url="${{ secrets.PROD_DATABASE_URL }}" \
          --from-literal=username="${{ secrets.PROD_DB_USER }}" \
          --from-literal=password="${{ secrets.PROD_DB_PASSWORD }}" \
          --namespace=ai-pm-prod \
          --dry-run=client -o yaml | kubectl apply -f -

        kubectl create secret generic redis-secret \
          --from-literal=url="${{ secrets.PROD_REDIS_URL }}" \
          --from-literal=password="${{ secrets.PROD_REDIS_PASSWORD }}" \
          --namespace=ai-pm-prod \
          --dry-run=client -o yaml | kubectl apply -f -

    - name: Deploy with Helm
      run: |
        # 使用Helm部署到生产环境
        helm upgrade --install ai-pm ./helm/ai-pm \
          --namespace=ai-pm-prod \
          --create-namespace \
          --values=deployment/production/values.yaml \
          --set global.imageTag=${{ github.sha }} \
          --set global.environment=production \
          --wait --timeout=15m

    - name: Verify deployment
      run: |
        # 验证部署状态
        kubectl rollout status deployment/backend -n ai-pm-prod --timeout=300s
        kubectl rollout status deployment/frontend -n ai-pm-prod --timeout=300s
        kubectl rollout status deployment/ai-service -n ai-pm-prod --timeout=300s

        # 运行健康检查
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=ai-pm -n ai-pm-prod --timeout=300s

    - name: Run smoke tests
      run: |
        # 运行冒烟测试
        cd tests/smoke
        npm install
        npm run test:production

    - name: Notify deployment success
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "🚀 AI项目管理平台生产环境部署成功！版本: ${{ github.ref_name }}"
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

    - name: Notify deployment failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ AI项目管理平台生产环境部署失败！版本: ${{ github.ref_name }}"
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()
