# 生产环境Dockerfile - 用户管理服务（OAuth2支持）

# 构建阶段
FROM maven:3.9-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests -Dmaven.javadoc.skip=true

# 运行阶段
FROM openjdk:17-jre-slim

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r aipm && useradd -r -g aipm aipm

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs /app/config && \
    chown -R aipm:aipm /app

# 复制构建的JAR文件
COPY --from=builder /app/target/*.jar app.jar

# 复制配置文件
COPY src/main/resources/application-prod.yml /app/config/

# 设置文件权限
RUN chown -R aipm:aipm /app

# 切换到应用用户
USER aipm

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# JVM参数优化
ENV JAVA_OPTS="-Xms512m -Xmx1g -XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:+UseStringDeduplication -XX:+OptimizeStringConcat -Djava.security.egd=file:/dev/./urandom"

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar --spring.profiles.active=prod --spring.config.additional-location=file:/app/config/"]

# 元数据标签
LABEL maintainer="AI项目管理平台开发团队" \
      version="1.0.0" \
      description="AI项目管理平台用户管理服务（OAuth2支持）" \
      org.opencontainers.image.title="AIPM User Management Service" \
      org.opencontainers.image.description="用户管理微服务，支持OAuth2认证" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="AI项目管理平台" \
      org.opencontainers.image.licenses="MIT"
