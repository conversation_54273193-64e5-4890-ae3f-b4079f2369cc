package com.aipm.usermanagement.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 统一API响应格式
 * 
 * 提供标准化的API响应结构，包含成功状态、消息、数据和时间戳
 * 
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "API统一响应格式")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {

    /**
     * 请求是否成功
     */
    @Schema(description = "请求是否成功", example = "true")
    private boolean success;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "操作成功")
    private String message;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private T data;

    /**
     * 错误代码（仅在失败时返回）
     */
    @Schema(description = "错误代码", example = "USER_NOT_FOUND")
    private String errorCode;

    /**
     * 错误详情（仅在失败时返回）
     */
    @Schema(description = "错误详情")
    private Object errorDetails;

    /**
     * 响应时间戳
     */
    @Schema(description = "响应时间戳", example = "2025-08-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    /**
     * 请求ID（用于追踪）
     */
    @Schema(description = "请求追踪ID", example = "req-123456789")
    private String requestId;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 基础构造函数
     * 
     * @param success 是否成功
     * @param message 响应消息
     */
    public ApiResponse(boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }

    /**
     * 成功响应构造函数
     * 
     * @param success 是否成功
     * @param message 响应消息
     * @param data 响应数据
     */
    public ApiResponse(boolean success, String message, T data) {
        this(success, message);
        this.data = data;
    }

    /**
     * 失败响应构造函数
     * 
     * @param success 是否成功
     * @param message 响应消息
     * @param errorCode 错误代码
     * @param errorDetails 错误详情
     */
    public ApiResponse(boolean success, String message, String errorCode, Object errorDetails) {
        this(success, message);
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }

    // ============================================================================
    // 静态工厂方法
    // ============================================================================

    /**
     * 创建成功响应
     * 
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(true, "操作成功");
    }

    /**
     * 创建成功响应（带消息）
     * 
     * @param message 成功消息
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message) {
        return new ApiResponse<>(true, message);
    }

    /**
     * 创建成功响应（带数据）
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, "操作成功", data);
    }

    /**
     * 创建成功响应（带消息和数据）
     * 
     * @param message 成功消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(true, message, data);
    }

    /**
     * 创建失败响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(false, message);
    }

    /**
     * 创建失败响应（带错误代码）
     * 
     * @param message 错误消息
     * @param errorCode 错误代码
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(String message, String errorCode) {
        return new ApiResponse<>(false, message, errorCode, null);
    }

    /**
     * 创建失败响应（带错误代码和详情）
     * 
     * @param message 错误消息
     * @param errorCode 错误代码
     * @param errorDetails 错误详情
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(String message, String errorCode, Object errorDetails) {
        return new ApiResponse<>(false, message, errorCode, errorDetails);
    }

    // ============================================================================
    // 链式调用方法
    // ============================================================================

    /**
     * 设置请求ID
     * 
     * @param requestId 请求ID
     * @return 当前对象
     */
    public ApiResponse<T> withRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    /**
     * 设置时间戳
     * 
     * @param timestamp 时间戳
     * @return 当前对象
     */
    public ApiResponse<T> withTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Object getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(Object errorDetails) {
        this.errorDetails = errorDetails;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public String toString() {
        return "ApiResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", errorCode='" + errorCode + '\'' +
                ", timestamp=" + timestamp +
                ", requestId='" + requestId + '\'' +
                '}';
    }
}
