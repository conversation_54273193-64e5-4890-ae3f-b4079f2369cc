package com.aipm.usermanagement.controller;

import com.aipm.usermanagement.dto.*;
import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.mapper.UserMapper;
import com.aipm.usermanagement.security.JwtTokenProvider;
import com.aipm.usermanagement.service.AuthenticationService;
import com.aipm.usermanagement.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 
 * 提供用户认证相关的REST API接口，包括登录、注册、令牌刷新等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "用户认证相关API")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthenticationService authenticationService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @param request HTTP请求对象
     * @return 认证响应
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "使用用户名/邮箱和密码进行登录认证")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "登录成功",
            content = @Content(schema = @Schema(implementation = AuthResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "401", 
            description = "认证失败",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "423", 
            description = "账户被锁定",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<AuthResponse>> login(
            @Valid @RequestBody LoginRequest loginRequest,
            HttpServletRequest request) {
        
        logger.info("用户登录请求: {}", loginRequest.getUsernameOrEmail());

        try {
            // 获取客户端IP地址
            String clientIp = getClientIpAddress(request);
            
            // 执行认证
            AuthenticationService.AuthenticationResult result = authenticationService.login(
                loginRequest.getUsernameOrEmail(),
                loginRequest.getPassword(),
                clientIp
            );

            if (result.isSuccess()) {
                // 构建认证响应
                UserDto userDto = userMapper.toDto(result.getUser());
                JwtTokenProvider.TokenInfo tokenInfo = result.getTokenInfo();
                
                AuthResponse authResponse = AuthResponse.success(
                    tokenInfo.getAccessToken(),
                    tokenInfo.getRefreshToken(),
                    tokenInfo.getAccessTokenExpiresAt(),
                    tokenInfo.getRefreshTokenExpiresAt(),
                    userDto
                ).withPermissions(result.getUser().getRole().getPermissions().toArray(new String[0]));

                logger.info("用户 {} 登录成功", result.getUser().getUsername());
                return ResponseEntity.ok(ApiResponse.success("登录成功", authResponse));
            } else {
                logger.warn("用户 {} 登录失败: {}", loginRequest.getUsernameOrEmail(), result.getMessage());
                
                // 根据错误类型返回不同的HTTP状态码
                HttpStatus status = result.getMessage().contains("锁定") ? 
                    HttpStatus.LOCKED : HttpStatus.UNAUTHORIZED;
                
                return ResponseEntity.status(status)
                    .body(ApiResponse.error(result.getMessage(), "LOGIN_FAILED"));
            }
        } catch (Exception e) {
            logger.error("登录处理异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("登录服务暂时不可用", "INTERNAL_ERROR"));
        }
    }

    /**
     * 用户注册
     * 
     * @param registerRequest 注册请求
     * @param request HTTP请求对象
     * @return 注册响应
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "创建新用户账户")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "201", 
            description = "注册成功",
            content = @Content(schema = @Schema(implementation = UserDto.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "注册信息无效",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "409", 
            description = "用户名或邮箱已存在",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<UserDto>> register(
            @Valid @RequestBody RegisterRequest registerRequest,
            HttpServletRequest request) {
        
        logger.info("用户注册请求: {}", registerRequest.getUsername());

        try {
            // 验证密码一致性
            if (!registerRequest.isPasswordMatching()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("密码和确认密码不一致", "PASSWORD_MISMATCH"));
            }

            // 验证服务条款同意
            if (!registerRequest.hasAgreedToTerms()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("必须同意服务条款才能注册", "TERMS_NOT_AGREED"));
            }

            // 检查用户名是否可用
            if (!userService.isUsernameAvailable(registerRequest.getUsername())) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error("用户名已存在", "USERNAME_EXISTS"));
            }

            // 检查邮箱是否可用
            if (!userService.isEmailAvailable(registerRequest.getEmail())) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error("邮箱已存在", "EMAIL_EXISTS"));
            }

            // 创建用户
            User user = userMapper.fromRegisterRequest(registerRequest);
            User createdUser = userService.createUser(user);
            UserDto userDto = userMapper.toDto(createdUser);

            logger.info("用户 {} 注册成功", createdUser.getUsername());
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("注册成功，请查收邮箱验证邮件", userDto));

        } catch (Exception e) {
            logger.error("注册处理异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("注册服务暂时不可用", "INTERNAL_ERROR"));
        }
    }

    /**
     * 刷新访问令牌
     * 
     * @param refreshTokenRequest 刷新令牌请求
     * @return 新的认证响应
     */
    @PostMapping("/refresh-token")
    @Operation(summary = "刷新访问令牌", description = "使用刷新令牌获取新的访问令牌")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "令牌刷新成功",
            content = @Content(schema = @Schema(implementation = AuthResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "401", 
            description = "刷新令牌无效或已过期",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<AuthResponse>> refreshToken(
            @RequestBody RefreshTokenRequest refreshTokenRequest) {
        
        logger.debug("令牌刷新请求");

        try {
            AuthenticationService.AuthenticationResult result = 
                authenticationService.refreshToken(refreshTokenRequest.getRefreshToken());

            if (result.isSuccess()) {
                UserDto userDto = userMapper.toDto(result.getUser());
                JwtTokenProvider.TokenInfo tokenInfo = result.getTokenInfo();
                
                AuthResponse authResponse = AuthResponse.success(
                    tokenInfo.getAccessToken(),
                    tokenInfo.getRefreshToken(),
                    tokenInfo.getAccessTokenExpiresAt(),
                    tokenInfo.getRefreshTokenExpiresAt(),
                    userDto
                ).withPermissions(result.getUser().getRole().getPermissions().toArray(new String[0]));

                logger.info("用户 {} 令牌刷新成功", result.getUser().getUsername());
                return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", authResponse));
            } else {
                logger.warn("令牌刷新失败: {}", result.getMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(result.getMessage(), "TOKEN_REFRESH_FAILED"));
            }
        } catch (Exception e) {
            logger.error("令牌刷新异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("令牌刷新服务暂时不可用", "INTERNAL_ERROR"));
        }
    }

    /**
     * 用户登出
     * 
     * @param request HTTP请求对象
     * @return 登出响应
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "注销当前用户会话")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "登出成功",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<Void>> logout(HttpServletRequest request) {
        logger.debug("用户登出请求");

        try {
            String accessToken = extractTokenFromRequest(request);
            if (StringUtils.hasText(accessToken)) {
                authenticationService.logout(accessToken);
                logger.info("用户登出成功");
            }
            
            return ResponseEntity.ok(ApiResponse.success("登出成功"));
        } catch (Exception e) {
            logger.error("登出处理异常: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.success("登出成功")); // 即使异常也返回成功
        }
    }

    // ============================================================================
    // 工具方法
    // ============================================================================

    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 从请求中提取访问令牌
     * 
     * @param request HTTP请求对象
     * @return 访问令牌
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 刷新令牌请求DTO
     */
    @Schema(description = "刷新令牌请求")
    public static class RefreshTokenRequest {
        @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
        private String refreshToken;

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }
    }
}
