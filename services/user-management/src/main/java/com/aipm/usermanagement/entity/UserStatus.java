package com.aipm.usermanagement.entity;

/**
 * 用户状态枚举
 * 
 * 定义用户账户的各种状态，用于控制用户的访问权限和账户管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public enum UserStatus {
    
    /**
     * 活跃状态 - 用户可以正常使用系统
     */
    ACTIVE("活跃", "用户账户处于正常活跃状态，可以正常使用所有功能"),
    
    /**
     * 非活跃状态 - 用户暂时无法使用系统
     */
    INACTIVE("非活跃", "用户账户暂时停用，无法登录和使用系统功能"),
    
    /**
     * 暂停状态 - 用户因违规等原因被暂停使用
     */
    SUSPENDED("暂停", "用户账户因违规或其他原因被暂停，需要管理员审核后才能恢复");

    /**
     * 状态显示名称
     */
    private final String displayName;
    
    /**
     * 状态描述
     */
    private final String description;

    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param description 状态描述
     */
    UserStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 获取状态显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 检查状态是否为活跃
     * 
     * @return true表示活跃状态
     */
    public boolean isActive() {
        return this == ACTIVE;
    }

    /**
     * 检查状态是否允许登录
     * 
     * @return true表示允许登录
     */
    public boolean canLogin() {
        return this == ACTIVE;
    }

    /**
     * 根据字符串获取状态枚举
     * 
     * @param status 状态字符串
     * @return 状态枚举，如果不匹配则返回INACTIVE
     */
    public static UserStatus fromString(String status) {
        if (status == null || status.trim().isEmpty()) {
            return INACTIVE;
        }
        
        try {
            return UserStatus.valueOf(status.toUpperCase());
        } catch (IllegalArgumentException e) {
            return INACTIVE;
        }
    }

    /**
     * 获取所有可用状态的显示名称
     * 
     * @return 状态显示名称数组
     */
    public static String[] getDisplayNames() {
        UserStatus[] statuses = UserStatus.values();
        String[] displayNames = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            displayNames[i] = statuses[i].getDisplayName();
        }
        return displayNames;
    }
}
