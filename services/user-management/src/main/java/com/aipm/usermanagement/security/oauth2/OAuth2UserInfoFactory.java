package com.aipm.usermanagement.security.oauth2;

import org.springframework.security.oauth2.core.OAuth2AuthenticationException;

import java.util.Map;

/**
 * OAuth2用户信息工厂类
 * 
 * 根据不同的OAuth2提供商创建对应的用户信息对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
public class OAuth2UserInfoFactory {

    /**
     * 根据注册ID创建OAuth2用户信息对象
     * 
     * @param registrationId OAuth2提供商注册ID
     * @param attributes 用户属性
     * @return OAuth2用户信息对象
     */
    public static OAuth2UserInfo getOAuth2UserInfo(String registrationId, Map<String, Object> attributes) {
        switch (registrationId.toLowerCase()) {
            case "google":
                return new GoogleOAuth2UserInfo(attributes);
            case "github":
                return new GitHubOAuth2UserInfo(attributes);
            case "custom":
                return new CustomOAuth2UserInfo(attributes);
            default:
                throw new OAuth2AuthenticationException("不支持的OAuth2提供商: " + registrationId);
        }
    }

    /**
     * Google OAuth2用户信息实现
     */
    public static class GoogleOAuth2UserInfo extends OAuth2UserInfo {
        public GoogleOAuth2UserInfo(Map<String, Object> attributes) {
            super(attributes);
        }

        @Override
        public String getId() {
            return (String) attributes.get("sub");
        }

        @Override
        public String getName() {
            return (String) attributes.get("name");
        }

        @Override
        public String getEmail() {
            return (String) attributes.get("email");
        }

        @Override
        public String getImageUrl() {
            return (String) attributes.get("picture");
        }
    }

    /**
     * GitHub OAuth2用户信息实现
     */
    public static class GitHubOAuth2UserInfo extends OAuth2UserInfo {
        public GitHubOAuth2UserInfo(Map<String, Object> attributes) {
            super(attributes);
        }

        @Override
        public String getId() {
            return String.valueOf(attributes.get("id"));
        }

        @Override
        public String getName() {
            return (String) attributes.get("name");
        }

        @Override
        public String getEmail() {
            return (String) attributes.get("email");
        }

        @Override
        public String getImageUrl() {
            return (String) attributes.get("avatar_url");
        }
    }

    /**
     * 自定义OAuth2用户信息实现
     * 
     * 适用于自定义的ID Provider，可根据实际情况调整字段映射
     */
    public static class CustomOAuth2UserInfo extends OAuth2UserInfo {
        public CustomOAuth2UserInfo(Map<String, Object> attributes) {
            super(attributes);
        }

        @Override
        public String getId() {
            // 根据自定义ID Provider的响应格式调整
            return (String) attributes.get("id");
        }

        @Override
        public String getName() {
            // 可能是 "name", "full_name", "display_name" 等
            String name = (String) attributes.get("name");
            if (name == null) {
                name = (String) attributes.get("full_name");
            }
            if (name == null) {
                name = (String) attributes.get("display_name");
            }
            return name;
        }

        @Override
        public String getEmail() {
            return (String) attributes.get("email");
        }

        @Override
        public String getImageUrl() {
            // 可能是 "avatar", "picture", "avatar_url" 等
            String imageUrl = (String) attributes.get("avatar");
            if (imageUrl == null) {
                imageUrl = (String) attributes.get("picture");
            }
            if (imageUrl == null) {
                imageUrl = (String) attributes.get("avatar_url");
            }
            return imageUrl;
        }
    }
}
