package com.aipm.usermanagement.security.oauth2;

import java.util.Map;

/**
 * OAuth2用户信息抽象类
 * 
 * 定义从不同OAuth2提供商获取用户信息的统一接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
public abstract class OAuth2UserInfo {
    
    protected Map<String, Object> attributes;

    public OAuth2UserInfo(Map<String, Object> attributes) {
        this.attributes = attributes;
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public abstract String getId();

    /**
     * 获取用户姓名
     * 
     * @return 用户姓名
     */
    public abstract String getName();

    /**
     * 获取用户邮箱
     * 
     * @return 用户邮箱
     */
    public abstract String getEmail();

    /**
     * 获取用户头像URL
     * 
     * @return 头像URL
     */
    public abstract String getImageUrl();
}
