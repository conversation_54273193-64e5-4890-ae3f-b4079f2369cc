package com.aipm.usermanagement.security.oauth2;

import com.aipm.usermanagement.entity.User;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.core.user.OAuth2User;

import java.util.Collection;
import java.util.Map;

/**
 * 自定义OAuth2用户主体
 * 
 * 将系统用户和OAuth2用户信息结合，提供统一的用户主体
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
public class CustomOAuth2UserPrincipal implements OAuth2User {

    private final User user;
    private final Map<String, Object> attributes;

    public CustomOAuth2UserPrincipal(User user, Map<String, Object> attributes) {
        this.user = user;
        this.attributes = attributes;
    }

    @Override
    public Map<String, Object> getAttributes() {
        return attributes;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return user.getAuthorities();
    }

    @Override
    public String getName() {
        return user.getUsername();
    }

    /**
     * 获取系统用户对象
     * 
     * @return 用户对象
     */
    public User getUser() {
        return user;
    }

    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public String getId() {
        return user.getId().toString();
    }

    /**
     * 获取用户邮箱
     * 
     * @return 用户邮箱
     */
    public String getEmail() {
        return user.getEmail();
    }

    /**
     * 获取用户全名
     * 
     * @return 用户全名
     */
    public String getFullName() {
        return user.getFullName();
    }

    /**
     * 获取用户头像URL
     * 
     * @return 头像URL
     */
    public String getAvatarUrl() {
        return user.getAvatarUrl();
    }
}
