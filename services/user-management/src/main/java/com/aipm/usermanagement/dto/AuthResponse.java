package com.aipm.usermanagement.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 认证响应DTO
 * 
 * 用于返回用户认证成功后的令牌信息和用户基本信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "认证响应")
public class AuthResponse {

    /**
     * 访问令牌
     */
    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String refreshToken;

    /**
     * 令牌类型
     */
    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    /**
     * 访问令牌过期时间
     */
    @Schema(description = "访问令牌过期时间", example = "2025-08-16T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime accessTokenExpiresAt;

    /**
     * 刷新令牌过期时间
     */
    @Schema(description = "刷新令牌过期时间", example = "2025-08-22T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refreshTokenExpiresAt;

    /**
     * 用户信息
     */
    @Schema(description = "用户基本信息")
    private UserDto user;

    /**
     * 权限列表
     */
    @Schema(description = "用户权限列表", example = "[\"user:read\", \"project:create\"]")
    private String[] permissions;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public AuthResponse() {
    }

    /**
     * 基础构造函数
     * 
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param user 用户信息
     */
    public AuthResponse(String accessToken, String refreshToken, UserDto user) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.user = user;
    }

    /**
     * 完整构造函数
     * 
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param accessTokenExpiresAt 访问令牌过期时间
     * @param refreshTokenExpiresAt 刷新令牌过期时间
     * @param user 用户信息
     */
    public AuthResponse(String accessToken, String refreshToken, 
                       LocalDateTime accessTokenExpiresAt, LocalDateTime refreshTokenExpiresAt,
                       UserDto user) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.accessTokenExpiresAt = accessTokenExpiresAt;
        this.refreshTokenExpiresAt = refreshTokenExpiresAt;
        this.user = user;
    }

    // ============================================================================
    // 静态工厂方法
    // ============================================================================

    /**
     * 创建认证成功响应
     * 
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param user 用户信息
     * @return 认证响应
     */
    public static AuthResponse success(String accessToken, String refreshToken, UserDto user) {
        return new AuthResponse(accessToken, refreshToken, user);
    }

    /**
     * 创建完整的认证成功响应
     * 
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param accessTokenExpiresAt 访问令牌过期时间
     * @param refreshTokenExpiresAt 刷新令牌过期时间
     * @param user 用户信息
     * @return 认证响应
     */
    public static AuthResponse success(String accessToken, String refreshToken,
                                     LocalDateTime accessTokenExpiresAt, LocalDateTime refreshTokenExpiresAt,
                                     UserDto user) {
        return new AuthResponse(accessToken, refreshToken, accessTokenExpiresAt, refreshTokenExpiresAt, user);
    }

    // ============================================================================
    // 链式调用方法
    // ============================================================================

    /**
     * 设置权限列表
     * 
     * @param permissions 权限数组
     * @return 当前对象
     */
    public AuthResponse withPermissions(String[] permissions) {
        this.permissions = permissions;
        return this;
    }

    /**
     * 设置令牌过期时间
     * 
     * @param accessTokenExpiresAt 访问令牌过期时间
     * @param refreshTokenExpiresAt 刷新令牌过期时间
     * @return 当前对象
     */
    public AuthResponse withExpirationTimes(LocalDateTime accessTokenExpiresAt, LocalDateTime refreshTokenExpiresAt) {
        this.accessTokenExpiresAt = accessTokenExpiresAt;
        this.refreshTokenExpiresAt = refreshTokenExpiresAt;
        return this;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查令牌是否即将过期（1小时内）
     * 
     * @return true表示即将过期
     */
    public boolean isAccessTokenExpiringSoon() {
        if (accessTokenExpiresAt == null) {
            return false;
        }
        return accessTokenExpiresAt.isBefore(LocalDateTime.now().plusHours(1));
    }

    /**
     * 获取访问令牌剩余有效时间（分钟）
     * 
     * @return 剩余分钟数，如果已过期返回0
     */
    public long getAccessTokenRemainingMinutes() {
        if (accessTokenExpiresAt == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        if (accessTokenExpiresAt.isBefore(now)) {
            return 0;
        }
        return java.time.Duration.between(now, accessTokenExpiresAt).toMinutes();
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public LocalDateTime getAccessTokenExpiresAt() {
        return accessTokenExpiresAt;
    }

    public void setAccessTokenExpiresAt(LocalDateTime accessTokenExpiresAt) {
        this.accessTokenExpiresAt = accessTokenExpiresAt;
    }

    public LocalDateTime getRefreshTokenExpiresAt() {
        return refreshTokenExpiresAt;
    }

    public void setRefreshTokenExpiresAt(LocalDateTime refreshTokenExpiresAt) {
        this.refreshTokenExpiresAt = refreshTokenExpiresAt;
    }

    public UserDto getUser() {
        return user;
    }

    public void setUser(UserDto user) {
        this.user = user;
    }

    public String[] getPermissions() {
        return permissions;
    }

    public void setPermissions(String[] permissions) {
        this.permissions = permissions;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public String toString() {
        return "AuthResponse{" +
                "tokenType='" + tokenType + '\'' +
                ", accessTokenExpiresAt=" + accessTokenExpiresAt +
                ", refreshTokenExpiresAt=" + refreshTokenExpiresAt +
                ", user=" + user +
                ", permissions=" + java.util.Arrays.toString(permissions) +
                '}';
    }
}
