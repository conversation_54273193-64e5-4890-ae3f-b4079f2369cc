package com.aipm.usermanagement.service;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.entity.UserRole;
import com.aipm.usermanagement.entity.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 用户服务接口
 * 
 * 定义用户管理的核心业务逻辑接口，包括用户的创建、查询、更新、删除等操作
 * 以及用户认证、权限管理等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface UserService {

    // ============================================================================
    // 用户基础操作
    // ============================================================================

    /**
     * 创建新用户
     * 
     * @param user 用户对象
     * @return 创建的用户
     * @throws IllegalArgumentException 如果用户数据无效
     * @throws RuntimeException 如果用户名或邮箱已存在
     */
    User createUser(User user);

    /**
     * 根据ID获取用户
     * 
     * @param userId 用户ID
     * @return 用户对象（可能为空）
     */
    Optional<User> getUserById(UUID userId);

    /**
     * 根据用户名获取用户
     * 
     * @param username 用户名
     * @return 用户对象（可能为空）
     */
    Optional<User> getUserByUsername(String username);

    /**
     * 根据邮箱获取用户
     * 
     * @param email 邮箱地址
     * @return 用户对象（可能为空）
     */
    Optional<User> getUserByEmail(String email);

    /**
     * 根据用户名或邮箱获取用户
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @return 用户对象（可能为空）
     */
    Optional<User> getUserByUsernameOrEmail(String usernameOrEmail);

    /**
     * 更新用户信息
     * 
     * @param user 用户对象
     * @return 更新后的用户
     * @throws IllegalArgumentException 如果用户数据无效
     * @throws RuntimeException 如果用户不存在
     */
    User updateUser(User user);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @throws RuntimeException 如果用户不存在
     */
    void deleteUser(UUID userId);

    /**
     * 软删除用户（设置为非活跃状态）
     * 
     * @param userId 用户ID
     * @throws RuntimeException 如果用户不存在
     */
    void deactivateUser(UUID userId);

    // ============================================================================
    // 用户查询操作
    // ============================================================================

    /**
     * 获取所有用户
     * 
     * @return 用户列表
     */
    List<User> getAllUsers();

    /**
     * 分页获取用户列表
     * 
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> getUsers(Pageable pageable);

    /**
     * 根据状态获取用户列表
     * 
     * @param status 用户状态
     * @return 用户列表
     */
    List<User> getUsersByStatus(UserStatus status);

    /**
     * 根据角色获取用户列表
     * 
     * @param role 用户角色
     * @return 用户列表
     */
    List<User> getUsersByRole(UserRole role);

    /**
     * 搜索用户
     * 
     * @param keyword 搜索关键词
     * @param status 用户状态（可选）
     * @param role 用户角色（可选）
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> searchUsers(String keyword, UserStatus status, UserRole role, Pageable pageable);

    // ============================================================================
    // 用户认证相关操作
    // ============================================================================

    /**
     * 验证用户凭证
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @param password 密码
     * @return 验证成功的用户对象
     * @throws RuntimeException 如果认证失败
     */
    User authenticate(String usernameOrEmail, String password);

    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @throws RuntimeException 如果旧密码不正确或用户不存在
     */
    void changePassword(UUID userId, String oldPassword, String newPassword);

    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @throws RuntimeException 如果用户不存在
     */
    void resetPassword(UUID userId, String newPassword);

    /**
     * 生成密码重置令牌
     * 
     * @param email 邮箱地址
     * @return 密码重置令牌
     * @throws RuntimeException 如果用户不存在
     */
    String generatePasswordResetToken(String email);

    /**
     * 验证密码重置令牌
     * 
     * @param token 密码重置令牌
     * @return 用户对象（如果令牌有效）
     * @throws RuntimeException 如果令牌无效或已过期
     */
    User validatePasswordResetToken(String token);

    // ============================================================================
    // 邮箱验证相关操作
    // ============================================================================

    /**
     * 生成邮箱验证令牌
     * 
     * @param userId 用户ID
     * @return 邮箱验证令牌
     * @throws RuntimeException 如果用户不存在
     */
    String generateEmailVerificationToken(UUID userId);

    /**
     * 验证邮箱
     * 
     * @param token 邮箱验证令牌
     * @return 验证成功的用户对象
     * @throws RuntimeException 如果令牌无效
     */
    User verifyEmail(String token);

    /**
     * 重新发送邮箱验证邮件
     * 
     * @param userId 用户ID
     * @throws RuntimeException 如果用户不存在或邮箱已验证
     */
    void resendEmailVerification(UUID userId);

    // ============================================================================
    // 用户状态管理
    // ============================================================================

    /**
     * 激活用户
     * 
     * @param userId 用户ID
     * @throws RuntimeException 如果用户不存在
     */
    void activateUser(UUID userId);

    /**
     * 暂停用户
     * 
     * @param userId 用户ID
     * @param reason 暂停原因
     * @throws RuntimeException 如果用户不存在
     */
    void suspendUser(UUID userId, String reason);

    /**
     * 锁定用户账户
     * 
     * @param userId 用户ID
     * @param durationMinutes 锁定时长（分钟）
     * @throws RuntimeException 如果用户不存在
     */
    void lockUser(UUID userId, int durationMinutes);

    /**
     * 解锁用户账户
     * 
     * @param userId 用户ID
     * @throws RuntimeException 如果用户不存在
     */
    void unlockUser(UUID userId);

    // ============================================================================
    // 登录管理
    // ============================================================================

    /**
     * 记录登录成功
     * 
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @throws RuntimeException 如果用户不存在
     */
    void recordLoginSuccess(UUID userId, String ipAddress);

    /**
     * 记录登录失败
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @param ipAddress IP地址
     * @return 是否需要锁定账户
     */
    boolean recordLoginFailure(String usernameOrEmail, String ipAddress);

    /**
     * 重置登录失败次数
     * 
     * @param userId 用户ID
     * @throws RuntimeException 如果用户不存在
     */
    void resetLoginFailures(UUID userId);

    // ============================================================================
    // 角色和权限管理
    // ============================================================================

    /**
     * 更新用户角色
     * 
     * @param userId 用户ID
     * @param newRole 新角色
     * @throws RuntimeException 如果用户不存在
     */
    void updateUserRole(UUID userId, UserRole newRole);

    /**
     * 检查用户是否拥有指定权限
     * 
     * @param userId 用户ID
     * @param permission 权限名称
     * @return true表示拥有权限
     */
    boolean hasPermission(UUID userId, String permission);

    /**
     * 检查用户是否可以管理目标用户
     * 
     * @param managerId 管理者ID
     * @param targetUserId 目标用户ID
     * @return true表示可以管理
     */
    boolean canManageUser(UUID managerId, UUID targetUserId);

    // ============================================================================
    // 统计和报告
    // ============================================================================

    /**
     * 统计用户总数
     * 
     * @return 用户总数
     */
    long getTotalUserCount();

    /**
     * 统计活跃用户数
     * 
     * @return 活跃用户数
     */
    long getActiveUserCount();

    /**
     * 统计指定角色的用户数
     * 
     * @param role 用户角色
     * @return 用户数量
     */
    long getUserCountByRole(UserRole role);

    /**
     * 统计邮箱已验证的用户数
     * 
     * @return 已验证用户数
     */
    long getVerifiedUserCount();

    // ============================================================================
    // 数据验证
    // ============================================================================

    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @return true表示可用
     */
    boolean isUsernameAvailable(String username);

    /**
     * 检查邮箱是否可用
     * 
     * @param email 邮箱地址
     * @return true表示可用
     */
    boolean isEmailAvailable(String email);

    /**
     * 检查用户名是否可用（排除指定用户）
     * 
     * @param username 用户名
     * @param excludeUserId 要排除的用户ID
     * @return true表示可用
     */
    boolean isUsernameAvailable(String username, UUID excludeUserId);

    /**
     * 检查邮箱是否可用（排除指定用户）
     * 
     * @param email 邮箱地址
     * @param excludeUserId 要排除的用户ID
     * @return true表示可用
     */
    boolean isEmailAvailable(String email, UUID excludeUserId);

    /**
     * 验证密码强度
     * 
     * @param password 密码
     * @return true表示密码强度符合要求
     */
    boolean isPasswordValid(String password);
}
