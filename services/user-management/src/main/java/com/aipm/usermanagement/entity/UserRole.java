package com.aipm.usermanagement.entity;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户角色枚举
 * 
 * 定义系统中的用户角色层次结构，用于权限控制和功能访问管理
 * 角色按权限级别从高到低排列：ADMIN > MANAGER > USER > VIEWER
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public enum UserRole {
    
    /**
     * 系统管理员 - 拥有系统的最高权限
     */
    ADMIN("系统管理员", "拥有系统的最高权限，可以管理所有用户、项目和系统配置", 4, Set.of(
        "user:create", "user:read", "user:update", "user:delete",
        "project:create", "project:read", "project:update", "project:delete",
        "organization:create", "organization:read", "organization:update", "organization:delete",
        "system:config", "system:monitor", "system:backup"
    )),
    
    /**
     * 项目经理 - 可以管理项目和团队成员
     */
    MANAGER("项目经理", "可以创建和管理项目，分配任务，管理团队成员", 3, Set.of(
        "user:read", "user:update",
        "project:create", "project:read", "project:update", "project:delete",
        "task:create", "task:read", "task:update", "task:delete",
        "team:manage", "report:generate"
    )),
    
    /**
     * 普通用户 - 可以参与项目和管理自己的任务
     */
    USER("普通用户", "可以参与项目，创建和管理自己的任务，查看相关报告", 2, Set.of(
        "user:read", "user:update:self",
        "project:read", "project:join",
        "task:create", "task:read", "task:update:own", "task:delete:own",
        "comment:create", "comment:read", "comment:update:own"
    )),
    
    /**
     * 查看者 - 只能查看被授权的内容
     */
    VIEWER("查看者", "只能查看被授权的项目和任务信息，无法进行修改操作", 1, Set.of(
        "user:read:self",
        "project:read:assigned",
        "task:read:assigned",
        "comment:read"
    ));

    /**
     * 角色显示名称
     */
    private final String displayName;
    
    /**
     * 角色描述
     */
    private final String description;
    
    /**
     * 权限级别（数字越大权限越高）
     */
    private final int level;
    
    /**
     * 角色拥有的权限集合
     */
    private final Set<String> permissions;

    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param description 角色描述
     * @param level 权限级别
     * @param permissions 权限集合
     */
    UserRole(String displayName, String description, int level, Set<String> permissions) {
        this.displayName = displayName;
        this.description = description;
        this.level = level;
        this.permissions = permissions;
    }

    /**
     * 获取角色显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取角色描述
     * 
     * @return 角色描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取权限级别
     * 
     * @return 权限级别
     */
    public int getLevel() {
        return level;
    }

    /**
     * 获取角色拥有的权限集合
     * 
     * @return 权限集合
     */
    public Set<String> getPermissions() {
        return permissions;
    }

    /**
     * 检查是否拥有指定权限
     * 
     * @param permission 权限名称
     * @return true表示拥有该权限
     */
    public boolean hasPermission(String permission) {
        return permissions.contains(permission);
    }

    /**
     * 检查是否为管理员角色
     * 
     * @return true表示是管理员
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }

    /**
     * 检查是否为经理级别或以上角色
     * 
     * @return true表示是经理级别或以上
     */
    public boolean isManagerOrAbove() {
        return this.level >= MANAGER.level;
    }

    /**
     * 检查是否可以管理指定角色的用户
     * 
     * @param targetRole 目标角色
     * @return true表示可以管理
     */
    public boolean canManage(UserRole targetRole) {
        return this.level > targetRole.level;
    }

    /**
     * 检查权限级别是否高于指定角色
     * 
     * @param other 其他角色
     * @return true表示权限级别更高
     */
    public boolean isHigherThan(UserRole other) {
        return this.level > other.level;
    }

    /**
     * 检查权限级别是否低于指定角色
     * 
     * @param other 其他角色
     * @return true表示权限级别更低
     */
    public boolean isLowerThan(UserRole other) {
        return this.level < other.level;
    }

    /**
     * 根据字符串获取角色枚举
     * 
     * @param role 角色字符串
     * @return 角色枚举，如果不匹配则返回USER
     */
    public static UserRole fromString(String role) {
        if (role == null || role.trim().isEmpty()) {
            return USER;
        }
        
        try {
            return UserRole.valueOf(role.toUpperCase());
        } catch (IllegalArgumentException e) {
            return USER;
        }
    }

    /**
     * 根据权限级别获取角色
     * 
     * @param level 权限级别
     * @return 对应的角色，如果级别无效则返回USER
     */
    public static UserRole fromLevel(int level) {
        return Arrays.stream(UserRole.values())
                .filter(role -> role.level == level)
                .findFirst()
                .orElse(USER);
    }

    /**
     * 获取所有可用角色的显示名称
     * 
     * @return 角色显示名称列表
     */
    public static List<String> getDisplayNames() {
        return Arrays.stream(UserRole.values())
                .map(UserRole::getDisplayName)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定级别及以下的所有角色
     * 
     * @param maxLevel 最大权限级别
     * @return 角色列表
     */
    public static List<UserRole> getRolesUpToLevel(int maxLevel) {
        return Arrays.stream(UserRole.values())
                .filter(role -> role.level <= maxLevel)
                .collect(Collectors.toList());
    }

    /**
     * 获取可以被指定角色管理的所有角色
     * 
     * @param managerRole 管理者角色
     * @return 可管理的角色列表
     */
    public static List<UserRole> getManageableRoles(UserRole managerRole) {
        return Arrays.stream(UserRole.values())
                .filter(role -> managerRole.canManage(role))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有权限的并集
     * 
     * @return 所有权限集合
     */
    public static Set<String> getAllPermissions() {
        return Arrays.stream(UserRole.values())
                .flatMap(role -> role.permissions.stream())
                .collect(Collectors.toSet());
    }
}
