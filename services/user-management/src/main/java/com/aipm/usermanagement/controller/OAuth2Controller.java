package com.aipm.usermanagement.controller;

import com.aipm.usermanagement.dto.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.StreamSupport;

/**
 * OAuth2认证控制器
 * 
 * 提供OAuth2相关的API端点
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
@RestController
@RequestMapping("/api/v1/oauth2")
@Tag(name = "OAuth2认证", description = "OAuth2认证相关API")
public class OAuth2Controller {

    private static final Logger logger = LoggerFactory.getLogger(OAuth2Controller.class);

    private final ClientRegistrationRepository clientRegistrationRepository;

    @Value("${app.oauth2.authorized-redirect-uris}")
    private String[] authorizedRedirectUris;

    public OAuth2Controller(ClientRegistrationRepository clientRegistrationRepository) {
        this.clientRegistrationRepository = clientRegistrationRepository;
    }

    /**
     * 获取可用的OAuth2提供商列表
     * 
     * @return OAuth2提供商列表
     */
    @GetMapping("/providers")
    @Operation(summary = "获取OAuth2提供商列表", description = "获取系统支持的所有OAuth2认证提供商")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getOAuth2Providers() {
        try {
            Map<String, Object> providers = new HashMap<>();
            
            // 获取所有注册的OAuth2客户端
            Iterable<ClientRegistration> clientRegistrations = 
                    (Iterable<ClientRegistration>) clientRegistrationRepository;
            
            StreamSupport.stream(clientRegistrations.spliterator(), false)
                    .forEach(registration -> {
                        Map<String, String> providerInfo = new HashMap<>();
                        providerInfo.put("clientName", registration.getClientName());
                        providerInfo.put("authorizationUri", "/oauth2/authorization/" + registration.getRegistrationId());
                        providers.put(registration.getRegistrationId(), providerInfo);
                    });

            Map<String, Object> response = new HashMap<>();
            response.put("providers", providers);
            response.put("redirectUris", authorizedRedirectUris);

            return ResponseEntity.ok(ApiResponse.success(response, "获取OAuth2提供商列表成功"));
            
        } catch (Exception e) {
            logger.error("获取OAuth2提供商列表失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取OAuth2提供商列表失败: " + e.getMessage()));
        }
    }

    /**
     * OAuth2认证状态检查
     * 
     * @param request HTTP请求
     * @return 认证状态
     */
    @GetMapping("/status")
    @Operation(summary = "检查OAuth2认证状态", description = "检查当前用户的OAuth2认证状态")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkOAuth2Status(HttpServletRequest request) {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 检查是否有活跃的OAuth2认证
            boolean isOAuth2Authenticated = request.getUserPrincipal() != null;
            status.put("authenticated", isOAuth2Authenticated);
            
            if (isOAuth2Authenticated) {
                status.put("principal", request.getUserPrincipal().getName());
            }

            return ResponseEntity.ok(ApiResponse.success(status, "获取OAuth2认证状态成功"));
            
        } catch (Exception e) {
            logger.error("检查OAuth2认证状态失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("检查OAuth2认证状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取OAuth2授权URL
     * 
     * @param provider OAuth2提供商ID
     * @param redirectUri 重定向URI
     * @return 授权URL
     */
    @GetMapping("/authorization-url/{provider}")
    @Operation(summary = "获取OAuth2授权URL", description = "获取指定提供商的OAuth2授权URL")
    public ResponseEntity<ApiResponse<Map<String, String>>> getAuthorizationUrl(
            @Parameter(description = "OAuth2提供商ID", example = "google")
            @PathVariable String provider,
            @Parameter(description = "重定向URI", example = "http://localhost:3000/oauth2/redirect")
            @RequestParam(required = false) String redirectUri) {
        
        try {
            // 构建授权URL
            String authorizationUrl = "/oauth2/authorization/" + provider;
            
            if (redirectUri != null && !redirectUri.isEmpty()) {
                authorizationUrl += "?redirect_uri=" + redirectUri;
            }

            Map<String, String> response = new HashMap<>();
            response.put("authorizationUrl", authorizationUrl);
            response.put("provider", provider);

            return ResponseEntity.ok(ApiResponse.success(response, "获取授权URL成功"));
            
        } catch (Exception e) {
            logger.error("获取OAuth2授权URL失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取OAuth2授权URL失败: " + e.getMessage()));
        }
    }
}
