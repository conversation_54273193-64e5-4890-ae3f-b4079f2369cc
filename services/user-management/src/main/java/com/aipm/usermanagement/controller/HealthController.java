package com.aipm.usermanagement.controller;

import com.aipm.usermanagement.dto.ApiResponse;
import com.aipm.usermanagement.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * 提供服务健康状态检查和基本信息查询接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@RestController
@RequestMapping("/health")
@Tag(name = "健康检查", description = "服务健康状态检查相关API")
public class HealthController {

    @Autowired
    private UserService userService;

    @Value("${spring.application.name:user-management-service}")
    private String applicationName;

    @Value("${spring.profiles.active:unknown}")
    private String activeProfile;

    /**
     * 基础健康检查
     */
    @GetMapping
    @Operation(summary = "基础健康检查", description = "检查服务是否正常运行")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "服务正常",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("service", applicationName);
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("environment", activeProfile);
        
        return ResponseEntity.ok(ApiResponse.success("服务运行正常", healthInfo));
    }

    /**
     * 详细健康检查
     */
    @GetMapping("/detailed")
    @Operation(summary = "详细健康检查", description = "检查服务及其依赖组件的健康状态")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "健康检查完成",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> detailedHealth() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("service", applicationName);
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("environment", activeProfile);
        
        // 检查数据库连接
        Map<String, Object> databaseStatus = new HashMap<>();
        try {
            long userCount = userService.getTotalUserCount();
            databaseStatus.put("status", "UP");
            databaseStatus.put("totalUsers", userCount);
        } catch (Exception e) {
            databaseStatus.put("status", "DOWN");
            databaseStatus.put("error", e.getMessage());
        }
        healthInfo.put("database", databaseStatus);
        
        // 检查内存使用情况
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> memoryInfo = new HashMap<>();
        memoryInfo.put("totalMemory", runtime.totalMemory());
        memoryInfo.put("freeMemory", runtime.freeMemory());
        memoryInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        memoryInfo.put("maxMemory", runtime.maxMemory());
        healthInfo.put("memory", memoryInfo);
        
        // 系统信息
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("osName", System.getProperty("os.name"));
        systemInfo.put("osVersion", System.getProperty("os.version"));
        systemInfo.put("processors", Runtime.getRuntime().availableProcessors());
        healthInfo.put("system", systemInfo);
        
        // 确定整体状态
        boolean isHealthy = "UP".equals(databaseStatus.get("status"));
        healthInfo.put("status", isHealthy ? "UP" : "DOWN");
        
        return ResponseEntity.ok(ApiResponse.success("详细健康检查完成", healthInfo));
    }

    /**
     * 服务信息
     */
    @GetMapping("/info")
    @Operation(summary = "服务信息", description = "获取服务的基本信息和版本")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "获取成功",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> info() {
        Map<String, Object> serviceInfo = new HashMap<>();
        serviceInfo.put("name", applicationName);
        serviceInfo.put("version", "1.0.0");
        serviceInfo.put("description", "AI项目管理平台用户管理微服务");
        serviceInfo.put("environment", activeProfile);
        serviceInfo.put("buildTime", "2025-08-15T10:30:00");
        serviceInfo.put("features", new String[]{
            "用户认证和授权",
            "JWT令牌管理", 
            "用户信息管理",
            "角色权限控制",
            "邮箱验证",
            "密码重置"
        });
        
        return ResponseEntity.ok(ApiResponse.success("获取服务信息成功", serviceInfo));
    }

    /**
     * 统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "统计信息", description = "获取用户相关的统计数据")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "获取成功",
            content = @Content(schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> stats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalUsers", userService.getTotalUserCount());
            stats.put("activeUsers", userService.getActiveUserCount());
            stats.put("verifiedUsers", userService.getVerifiedUserCount());
            stats.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(ApiResponse.success("获取统计信息成功", stats));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
        }
    }
}
