package com.aipm.usermanagement.service;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.security.JwtTokenProvider;

/**
 * 认证服务接口
 * 
 * 提供用户认证、令牌管理等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface AuthenticationService {

    /**
     * 用户登录
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @param password 密码
     * @param ipAddress 客户端IP地址
     * @return 认证结果
     */
    AuthenticationResult login(String usernameOrEmail, String password, String ipAddress);

    /**
     * 刷新访问令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的认证结果
     */
    AuthenticationResult refreshToken(String refreshToken);

    /**
     * 用户登出
     * 
     * @param accessToken 访问令牌
     */
    void logout(String accessToken);

    /**
     * 验证访问令牌
     * 
     * @param accessToken 访问令牌
     * @return 用户对象（如果令牌有效）
     */
    User validateAccessToken(String accessToken);

    /**
     * 认证结果类
     */
    class AuthenticationResult {
        private final boolean success;
        private final String message;
        private final User user;
        private final JwtTokenProvider.TokenInfo tokenInfo;

        public AuthenticationResult(boolean success, String message, User user, JwtTokenProvider.TokenInfo tokenInfo) {
            this.success = success;
            this.message = message;
            this.user = user;
            this.tokenInfo = tokenInfo;
        }

        // 静态工厂方法
        public static AuthenticationResult success(User user, JwtTokenProvider.TokenInfo tokenInfo) {
            return new AuthenticationResult(true, "认证成功", user, tokenInfo);
        }

        public static AuthenticationResult failure(String message) {
            return new AuthenticationResult(false, message, null, null);
        }

        // Getter方法
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public User getUser() { return user; }
        public JwtTokenProvider.TokenInfo getTokenInfo() { return tokenInfo; }
    }
}
