package com.aipm.usermanagement.security.oauth2;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.entity.UserRole;
import com.aipm.usermanagement.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 自定义OAuth2用户服务
 * 
 * 处理OAuth2用户信息，实现用户自动注册和信息同步
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
@Service
public class CustomOAuth2UserService extends DefaultOAuth2UserService {

    private static final Logger logger = LoggerFactory.getLogger(CustomOAuth2UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        OAuth2User oauth2User = super.loadUser(userRequest);
        
        try {
            return processOAuth2User(userRequest, oauth2User);
        } catch (Exception ex) {
            logger.error("OAuth2用户处理失败: {}", ex.getMessage(), ex);
            throw new OAuth2AuthenticationException("OAuth2用户处理失败: " + ex.getMessage());
        }
    }

    /**
     * 处理OAuth2用户信息
     * 
     * @param userRequest OAuth2用户请求
     * @param oauth2User OAuth2用户信息
     * @return 处理后的用户信息
     */
    private OAuth2User processOAuth2User(OAuth2UserRequest userRequest, OAuth2User oauth2User) {
        String registrationId = userRequest.getClientRegistration().getRegistrationId();
        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo(registrationId, oauth2User.getAttributes());
        
        if (userInfo.getEmail() == null || userInfo.getEmail().isEmpty()) {
            throw new OAuth2AuthenticationException("OAuth2提供商未返回邮箱信息");
        }

        Optional<User> userOptional = userRepository.findByEmail(userInfo.getEmail());
        User user;
        
        if (userOptional.isPresent()) {
            user = userOptional.get();
            // 更新现有用户信息
            user = updateExistingUser(user, userInfo, registrationId);
        } else {
            // 创建新用户
            user = createNewUser(userInfo, registrationId);
        }

        return new CustomOAuth2UserPrincipal(user, oauth2User.getAttributes());
    }

    /**
     * 创建新用户
     * 
     * @param userInfo OAuth2用户信息
     * @param provider OAuth2提供商
     * @return 新创建的用户
     */
    private User createNewUser(OAuth2UserInfo userInfo, String provider) {
        User user = new User();
        user.setId(UUID.randomUUID());
        user.setUsername(generateUniqueUsername(userInfo.getName()));
        user.setEmail(userInfo.getEmail());
        user.setFullName(userInfo.getName());
        user.setAvatarUrl(userInfo.getImageUrl());
        user.setRole(UserRole.USER); // 默认角色
        user.setEnabled(true);
        user.setAccountNonExpired(true);
        user.setAccountNonLocked(true);
        user.setCredentialsNonExpired(true);
        user.setEmailVerified(true); // OAuth2用户默认邮箱已验证
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        
        // 设置OAuth2提供商信息
        user.setOauth2Provider(provider);
        user.setOauth2ProviderId(userInfo.getId());
        
        user = userRepository.save(user);
        logger.info("通过OAuth2创建新用户: {} ({})", user.getUsername(), user.getEmail());
        
        return user;
    }

    /**
     * 更新现有用户信息
     * 
     * @param existingUser 现有用户
     * @param userInfo OAuth2用户信息
     * @param provider OAuth2提供商
     * @return 更新后的用户
     */
    private User updateExistingUser(User existingUser, OAuth2UserInfo userInfo, String provider) {
        // 更新用户信息
        if (userInfo.getName() != null && !userInfo.getName().equals(existingUser.getFullName())) {
            existingUser.setFullName(userInfo.getName());
        }
        
        if (userInfo.getImageUrl() != null && !userInfo.getImageUrl().equals(existingUser.getAvatarUrl())) {
            existingUser.setAvatarUrl(userInfo.getImageUrl());
        }
        
        // 更新OAuth2提供商信息
        existingUser.setOauth2Provider(provider);
        existingUser.setOauth2ProviderId(userInfo.getId());
        existingUser.setUpdatedAt(LocalDateTime.now());
        existingUser.setLastLoginAt(LocalDateTime.now());
        
        existingUser = userRepository.save(existingUser);
        logger.info("更新OAuth2用户信息: {} ({})", existingUser.getUsername(), existingUser.getEmail());
        
        return existingUser;
    }

    /**
     * 生成唯一用户名
     * 
     * @param name 用户姓名
     * @return 唯一用户名
     */
    private String generateUniqueUsername(String name) {
        String baseUsername = name.toLowerCase().replaceAll("[^a-z0-9]", "");
        if (baseUsername.length() < 3) {
            baseUsername = "user" + baseUsername;
        }
        
        String username = baseUsername;
        int counter = 1;
        
        while (userRepository.findByUsername(username).isPresent()) {
            username = baseUsername + counter;
            counter++;
        }
        
        return username;
    }
}
