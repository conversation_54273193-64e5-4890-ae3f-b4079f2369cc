/**
 * API访问频率限制过滤器
 * 
 * 实现基于Redis的分布式限流机制，防止API滥用和DDoS攻击
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-28
 */

package com.aipm.usermanagement.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * API访问频率限制过滤器
 * 
 * 使用滑动窗口算法实现精确的频率限制
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateLimitingFilter extends OncePerRequestFilter {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    // Redis Lua脚本 - 滑动窗口限流算法
    private static final String RATE_LIMIT_SCRIPT = """
        local key = KEYS[1]
        local window = tonumber(ARGV[1])
        local limit = tonumber(ARGV[2])
        local current_time = tonumber(ARGV[3])
        
        -- 清理过期的请求记录
        redis.call('ZREMRANGEBYSCORE', key, 0, current_time - window * 1000)
        
        -- 获取当前窗口内的请求数量
        local current_requests = redis.call('ZCARD', key)
        
        if current_requests < limit then
            -- 添加当前请求记录
            redis.call('ZADD', key, current_time, current_time)
            redis.call('EXPIRE', key, window)
            return {1, limit - current_requests - 1}
        else
            return {0, 0}
        end
        """;

    private final DefaultRedisScript<Object> rateLimitScript = new DefaultRedisScript<>(RATE_LIMIT_SCRIPT, Object.class);

    // 限流配置
    private static final Map<String, RateLimitConfig> RATE_LIMIT_CONFIGS = Map.of(
        "/api/v1/auth/login", new RateLimitConfig(5, 300), // 登录：5次/5分钟
        "/api/v1/auth/register", new RateLimitConfig(3, 3600), // 注册：3次/小时
        "/api/v1/auth/forgot-password", new RateLimitConfig(3, 3600), // 忘记密码：3次/小时
        "/api/v1/auth/refresh-token", new RateLimitConfig(10, 300), // 刷新令牌：10次/5分钟
        "DEFAULT", new RateLimitConfig(100, 60) // 默认：100次/分钟
    );

    /**
     * 限流配置类
     */
    private static class RateLimitConfig {
        final int limit; // 限制次数
        final int windowSeconds; // 时间窗口（秒）

        RateLimitConfig(int limit, int windowSeconds) {
            this.limit = limit;
            this.windowSeconds = windowSeconds;
        }
    }

    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        // 获取客户端标识
        String clientId = getClientIdentifier(request);
        String requestPath = request.getRequestURI();

        // 获取限流配置
        RateLimitConfig config = getRateLimitConfig(requestPath);

        // 执行限流检查
        RateLimitResult result = checkRateLimit(clientId, requestPath, config);

        if (!result.allowed) {
            // 请求被限流，返回429状态码
            handleRateLimitExceeded(response, result);
            return;
        }

        // 添加限流信息到响应头
        addRateLimitHeaders(response, result);

        // 继续处理请求
        filterChain.doFilter(request, response);
    }

    /**
     * 获取客户端标识符
     */
    private String getClientIdentifier(HttpServletRequest request) {
        // 优先使用用户ID（如果已认证）
        String userId = getUserIdFromRequest(request);
        if (userId != null) {
            return "user:" + userId;
        }

        // 使用IP地址
        String clientIp = getClientIpAddress(request);
        return "ip:" + clientIp;
    }

    /**
     * 从请求中获取用户ID
     */
    private String getUserIdFromRequest(HttpServletRequest request) {
        // 从JWT令牌中提取用户ID
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            try {
                // 这里应该调用JWT解析服务
                // 为了简化，暂时返回null
                return null;
            } catch (Exception e) {
                log.debug("无法从令牌中提取用户ID: {}", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 获取限流配置
     */
    private RateLimitConfig getRateLimitConfig(String requestPath) {
        return RATE_LIMIT_CONFIGS.getOrDefault(requestPath, RATE_LIMIT_CONFIGS.get("DEFAULT"));
    }

    /**
     * 执行限流检查
     */
    private RateLimitResult checkRateLimit(String clientId, String requestPath, RateLimitConfig config) {
        String key = "rate_limit:" + clientId + ":" + requestPath;
        long currentTime = System.currentTimeMillis();

        try {
            @SuppressWarnings("unchecked")
            java.util.List<Long> result = (java.util.List<Long>) redisTemplate.execute(
                rateLimitScript,
                Arrays.asList(key),
                config.windowSeconds,
                config.limit,
                currentTime
            );

            boolean allowed = result.get(0) == 1;
            long remaining = result.get(1);

            return new RateLimitResult(allowed, config.limit, remaining, config.windowSeconds);

        } catch (Exception e) {
            log.error("限流检查失败: {}", e.getMessage());
            // 发生错误时允许请求通过，避免影响正常业务
            return new RateLimitResult(true, config.limit, config.limit - 1, config.windowSeconds);
        }
    }

    /**
     * 处理限流超出的情况
     */
    private void handleRateLimitExceeded(HttpServletResponse response, RateLimitResult result) throws IOException {
        response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        // 添加限流信息到响应头
        addRateLimitHeaders(response, result);

        // 构建错误响应
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("code", "RATE_LIMIT_EXCEEDED");
        errorResponse.put("message", "请求过于频繁，请稍后再试");
        errorResponse.put("details", Map.of(
            "limit", result.limit,
            "remaining", result.remaining,
            "resetTime", System.currentTimeMillis() + (result.windowSeconds * 1000)
        ));

        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);

        log.warn("客户端请求被限流: limit={}, remaining={}, window={}s", 
                result.limit, result.remaining, result.windowSeconds);
    }

    /**
     * 添加限流信息到响应头
     */
    private void addRateLimitHeaders(HttpServletResponse response, RateLimitResult result) {
        response.setHeader("X-RateLimit-Limit", String.valueOf(result.limit));
        response.setHeader("X-RateLimit-Remaining", String.valueOf(result.remaining));
        response.setHeader("X-RateLimit-Reset", String.valueOf(System.currentTimeMillis() + (result.windowSeconds * 1000)));
    }

    /**
     * 限流结果类
     */
    private static class RateLimitResult {
        final boolean allowed;
        final int limit;
        final long remaining;
        final int windowSeconds;

        RateLimitResult(boolean allowed, int limit, long remaining, int windowSeconds) {
            this.allowed = allowed;
            this.limit = limit;
            this.remaining = remaining;
            this.windowSeconds = windowSeconds;
        }
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();
        
        // 跳过健康检查和文档端点
        return path.startsWith("/actuator/") ||
               path.startsWith("/api-docs/") ||
               path.startsWith("/swagger-ui/") ||
               path.equals("/swagger-ui.html") ||
               path.startsWith("/v3/api-docs/");
    }
}
