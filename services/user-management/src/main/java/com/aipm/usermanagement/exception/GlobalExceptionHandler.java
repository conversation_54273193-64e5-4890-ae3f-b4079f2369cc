package com.aipm.usermanagement.exception;

import com.aipm.usermanagement.dto.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * 全局异常处理器
 * 
 * 统一处理应用中的各种异常，提供标准化的错误响应格式
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationException(
            MethodArgumentNotValidException ex, HttpServletRequest request) {
        
        logger.warn("参数验证失败: {}", ex.getMessage());

        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        ApiResponse<Object> response = ApiResponse.error(
            "请求参数验证失败", 
            "VALIDATION_FAILED", 
            errors
        ).withRequestId(generateRequestId());

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Object>> handleBindException(
            BindException ex, HttpServletRequest request) {
        
        logger.warn("数据绑定失败: {}", ex.getMessage());

        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        ApiResponse<Object> response = ApiResponse.error(
            "数据绑定失败", 
            "BIND_FAILED", 
            errors
        ).withRequestId(generateRequestId());

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleConstraintViolationException(
            ConstraintViolationException ex, HttpServletRequest request) {
        
        logger.warn("约束验证失败: {}", ex.getMessage());

        Map<String, String> errors = new HashMap<>();
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            String fieldName = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();
            errors.put(fieldName, errorMessage);
        }

        ApiResponse<Object> response = ApiResponse.error(
            "约束验证失败", 
            "CONSTRAINT_VIOLATION", 
            errors
        ).withRequestId(generateRequestId());

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpMessageNotReadableException(
            HttpMessageNotReadableException ex, HttpServletRequest request) {
        
        logger.warn("HTTP消息不可读: {}", ex.getMessage());

        ApiResponse<Object> response = ApiResponse.error(
            "请求体格式错误或不可读", 
            "MESSAGE_NOT_READABLE"
        ).withRequestId(generateRequestId());

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Object>> handleMissingServletRequestParameterException(
            MissingServletRequestParameterException ex, HttpServletRequest request) {
        
        logger.warn("缺少请求参数: {}", ex.getMessage());

        Map<String, String> details = new HashMap<>();
        details.put("parameterName", ex.getParameterName());
        details.put("parameterType", ex.getParameterType());

        ApiResponse<Object> response = ApiResponse.error(
            String.format("缺少必需的请求参数: %s", ex.getParameterName()), 
            "MISSING_PARAMETER", 
            details
        ).withRequestId(generateRequestId());

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理方法参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodArgumentTypeMismatchException(
            MethodArgumentTypeMismatchException ex, HttpServletRequest request) {
        
        logger.warn("方法参数类型不匹配: {}", ex.getMessage());

        Map<String, String> details = new HashMap<>();
        details.put("parameterName", ex.getName());
        details.put("expectedType", ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "unknown");
        details.put("providedValue", ex.getValue() != null ? ex.getValue().toString() : "null");

        ApiResponse<Object> response = ApiResponse.error(
            String.format("参数 '%s' 类型不匹配", ex.getName()), 
            "TYPE_MISMATCH", 
            details
        ).withRequestId(generateRequestId());

        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理HTTP请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleHttpRequestMethodNotSupportedException(
            HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        
        logger.warn("HTTP方法不支持: {}", ex.getMessage());

        Map<String, Object> details = new HashMap<>();
        details.put("method", ex.getMethod());
        details.put("supportedMethods", ex.getSupportedMethods());

        ApiResponse<Object> response = ApiResponse.error(
            String.format("HTTP方法 '%s' 不支持", ex.getMethod()), 
            "METHOD_NOT_SUPPORTED", 
            details
        ).withRequestId(generateRequestId());

        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response);
    }

    /**
     * 处理找不到处理器异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleNoHandlerFoundException(
            NoHandlerFoundException ex, HttpServletRequest request) {
        
        logger.warn("找不到请求处理器: {}", ex.getMessage());

        Map<String, String> details = new HashMap<>();
        details.put("httpMethod", ex.getHttpMethod());
        details.put("requestURL", ex.getRequestURL());

        ApiResponse<Object> response = ApiResponse.error(
            "请求的资源不存在", 
            "NOT_FOUND", 
            details
        ).withRequestId(generateRequestId());

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ApiResponse<Object>> handleAuthenticationException(
            AuthenticationException ex, HttpServletRequest request) {
        
        logger.warn("认证失败: {}", ex.getMessage());

        String errorCode = "AUTHENTICATION_FAILED";
        String message = "认证失败";

        if (ex instanceof BadCredentialsException) {
            errorCode = "BAD_CREDENTIALS";
            message = "用户名或密码错误";
        }

        ApiResponse<Object> response = ApiResponse.error(message, errorCode)
            .withRequestId(generateRequestId());

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }

    /**
     * 处理访问拒绝异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleAccessDeniedException(
            AccessDeniedException ex, HttpServletRequest request) {
        
        logger.warn("访问被拒绝: {}", ex.getMessage());

        ApiResponse<Object> response = ApiResponse.error(
            "访问被拒绝，权限不足", 
            "ACCESS_DENIED"
        ).withRequestId(generateRequestId());

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(
            RuntimeException ex, HttpServletRequest request) {
        
        logger.error("业务异常: {}", ex.getMessage(), ex);

        // 根据异常消息判断错误类型
        String errorCode = "BUSINESS_ERROR";
        HttpStatus status = HttpStatus.BAD_REQUEST;

        String message = ex.getMessage();
        if (message != null) {
            if (message.contains("不存在")) {
                errorCode = "NOT_FOUND";
                status = HttpStatus.NOT_FOUND;
            } else if (message.contains("已存在")) {
                errorCode = "ALREADY_EXISTS";
                status = HttpStatus.CONFLICT;
            } else if (message.contains("权限") || message.contains("无权")) {
                errorCode = "ACCESS_DENIED";
                status = HttpStatus.FORBIDDEN;
            } else if (message.contains("锁定")) {
                errorCode = "ACCOUNT_LOCKED";
                status = HttpStatus.LOCKED;
            }
        }

        ApiResponse<Object> response = ApiResponse.error(
            message != null ? message : "业务处理失败", 
            errorCode
        ).withRequestId(generateRequestId());

        return ResponseEntity.status(status).body(response);
    }

    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(
            Exception ex, HttpServletRequest request) {
        
        logger.error("系统异常: {}", ex.getMessage(), ex);

        ApiResponse<Object> response = ApiResponse.error(
            "系统内部错误，请稍后重试", 
            "INTERNAL_ERROR"
        ).withRequestId(generateRequestId());

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "req-" + UUID.randomUUID().toString().substring(0, 8);
    }
}
