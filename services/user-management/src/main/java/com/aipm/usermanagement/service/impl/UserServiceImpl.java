package com.aipm.usermanagement.service.impl;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.entity.UserRole;
import com.aipm.usermanagement.entity.UserStatus;
import com.aipm.usermanagement.repository.UserRepository;
import com.aipm.usermanagement.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * 用户服务实现类
 * 
 * 实现用户管理的核心业务逻辑，包括用户的CRUD操作、认证、权限管理等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // 配置参数
    @Value("${app.user.max-login-attempts:5}")
    private int maxLoginAttempts;

    @Value("${security.password.min-length:8}")
    private int minPasswordLength;

    @Value("${security.password.require-uppercase:true}")
    private boolean requireUppercase;

    @Value("${security.password.require-lowercase:true}")
    private boolean requireLowercase;

    @Value("${security.password.require-digit:true}")
    private boolean requireDigit;

    @Value("${security.password.require-special-char:false}")
    private boolean requireSpecialChar;

    // 密码强度验证正则表达式
    private static final Pattern UPPERCASE_PATTERN = Pattern.compile(".*[A-Z].*");
    private static final Pattern LOWERCASE_PATTERN = Pattern.compile(".*[a-z].*");
    private static final Pattern DIGIT_PATTERN = Pattern.compile(".*\\d.*");
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*");

    // ============================================================================
    // 用户基础操作实现
    // ============================================================================

    @Override
    public User createUser(User user) {
        logger.info("开始创建用户: {}", user.getUsername());

        // 验证用户数据
        validateUserForCreation(user);

        // 检查用户名和邮箱是否已存在
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在: " + user.getUsername());
        }

        if (userRepository.existsByEmail(user.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + user.getEmail());
        }

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // 设置默认值
        if (user.getStatus() == null) {
            user.setStatus(UserStatus.ACTIVE);
        }
        if (user.getRole() == null) {
            user.setRole(UserRole.USER);
        }
        if (user.getEmailVerified() == null) {
            user.setEmailVerified(false);
        }

        // 生成邮箱验证令牌
        user.setEmailVerificationToken(generateToken());

        // 保存用户
        User savedUser = userRepository.save(user);
        logger.info("用户创建成功: {} (ID: {})", savedUser.getUsername(), savedUser.getId());

        return savedUser;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> getUserById(UUID userId) {
        return userRepository.findById(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> getUserByUsernameOrEmail(String usernameOrEmail) {
        return userRepository.findByUsernameOrEmail(usernameOrEmail, usernameOrEmail);
    }

    @Override
    public User updateUser(User user) {
        logger.info("开始更新用户: {} (ID: {})", user.getUsername(), user.getId());

        // 检查用户是否存在
        User existingUser = userRepository.findById(user.getId())
                .orElseThrow(() -> new RuntimeException("用户不存在: " + user.getId()));

        // 验证用户数据
        validateUserForUpdate(user, existingUser);

        // 检查用户名和邮箱是否被其他用户使用
        if (!existingUser.getUsername().equals(user.getUsername()) &&
            userRepository.existsByUsernameAndIdNot(user.getUsername(), user.getId())) {
            throw new RuntimeException("用户名已被其他用户使用: " + user.getUsername());
        }

        if (!existingUser.getEmail().equals(user.getEmail()) &&
            userRepository.existsByEmailAndIdNot(user.getEmail(), user.getId())) {
            throw new RuntimeException("邮箱已被其他用户使用: " + user.getEmail());
        }

        // 如果邮箱发生变化，需要重新验证
        if (!existingUser.getEmail().equals(user.getEmail())) {
            user.setEmailVerified(false);
            user.setEmailVerificationToken(generateToken());
            logger.info("用户 {} 的邮箱发生变化，需要重新验证", user.getUsername());
        }

        // 保存更新
        User updatedUser = userRepository.save(user);
        logger.info("用户更新成功: {} (ID: {})", updatedUser.getUsername(), updatedUser.getId());

        return updatedUser;
    }

    @Override
    public void deleteUser(UUID userId) {
        logger.info("开始删除用户: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        userRepository.delete(user);
        logger.info("用户删除成功: {} (ID: {})", user.getUsername(), userId);
    }

    @Override
    public void deactivateUser(UUID userId) {
        logger.info("开始停用用户: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        user.setStatus(UserStatus.INACTIVE);
        userRepository.save(user);
        logger.info("用户停用成功: {} (ID: {})", user.getUsername(), userId);
    }

    // ============================================================================
    // 用户查询操作实现
    // ============================================================================

    @Override
    @Transactional(readOnly = true)
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> getUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersByStatus(UserStatus status) {
        return userRepository.findByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersByRole(UserRole role) {
        return userRepository.findByRole(role);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> searchUsers(String keyword, UserStatus status, UserRole role, Pageable pageable) {
        return userRepository.searchUsers(keyword, status, role, pageable);
    }

    // ============================================================================
    // 用户认证相关操作实现
    // ============================================================================

    @Override
    @Transactional(readOnly = true)
    public User authenticate(String usernameOrEmail, String password) {
        logger.debug("开始验证用户凭证: {}", usernameOrEmail);

        // 查找用户
        User user = getUserByUsernameOrEmail(usernameOrEmail)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + usernameOrEmail));

        // 检查账户状态
        if (!user.isEnabled()) {
            throw new RuntimeException("账户已被禁用");
        }

        if (!user.isAccountNonLocked()) {
            throw new RuntimeException("账户已被锁定");
        }

        // 验证密码
        if (!passwordEncoder.matches(password, user.getPassword())) {
            logger.warn("用户 {} 密码验证失败", usernameOrEmail);
            throw new RuntimeException("密码错误");
        }

        logger.info("用户 {} 认证成功", user.getUsername());
        return user;
    }

    @Override
    public void changePassword(UUID userId, String oldPassword, String newPassword) {
        logger.info("开始更改用户密码: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("旧密码不正确");
        }

        // 验证新密码强度
        if (!isPasswordValid(newPassword)) {
            throw new RuntimeException("新密码不符合安全要求");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        logger.info("用户 {} 密码更改成功", user.getUsername());
    }

    @Override
    public void resetPassword(UUID userId, String newPassword) {
        logger.info("开始重置用户密码: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        // 验证新密码强度
        if (!isPasswordValid(newPassword)) {
            throw new RuntimeException("新密码不符合安全要求");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setPasswordResetToken(null);
        user.setPasswordResetExpiresAt(null);
        userRepository.save(user);

        logger.info("用户 {} 密码重置成功", user.getUsername());
    }

    @Override
    public String generatePasswordResetToken(String email) {
        logger.info("开始生成密码重置令牌: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("邮箱不存在: " + email));

        String token = generateToken();
        user.setPasswordResetToken(token);
        user.setPasswordResetExpiresAt(LocalDateTime.now().plusHours(1)); // 1小时后过期
        userRepository.save(user);

        logger.info("为用户 {} 生成密码重置令牌成功", user.getUsername());
        return token;
    }

    @Override
    @Transactional(readOnly = true)
    public User validatePasswordResetToken(String token) {
        User user = userRepository.findByPasswordResetToken(token)
                .orElseThrow(() -> new RuntimeException("无效的密码重置令牌"));

        if (user.getPasswordResetExpiresAt() == null ||
            user.getPasswordResetExpiresAt().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("密码重置令牌已过期");
        }

        return user;
    }

    // ============================================================================
    // 邮箱验证相关操作实现
    // ============================================================================

    @Override
    public String generateEmailVerificationToken(UUID userId) {
        logger.info("开始生成邮箱验证令牌: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        if (user.getEmailVerified()) {
            throw new RuntimeException("邮箱已经验证过了");
        }

        String token = generateToken();
        user.setEmailVerificationToken(token);
        userRepository.save(user);

        logger.info("为用户 {} 生成邮箱验证令牌成功", user.getUsername());
        return token;
    }

    @Override
    public User verifyEmail(String token) {
        logger.info("开始验证邮箱令牌");

        User user = userRepository.findByEmailVerificationToken(token)
                .orElseThrow(() -> new RuntimeException("无效的邮箱验证令牌"));

        user.setEmailVerified(true);
        user.setEmailVerificationToken(null);
        userRepository.save(user);

        logger.info("用户 {} 邮箱验证成功", user.getUsername());
        return user;
    }

    @Override
    public void resendEmailVerification(UUID userId) {
        logger.info("开始重新发送邮箱验证: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        if (user.getEmailVerified()) {
            throw new RuntimeException("邮箱已经验证过了");
        }

        // 生成新的验证令牌
        String token = generateToken();
        user.setEmailVerificationToken(token);
        userRepository.save(user);

        logger.info("为用户 {} 重新生成邮箱验证令牌成功", user.getUsername());
    }

    // ============================================================================
    // 用户状态管理实现
    // ============================================================================

    @Override
    public void activateUser(UUID userId) {
        logger.info("开始激活用户: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        user.setStatus(UserStatus.ACTIVE);
        userRepository.save(user);

        logger.info("用户 {} 激活成功", user.getUsername());
    }

    @Override
    public void suspendUser(UUID userId, String reason) {
        logger.info("开始暂停用户: {}, 原因: {}", userId, reason);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        user.setStatus(UserStatus.SUSPENDED);
        userRepository.save(user);

        logger.info("用户 {} 暂停成功", user.getUsername());
    }

    @Override
    public void lockUser(UUID userId, int durationMinutes) {
        logger.info("开始锁定用户: {}, 时长: {} 分钟", userId, durationMinutes);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        user.lockAccount(durationMinutes);
        userRepository.save(user);

        logger.info("用户 {} 锁定成功", user.getUsername());
    }

    @Override
    public void unlockUser(UUID userId) {
        logger.info("开始解锁用户: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        user.resetFailedLoginAttempts();
        userRepository.save(user);

        logger.info("用户 {} 解锁成功", user.getUsername());
    }

    // ============================================================================
    // 登录管理实现
    // ============================================================================

    @Override
    public void recordLoginSuccess(UUID userId, String ipAddress) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        user.updateLastLogin(ipAddress);
        userRepository.save(user);

        logger.info("记录用户 {} 登录成功，IP: {}", user.getUsername(), ipAddress);
    }

    @Override
    public boolean recordLoginFailure(String usernameOrEmail, String ipAddress) {
        Optional<User> userOpt = getUserByUsernameOrEmail(usernameOrEmail);
        if (userOpt.isEmpty()) {
            logger.warn("尝试登录不存在的用户: {}", usernameOrEmail);
            return false;
        }

        User user = userOpt.get();
        user.incrementFailedLoginAttempts();

        boolean shouldLock = user.getFailedLoginAttempts() >= maxLoginAttempts;
        if (shouldLock) {
            user.lockAccount(5); // 锁定5分钟
            logger.warn("用户 {} 登录失败次数过多，账户已锁定", user.getUsername());
        }

        userRepository.save(user);
        logger.warn("记录用户 {} 登录失败，IP: {}，失败次数: {}", 
                user.getUsername(), ipAddress, user.getFailedLoginAttempts());

        return shouldLock;
    }

    @Override
    public void resetLoginFailures(UUID userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        user.resetFailedLoginAttempts();
        userRepository.save(user);

        logger.info("重置用户 {} 登录失败次数", user.getUsername());
    }

    // ============================================================================
    // 工具方法
    // ============================================================================

    /**
     * 验证用户创建数据
     */
    private void validateUserForCreation(User user) {
        if (!StringUtils.hasText(user.getUsername())) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (!StringUtils.hasText(user.getEmail())) {
            throw new IllegalArgumentException("邮箱不能为空");
        }
        if (!StringUtils.hasText(user.getPassword())) {
            throw new IllegalArgumentException("密码不能为空");
        }
        if (!isPasswordValid(user.getPassword())) {
            throw new IllegalArgumentException("密码不符合安全要求");
        }
    }

    /**
     * 验证用户更新数据
     */
    private void validateUserForUpdate(User user, User existingUser) {
        if (!StringUtils.hasText(user.getUsername())) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (!StringUtils.hasText(user.getEmail())) {
            throw new IllegalArgumentException("邮箱不能为空");
        }
    }

    /**
     * 生成随机令牌
     */
    private String generateToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    // ============================================================================
    // 接口方法实现（续）
    // ============================================================================

    @Override
    public void updateUserRole(UUID userId, UserRole newRole) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        user.setRole(newRole);
        userRepository.save(user);

        logger.info("用户 {} 角色更新为: {}", user.getUsername(), newRole);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasPermission(UUID userId, String permission) {
        User user = userRepository.findById(userId).orElse(null);
        return user != null && user.getRole().hasPermission(permission);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canManageUser(UUID managerId, UUID targetUserId) {
        User manager = userRepository.findById(managerId).orElse(null);
        User target = userRepository.findById(targetUserId).orElse(null);
        
        return manager != null && target != null && 
               manager.getRole().canManage(target.getRole());
    }

    @Override
    @Transactional(readOnly = true)
    public long getTotalUserCount() {
        return userRepository.count();
    }

    @Override
    @Transactional(readOnly = true)
    public long getActiveUserCount() {
        return userRepository.countByStatus(UserStatus.ACTIVE);
    }

    @Override
    @Transactional(readOnly = true)
    public long getUserCountByRole(UserRole role) {
        return userRepository.countByRole(role);
    }

    @Override
    @Transactional(readOnly = true)
    public long getVerifiedUserCount() {
        return userRepository.countByEmailVerified(true);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUsernameAvailable(String username, UUID excludeUserId) {
        return !userRepository.existsByUsernameAndIdNot(username, excludeUserId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isEmailAvailable(String email, UUID excludeUserId) {
        return !userRepository.existsByEmailAndIdNot(email, excludeUserId);
    }

    @Override
    public boolean isPasswordValid(String password) {
        if (password == null || password.length() < minPasswordLength) {
            return false;
        }

        if (requireUppercase && !UPPERCASE_PATTERN.matcher(password).matches()) {
            return false;
        }

        if (requireLowercase && !LOWERCASE_PATTERN.matcher(password).matches()) {
            return false;
        }

        if (requireDigit && !DIGIT_PATTERN.matcher(password).matches()) {
            return false;
        }

        if (requireSpecialChar && !SPECIAL_CHAR_PATTERN.matcher(password).matches()) {
            return false;
        }

        return true;
    }
}
