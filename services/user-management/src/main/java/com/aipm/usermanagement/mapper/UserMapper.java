package com.aipm.usermanagement.mapper;

import com.aipm.usermanagement.dto.RegisterRequest;
import com.aipm.usermanagement.dto.UserDto;
import com.aipm.usermanagement.entity.User;
import org.mapstruct.*;

import java.util.List;

/**
 * 用户实体与DTO之间的映射器
 * 
 * 使用MapStruct自动生成映射代码，提供实体和DTO之间的转换功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface UserMapper {

    /**
     * 将用户实体转换为用户DTO
     * 
     * @param user 用户实体
     * @return 用户DTO
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "fullName", source = "fullName")
    @Mapping(target = "avatarUrl", source = "avatarUrl")
    @Mapping(target = "phone", source = "phone")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "role", source = "role")
    @Mapping(target = "emailVerified", source = "emailVerified")
    @Mapping(target = "lastLoginAt", source = "lastLoginAt")
    @Mapping(target = "lastLoginIp", source = "lastLoginIp")
    @Mapping(target = "createdAt", source = "createdAt")
    @Mapping(target = "updatedAt", source = "updatedAt")
    UserDto toDto(User user);

    /**
     * 将用户实体列表转换为用户DTO列表
     * 
     * @param users 用户实体列表
     * @return 用户DTO列表
     */
    List<UserDto> toDtoList(List<User> users);

    /**
     * 将用户DTO转换为用户实体
     * 
     * @param userDto 用户DTO
     * @return 用户实体
     */
    @Mapping(target = "password", ignore = true) // 密码不从DTO映射
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetExpiresAt", ignore = true)
    @Mapping(target = "failedLoginAttempts", ignore = true)
    @Mapping(target = "lockedUntil", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    User toEntity(UserDto userDto);

    /**
     * 将注册请求转换为用户实体
     * 
     * @param registerRequest 注册请求
     * @return 用户实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "username", source = "username")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "password", source = "password") // 密码需要在服务层加密
    @Mapping(target = "fullName", source = "fullName")
    @Mapping(target = "phone", source = "phone")
    @Mapping(target = "status", ignore = true) // 在服务层设置默认值
    @Mapping(target = "role", ignore = true) // 在服务层设置默认值
    @Mapping(target = "emailVerified", ignore = true) // 在服务层设置默认值
    @Mapping(target = "avatarUrl", ignore = true)
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetExpiresAt", ignore = true)
    @Mapping(target = "lastLoginAt", ignore = true)
    @Mapping(target = "lastLoginIp", ignore = true)
    @Mapping(target = "failedLoginAttempts", ignore = true)
    @Mapping(target = "lockedUntil", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    User fromRegisterRequest(RegisterRequest registerRequest);

    /**
     * 更新用户实体（部分更新）
     * 
     * @param userDto 用户DTO
     * @param user 目标用户实体
     */
    @Mapping(target = "id", ignore = true) // ID不能更新
    @Mapping(target = "password", ignore = true) // 密码单独更新
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetExpiresAt", ignore = true)
    @Mapping(target = "failedLoginAttempts", ignore = true)
    @Mapping(target = "lockedUntil", ignore = true)
    @Mapping(target = "createdAt", ignore = true) // 创建时间不能更新
    @Mapping(target = "createdBy", ignore = true) // 创建者不能更新
    @Mapping(target = "updatedAt", ignore = true) // 更新时间由JPA自动处理
    @Mapping(target = "updatedBy", ignore = true) // 更新者在服务层设置
    void updateUserFromDto(UserDto userDto, @MappingTarget User user);

    /**
     * 创建用户摘要DTO（仅包含基本信息）
     * 
     * @param user 用户实体
     * @return 用户摘要DTO
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "fullName", source = "fullName")
    @Mapping(target = "avatarUrl", source = "avatarUrl")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "role", source = "role")
    @Mapping(target = "emailVerified", source = "emailVerified")
    @Mapping(target = "phone", ignore = true)
    @Mapping(target = "lastLoginAt", ignore = true)
    @Mapping(target = "lastLoginIp", ignore = true)
    @Mapping(target = "createdAt", source = "createdAt")
    @Mapping(target = "updatedAt", ignore = true)
    UserDto toSummaryDto(User user);

    /**
     * 创建公开用户信息DTO（不包含敏感信息）
     * 
     * @param user 用户实体
     * @return 公开用户信息DTO
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    @Mapping(target = "fullName", source = "fullName")
    @Mapping(target = "avatarUrl", source = "avatarUrl")
    @Mapping(target = "role", source = "role")
    @Mapping(target = "email", ignore = true) // 不暴露邮箱
    @Mapping(target = "phone", ignore = true) // 不暴露电话
    @Mapping(target = "status", ignore = true) // 不暴露状态
    @Mapping(target = "emailVerified", ignore = true)
    @Mapping(target = "lastLoginAt", ignore = true)
    @Mapping(target = "lastLoginIp", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    UserDto toPublicDto(User user);

    /**
     * 将公开用户信息DTO列表转换
     * 
     * @param users 用户实体列表
     * @return 公开用户信息DTO列表
     */
    List<UserDto> toPublicDtoList(List<User> users);
}
