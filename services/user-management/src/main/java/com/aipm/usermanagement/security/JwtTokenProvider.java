package com.aipm.usermanagement.security;

import com.aipm.usermanagement.entity.User;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * JWT令牌提供者
 * 
 * 负责JWT令牌的生成、验证、解析等操作
 * 支持访问令牌和刷新令牌的管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Component
public class JwtTokenProvider {

    private static final Logger logger = LoggerFactory.getLogger(JwtTokenProvider.class);

    /**
     * JWT密钥
     */
    private final SecretKey secretKey;

    /**
     * 访问令牌过期时间（毫秒）
     */
    @Value("${security.jwt.expiration:86400000}")
    private long accessTokenExpiration;

    /**
     * 刷新令牌过期时间（毫秒）
     */
    @Value("${security.jwt.refresh-expiration:604800000}")
    private long refreshTokenExpiration;

    /**
     * JWT签发者
     */
    @Value("${security.jwt.issuer:aipm-user-service}")
    private String issuer;

    // JWT声明常量
    private static final String CLAIM_USER_ID = "userId";
    private static final String CLAIM_USERNAME = "username";
    private static final String CLAIM_EMAIL = "email";
    private static final String CLAIM_FULL_NAME = "fullName";
    private static final String CLAIM_ROLE = "role";
    private static final String CLAIM_AUTHORITIES = "authorities";
    private static final String CLAIM_TOKEN_TYPE = "tokenType";
    private static final String TOKEN_TYPE_ACCESS = "access";
    private static final String TOKEN_TYPE_REFRESH = "refresh";

    /**
     * 构造函数
     * 
     * @param jwtSecret JWT密钥字符串
     */
    public JwtTokenProvider(@Value("${security.jwt.secret}") String jwtSecret) {
        // 使用HMAC-SHA算法生成密钥
        this.secretKey = Keys.hmacShaKeyFor(jwtSecret.getBytes());
        logger.info("JWT令牌提供者初始化完成");
    }

    /**
     * 为用户生成访问令牌
     * 
     * @param user 用户对象
     * @return JWT访问令牌
     */
    public String generateAccessToken(User user) {
        return generateToken(user, accessTokenExpiration, TOKEN_TYPE_ACCESS);
    }

    /**
     * 为用户生成刷新令牌
     * 
     * @param user 用户对象
     * @return JWT刷新令牌
     */
    public String generateRefreshToken(User user) {
        return generateToken(user, refreshTokenExpiration, TOKEN_TYPE_REFRESH);
    }

    /**
     * 从认证对象生成访问令牌
     * 
     * @param authentication Spring Security认证对象
     * @return JWT访问令牌
     */
    public String generateAccessTokenFromAuthentication(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        return generateAccessToken(user);
    }

    /**
     * 生成JWT令牌的核心方法
     * 
     * @param user 用户对象
     * @param expiration 过期时间（毫秒）
     * @param tokenType 令牌类型
     * @return JWT令牌
     */
    private String generateToken(User user, long expiration, String tokenType) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        // 构建JWT声明
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_USER_ID, user.getId().toString());
        claims.put(CLAIM_USERNAME, user.getUsername());
        claims.put(CLAIM_EMAIL, user.getEmail());
        claims.put(CLAIM_FULL_NAME, user.getFullName());
        claims.put(CLAIM_ROLE, user.getRole().name());
        claims.put(CLAIM_TOKEN_TYPE, tokenType);
        
        // 添加权限信息（仅访问令牌）
        if (TOKEN_TYPE_ACCESS.equals(tokenType)) {
            String authorities = user.getAuthorities().stream()
                    .map(GrantedAuthority::getAuthority)
                    .collect(Collectors.joining(","));
            claims.put(CLAIM_AUTHORITIES, authorities);
        }

        // 生成JWT令牌
        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(user.getId().toString())
                .setIssuer(issuer)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(secretKey, SignatureAlgorithm.HS512)
                .compact();

        logger.debug("为用户 {} 生成了 {} 令牌，过期时间: {}", 
                user.getUsername(), tokenType, expiryDate);

        return token;
    }

    /**
     * 从JWT令牌中获取用户ID
     * 
     * @param token JWT令牌
     * @return 用户ID
     */
    public UUID getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        String userIdStr = claims.get(CLAIM_USER_ID, String.class);
        return UUID.fromString(userIdStr);
    }

    /**
     * 从JWT令牌中获取用户名
     * 
     * @param token JWT令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get(CLAIM_USERNAME, String.class);
    }

    /**
     * 从JWT令牌中获取邮箱
     * 
     * @param token JWT令牌
     * @return 邮箱地址
     */
    public String getEmailFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get(CLAIM_EMAIL, String.class);
    }

    /**
     * 从JWT令牌中获取用户角色
     * 
     * @param token JWT令牌
     * @return 用户角色
     */
    public String getRoleFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get(CLAIM_ROLE, String.class);
    }

    /**
     * 从JWT令牌中获取令牌类型
     * 
     * @param token JWT令牌
     * @return 令牌类型
     */
    public String getTokenTypeFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get(CLAIM_TOKEN_TYPE, String.class);
    }

    /**
     * 获取JWT令牌的过期时间
     * 
     * @param token JWT令牌
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration();
    }

    /**
     * 获取JWT令牌的签发时间
     * 
     * @param token JWT令牌
     * @return 签发时间
     */
    public Date getIssuedAtFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getIssuedAt();
    }

    /**
     * 检查JWT令牌是否过期
     * 
     * @param token JWT令牌
     * @return true表示已过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            logger.warn("检查令牌过期状态时发生错误: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 验证JWT令牌的有效性
     * 
     * @param token JWT令牌
     * @return true表示令牌有效
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (SecurityException ex) {
            logger.error("JWT令牌签名无效: {}", ex.getMessage());
        } catch (MalformedJwtException ex) {
            logger.error("JWT令牌格式错误: {}", ex.getMessage());
        } catch (ExpiredJwtException ex) {
            logger.error("JWT令牌已过期: {}", ex.getMessage());
        } catch (UnsupportedJwtException ex) {
            logger.error("不支持的JWT令牌: {}", ex.getMessage());
        } catch (IllegalArgumentException ex) {
            logger.error("JWT令牌参数为空: {}", ex.getMessage());
        } catch (Exception ex) {
            logger.error("JWT令牌验证失败: {}", ex.getMessage());
        }
        return false;
    }

    /**
     * 验证令牌是否为访问令牌
     * 
     * @param token JWT令牌
     * @return true表示是访问令牌
     */
    public boolean isAccessToken(String token) {
        try {
            String tokenType = getTokenTypeFromToken(token);
            return TOKEN_TYPE_ACCESS.equals(tokenType);
        } catch (Exception e) {
            logger.warn("检查令牌类型时发生错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证令牌是否为刷新令牌
     * 
     * @param token JWT令牌
     * @return true表示是刷新令牌
     */
    public boolean isRefreshToken(String token) {
        try {
            String tokenType = getTokenTypeFromToken(token);
            return TOKEN_TYPE_REFRESH.equals(tokenType);
        } catch (Exception e) {
            logger.warn("检查令牌类型时发生错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取令牌的剩余有效时间（秒）
     * 
     * @param token JWT令牌
     * @return 剩余有效时间（秒），如果已过期返回0
     */
    public long getRemainingValidityInSeconds(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            long remainingTime = (expiration.getTime() - System.currentTimeMillis()) / 1000;
            return Math.max(0, remainingTime);
        } catch (Exception e) {
            logger.warn("获取令牌剩余有效时间时发生错误: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 从JWT令牌中解析声明
     * 
     * @param token JWT令牌
     * @return JWT声明对象
     * @throws JwtException 如果令牌无效
     */
    private Claims getClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(secretKey)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 创建令牌信息对象
     * 
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @return 令牌信息
     */
    public TokenInfo createTokenInfo(String accessToken, String refreshToken) {
        Date accessTokenExpiry = getExpirationDateFromToken(accessToken);
        Date refreshTokenExpiry = getExpirationDateFromToken(refreshToken);
        
        return new TokenInfo(
                accessToken,
                refreshToken,
                accessTokenExpiry.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                refreshTokenExpiry.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
        );
    }

    /**
     * 令牌信息类
     */
    public static class TokenInfo {
        private final String accessToken;
        private final String refreshToken;
        private final LocalDateTime accessTokenExpiresAt;
        private final LocalDateTime refreshTokenExpiresAt;

        public TokenInfo(String accessToken, String refreshToken, 
                        LocalDateTime accessTokenExpiresAt, LocalDateTime refreshTokenExpiresAt) {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            this.accessTokenExpiresAt = accessTokenExpiresAt;
            this.refreshTokenExpiresAt = refreshTokenExpiresAt;
        }

        // Getter方法
        public String getAccessToken() { return accessToken; }
        public String getRefreshToken() { return refreshToken; }
        public LocalDateTime getAccessTokenExpiresAt() { return accessTokenExpiresAt; }
        public LocalDateTime getRefreshTokenExpiresAt() { return refreshTokenExpiresAt; }
    }
}
