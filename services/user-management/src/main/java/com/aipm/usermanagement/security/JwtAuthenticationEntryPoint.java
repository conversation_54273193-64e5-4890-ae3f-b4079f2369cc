package com.aipm.usermanagement.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT认证入口点
 * 
 * 处理未认证用户访问受保护资源时的响应
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationEntryPoint.class);

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request,
                        HttpServletResponse response,
                        AuthenticationException authException) throws IOException {
        
        logger.warn("未认证用户尝试访问受保护资源: {} {}", request.getMethod(), request.getRequestURI());

        // 设置响应状态码和内容类型
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        // 构建错误响应
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("timestamp", LocalDateTime.now().toString());
        errorResponse.put("status", HttpServletResponse.SC_UNAUTHORIZED);
        errorResponse.put("error", "Unauthorized");
        errorResponse.put("message", "访问此资源需要完整的身份认证");
        errorResponse.put("path", request.getRequestURI());

        // 添加详细错误信息
        Map<String, Object> details = new HashMap<>();
        details.put("code", "AUTHENTICATION_REQUIRED");
        details.put("description", "请提供有效的访问令牌");
        details.put("suggestion", "请先登录获取访问令牌，然后在请求头中添加 'Authorization: Bearer <token>'");
        
        errorResponse.put("details", details);

        // 写入响应
        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
    }
}
