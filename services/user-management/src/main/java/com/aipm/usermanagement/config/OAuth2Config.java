package com.aipm.usermanagement.config;

import com.aipm.usermanagement.security.oauth2.CustomOAuth2UserService;
import com.aipm.usermanagement.security.oauth2.OAuth2AuthenticationSuccessHandler;
import com.aipm.usermanagement.security.oauth2.OAuth2AuthenticationFailureHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.DefaultOAuth2AuthorizationRequestResolver;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestResolver;
import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationRequest;

/**
 * OAuth 2.0 配置类
 * 
 * 配置OAuth 2.0客户端和自定义ID Provider集成
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
@Configuration
public class OAuth2Config {

    @Autowired
    private CustomOAuth2UserService customOAuth2UserService;

    @Autowired
    private OAuth2AuthenticationSuccessHandler oAuth2AuthenticationSuccessHandler;

    @Autowired
    private OAuth2AuthenticationFailureHandler oAuth2AuthenticationFailureHandler;

    /**
     * 自定义OAuth2授权请求解析器
     * 
     * @param clientRegistrationRepository 客户端注册仓库
     * @return OAuth2授权请求解析器
     */
    @Bean
    public OAuth2AuthorizationRequestResolver authorizationRequestResolver(
            ClientRegistrationRepository clientRegistrationRepository) {
        
        DefaultOAuth2AuthorizationRequestResolver authorizationRequestResolver =
                new DefaultOAuth2AuthorizationRequestResolver(
                        clientRegistrationRepository, "/oauth2/authorization");
        
        // 自定义授权请求参数
        authorizationRequestResolver.setAuthorizationRequestCustomizer(
                this::customizeAuthorizationRequest);
        
        return authorizationRequestResolver;
    }

    /**
     * 自定义授权请求参数
     * 
     * @param builder 授权请求构建器
     */
    private void customizeAuthorizationRequest(OAuth2AuthorizationRequest.Builder builder) {
        // 添加自定义参数，例如指定权限范围
        builder.additionalParameters(params -> {
            params.put("prompt", "select_account");
            params.put("access_type", "offline");
        });
    }
}
