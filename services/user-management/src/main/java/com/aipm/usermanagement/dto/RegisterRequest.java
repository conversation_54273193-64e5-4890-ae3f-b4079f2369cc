package com.aipm.usermanagement.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 用户注册请求DTO
 * 
 * 用于接收用户注册时提交的基本信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "用户注册请求")
public class RegisterRequest {

    /**
     * 用户名
     */
    @Schema(description = "用户名", example = "johndoe")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "用户名只能包含字母、数字、下划线和连字符")
    private String username;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    /**
     * 密码
     */
    @Schema(description = "密码", example = "Password123!")
    @NotBlank(message = "密码不能为空")
    @Size(min = 8, max = 100, message = "密码长度必须在8-100个字符之间")
    private String password;

    /**
     * 确认密码
     */
    @Schema(description = "确认密码", example = "Password123!")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 用户全名
     */
    @Schema(description = "用户全名", example = "John Doe")
    @Size(max = 100, message = "全名长度不能超过100个字符")
    private String fullName;

    /**
     * 电话号码
     */
    @Schema(description = "电话号码", example = "+86 138 0013 8000")
    @Size(max = 20, message = "电话号码长度不能超过20个字符")
    @Pattern(regexp = "^[+]?[0-9\\s-()]+$", message = "电话号码格式不正确")
    private String phone;

    /**
     * 同意服务条款
     */
    @Schema(description = "是否同意服务条款", example = "true")
    private Boolean agreeToTerms = false;

    /**
     * 接收营销邮件
     */
    @Schema(description = "是否接收营销邮件", example = "false")
    private Boolean acceptMarketing = false;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public RegisterRequest() {
    }

    /**
     * 基础构造函数
     * 
     * @param username 用户名
     * @param email 邮箱
     * @param password 密码
     * @param confirmPassword 确认密码
     */
    public RegisterRequest(String username, String email, String password, String confirmPassword) {
        this.username = username;
        this.email = email;
        this.password = password;
        this.confirmPassword = confirmPassword;
    }

    // ============================================================================
    // 业务验证方法
    // ============================================================================

    /**
     * 验证密码是否一致
     * 
     * @return true表示密码一致
     */
    public boolean isPasswordMatching() {
        return password != null && password.equals(confirmPassword);
    }

    /**
     * 验证是否同意服务条款
     * 
     * @return true表示同意
     */
    public boolean hasAgreedToTerms() {
        return Boolean.TRUE.equals(agreeToTerms);
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Boolean getAgreeToTerms() {
        return agreeToTerms;
    }

    public void setAgreeToTerms(Boolean agreeToTerms) {
        this.agreeToTerms = agreeToTerms;
    }

    public Boolean getAcceptMarketing() {
        return acceptMarketing;
    }

    public void setAcceptMarketing(Boolean acceptMarketing) {
        this.acceptMarketing = acceptMarketing;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public String toString() {
        return "RegisterRequest{" +
                "username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", password='[PROTECTED]'" +
                ", confirmPassword='[PROTECTED]'" +
                ", fullName='" + fullName + '\'' +
                ", phone='" + phone + '\'' +
                ", agreeToTerms=" + agreeToTerms +
                ", acceptMarketing=" + acceptMarketing +
                '}';
    }
}
