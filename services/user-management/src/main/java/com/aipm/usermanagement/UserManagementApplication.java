package com.aipm.usermanagement;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * AI项目管理平台 - 用户管理服务主应用类
 * 
 * 该服务负责处理用户认证、授权、用户信息管理等核心功能
 * 采用Spring Boot 3.x + Spring Security + JWT的技术架构
 * 
 * 主要功能模块：
 * - 用户注册和登录
 * - JWT令牌管理
 * - 角色和权限控制
 * - 用户资料管理
 * - 组织和团队管理
 * - gRPC服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@SpringBootApplication
@EnableJpaAuditing // 启用JPA审计功能，自动填充创建时间、更新时间等字段
@EnableAsync // 启用异步处理功能，用于邮件发送等耗时操作
@EnableTransactionManagement // 启用事务管理
public class UserManagementApplication {

    /**
     * 应用程序入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置系统属性，优化启动性能
        System.setProperty("spring.devtools.restart.enabled", "true");
        System.setProperty("spring.devtools.livereload.enabled", "true");
        
        // 启动Spring Boot应用
        SpringApplication.run(UserManagementApplication.class, args);
    }
}
