/**
 * 安全审计服务
 * 
 * 记录和管理系统安全相关的审计日志，包括认证、授权、敏感操作等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-28
 */

package com.aipm.usermanagement.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 安全审计服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecurityAuditService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    // 审计事件类型
    public enum AuditEventType {
        // 认证相关
        LOGIN_SUCCESS("登录成功"),
        LOGIN_FAILURE("登录失败"),
        LOGOUT("用户登出"),
        TOKEN_REFRESH("令牌刷新"),
        TOKEN_BLACKLIST("令牌加入黑名单"),
        
        // 授权相关
        ACCESS_GRANTED("访问授权"),
        ACCESS_DENIED("访问拒绝"),
        PERMISSION_CHANGE("权限变更"),
        ROLE_CHANGE("角色变更"),
        
        // 用户管理
        USER_CREATED("用户创建"),
        USER_UPDATED("用户更新"),
        USER_DELETED("用户删除"),
        USER_LOCKED("用户锁定"),
        USER_UNLOCKED("用户解锁"),
        PASSWORD_CHANGE("密码修改"),
        PASSWORD_RESET("密码重置"),
        
        // 安全事件
        SUSPICIOUS_ACTIVITY("可疑活动"),
        RATE_LIMIT_EXCEEDED("频率限制超出"),
        SECURITY_VIOLATION("安全违规"),
        DATA_BREACH_ATTEMPT("数据泄露尝试"),
        
        // 系统事件
        SYSTEM_CONFIG_CHANGE("系统配置变更"),
        BACKUP_CREATED("备份创建"),
        BACKUP_RESTORED("备份恢复");

        private final String description;

        AuditEventType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 风险级别
    public enum RiskLevel {
        LOW("低风险"),
        MEDIUM("中风险"),
        HIGH("高风险"),
        CRITICAL("严重风险");

        private final String description;

        RiskLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 记录审计事件
     */
    public void logAuditEvent(AuditEventType eventType, String userId, String details, HttpServletRequest request) {
        logAuditEvent(eventType, userId, details, getRiskLevel(eventType), request);
    }

    /**
     * 记录审计事件（指定风险级别）
     */
    public void logAuditEvent(AuditEventType eventType, String userId, String details, RiskLevel riskLevel, HttpServletRequest request) {
        try {
            AuditEvent auditEvent = createAuditEvent(eventType, userId, details, riskLevel, request);
            
            // 记录到日志文件
            logToFile(auditEvent);
            
            // 存储到Redis（用于实时监控）
            storeToRedis(auditEvent);
            
            // 高风险事件立即告警
            if (riskLevel == RiskLevel.HIGH || riskLevel == RiskLevel.CRITICAL) {
                triggerSecurityAlert(auditEvent);
            }
            
        } catch (Exception e) {
            log.error("记录审计事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建审计事件对象
     */
    private AuditEvent createAuditEvent(AuditEventType eventType, String userId, String details, RiskLevel riskLevel, HttpServletRequest request) {
        AuditEvent event = new AuditEvent();
        event.setEventId(UUID.randomUUID().toString());
        event.setEventType(eventType);
        event.setUserId(userId);
        event.setDetails(details);
        event.setRiskLevel(riskLevel);
        event.setTimestamp(LocalDateTime.now());
        
        if (request != null) {
            event.setClientIp(getClientIpAddress(request));
            event.setUserAgent(request.getHeader("User-Agent"));
            event.setRequestUri(request.getRequestURI());
            event.setRequestMethod(request.getMethod());
            event.setSessionId(request.getSession(false) != null ? request.getSession().getId() : null);
        }
        
        return event;
    }

    /**
     * 记录到日志文件
     */
    private void logToFile(AuditEvent event) {
        String logMessage = String.format(
            "[SECURITY_AUDIT] EventType=%s, UserId=%s, RiskLevel=%s, ClientIP=%s, Details=%s",
            event.getEventType().name(),
            event.getUserId(),
            event.getRiskLevel().name(),
            event.getClientIp(),
            event.getDetails()
        );
        
        // 根据风险级别选择日志级别
        switch (event.getRiskLevel()) {
            case CRITICAL:
            case HIGH:
                log.error(logMessage);
                break;
            case MEDIUM:
                log.warn(logMessage);
                break;
            case LOW:
            default:
                log.info(logMessage);
                break;
        }
    }

    /**
     * 存储到Redis
     */
    private void storeToRedis(AuditEvent event) {
        try {
            String key = "security_audit:" + event.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String eventJson = objectMapper.writeValueAsString(event);
            
            // 使用列表存储当天的审计事件
            redisTemplate.opsForList().rightPush(key, eventJson);
            
            // 设置过期时间（保留30天）
            redisTemplate.expire(key, 30, TimeUnit.DAYS);
            
            // 存储用户相关的审计事件
            if (event.getUserId() != null) {
                String userKey = "user_audit:" + event.getUserId();
                redisTemplate.opsForList().rightPush(userKey, eventJson);
                redisTemplate.expire(userKey, 7, TimeUnit.DAYS); // 用户审计保留7天
            }
            
        } catch (Exception e) {
            log.error("存储审计事件到Redis失败: {}", e.getMessage());
        }
    }

    /**
     * 触发安全告警
     */
    private void triggerSecurityAlert(AuditEvent event) {
        try {
            // 构建告警信息
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("eventId", event.getEventId());
            alertData.put("eventType", event.getEventType().name());
            alertData.put("riskLevel", event.getRiskLevel().name());
            alertData.put("userId", event.getUserId());
            alertData.put("clientIp", event.getClientIp());
            alertData.put("details", event.getDetails());
            alertData.put("timestamp", event.getTimestamp().toString());
            
            // 发送到告警队列
            String alertKey = "security_alerts";
            String alertJson = objectMapper.writeValueAsString(alertData);
            redisTemplate.opsForList().rightPush(alertKey, alertJson);
            
            // 立即记录严重告警
            log.error("[SECURITY_ALERT] 检测到高风险安全事件: {}", alertJson);
            
        } catch (Exception e) {
            log.error("触发安全告警失败: {}", e.getMessage());
        }
    }

    /**
     * 获取风险级别
     */
    private RiskLevel getRiskLevel(AuditEventType eventType) {
        return switch (eventType) {
            case LOGIN_FAILURE, ACCESS_DENIED, RATE_LIMIT_EXCEEDED -> RiskLevel.MEDIUM;
            case SUSPICIOUS_ACTIVITY, SECURITY_VIOLATION, DATA_BREACH_ATTEMPT -> RiskLevel.HIGH;
            case USER_DELETED, SYSTEM_CONFIG_CHANGE -> RiskLevel.HIGH;
            case PERMISSION_CHANGE, ROLE_CHANGE, USER_LOCKED -> RiskLevel.MEDIUM;
            default -> RiskLevel.LOW;
        };
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 检查可疑活动
     */
    public void checkSuspiciousActivity(String userId, String clientIp, AuditEventType eventType) {
        try {
            String key = "suspicious_check:" + userId + ":" + clientIp;
            
            // 检查短时间内的失败次数
            if (eventType == AuditEventType.LOGIN_FAILURE) {
                Long failureCount = redisTemplate.opsForValue().increment(key);
                redisTemplate.expire(key, 15, TimeUnit.MINUTES);
                
                if (failureCount != null && failureCount >= 5) {
                    logAuditEvent(
                        AuditEventType.SUSPICIOUS_ACTIVITY,
                        userId,
                        String.format("短时间内登录失败%d次，IP: %s", failureCount, clientIp),
                        RiskLevel.HIGH,
                        null
                    );
                }
            }
            
        } catch (Exception e) {
            log.error("检查可疑活动失败: {}", e.getMessage());
        }
    }

    /**
     * 审计事件数据类
     */
    public static class AuditEvent {
        private String eventId;
        private AuditEventType eventType;
        private String userId;
        private String details;
        private RiskLevel riskLevel;
        private LocalDateTime timestamp;
        private String clientIp;
        private String userAgent;
        private String requestUri;
        private String requestMethod;
        private String sessionId;

        // Getters and Setters
        public String getEventId() { return eventId; }
        public void setEventId(String eventId) { this.eventId = eventId; }

        public AuditEventType getEventType() { return eventType; }
        public void setEventType(AuditEventType eventType) { this.eventType = eventType; }

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getDetails() { return details; }
        public void setDetails(String details) { this.details = details; }

        public RiskLevel getRiskLevel() { return riskLevel; }
        public void setRiskLevel(RiskLevel riskLevel) { this.riskLevel = riskLevel; }

        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }

        public String getClientIp() { return clientIp; }
        public void setClientIp(String clientIp) { this.clientIp = clientIp; }

        public String getUserAgent() { return userAgent; }
        public void setUserAgent(String userAgent) { this.userAgent = userAgent; }

        public String getRequestUri() { return requestUri; }
        public void setRequestUri(String requestUri) { this.requestUri = requestUri; }

        public String getRequestMethod() { return requestMethod; }
        public void setRequestMethod(String requestMethod) { this.requestMethod = requestMethod; }

        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    }
}
