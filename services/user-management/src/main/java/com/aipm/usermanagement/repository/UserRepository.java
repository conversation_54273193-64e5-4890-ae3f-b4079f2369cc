package com.aipm.usermanagement.repository;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.entity.UserRole;
import com.aipm.usermanagement.entity.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 用户数据访问接口
 * 
 * 提供用户实体的数据库操作方法，包括基本的CRUD操作和自定义查询
 * 继承JpaRepository获得基础的数据库操作能力
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    // ============================================================================
    // 基础查询方法
    // ============================================================================

    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户对象（可能为空）
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     * 
     * @param email 邮箱地址
     * @return 用户对象（可能为空）
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     * 
     * @param username 用户名
     * @param email 邮箱地址
     * @return 用户对象（可能为空）
     */
    Optional<User> findByUsernameOrEmail(String username, String email);

    /**
     * 根据邮箱验证令牌查找用户
     * 
     * @param token 邮箱验证令牌
     * @return 用户对象（可能为空）
     */
    Optional<User> findByEmailVerificationToken(String token);

    /**
     * 根据密码重置令牌查找用户
     * 
     * @param token 密码重置令牌
     * @return 用户对象（可能为空）
     */
    Optional<User> findByPasswordResetToken(String token);

    // ============================================================================
    // 存在性检查方法
    // ============================================================================

    /**
     * 检查用户名是否已存在
     * 
     * @param username 用户名
     * @return true表示已存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否已存在
     * 
     * @param email 邮箱地址
     * @return true表示已存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查用户名是否已存在（排除指定用户）
     * 
     * @param username 用户名
     * @param userId 要排除的用户ID
     * @return true表示已存在
     */
    boolean existsByUsernameAndIdNot(String username, UUID userId);

    /**
     * 检查邮箱是否已存在（排除指定用户）
     * 
     * @param email 邮箱地址
     * @param userId 要排除的用户ID
     * @return true表示已存在
     */
    boolean existsByEmailAndIdNot(String email, UUID userId);

    // ============================================================================
    // 条件查询方法
    // ============================================================================

    /**
     * 根据状态查找用户列表
     * 
     * @param status 用户状态
     * @return 用户列表
     */
    List<User> findByStatus(UserStatus status);

    /**
     * 根据角色查找用户列表
     * 
     * @param role 用户角色
     * @return 用户列表
     */
    List<User> findByRole(UserRole role);

    /**
     * 根据状态和角色查找用户列表
     * 
     * @param status 用户状态
     * @param role 用户角色
     * @return 用户列表
     */
    List<User> findByStatusAndRole(UserStatus status, UserRole role);

    /**
     * 查找邮箱已验证的用户
     * 
     * @param emailVerified 邮箱验证状态
     * @return 用户列表
     */
    List<User> findByEmailVerified(Boolean emailVerified);

    /**
     * 查找锁定的用户（锁定时间未过期）
     * 
     * @param now 当前时间
     * @return 用户列表
     */
    List<User> findByLockedUntilAfter(LocalDateTime now);

    // ============================================================================
    // 分页查询方法
    // ============================================================================

    /**
     * 根据状态分页查询用户
     * 
     * @param status 用户状态
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findByStatus(UserStatus status, Pageable pageable);

    /**
     * 根据角色分页查询用户
     * 
     * @param role 用户角色
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findByRole(UserRole role, Pageable pageable);

    /**
     * 根据关键词搜索用户（用户名、邮箱、全名）
     * 
     * @param keyword 搜索关键词
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    @Query("SELECT u FROM User u WHERE " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.fullName) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<User> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据多个条件搜索用户
     * 
     * @param keyword 搜索关键词
     * @param status 用户状态
     * @param role 用户角色
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    @Query("SELECT u FROM User u WHERE " +
           "(:keyword IS NULL OR " +
           " LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           " LOWER(u.email) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           " LOWER(u.fullName) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:status IS NULL OR u.status = :status) AND " +
           "(:role IS NULL OR u.role = :role)")
    Page<User> searchUsers(@Param("keyword") String keyword,
                          @Param("status") UserStatus status,
                          @Param("role") UserRole role,
                          Pageable pageable);

    // ============================================================================
    // 统计查询方法
    // ============================================================================

    /**
     * 统计指定状态的用户数量
     * 
     * @param status 用户状态
     * @return 用户数量
     */
    long countByStatus(UserStatus status);

    /**
     * 统计指定角色的用户数量
     * 
     * @param role 用户角色
     * @return 用户数量
     */
    long countByRole(UserRole role);

    /**
     * 统计邮箱已验证的用户数量
     * 
     * @param emailVerified 邮箱验证状态
     * @return 用户数量
     */
    long countByEmailVerified(Boolean emailVerified);

    /**
     * 统计指定时间范围内创建的用户数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户数量
     */
    long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    // ============================================================================
    // 更新操作方法
    // ============================================================================

    /**
     * 更新用户最后登录时间和IP
     * 
     * @param userId 用户ID
     * @param lastLoginAt 最后登录时间
     * @param lastLoginIp 最后登录IP
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginAt = :lastLoginAt, u.lastLoginIp = :lastLoginIp " +
           "WHERE u.id = :userId")
    int updateLastLogin(@Param("userId") UUID userId,
                       @Param("lastLoginAt") LocalDateTime lastLoginAt,
                       @Param("lastLoginIp") String lastLoginIp);

    /**
     * 重置用户登录失败次数
     * 
     * @param userId 用户ID
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE User u SET u.failedLoginAttempts = 0, u.lockedUntil = NULL " +
           "WHERE u.id = :userId")
    int resetFailedLoginAttempts(@Param("userId") UUID userId);

    /**
     * 增加用户登录失败次数
     * 
     * @param userId 用户ID
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE User u SET u.failedLoginAttempts = u.failedLoginAttempts + 1 " +
           "WHERE u.id = :userId")
    int incrementFailedLoginAttempts(@Param("userId") UUID userId);

    /**
     * 锁定用户账户
     * 
     * @param userId 用户ID
     * @param lockedUntil 锁定截止时间
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE User u SET u.lockedUntil = :lockedUntil " +
           "WHERE u.id = :userId")
    int lockUser(@Param("userId") UUID userId,
                @Param("lockedUntil") LocalDateTime lockedUntil);

    /**
     * 验证用户邮箱
     * 
     * @param userId 用户ID
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE User u SET u.emailVerified = true, u.emailVerificationToken = NULL " +
           "WHERE u.id = :userId")
    int verifyEmail(@Param("userId") UUID userId);

    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码（已加密）
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE User u SET u.password = :newPassword, " +
           "u.passwordResetToken = NULL, u.passwordResetExpiresAt = NULL " +
           "WHERE u.id = :userId")
    int updatePassword(@Param("userId") UUID userId,
                      @Param("newPassword") String newPassword);

    // ============================================================================
    // 自定义查询方法
    // ============================================================================

    /**
     * 查找即将过期的密码重置令牌
     * 
     * @param expirationTime 过期时间阈值
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE u.passwordResetToken IS NOT NULL " +
           "AND u.passwordResetExpiresAt <= :expirationTime")
    List<User> findUsersWithExpiringPasswordResetTokens(@Param("expirationTime") LocalDateTime expirationTime);

    /**
     * 查找活跃用户（最近登录）
     * 
     * @param since 时间阈值
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt >= :since AND u.status = 'ACTIVE'")
    List<User> findActiveUsersSince(@Param("since") LocalDateTime since);

    /**
     * 查找需要清理的过期令牌用户
     * 
     * @param now 当前时间
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE " +
           "(u.passwordResetToken IS NOT NULL AND u.passwordResetExpiresAt < :now)")
    List<User> findUsersWithExpiredTokens(@Param("now") LocalDateTime now);
}
