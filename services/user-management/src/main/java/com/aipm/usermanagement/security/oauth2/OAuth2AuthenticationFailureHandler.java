package com.aipm.usermanagement.security.oauth2;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * OAuth2认证失败处理器
 * 
 * 处理OAuth2认证失败的情况，重定向到前端错误页面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
@Component
public class OAuth2AuthenticationFailureHandler extends SimpleUrlAuthenticationFailureHandler {

    private static final Logger logger = LoggerFactory.getLogger(OAuth2AuthenticationFailureHandler.class);

    @Value("${app.oauth2.authorized-redirect-uris:http://localhost:3000/oauth2/redirect}")
    private String[] authorizedRedirectUris;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) throws IOException, ServletException {
        
        String targetUrl = determineTargetUrl(request, exception);
        
        if (response.isCommitted()) {
            logger.debug("响应已提交，无法重定向到 {}", targetUrl);
            return;
        }

        logger.warn("OAuth2认证失败: {}", exception.getMessage());
        getRedirectStrategy().sendRedirect(request, response, targetUrl);
    }

    /**
     * 确定目标URL
     * 
     * @param request HTTP请求
     * @param exception 认证异常
     * @return 目标URL
     */
    private String determineTargetUrl(HttpServletRequest request, AuthenticationException exception) {
        String redirectUri = getRedirectUri(request);
        String errorMessage = exception.getLocalizedMessage();
        
        return UriComponentsBuilder.fromUriString(redirectUri)
                .queryParam("error", "authentication_failed")
                .queryParam("message", URLEncoder.encode(errorMessage, StandardCharsets.UTF_8))
                .build().toUriString();
    }

    /**
     * 获取重定向URI
     * 
     * @param request HTTP请求
     * @return 重定向URI
     */
    private String getRedirectUri(HttpServletRequest request) {
        String redirectUri = request.getParameter("redirect_uri");
        
        if (redirectUri == null || redirectUri.isEmpty()) {
            redirectUri = authorizedRedirectUris[0]; // 使用默认重定向URI
        }
        
        return redirectUri;
    }
}
