package com.aipm.usermanagement.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 用户登录请求DTO
 * 
 * 用于接收用户登录时提交的用户名/邮箱和密码信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "用户登录请求")
public class LoginRequest {

    /**
     * 用户名或邮箱地址
     */
    @Schema(description = "用户名或邮箱地址", example = "<EMAIL>")
    @NotBlank(message = "用户名或邮箱不能为空")
    @Size(min = 3, max = 100, message = "用户名或邮箱长度必须在3-100个字符之间")
    private String usernameOrEmail;

    /**
     * 密码
     */
    @Schema(description = "用户密码", example = "password123")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    /**
     * 记住我选项
     */
    @Schema(description = "是否记住登录状态", example = "false")
    private Boolean rememberMe = false;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public LoginRequest() {
    }

    /**
     * 构造函数
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @param password 密码
     */
    public LoginRequest(String usernameOrEmail, String password) {
        this.usernameOrEmail = usernameOrEmail;
        this.password = password;
    }

    /**
     * 完整构造函数
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @param password 密码
     * @param rememberMe 记住我
     */
    public LoginRequest(String usernameOrEmail, String password, Boolean rememberMe) {
        this.usernameOrEmail = usernameOrEmail;
        this.password = password;
        this.rememberMe = rememberMe;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public String getUsernameOrEmail() {
        return usernameOrEmail;
    }

    public void setUsernameOrEmail(String usernameOrEmail) {
        this.usernameOrEmail = usernameOrEmail;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public String toString() {
        return "LoginRequest{" +
                "usernameOrEmail='" + usernameOrEmail + '\'' +
                ", password='[PROTECTED]'" +
                ", rememberMe=" + rememberMe +
                '}';
    }
}
