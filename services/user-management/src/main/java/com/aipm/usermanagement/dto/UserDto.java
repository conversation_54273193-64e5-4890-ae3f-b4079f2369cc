package com.aipm.usermanagement.dto;

import com.aipm.usermanagement.entity.UserRole;
import com.aipm.usermanagement.entity.UserStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 用户信息DTO
 * 
 * 用于API响应中返回用户信息，不包含敏感数据如密码
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "用户信息")
public class UserDto {

    /**
     * 用户ID
     */
    @Schema(description = "用户唯一标识", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    /**
     * 用户名
     */
    @Schema(description = "用户名", example = "johndoe")
    private String username;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    /**
     * 用户全名
     */
    @Schema(description = "用户全名", example = "John Doe")
    private String fullName;

    /**
     * 头像URL
     */
    @Schema(description = "头像图片URL", example = "https://example.com/avatars/johndoe.jpg")
    private String avatarUrl;

    /**
     * 电话号码
     */
    @Schema(description = "电话号码", example = "+86 138 0013 8000")
    private String phone;

    /**
     * 用户状态
     */
    @Schema(description = "用户状态", example = "ACTIVE")
    private UserStatus status;

    /**
     * 用户角色
     */
    @Schema(description = "用户角色", example = "USER")
    private UserRole role;

    /**
     * 邮箱验证状态
     */
    @Schema(description = "邮箱是否已验证", example = "true")
    private Boolean emailVerified;

    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间", example = "2025-08-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginAt;

    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP地址", example = "*************")
    private String lastLoginIp;

    /**
     * 创建时间
     */
    @Schema(description = "账户创建时间", example = "2025-08-15T09:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "信息最后更新时间", example = "2025-08-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public UserDto() {
    }

    /**
     * 基础构造函数
     * 
     * @param id 用户ID
     * @param username 用户名
     * @param email 邮箱
     * @param fullName 全名
     */
    public UserDto(UUID id, String username, String email, String fullName) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.fullName = fullName;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查用户是否为管理员
     * 
     * @return true表示是管理员
     */
    public boolean isAdmin() {
        return role == UserRole.ADMIN;
    }

    /**
     * 检查用户是否为经理级别或以上
     * 
     * @return true表示是经理级别或以上
     */
    public boolean isManagerOrAbove() {
        return role != null && role.isManagerOrAbove();
    }

    /**
     * 检查用户是否处于活跃状态
     * 
     * @return true表示活跃
     */
    public boolean isActive() {
        return status == UserStatus.ACTIVE;
    }

    /**
     * 获取用户显示名称（优先使用全名，否则使用用户名）
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return (fullName != null && !fullName.trim().isEmpty()) ? fullName : username;
    }

    /**
     * 获取角色显示名称
     * 
     * @return 角色显示名称
     */
    public String getRoleDisplayName() {
        return role != null ? role.getDisplayName() : "未知";
    }

    /**
     * 获取状态显示名称
     * 
     * @return 状态显示名称
     */
    public String getStatusDisplayName() {
        return status != null ? status.getDisplayName() : "未知";
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public UserStatus getStatus() {
        return status;
    }

    public void setStatus(UserStatus status) {
        this.status = status;
    }

    public UserRole getRole() {
        return role;
    }

    public void setRole(UserRole role) {
        this.role = role;
    }

    public Boolean getEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(Boolean emailVerified) {
        this.emailVerified = emailVerified;
    }

    public LocalDateTime getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }

    public String getLastLoginIp() {
        return lastLoginIp;
    }

    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserDto userDto)) return false;
        return id != null && id.equals(userDto.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "UserDto{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", fullName='" + fullName + '\'' +
                ", status=" + status +
                ", role=" + role +
                ", emailVerified=" + emailVerified +
                ", createdAt=" + createdAt +
                '}';
    }
}
