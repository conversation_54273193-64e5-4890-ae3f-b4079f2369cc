package com.aipm.usermanagement.controller;

import com.aipm.usermanagement.dto.ApiResponse;
import com.aipm.usermanagement.dto.UserDto;
import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.entity.UserRole;
import com.aipm.usermanagement.entity.UserStatus;
import com.aipm.usermanagement.mapper.UserMapper;
import com.aipm.usermanagement.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 用户管理控制器
 * 
 * 提供用户管理相关的REST API接口，包括用户CRUD操作、状态管理等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户管理相关API")
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private UserMapper userMapper;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "获取成功",
            content = @Content(schema = @Schema(implementation = UserDto.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "401", 
            description = "未认证"
        )
    })
    public ResponseEntity<ApiResponse<UserDto>> getCurrentUser(Authentication authentication) {
        logger.debug("获取当前用户信息请求");

        try {
            User currentUser = (User) authentication.getPrincipal();
            UserDto userDto = userMapper.toDto(currentUser);
            
            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", userDto));
        } catch (Exception e) {
            logger.error("获取当前用户信息异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户信息失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 根据ID获取用户信息
     */
    @GetMapping("/{userId}")
    @Operation(summary = "根据ID获取用户信息", description = "根据用户ID获取用户详细信息")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or #userId == authentication.principal.id.toString()")
    public ResponseEntity<ApiResponse<UserDto>> getUserById(
            @Parameter(description = "用户ID", required = true)
            @PathVariable UUID userId,
            Authentication authentication) {
        
        logger.debug("获取用户信息请求: {}", userId);

        try {
            Optional<User> userOpt = userService.getUserById(userId);
            if (userOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("用户不存在", "USER_NOT_FOUND"));
            }

            User currentUser = (User) authentication.getPrincipal();
            User targetUser = userOpt.get();
            
            // 根据权限返回不同详细程度的信息
            UserDto userDto;
            if (currentUser.getId().equals(targetUser.getId()) || 
                currentUser.getRole().isManagerOrAbove()) {
                userDto = userMapper.toDto(targetUser);
            } else {
                userDto = userMapper.toPublicDto(targetUser);
            }
            
            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", userDto));
        } catch (Exception e) {
            logger.error("获取用户信息异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户信息失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 分页获取用户列表
     */
    @GetMapping
    @Operation(summary = "分页获取用户列表", description = "分页查询用户列表，支持搜索和过滤")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<PagedResponse<UserDto>>> getUsers(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String search,
            @Parameter(description = "用户状态过滤") @RequestParam(required = false) UserStatus status,
            @Parameter(description = "用户角色过滤") @RequestParam(required = false) UserRole role) {
        
        logger.debug("获取用户列表请求: page={}, size={}, search={}", page, size, search);

        try {
            // 创建分页和排序参数
            Sort.Direction direction = "desc".equalsIgnoreCase(sortDir) ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            // 执行查询
            Page<User> userPage = userService.searchUsers(search, status, role, pageable);
            
            // 转换为DTO
            List<UserDto> userDtos = userMapper.toDtoList(userPage.getContent());
            
            PagedResponse<UserDto> pagedResponse = new PagedResponse<>(
                userDtos,
                userPage.getNumber(),
                userPage.getSize(),
                userPage.getTotalElements(),
                userPage.getTotalPages(),
                userPage.isFirst(),
                userPage.isLast()
            );
            
            return ResponseEntity.ok(ApiResponse.success("获取用户列表成功", pagedResponse));
        } catch (Exception e) {
            logger.error("获取用户列表异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户列表失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{userId}")
    @Operation(summary = "更新用户信息", description = "更新指定用户的基本信息")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or #userId == authentication.principal.id.toString()")
    public ResponseEntity<ApiResponse<UserDto>> updateUser(
            @Parameter(description = "用户ID", required = true) @PathVariable UUID userId,
            @Valid @RequestBody UserDto userDto,
            Authentication authentication) {
        
        logger.info("更新用户信息请求: {}", userId);

        try {
            Optional<User> userOpt = userService.getUserById(userId);
            if (userOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("用户不存在", "USER_NOT_FOUND"));
            }

            User existingUser = userOpt.get();
            User currentUser = (User) authentication.getPrincipal();
            
            // 权限检查：普通用户只能更新自己的基本信息
            if (!currentUser.getRole().isManagerOrAbove() && 
                !currentUser.getId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限更新其他用户信息", "ACCESS_DENIED"));
            }

            // 普通用户不能修改角色和状态
            if (!currentUser.getRole().isManagerOrAbove()) {
                userDto.setRole(existingUser.getRole());
                userDto.setStatus(existingUser.getStatus());
            }

            // 更新用户信息
            userMapper.updateUserFromDto(userDto, existingUser);
            User updatedUser = userService.updateUser(existingUser);
            UserDto responseDto = userMapper.toDto(updatedUser);
            
            logger.info("用户 {} 信息更新成功", updatedUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户信息更新成功", responseDto));
        } catch (Exception e) {
            logger.error("更新用户信息异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新用户信息失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 更新用户状态
     */
    @PatchMapping("/{userId}/status")
    @Operation(summary = "更新用户状态", description = "更新指定用户的状态（激活、暂停等）")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<UserDto>> updateUserStatus(
            @Parameter(description = "用户ID", required = true) @PathVariable UUID userId,
            @Parameter(description = "新状态", required = true) @RequestParam UserStatus status,
            @Parameter(description = "状态变更原因") @RequestParam(required = false) String reason) {
        
        logger.info("更新用户状态请求: {} -> {}", userId, status);

        try {
            Optional<User> userOpt = userService.getUserById(userId);
            if (userOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("用户不存在", "USER_NOT_FOUND"));
            }

            User user = userOpt.get();
            
            // 根据状态执行相应操作
            switch (status) {
                case ACTIVE:
                    userService.activateUser(userId);
                    break;
                case SUSPENDED:
                    userService.suspendUser(userId, reason != null ? reason : "管理员操作");
                    break;
                case INACTIVE:
                    userService.deactivateUser(userId);
                    break;
                default:
                    return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无效的用户状态", "INVALID_STATUS"));
            }

            // 获取更新后的用户信息
            User updatedUser = userService.getUserById(userId).orElse(user);
            UserDto userDto = userMapper.toDto(updatedUser);
            
            logger.info("用户 {} 状态更新为: {}", user.getUsername(), status);
            return ResponseEntity.ok(ApiResponse.success("用户状态更新成功", userDto));
        } catch (Exception e) {
            logger.error("更新用户状态异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新用户状态失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 更新用户角色
     */
    @PatchMapping("/{userId}/role")
    @Operation(summary = "更新用户角色", description = "更新指定用户的角色权限")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserDto>> updateUserRole(
            @Parameter(description = "用户ID", required = true) @PathVariable UUID userId,
            @Parameter(description = "新角色", required = true) @RequestParam UserRole role) {
        
        logger.info("更新用户角色请求: {} -> {}", userId, role);

        try {
            Optional<User> userOpt = userService.getUserById(userId);
            if (userOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("用户不存在", "USER_NOT_FOUND"));
            }

            userService.updateUserRole(userId, role);
            
            // 获取更新后的用户信息
            User updatedUser = userService.getUserById(userId).get();
            UserDto userDto = userMapper.toDto(updatedUser);
            
            logger.info("用户 {} 角色更新为: {}", updatedUser.getUsername(), role);
            return ResponseEntity.ok(ApiResponse.success("用户角色更新成功", userDto));
        } catch (Exception e) {
            logger.error("更新用户角色异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新用户角色失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    @Operation(summary = "删除用户", description = "删除指定用户（软删除）")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteUser(
            @Parameter(description = "用户ID", required = true) @PathVariable UUID userId) {
        
        logger.info("删除用户请求: {}", userId);

        try {
            Optional<User> userOpt = userService.getUserById(userId);
            if (userOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("用户不存在", "USER_NOT_FOUND"));
            }

            User user = userOpt.get();
            userService.deactivateUser(userId); // 使用软删除
            
            logger.info("用户 {} 删除成功", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户删除成功"));
        } catch (Exception e) {
            logger.error("删除用户异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("删除用户失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 分页响应包装类
     */
    @Schema(description = "分页响应")
    public static class PagedResponse<T> {
        @Schema(description = "数据列表")
        private List<T> content;
        
        @Schema(description = "当前页码")
        private int page;
        
        @Schema(description = "每页大小")
        private int size;
        
        @Schema(description = "总记录数")
        private long totalElements;
        
        @Schema(description = "总页数")
        private int totalPages;
        
        @Schema(description = "是否为第一页")
        private boolean first;
        
        @Schema(description = "是否为最后一页")
        private boolean last;

        public PagedResponse(List<T> content, int page, int size, long totalElements, 
                           int totalPages, boolean first, boolean last) {
            this.content = content;
            this.page = page;
            this.size = size;
            this.totalElements = totalElements;
            this.totalPages = totalPages;
            this.first = first;
            this.last = last;
        }

        // Getter方法
        public List<T> getContent() { return content; }
        public int getPage() { return page; }
        public int getSize() { return size; }
        public long getTotalElements() { return totalElements; }
        public int getTotalPages() { return totalPages; }
        public boolean isFirst() { return first; }
        public boolean isLast() { return last; }
    }
}
