package com.aipm.usermanagement.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI (Swagger) 配置
 * 
 * 配置API文档的基本信息、安全认证方案等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Configuration
public class OpenApiConfig {

    @Value("${spring.application.name:user-management-service}")
    private String applicationName;

    @Value("${server.servlet.context-path:/api/v1}")
    private String contextPath;

    /**
     * 配置OpenAPI文档
     * 
     * @return OpenAPI配置对象
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(apiInfo())
            .servers(serverList())
            .addSecurityItem(securityRequirement())
            .components(securityComponents());
    }

    /**
     * API基本信息配置
     * 
     * @return API信息对象
     */
    private Info apiInfo() {
        return new Info()
            .title("AI项目管理平台 - 用户管理服务API")
            .description("""
                AI项目管理平台的用户管理微服务API文档
                
                ## 功能特性
                - 用户注册和登录认证
                - JWT令牌管理（访问令牌和刷新令牌）
                - 用户信息管理（CRUD操作）
                - 角色和权限控制
                - 用户状态管理
                - 邮箱验证和密码重置
                
                ## 认证方式
                本API使用JWT Bearer Token进行认证。请在请求头中添加：
                ```
                Authorization: Bearer <your-access-token>
                ```
                
                ## 错误处理
                所有API响应都遵循统一的格式：
                ```json
                {
                  "success": true/false,
                  "message": "响应消息",
                  "data": "响应数据（成功时）",
                  "errorCode": "错误代码（失败时）",
                  "errorDetails": "错误详情（失败时）",
                  "timestamp": "响应时间戳",
                  "requestId": "请求追踪ID"
                }
                ```
                
                ## 分页查询
                分页查询接口支持以下参数：
                - `page`: 页码（从0开始）
                - `size`: 每页大小
                - `sortBy`: 排序字段
                - `sortDir`: 排序方向（asc/desc）
                
                ## 权限说明
                - **ADMIN**: 系统管理员，拥有所有权限
                - **MANAGER**: 项目经理，可以管理用户和项目
                - **USER**: 普通用户，可以管理自己的信息
                - **VIEWER**: 查看者，只能查看被授权的内容
                """)
            .version("1.0.0")
            .contact(contact())
            .license(license());
    }

    /**
     * 联系信息配置
     * 
     * @return 联系信息对象
     */
    private Contact contact() {
        return new Contact()
            .name("AI项目管理平台开发团队")
            .email("<EMAIL>")
            .url("https://github.com/aipm/user-management");
    }

    /**
     * 许可证信息配置
     * 
     * @return 许可证信息对象
     */
    private License license() {
        return new License()
            .name("MIT License")
            .url("https://opensource.org/licenses/MIT");
    }

    /**
     * 服务器列表配置
     * 
     * @return 服务器列表
     */
    private List<Server> serverList() {
        return List.of(
            new Server()
                .url("http://localhost:8080" + contextPath)
                .description("本地开发环境"),
            new Server()
                .url("https://dev-api.aipm.com" + contextPath)
                .description("开发测试环境"),
            new Server()
                .url("https://api.aipm.com" + contextPath)
                .description("生产环境")
        );
    }

    /**
     * 安全要求配置
     * 
     * @return 安全要求对象
     */
    private SecurityRequirement securityRequirement() {
        return new SecurityRequirement().addList("bearerAuth");
    }

    /**
     * 安全组件配置
     * 
     * @return 安全组件对象
     */
    private Components securityComponents() {
        return new Components()
            .addSecuritySchemes("bearerAuth", bearerAuthScheme());
    }

    /**
     * Bearer认证方案配置
     * 
     * @return Bearer认证方案对象
     */
    private SecurityScheme bearerAuthScheme() {
        return new SecurityScheme()
            .type(SecurityScheme.Type.HTTP)
            .scheme("bearer")
            .bearerFormat("JWT")
            .description("""
                JWT访问令牌认证
                
                请在登录后获取访问令牌，然后在请求头中添加：
                Authorization: Bearer <your-access-token>
                
                令牌有效期为24小时，过期后需要使用刷新令牌获取新的访问令牌。
                """);
    }
}
