package com.aipm.usermanagement.security.oauth2;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.security.JwtTokenProvider;
import com.aipm.usermanagement.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * OAuth2认证成功处理器
 * 
 * 处理OAuth2认证成功后的逻辑，生成JWT令牌并重定向到前端
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
@Component
public class OAuth2AuthenticationSuccessHandler extends SimpleUrlAuthenticationSuccessHandler {

    private static final Logger logger = LoggerFactory.getLogger(OAuth2AuthenticationSuccessHandler.class);
    private static final String REFRESH_TOKEN_PREFIX = "refresh_token:";

    @Value("${app.oauth2.authorized-redirect-uris:http://localhost:3000/oauth2/redirect}")
    private String[] authorizedRedirectUris;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private UserService userService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
        
        String targetUrl = determineTargetUrl(request, response, authentication);
        
        if (response.isCommitted()) {
            logger.debug("响应已提交，无法重定向到 {}", targetUrl);
            return;
        }

        clearAuthenticationAttributes(request);
        getRedirectStrategy().sendRedirect(request, response, targetUrl);
    }

    protected String determineTargetUrl(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) {
        
        // 获取重定向URL（可以从请求参数或session中获取）
        String redirectUri = getRedirectUri(request);
        
        try {
            // 获取OAuth2用户信息
            CustomOAuth2UserPrincipal userPrincipal = (CustomOAuth2UserPrincipal) authentication.getPrincipal();
            User user = userPrincipal.getUser();

            // 生成JWT令牌
            String accessToken = jwtTokenProvider.generateAccessToken(user);
            String refreshToken = jwtTokenProvider.generateRefreshToken(user);

            // 将刷新令牌存储到Redis
            String refreshTokenKey = REFRESH_TOKEN_PREFIX + user.getId();
            redisTemplate.opsForValue().set(refreshTokenKey, refreshToken,
                    jwtTokenProvider.getRemainingValidityInSeconds(refreshToken), TimeUnit.SECONDS);

            // 记录登录成功
            userService.recordLoginSuccess(user.getId(), getClientIpAddress(request));

            logger.info("OAuth2用户 {} 认证成功", user.getUsername());

            // 构建重定向URL，包含令牌信息
            return UriComponentsBuilder.fromUriString(redirectUri)
                    .queryParam("token", accessToken)
                    .queryParam("refreshToken", refreshToken)
                    .build().toUriString();

        } catch (Exception ex) {
            logger.error("OAuth2认证成功处理失败: {}", ex.getMessage(), ex);
            return UriComponentsBuilder.fromUriString(redirectUri)
                    .queryParam("error", "authentication_failed")
                    .build().toUriString();
        }
    }

    /**
     * 获取重定向URI
     * 
     * @param request HTTP请求
     * @return 重定向URI
     */
    private String getRedirectUri(HttpServletRequest request) {
        String redirectUri = request.getParameter("redirect_uri");
        
        if (redirectUri == null || redirectUri.isEmpty()) {
            redirectUri = authorizedRedirectUris[0]; // 使用默认重定向URI
        }
        
        // 验证重定向URI是否在授权列表中
        if (!isAuthorizedRedirectUri(redirectUri)) {
            logger.warn("未授权的重定向URI: {}", redirectUri);
            redirectUri = authorizedRedirectUris[0];
        }
        
        return redirectUri;
    }

    /**
     * 验证重定向URI是否被授权
     * 
     * @param uri 重定向URI
     * @return 是否被授权
     */
    private boolean isAuthorizedRedirectUri(String uri) {
        URI clientRedirectUri = URI.create(uri);
        
        for (String authorizedRedirectUri : authorizedRedirectUris) {
            URI authorizedURI = URI.create(authorizedRedirectUri);
            
            if (authorizedURI.getHost().equalsIgnoreCase(clientRedirectUri.getHost())
                    && authorizedURI.getPort() == clientRedirectUri.getPort()) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
