package com.aipm.usermanagement.service.impl;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.repository.UserRepository;
import com.aipm.usermanagement.security.JwtTokenProvider;
import com.aipm.usermanagement.service.AuthenticationService;
import com.aipm.usermanagement.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 * 
 * 实现用户认证、令牌管理等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Service
@Transactional
public class AuthenticationServiceImpl implements AuthenticationService {

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationServiceImpl.class);

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // Redis键前缀
    private static final String BLACKLIST_PREFIX = "auth:blacklist:";
    private static final String REFRESH_TOKEN_PREFIX = "auth:refresh:";

    @Override
    public AuthenticationResult login(String usernameOrEmail, String password, String ipAddress) {
        logger.info("用户登录尝试: {}, IP: {}", usernameOrEmail, ipAddress);

        try {
            // 验证用户凭证
            User user = userService.authenticate(usernameOrEmail, password);

            // 生成令牌
            String accessToken = jwtTokenProvider.generateAccessToken(user);
            String refreshToken = jwtTokenProvider.generateRefreshToken(user);
            JwtTokenProvider.TokenInfo tokenInfo = jwtTokenProvider.createTokenInfo(accessToken, refreshToken);

            // 将刷新令牌存储到Redis
            String refreshTokenKey = REFRESH_TOKEN_PREFIX + user.getId();
            redisTemplate.opsForValue().set(refreshTokenKey, refreshToken, 
                    jwtTokenProvider.getRemainingValidityInSeconds(refreshToken), TimeUnit.SECONDS);

            // 记录登录成功
            userService.recordLoginSuccess(user.getId(), ipAddress);

            logger.info("用户 {} 登录成功", user.getUsername());
            return AuthenticationResult.success(user, tokenInfo);

        } catch (Exception e) {
            logger.warn("用户 {} 登录失败: {}", usernameOrEmail, e.getMessage());

            // 记录登录失败
            boolean accountLocked = userService.recordLoginFailure(usernameOrEmail, ipAddress);
            
            String message = e.getMessage();
            if (accountLocked) {
                message = "登录失败次数过多，账户已被锁定";
            }

            return AuthenticationResult.failure(message);
        }
    }

    @Override
    public AuthenticationResult refreshToken(String refreshToken) {
        logger.debug("开始刷新访问令牌");

        try {
            // 验证刷新令牌
            if (!jwtTokenProvider.validateToken(refreshToken) || 
                !jwtTokenProvider.isRefreshToken(refreshToken)) {
                return AuthenticationResult.failure("无效的刷新令牌");
            }

            // 获取用户ID
            UUID userId = jwtTokenProvider.getUserIdFromToken(refreshToken);

            // 检查刷新令牌是否在Redis中存在
            String refreshTokenKey = REFRESH_TOKEN_PREFIX + userId;
            String storedRefreshToken = (String) redisTemplate.opsForValue().get(refreshTokenKey);
            if (storedRefreshToken == null || !storedRefreshToken.equals(refreshToken)) {
                return AuthenticationResult.failure("刷新令牌已失效");
            }

            // 获取用户信息
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 检查用户状态
            if (!user.isEnabled() || !user.isAccountNonLocked()) {
                return AuthenticationResult.failure("用户账户已被禁用或锁定");
            }

            // 生成新的访问令牌
            String newAccessToken = jwtTokenProvider.generateAccessToken(user);
            String newRefreshToken = jwtTokenProvider.generateRefreshToken(user);
            JwtTokenProvider.TokenInfo tokenInfo = jwtTokenProvider.createTokenInfo(newAccessToken, newRefreshToken);

            // 更新Redis中的刷新令牌
            redisTemplate.opsForValue().set(refreshTokenKey, newRefreshToken, 
                    jwtTokenProvider.getRemainingValidityInSeconds(newRefreshToken), TimeUnit.SECONDS);

            logger.info("用户 {} 令牌刷新成功", user.getUsername());
            return AuthenticationResult.success(user, tokenInfo);

        } catch (Exception e) {
            logger.warn("令牌刷新失败: {}", e.getMessage());
            return AuthenticationResult.failure("令牌刷新失败: " + e.getMessage());
        }
    }

    @Override
    public void logout(String accessToken) {
        logger.debug("用户登出");

        try {
            // 验证访问令牌
            if (!jwtTokenProvider.validateToken(accessToken) || 
                !jwtTokenProvider.isAccessToken(accessToken)) {
                logger.warn("尝试使用无效的访问令牌登出");
                return;
            }

            // 获取用户ID
            UUID userId = jwtTokenProvider.getUserIdFromToken(accessToken);

            // 将访问令牌加入黑名单
            String blacklistKey = BLACKLIST_PREFIX + accessToken;
            long remainingTime = jwtTokenProvider.getRemainingValidityInSeconds(accessToken);
            if (remainingTime > 0) {
                redisTemplate.opsForValue().set(blacklistKey, "blacklisted", remainingTime, TimeUnit.SECONDS);
            }

            // 删除刷新令牌
            String refreshTokenKey = REFRESH_TOKEN_PREFIX + userId;
            redisTemplate.delete(refreshTokenKey);

            logger.info("用户 {} 登出成功", userId);

        } catch (Exception e) {
            logger.warn("用户登出处理失败: {}", e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public User validateAccessToken(String accessToken) {
        try {
            // 验证令牌格式和签名
            if (!jwtTokenProvider.validateToken(accessToken) || 
                !jwtTokenProvider.isAccessToken(accessToken)) {
                return null;
            }

            // 检查令牌是否在黑名单中
            String blacklistKey = BLACKLIST_PREFIX + accessToken;
            if (redisTemplate.hasKey(blacklistKey)) {
                logger.debug("访问令牌在黑名单中");
                return null;
            }

            // 获取用户信息
            UUID userId = jwtTokenProvider.getUserIdFromToken(accessToken);
            User user = userRepository.findById(userId).orElse(null);

            // 检查用户状态
            if (user == null || !user.isEnabled() || !user.isAccountNonLocked()) {
                return null;
            }

            return user;

        } catch (Exception e) {
            logger.debug("访问令牌验证失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查令牌是否在黑名单中
     * 
     * @param token 令牌
     * @return true表示在黑名单中
     */
    public boolean isTokenBlacklisted(String token) {
        String blacklistKey = BLACKLIST_PREFIX + token;
        return redisTemplate.hasKey(blacklistKey);
    }

    /**
     * 清理过期的黑名单令牌
     * 
     * 这个方法可以通过定时任务调用，清理Redis中过期的黑名单条目
     */
    public void cleanupExpiredBlacklistTokens() {
        // Redis会自动清理过期的键，这里可以添加额外的清理逻辑
        logger.debug("清理过期的黑名单令牌");
    }

    /**
     * 获取用户的活跃会话数量
     * 
     * @param userId 用户ID
     * @return 活跃会话数量
     */
    public int getActiveSessionCount(UUID userId) {
        String refreshTokenKey = REFRESH_TOKEN_PREFIX + userId;
        return redisTemplate.hasKey(refreshTokenKey) ? 1 : 0;
    }

    /**
     * 强制用户下线（清除所有会话）
     * 
     * @param userId 用户ID
     */
    public void forceLogoutUser(UUID userId) {
        logger.info("强制用户 {} 下线", userId);

        // 删除刷新令牌
        String refreshTokenKey = REFRESH_TOKEN_PREFIX + userId;
        redisTemplate.delete(refreshTokenKey);

        // 注意：这里无法将所有访问令牌加入黑名单，因为我们不知道用户有哪些访问令牌
        // 在实际应用中，可以考虑维护一个用户会话列表来解决这个问题
    }
}
