-- 添加OAuth2支持的数据库迁移脚本
-- 版本: V2
-- 描述: 为用户表添加OAuth2相关字段

-- 为用户表添加OAuth2提供商字段
ALTER TABLE users 
ADD COLUMN oauth2_provider VARCHAR(50),
ADD COLUMN oauth2_provider_id VARCHAR(100);

-- 添加索引以提高查询性能
CREATE INDEX idx_users_oauth2_provider ON users(oauth2_provider);
CREATE INDEX idx_users_oauth2_provider_id ON users(oauth2_provider_id);

-- 添加唯一约束，确保同一个OAuth2提供商的用户ID不重复
CREATE UNIQUE INDEX idx_users_oauth2_unique ON users(oauth2_provider, oauth2_provider_id) 
WHERE oauth2_provider IS NOT NULL AND oauth2_provider_id IS NOT NULL;

-- 添加注释
COMMENT ON COLUMN users.oauth2_provider IS 'OAuth2提供商名称 (google, github, custom等)';
COMMENT ON COLUMN users.oauth2_provider_id IS 'OAuth2提供商中的用户ID';

-- 更新现有用户的密码字段为可空（OAuth2用户可能没有密码）
ALTER TABLE users ALTER COLUMN password DROP NOT NULL;
