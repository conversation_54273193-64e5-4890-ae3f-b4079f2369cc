-- AI项目管理平台 - 用户管理服务数据库初始化脚本
-- 创建用户表和相关索引

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    -- 主键和基本信息
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url VARCHAR(500),
    phone VARCHAR(20),
    
    -- 状态和角色
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    role VARCHAR(20) NOT NULL DEFAULT 'USER',
    
    -- 邮箱验证
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    
    -- 密码重置
    password_reset_token VARCHAR(255),
    password_reset_expires_at TIMESTAMP,
    
    -- 登录信息
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(45),
    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
    locked_until TIMESTAMP,
    
    -- 审计字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    -- 约束检查
    CONSTRAINT chk_users_status CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED')),
    CONSTRAINT chk_users_role CHECK (role IN ('ADMIN', 'MANAGER', 'USER', 'VIEWER')),
    CONSTRAINT chk_users_username_length CHECK (LENGTH(username) >= 3),
    CONSTRAINT chk_users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_users_failed_attempts CHECK (failed_login_attempts >= 0)
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_last_login_at ON users(last_login_at);
CREATE INDEX IF NOT EXISTS idx_users_email_verification_token ON users(email_verification_token);
CREATE INDEX IF NOT EXISTS idx_users_password_reset_token ON users(password_reset_token);

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_users_status_role ON users(status, role);
CREATE INDEX IF NOT EXISTS idx_users_email_verified_status ON users(email_verified, status);

-- 创建更新时间自动更新的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 插入默认管理员用户
-- 密码: admin123 (BCrypt加密)
INSERT INTO users (
    username, 
    email, 
    password_hash, 
    full_name, 
    status, 
    role, 
    email_verified,
    created_at,
    updated_at
) VALUES (
    'admin',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPd.LhK9BMEym', -- admin123
    '系统管理员',
    'ACTIVE',
    'ADMIN',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (username) DO NOTHING;

-- 插入测试用户
INSERT INTO users (
    username, 
    email, 
    password_hash, 
    full_name, 
    status, 
    role, 
    email_verified,
    created_at,
    updated_at
) VALUES 
(
    'manager',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPd.LhK9BMEym', -- admin123
    '项目经理',
    'ACTIVE',
    'MANAGER',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'user',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPd.LhK9BMEym', -- admin123
    '普通用户',
    'ACTIVE',
    'USER',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'viewer',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPd.LhK9BMEym', -- admin123
    '查看者',
    'ACTIVE',
    'VIEWER',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (username) DO NOTHING;

-- 创建用户统计视图
CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_users,
    COUNT(CASE WHEN status = 'INACTIVE' THEN 1 END) as inactive_users,
    COUNT(CASE WHEN status = 'SUSPENDED' THEN 1 END) as suspended_users,
    COUNT(CASE WHEN email_verified = TRUE THEN 1 END) as verified_users,
    COUNT(CASE WHEN role = 'ADMIN' THEN 1 END) as admin_users,
    COUNT(CASE WHEN role = 'MANAGER' THEN 1 END) as manager_users,
    COUNT(CASE WHEN role = 'USER' THEN 1 END) as regular_users,
    COUNT(CASE WHEN role = 'VIEWER' THEN 1 END) as viewer_users,
    COUNT(CASE WHEN last_login_at > CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 1 END) as recent_active_users
FROM users;

-- 添加表注释
COMMENT ON TABLE users IS 'AI项目管理平台用户表';
COMMENT ON COLUMN users.id IS '用户唯一标识';
COMMENT ON COLUMN users.username IS '用户名，用于登录';
COMMENT ON COLUMN users.email IS '邮箱地址，用于登录和通知';
COMMENT ON COLUMN users.password_hash IS '密码哈希值';
COMMENT ON COLUMN users.full_name IS '用户全名';
COMMENT ON COLUMN users.avatar_url IS '头像图片URL';
COMMENT ON COLUMN users.phone IS '电话号码';
COMMENT ON COLUMN users.status IS '用户状态：ACTIVE-活跃，INACTIVE-非活跃，SUSPENDED-暂停';
COMMENT ON COLUMN users.role IS '用户角色：ADMIN-管理员，MANAGER-经理，USER-用户，VIEWER-查看者';
COMMENT ON COLUMN users.email_verified IS '邮箱是否已验证';
COMMENT ON COLUMN users.email_verification_token IS '邮箱验证令牌';
COMMENT ON COLUMN users.password_reset_token IS '密码重置令牌';
COMMENT ON COLUMN users.password_reset_expires_at IS '密码重置令牌过期时间';
COMMENT ON COLUMN users.last_login_at IS '最后登录时间';
COMMENT ON COLUMN users.last_login_ip IS '最后登录IP地址';
COMMENT ON COLUMN users.failed_login_attempts IS '登录失败次数';
COMMENT ON COLUMN users.locked_until IS '账户锁定截止时间';
COMMENT ON COLUMN users.created_at IS '创建时间';
COMMENT ON COLUMN users.updated_at IS '更新时间';
COMMENT ON COLUMN users.created_by IS '创建者用户ID';
COMMENT ON COLUMN users.updated_by IS '更新者用户ID';
