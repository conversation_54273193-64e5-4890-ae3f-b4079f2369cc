# OAuth2 配置文件
# 用于配置各种OAuth2提供商的客户端信息

spring:
  security:
    oauth2:
      client:
        registration:
          # Google OAuth2 配置
          google:
            client-id: ${GOOGLE_CLIENT_ID:your-google-client-id}
            client-secret: ${GOOGLE_CLIENT_SECRET:your-google-client-secret}
            scope:
              - openid
              - profile
              - email
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
            client-name: Google
            
          # GitHub OAuth2 配置
          github:
            client-id: ${GITHUB_CLIENT_ID:your-github-client-id}
            client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
            scope:
              - user:email
              - read:user
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
            client-name: GitHub
            
          # 自定义OAuth2提供商配置
          custom:
            client-id: ${CUSTOM_CLIENT_ID:your-custom-client-id}
            client-secret: ${CUSTOM_CLIENT_SECRET:your-custom-client-secret}
            scope:
              - openid
              - profile
              - email
            redirect-uri: "{baseUrl}/oauth2/callback/{registrationId}"
            client-name: "自定义ID Provider"
            authorization-grant-type: authorization_code
            client-authentication-method: client_secret_basic
            
        provider:
          # 自定义OAuth2提供商端点配置
          custom:
            authorization-uri: ${CUSTOM_AUTHORIZATION_URI:https://your-custom-provider.com/oauth2/authorize}
            token-uri: ${CUSTOM_TOKEN_URI:https://your-custom-provider.com/oauth2/token}
            user-info-uri: ${CUSTOM_USER_INFO_URI:https://your-custom-provider.com/oauth2/userinfo}
            user-name-attribute: ${CUSTOM_USER_NAME_ATTRIBUTE:sub}
            jwk-set-uri: ${CUSTOM_JWK_SET_URI:https://your-custom-provider.com/.well-known/jwks.json}

# 应用OAuth2配置
app:
  oauth2:
    # 授权重定向URI列表（前端回调地址）
    authorized-redirect-uris:
      - http://localhost:3000/oauth2/redirect
      - http://localhost:3000/login/oauth2/callback
      - https://your-frontend-domain.com/oauth2/redirect
      - https://your-frontend-domain.com/login/oauth2/callback
    
    # OAuth2用户信息映射配置
    user-info-mapping:
      # Google用户信息字段映射
      google:
        id-attribute: sub
        name-attribute: name
        email-attribute: email
        picture-attribute: picture
        
      # GitHub用户信息字段映射
      github:
        id-attribute: id
        name-attribute: name
        email-attribute: email
        picture-attribute: avatar_url
        
      # 自定义提供商用户信息字段映射
      custom:
        id-attribute: ${CUSTOM_ID_ATTRIBUTE:id}
        name-attribute: ${CUSTOM_NAME_ATTRIBUTE:name}
        email-attribute: ${CUSTOM_EMAIL_ATTRIBUTE:email}
        picture-attribute: ${CUSTOM_PICTURE_ATTRIBUTE:avatar}

# 日志配置
logging:
  level:
    org.springframework.security.oauth2: DEBUG
    com.aipm.usermanagement.security.oauth2: DEBUG
