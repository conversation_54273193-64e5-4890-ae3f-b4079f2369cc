# AI项目管理平台 - 用户管理服务配置文件
# 提供开发、测试、生产环境的统一配置管理

# ============================================================================
# 服务基本配置
# ============================================================================
server:
  port: 8080
  servlet:
    context-path: /api/v1
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
  error:
    include-message: always
    include-binding-errors: always

# ============================================================================
# Spring 框架配置
# ============================================================================
spring:
  application:
    name: user-management-service
  
  # 环境配置
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    url: *****************************************
    username: dev_user
    password: dev_password_123
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: UserManagementHikariCP
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      leak-detection-threshold: 60000
  
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: validate # 生产环境使用validate，开发环境可使用update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      password: redis_password_123
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # Jackson 配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时
      cache-null-values: false

# ============================================================================
# 安全配置
# ============================================================================
security:
  jwt:
    secret: aipm-user-management-jwt-secret-key-2025
    expiration: 86400000 # 24小时（毫秒）
    refresh-expiration: 604800000 # 7天（毫秒）
    issuer: aipm-user-service
  
  # 密码策略配置
  password:
    min-length: 8
    require-uppercase: true
    require-lowercase: true
    require-digit: true
    require-special-char: false
    max-attempts: 5
    lockout-duration: 300000 # 5分钟（毫秒）

# ============================================================================
# gRPC 配置
# ============================================================================
grpc:
  server:
    port: 9090
    enable-reflection: true
  client:
    # 其他服务的gRPC客户端配置
    project-service:
      address: static://localhost:9091
      negotiation-type: plaintext

# ============================================================================
# 监控和管理配置
# ============================================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active}

# ============================================================================
# 日志配置
# ============================================================================
logging:
  level:
    com.aipm.usermanagement: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/user-management.log
    max-size: 100MB
    max-history: 30

# ============================================================================
# API 文档配置
# ============================================================================
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operations-sorter: method
    tags-sorter: alpha
    doc-expansion: none
  info:
    title: AI项目管理平台 - 用户管理服务API
    description: 提供用户认证、授权、用户信息管理等功能的RESTful API
    version: 1.0.0
    contact:
      name: AI项目管理平台开发团队
      email: <EMAIL>
    license:
      name: MIT License
      url: https://opensource.org/licenses/MIT

# ============================================================================
# 应用自定义配置
# ============================================================================
app:
  # 用户配置
  user:
    default-role: USER
    email-verification-required: true
    registration-enabled: true
    max-login-attempts: 5
  
  # 邮件配置
  mail:
    enabled: true
    from: <EMAIL>
    templates:
      welcome: classpath:templates/email/welcome.html
      password-reset: classpath:templates/email/password-reset.html
      verification: classpath:templates/email/verification.html
  
  # 文件上传配置
  upload:
    avatar:
      max-size: 5MB
      allowed-types: jpg,jpeg,png,gif
      path: uploads/avatars/
  
  # 国际化配置
  i18n:
    default-locale: zh_CN
    supported-locales: zh_CN,en_US

---
# ============================================================================
# 开发环境配置
# ============================================================================
spring:
  config:
    activate:
      on-profile: dev
  
  # 开发环境数据源
  datasource:
    url: *****************************************
    username: dev_user
    password: dev_password_123
  
  # 开发环境JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  
  # 开发环境Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: redis_password_123

# 开发环境日志配置
logging:
  level:
    com.aipm.usermanagement: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG

---
# ============================================================================
# 测试环境配置
# ============================================================================
spring:
  config:
    activate:
      on-profile: test
  
  # 测试环境使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # 测试环境JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
  
  # 测试环境Redis配置（使用嵌入式Redis）
  data:
    redis:
      host: localhost
      port: 6370

# 测试环境安全配置
security:
  jwt:
    secret: test-jwt-secret-key
    expiration: 3600000 # 1小时

---
# ============================================================================
# 生产环境配置
# ============================================================================
spring:
  config:
    activate:
      on-profile: prod
  
  # 生产环境数据源（从环境变量获取）
  datasource:
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
  
  # 生产环境JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  
  # 生产环境Redis配置
  data:
    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      password: ${REDIS_PASSWORD}

# 生产环境安全配置
security:
  jwt:
    secret: ${JWT_SECRET}
    expiration: ${JWT_EXPIRATION:86400000}

# 生产环境日志配置
logging:
  level:
    com.aipm.usermanagement: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
