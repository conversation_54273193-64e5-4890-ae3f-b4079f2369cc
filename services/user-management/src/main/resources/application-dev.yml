# AI项目管理平台 - 用户管理服务开发环境配置

# ============================================================================
# 服务配置
# ============================================================================
server:
  port: 8080
  servlet:
    context-path: /api/v1

# ============================================================================
# Spring 配置
# ============================================================================
spring:
  application:
    name: user-management-service
  
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    url: *****************************************
    username: dev_user
    password: dev_password_123
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: UserManagementHikariCP
      maximum-pool-size: 10
      minimum-idle: 2
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
  
  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      password: redis_password_123
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # Jackson 配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false

# ============================================================================
# 安全配置
# ============================================================================
security:
  jwt:
    secret: aipm-user-management-jwt-secret-key-dev-2025
    expiration: 86400000 # 24小时
    refresh-expiration: 604800000 # 7天
    issuer: aipm-user-service-dev

# ============================================================================
# 日志配置
# ============================================================================
logging:
  level:
    com.aipm.usermanagement: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"

# ============================================================================
# 监控配置
# ============================================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
      base-path: /actuator
  endpoint:
    health:
      show-details: always

# ============================================================================
# API 文档配置
# ============================================================================
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html

# ============================================================================
# 应用自定义配置
# ============================================================================
app:
  user:
    default-role: USER
    email-verification-required: false # 开发环境不强制邮箱验证
    registration-enabled: true
    max-login-attempts: 5
