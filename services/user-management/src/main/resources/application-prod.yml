# 生产环境OAuth2配置文件

spring:
  # 数据源配置
  datasource:
    url: ${DATABASE_URL:******************************************}
    username: ${DATABASE_USERNAME:aipm_user}
    password: ${DATABASE_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
  
  # OAuth2生产配置
  security:
    oauth2:
      client:
        registration:
          # Google OAuth2生产配置
          google:
            client-id: ${GOOGLE_CLIENT_ID}
            client-secret: ${GOOGLE_CLIENT_SECRET}
            scope:
              - openid
              - profile
              - email
            redirect-uri: "${APP_BASE_URL}/oauth2/callback/{registrationId}"
            client-name: Google
            
          # GitHub OAuth2生产配置
          github:
            client-id: ${GITHUB_CLIENT_ID}
            client-secret: ${GITHUB_CLIENT_SECRET}
            scope:
              - user:email
              - read:user
            redirect-uri: "${APP_BASE_URL}/oauth2/callback/{registrationId}"
            client-name: GitHub
            
          # 自定义OAuth2提供商生产配置
          custom:
            client-id: ${CUSTOM_CLIENT_ID}
            client-secret: ${CUSTOM_CLIENT_SECRET}
            scope:
              - openid
              - profile
              - email
            redirect-uri: "${APP_BASE_URL}/oauth2/callback/{registrationId}"
            client-name: "${CUSTOM_PROVIDER_NAME:自定义ID Provider}"
            authorization-grant-type: authorization_code
            client-authentication-method: client_secret_basic
            
        provider:
          # 自定义OAuth2提供商端点配置
          custom:
            authorization-uri: ${CUSTOM_AUTHORIZATION_URI}
            token-uri: ${CUSTOM_TOKEN_URI}
            user-info-uri: ${CUSTOM_USER_INFO_URI}
            user-name-attribute: ${CUSTOM_USER_NAME_ATTRIBUTE:sub}
            jwk-set-uri: ${CUSTOM_JWK_SET_URI}

# 应用OAuth2生产配置
app:
  oauth2:
    # 生产环境授权重定向URI列表
    authorized-redirect-uris:
      - ${FRONTEND_URL}/oauth2/redirect
      - ${FRONTEND_URL}/login/oauth2/callback
      - ${APP_BASE_URL}/oauth2/redirect
    
    # OAuth2用户信息映射配置
    user-info-mapping:
      google:
        id-attribute: sub
        name-attribute: name
        email-attribute: email
        picture-attribute: picture
      github:
        id-attribute: id
        name-attribute: name
        email-attribute: email
        picture-attribute: avatar_url
      custom:
        id-attribute: ${CUSTOM_ID_ATTRIBUTE:id}
        name-attribute: ${CUSTOM_NAME_ATTRIBUTE:name}
        email-attribute: ${CUSTOM_EMAIL_ATTRIBUTE:email}
        picture-attribute: ${CUSTOM_PICTURE_ATTRIBUTE:avatar}

# JWT生产配置
security:
  jwt:
    secret: ${JWT_SECRET}
    access-token-expiration: ${JWT_ACCESS_TOKEN_EXPIRATION:3600000}  # 1小时
    refresh-token-expiration: ${JWT_REFRESH_TOKEN_EXPIRATION:604800000} # 7天

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:}
  # SSL配置（如果使用HTTPS）
  ssl:
    enabled: ${SSL_ENABLED:false}
    key-store: ${SSL_KEY_STORE:}
    key-store-password: ${SSL_KEY_STORE_PASSWORD:}
    key-store-type: ${SSL_KEY_STORE_TYPE:PKCS12}
    key-alias: ${SSL_KEY_ALIAS:}

# 日志配置
logging:
  level:
    root: INFO
    com.aipm.usermanagement: INFO
    org.springframework.security.oauth2: WARN
    org.springframework.web: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:/var/log/aipm/user-management.log}
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
  health:
    redis:
      enabled: true
    db:
      enabled: true

# 跨域配置
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:${FRONTEND_URL}}
  allowed-methods: GET,POST,PUT,DELETE,PATCH,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600
