package com.aipm.usermanagement.security.oauth2;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.repository.UserRepository;
import com.aipm.usermanagement.security.JwtTokenProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OAuth2端到端测试
 * 
 * 测试完整的OAuth2认证流程
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.oauth2.authorized-redirect-uris=http://localhost:3000/oauth2/redirect,http://localhost:8080/test/callback",
    "spring.security.oauth2.client.registration.google.client-id=test-google-client-id",
    "spring.security.oauth2.client.registration.google.client-secret=test-google-client-secret",
    "spring.security.oauth2.client.registration.github.client-id=test-github-client-id",
    "spring.security.oauth2.client.registration.github.client-secret=test-github-client-secret",
    "spring.security.oauth2.client.registration.custom.client-id=test-custom-client-id",
    "spring.security.oauth2.client.registration.custom.client-secret=test-custom-client-secret"
})
@Transactional
class OAuth2EndToEndTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    private String baseUrl;

    @BeforeEach
    void setUp() {
        baseUrl = "http://localhost:" + port;
        userRepository.deleteAll();
    }

    @Test
    @DisplayName("测试获取OAuth2提供商列表")
    void testGetOAuth2Providers() {
        String url = baseUrl + "/api/v1/oauth2/providers";
        
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertTrue((Boolean) body.get("success"));
        assertNotNull(body.get("data"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) body.get("data");
        assertNotNull(data.get("providers"));
        assertNotNull(data.get("redirectUris"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> providers = (Map<String, Object>) data.get("providers");
        
        // 验证配置的提供商存在
        assertTrue(providers.containsKey("google") || 
                  providers.containsKey("github") || 
                  providers.containsKey("custom"));
    }

    @Test
    @DisplayName("测试OAuth2认证状态检查")
    void testOAuth2StatusCheck() {
        String url = baseUrl + "/api/v1/oauth2/status";
        
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertTrue((Boolean) body.get("success"));
        assertNotNull(body.get("data"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) body.get("data");
        assertNotNull(data.get("authenticated"));
        
        // 未认证状态下应该返回false
        assertFalse((Boolean) data.get("authenticated"));
    }

    @Test
    @DisplayName("测试Google OAuth2授权URL生成")
    void testGoogleAuthorizationUrlGeneration() {
        String url = baseUrl + "/api/v1/oauth2/authorization-url/google";
        
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertTrue((Boolean) body.get("success"));
        assertNotNull(body.get("data"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) body.get("data");
        assertNotNull(data.get("authorizationUrl"));
        assertEquals("google", data.get("provider"));
        
        String authUrl = (String) data.get("authorizationUrl");
        assertTrue(authUrl.contains("/oauth2/authorization/google"));
    }

    @Test
    @DisplayName("测试GitHub OAuth2授权URL生成")
    void testGitHubAuthorizationUrlGeneration() {
        String url = baseUrl + "/api/v1/oauth2/authorization-url/github?redirect_uri=http://localhost:3000/oauth2/redirect";
        
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertTrue((Boolean) body.get("success"));
        assertNotNull(body.get("data"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) body.get("data");
        assertNotNull(data.get("authorizationUrl"));
        assertEquals("github", data.get("provider"));
    }

    @Test
    @DisplayName("测试自定义OAuth2授权URL生成")
    void testCustomAuthorizationUrlGeneration() {
        String url = baseUrl + "/api/v1/oauth2/authorization-url/custom";
        
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertTrue((Boolean) body.get("success"));
        assertNotNull(body.get("data"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) body.get("data");
        assertNotNull(data.get("authorizationUrl"));
        assertEquals("custom", data.get("provider"));
    }

    @Test
    @DisplayName("测试不存在的OAuth2提供商")
    void testNonExistentOAuth2Provider() {
        String url = baseUrl + "/api/v1/oauth2/authorization-url/nonexistent";
        
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        
        Map<String, Object> body = response.getBody();
        assertFalse((Boolean) body.get("success"));
        assertNotNull(body.get("message"));
    }

    @Test
    @DisplayName("测试OAuth2重定向端点访问")
    void testOAuth2RedirectEndpointAccess() {
        // 测试OAuth2授权端点是否可访问（不会实际重定向，但应该返回302）
        String url = baseUrl + "/oauth2/authorization/google";
        
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        // OAuth2授权端点应该重定向到提供商
        assertTrue(response.getStatusCode().is3xxRedirection() || 
                  response.getStatusCode().is4xxClientError());
    }

    @Test
    @DisplayName("测试OAuth2回调端点访问")
    void testOAuth2CallbackEndpointAccess() {
        // 测试OAuth2回调端点（模拟认证失败的情况）
        String url = baseUrl + "/oauth2/callback/google?error=access_denied";
        
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        // 回调端点应该处理错误并重定向
        assertTrue(response.getStatusCode().is3xxRedirection() || 
                  response.getStatusCode().is2xxSuccessful());
    }

    @Test
    @DisplayName("测试JWT令牌生成和验证")
    void testJwtTokenGenerationAndValidation() {
        // 创建测试用户
        User testUser = new User();
        testUser.setUsername("oauth2_test_user");
        testUser.setEmail("<EMAIL>");
        testUser.setFullName("OAuth2测试用户");
        testUser.setOauth2Provider("google");
        testUser.setOauth2ProviderId("google-test-123");
        
        // 生成JWT令牌
        String accessToken = jwtTokenProvider.generateAccessToken(testUser);
        String refreshToken = jwtTokenProvider.generateRefreshToken(testUser);
        
        assertNotNull(accessToken);
        assertNotNull(refreshToken);
        
        // 验证令牌
        assertTrue(jwtTokenProvider.validateToken(accessToken));
        assertTrue(jwtTokenProvider.validateToken(refreshToken));
        
        // 验证令牌类型
        assertTrue(jwtTokenProvider.isAccessToken(accessToken));
        assertTrue(jwtTokenProvider.isRefreshToken(refreshToken));
        
        // 验证用户信息
        String usernameFromToken = jwtTokenProvider.getUsernameFromToken(accessToken);
        assertEquals("oauth2_test_user", usernameFromToken);
    }

    @Test
    @DisplayName("测试OAuth2用户主体创建")
    void testOAuth2UserPrincipalCreation() {
        // 创建测试用户
        User testUser = new User();
        testUser.setUsername("oauth2_principal_test");
        testUser.setEmail("<EMAIL>");
        testUser.setFullName("主体测试用户");
        testUser.setOauth2Provider("github");
        testUser.setOauth2ProviderId("github-test-456");
        
        // 创建OAuth2用户属性
        Map<String, Object> attributes = Map.of(
            "id", "github-test-456",
            "name", "主体测试用户",
            "email", "<EMAIL>",
            "avatar_url", "https://github.com/avatar.jpg"
        );
        
        // 创建OAuth2用户主体
        CustomOAuth2UserPrincipal principal = new CustomOAuth2UserPrincipal(testUser, attributes);
        
        assertNotNull(principal);
        assertEquals("oauth2_principal_test", principal.getName());
        assertEquals("<EMAIL>", principal.getEmail());
        assertEquals("主体测试用户", principal.getFullName());
        assertEquals(testUser, principal.getUser());
        assertEquals(attributes, principal.getAttributes());
    }
}
