package com.aipm.usermanagement.security.oauth2;

import com.aipm.usermanagement.entity.User;
import com.aipm.usermanagement.entity.UserRole;
import com.aipm.usermanagement.repository.UserRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * OAuth2集成测试
 * 
 * 测试OAuth2认证流程和用户信息处理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.security.oauth2.client.registration.google.client-id=test-google-client-id",
    "spring.security.oauth2.client.registration.google.client-secret=test-google-client-secret",
    "spring.security.oauth2.client.registration.github.client-id=test-github-client-id",
    "spring.security.oauth2.client.registration.github.client-secret=test-github-client-secret"
})
@Transactional
class OAuth2IntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CustomOAuth2UserService customOAuth2UserService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ClientRegistrationRepository clientRegistrationRepository;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        userRepository.deleteAll();
    }

    @Test
    @DisplayName("测试Google OAuth2用户信息处理")
    void testGoogleOAuth2UserProcessing() {
        // 准备Google OAuth2用户数据
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("sub", "google-user-123");
        attributes.put("name", "张三");
        attributes.put("email", "<EMAIL>");
        attributes.put("picture", "https://lh3.googleusercontent.com/avatar.jpg");

        // 测试用户信息工厂
        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo("google", attributes);
        
        assertNotNull(userInfo);
        assertEquals("google-user-123", userInfo.getId());
        assertEquals("张三", userInfo.getName());
        assertEquals("<EMAIL>", userInfo.getEmail());
        assertEquals("https://lh3.googleusercontent.com/avatar.jpg", userInfo.getImageUrl());
    }

    @Test
    @DisplayName("测试GitHub OAuth2用户信息处理")
    void testGitHubOAuth2UserProcessing() {
        // 准备GitHub OAuth2用户数据
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("id", 12345);
        attributes.put("name", "李四");
        attributes.put("email", "<EMAIL>");
        attributes.put("avatar_url", "https://avatars.githubusercontent.com/u/12345");

        // 测试用户信息工厂
        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo("github", attributes);
        
        assertNotNull(userInfo);
        assertEquals("12345", userInfo.getId());
        assertEquals("李四", userInfo.getName());
        assertEquals("<EMAIL>", userInfo.getEmail());
        assertEquals("https://avatars.githubusercontent.com/u/12345", userInfo.getImageUrl());
    }

    @Test
    @DisplayName("测试自定义OAuth2用户信息处理")
    void testCustomOAuth2UserProcessing() {
        // 准备自定义OAuth2用户数据
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("id", "custom-user-456");
        attributes.put("name", "王五");
        attributes.put("email", "<EMAIL>");
        attributes.put("avatar", "https://custom.com/avatars/456.jpg");

        // 测试用户信息工厂
        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo("custom", attributes);
        
        assertNotNull(userInfo);
        assertEquals("custom-user-456", userInfo.getId());
        assertEquals("王五", userInfo.getName());
        assertEquals("<EMAIL>", userInfo.getEmail());
        assertEquals("https://custom.com/avatars/456.jpg", userInfo.getImageUrl());
    }

    @Test
    @DisplayName("测试新用户自动注册")
    void testNewUserAutoRegistration() {
        // 准备OAuth2用户数据
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("sub", "new-user-789");
        attributes.put("name", "新用户");
        attributes.put("email", "<EMAIL>");
        attributes.put("picture", "https://example.com/avatar.jpg");

        OAuth2User oauth2User = new DefaultOAuth2User(
            List.of(() -> "ROLE_USER"),
            attributes,
            "sub"
        );

        // 验证用户不存在
        Optional<User> existingUser = userRepository.findByEmail("<EMAIL>");
        assertFalse(existingUser.isPresent());

        // 模拟OAuth2用户处理（这里需要模拟OAuth2UserRequest）
        // 实际测试中需要创建完整的OAuth2UserRequest对象
        
        // 验证用户创建逻辑
        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo("google", attributes);
        assertNotNull(userInfo);
        assertEquals("<EMAIL>", userInfo.getEmail());
    }

    @Test
    @DisplayName("测试现有用户信息更新")
    void testExistingUserUpdate() {
        // 创建现有用户
        User existingUser = new User();
        existingUser.setUsername("existing_user");
        existingUser.setEmail("<EMAIL>");
        existingUser.setFullName("旧姓名");
        existingUser.setRole(UserRole.USER);
        existingUser.setEnabled(true);
        existingUser.setAccountNonExpired(true);
        existingUser.setAccountNonLocked(true);
        existingUser.setCredentialsNonExpired(true);
        existingUser.setEmailVerified(true);
        
        userRepository.save(existingUser);

        // 准备更新的OAuth2用户数据
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("sub", "existing-user-123");
        attributes.put("name", "新姓名");
        attributes.put("email", "<EMAIL>");
        attributes.put("picture", "https://example.com/new-avatar.jpg");

        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo("google", attributes);
        
        // 验证用户信息
        assertEquals("<EMAIL>", userInfo.getEmail());
        assertEquals("新姓名", userInfo.getName());
        assertEquals("https://example.com/new-avatar.jpg", userInfo.getImageUrl());
    }

    @Test
    @DisplayName("测试OAuth2提供商API端点")
    void testOAuth2ProvidersEndpoint() throws Exception {
        mockMvc.perform(get("/api/v1/oauth2/providers"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.providers").exists())
                .andExpect(jsonPath("$.data.redirectUris").exists());
    }

    @Test
    @DisplayName("测试OAuth2状态检查端点")
    void testOAuth2StatusEndpoint() throws Exception {
        mockMvc.perform(get("/api/v1/oauth2/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.authenticated").exists());
    }

    @Test
    @DisplayName("测试OAuth2授权URL生成")
    void testOAuth2AuthorizationUrlGeneration() throws Exception {
        mockMvc.perform(get("/api/v1/oauth2/authorization-url/google")
                .param("redirect_uri", "http://localhost:3000/oauth2/redirect"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.authorizationUrl").exists())
                .andExpect(jsonPath("$.data.provider").value("google"));
    }

    @Test
    @DisplayName("测试不支持的OAuth2提供商")
    void testUnsupportedOAuth2Provider() {
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("id", "test-id");
        
        assertThrows(Exception.class, () -> {
            OAuth2UserInfoFactory.getOAuth2UserInfo("unsupported", attributes);
        });
    }

    @Test
    @DisplayName("测试OAuth2用户信息缺少邮箱")
    void testOAuth2UserInfoMissingEmail() {
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("sub", "user-without-email");
        attributes.put("name", "无邮箱用户");
        // 故意不添加email字段

        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo("google", attributes);
        
        assertNotNull(userInfo);
        assertNull(userInfo.getEmail());
    }
}
