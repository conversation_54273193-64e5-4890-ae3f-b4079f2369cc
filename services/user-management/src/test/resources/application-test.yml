# OAuth2测试配置文件

spring:
  # 数据源配置 - 使用H2内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  # Redis配置 - 使用嵌入式Redis进行测试
  data:
    redis:
      host: localhost
      port: 6370
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # OAuth2测试配置
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: test-google-client-id
            client-secret: test-google-client-secret
            scope:
              - openid
              - profile
              - email
            redirect-uri: "http://localhost:8080/oauth2/callback/{registrationId}"
            client-name: Google
            
          github:
            client-id: test-github-client-id
            client-secret: test-github-client-secret
            scope:
              - user:email
              - read:user
            redirect-uri: "http://localhost:8080/oauth2/callback/{registrationId}"
            client-name: GitHub
            
          custom:
            client-id: test-custom-client-id
            client-secret: test-custom-client-secret
            scope:
              - openid
              - profile
              - email
            redirect-uri: "http://localhost:8080/oauth2/callback/{registrationId}"
            client-name: "测试自定义Provider"
            authorization-grant-type: authorization_code
            client-authentication-method: client_secret_basic
            
        provider:
          custom:
            authorization-uri: https://test-provider.com/oauth2/authorize
            token-uri: https://test-provider.com/oauth2/token
            user-info-uri: https://test-provider.com/oauth2/userinfo
            user-name-attribute: sub

# 应用配置
app:
  oauth2:
    authorized-redirect-uris:
      - http://localhost:3000/oauth2/redirect
      - http://localhost:8080/test/callback
    user-info-mapping:
      google:
        id-attribute: sub
        name-attribute: name
        email-attribute: email
        picture-attribute: picture
      github:
        id-attribute: id
        name-attribute: name
        email-attribute: email
        picture-attribute: avatar_url
      custom:
        id-attribute: id
        name-attribute: name
        email-attribute: email
        picture-attribute: avatar

# JWT配置
security:
  jwt:
    secret: test-jwt-secret-key-for-oauth2-integration-testing-purposes-only
    access-token-expiration: 3600000  # 1小时
    refresh-token-expiration: 86400000 # 24小时

# 日志配置
logging:
  level:
    org.springframework.security.oauth2: DEBUG
    com.aipm.usermanagement.security.oauth2: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
