/**
 * 集成服务控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package com.aipm.integration.controller;

import com.aipm.integration.model.*;
import com.aipm.integration.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/integrations")
@RequiredArgsConstructor
@Tag(name = "集成服务", description = "第三方工具集成API")
public class IntegrationController {

    private final JiraIntegrationService jiraService;
    private final DingTalkIntegrationService dingTalkService;
    private final SlackIntegrationService slackService;
    private final CiCdIntegrationService ciCdService;

    // ==================== Jira集成 ====================

    @PostMapping("/jira/test-connection")
    @Operation(summary = "测试Jira连接")
    public ResponseEntity<Map<String, Object>> testJiraConnection(@RequestBody JiraConfig config) {
        try {
            boolean success = jiraService.testConnection(config);
            return ResponseEntity.ok(Map.of(
                "success", success,
                "message", success ? "连接成功" : "连接失败"
            ));
        } catch (Exception e) {
            log.error("测试Jira连接失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "连接失败: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/jira/sync-projects")
    @Operation(summary = "同步Jira项目")
    public ResponseEntity<SyncResult> syncJiraProjects(@RequestBody JiraConfig config) {
        SyncResult result = jiraService.syncProjects(config);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/jira/sync-issues")
    @Operation(summary = "同步Jira问题")
    public ResponseEntity<SyncResult> syncJiraIssues(@RequestBody JiraConfig config, 
                                                   @RequestParam String projectKey) {
        SyncResult result = jiraService.syncIssues(config, projectKey);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/jira/create-issue")
    @Operation(summary = "创建Jira问题")
    public ResponseEntity<JiraIssue> createJiraIssue(@RequestBody JiraConfig config, 
                                                    @RequestBody JiraIssue issue) {
        JiraIssue createdIssue = jiraService.createIssue(config, issue);
        return ResponseEntity.ok(createdIssue);
    }

    @PostMapping("/jira/webhook")
    @Operation(summary = "处理Jira Webhook")
    public ResponseEntity<String> handleJiraWebhook(@RequestBody Map<String, Object> event) {
        jiraService.handleWebhookEvent(event);
        return ResponseEntity.ok("OK");
    }

    // ==================== 钉钉集成 ====================

    @PostMapping("/dingtalk/test-connection")
    @Operation(summary = "测试钉钉连接")
    public ResponseEntity<Map<String, Object>> testDingTalkConnection(@RequestBody DingTalkConfig config) {
        try {
            boolean success = dingTalkService.testConnection(config);
            return ResponseEntity.ok(Map.of(
                "success", success,
                "message", success ? "连接成功" : "连接失败"
            ));
        } catch (Exception e) {
            log.error("测试钉钉连接失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "连接失败: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/dingtalk/send-message")
    @Operation(summary = "发送钉钉消息")
    public ResponseEntity<Map<String, Object>> sendDingTalkMessage(
            @RequestBody DingTalkConfig config,
            @RequestParam String content,
            @RequestParam List<String> userIds) {
        try {
            boolean success = dingTalkService.sendTextMessage(config, content, userIds);
            return ResponseEntity.ok(Map.of(
                "success", success,
                "message", success ? "发送成功" : "发送失败"
            ));
        } catch (Exception e) {
            log.error("发送钉钉消息失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "发送失败: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/dingtalk/send-project-notification")
    @Operation(summary = "发送项目通知到钉钉")
    public ResponseEntity<Map<String, Object>> sendDingTalkProjectNotification(
            @RequestBody DingTalkConfig config,
            @RequestParam String projectName,
            @RequestParam String action,
            @RequestParam String details,
            @RequestParam List<String> userIds) {
        try {
            boolean success = dingTalkService.sendProjectNotification(config, projectName, action, details, userIds);
            return ResponseEntity.ok(Map.of(
                "success", success,
                "message", success ? "发送成功" : "发送失败"
            ));
        } catch (Exception e) {
            log.error("发送钉钉项目通知失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "发送失败: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/dingtalk/callback")
    @Operation(summary = "处理钉钉回调")
    public ResponseEntity<String> handleDingTalkCallback(@RequestBody Map<String, Object> callbackData) {
        dingTalkService.handleCallback(callbackData);
        return ResponseEntity.ok("OK");
    }

    // ==================== Slack集成 ====================

    @PostMapping("/slack/test-connection")
    @Operation(summary = "测试Slack连接")
    public ResponseEntity<Map<String, Object>> testSlackConnection(@RequestBody SlackConfig config) {
        try {
            boolean success = slackService.testConnection(config);
            return ResponseEntity.ok(Map.of(
                "success", success,
                "message", success ? "连接成功" : "连接失败"
            ));
        } catch (Exception e) {
            log.error("测试Slack连接失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "连接失败: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/slack/send-message")
    @Operation(summary = "发送Slack消息")
    public ResponseEntity<Map<String, Object>> sendSlackMessage(
            @RequestBody SlackConfig config,
            @RequestParam String channel,
            @RequestParam String text) {
        try {
            boolean success = slackService.sendTextMessage(config, channel, text);
            return ResponseEntity.ok(Map.of(
                "success", success,
                "message", success ? "发送成功" : "发送失败"
            ));
        } catch (Exception e) {
            log.error("发送Slack消息失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "发送失败: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/slack/send-project-notification")
    @Operation(summary = "发送项目通知到Slack")
    public ResponseEntity<Map<String, Object>> sendSlackProjectNotification(
            @RequestBody SlackConfig config,
            @RequestParam String channel,
            @RequestParam String projectName,
            @RequestParam String action,
            @RequestParam String details,
            @RequestParam String userId) {
        try {
            boolean success = slackService.sendProjectNotification(config, channel, projectName, action, details, userId);
            return ResponseEntity.ok(Map.of(
                "success", success,
                "message", success ? "发送成功" : "发送失败"
            ));
        } catch (Exception e) {
            log.error("发送Slack项目通知失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "发送失败: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/slack/channels")
    @Operation(summary = "获取Slack频道列表")
    public ResponseEntity<List<Map<String, Object>>> getSlackChannels(@RequestBody SlackConfig config) {
        List<Map<String, Object>> channels = slackService.getChannels(config);
        return ResponseEntity.ok(channels);
    }

    @PostMapping("/slack/callback")
    @Operation(summary = "处理Slack回调")
    public ResponseEntity<String> handleSlackCallback(@RequestBody Map<String, Object> eventData) {
        slackService.handleEventCallback(eventData);
        return ResponseEntity.ok("OK");
    }

    // ==================== CI/CD集成 ====================

    @PostMapping("/cicd/test-connection")
    @Operation(summary = "测试CI/CD连接")
    public ResponseEntity<Map<String, Object>> testCiCdConnection(@RequestBody CiCdConfig config) {
        try {
            boolean success = ciCdService.testConnection(config);
            return ResponseEntity.ok(Map.of(
                "success", success,
                "message", success ? "连接成功" : "连接失败"
            ));
        } catch (Exception e) {
            log.error("测试CI/CD连接失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "连接失败: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/cicd/builds")
    @Operation(summary = "获取构建列表")
    public ResponseEntity<List<BuildInfo>> getBuilds(@RequestBody CiCdConfig config, 
                                                    @RequestParam String projectName) {
        List<BuildInfo> builds = ciCdService.getBuilds(config, projectName);
        return ResponseEntity.ok(builds);
    }

    @PostMapping("/cicd/trigger-build")
    @Operation(summary = "触发构建")
    public ResponseEntity<BuildInfo> triggerBuild(@RequestBody CiCdConfig config,
                                                 @RequestParam String projectName,
                                                 @RequestBody Map<String, String> parameters) {
        BuildInfo build = ciCdService.triggerBuild(config, projectName, parameters);
        return ResponseEntity.ok(build);
    }

    @GetMapping("/cicd/build-stats")
    @Operation(summary = "获取构建统计")
    public ResponseEntity<Map<String, Object>> getBuildStats(@RequestBody CiCdConfig config,
                                                           @RequestParam String projectName,
                                                           @RequestParam(defaultValue = "30") int days) {
        Map<String, Object> stats = ciCdService.getBuildStats(config, projectName, days);
        return ResponseEntity.ok(stats);
    }

    @PostMapping("/cicd/webhook")
    @Operation(summary = "处理CI/CD Webhook")
    public ResponseEntity<String> handleCiCdWebhook(@RequestBody CiCdConfig config,
                                                   @RequestBody Map<String, Object> event) {
        ciCdService.handleWebhookEvent(config, event);
        return ResponseEntity.ok("OK");
    }

    // ==================== 通用接口 ====================

    @GetMapping("/health")
    @Operation(summary = "健康检查")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "integration-service",
            "timestamp", System.currentTimeMillis()
        ));
    }
}
