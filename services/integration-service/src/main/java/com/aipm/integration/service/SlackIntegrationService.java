/**
 * Slack集成服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package com.aipm.integration.service;

import com.aipm.integration.client.SlackClient;
import com.aipm.integration.model.SlackConfig;
import com.aipm.integration.model.SlackMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SlackIntegrationService {

    private final SlackClient slackClient;

    /**
     * 测试Slack连接
     */
    public boolean testConnection(SlackConfig config) {
        try {
            log.info("测试Slack连接");
            return slackClient.testConnection(config);
        } catch (Exception e) {
            log.error("Slack连接测试失败", e);
            return false;
        }
    }

    /**
     * 发送简单文本消息
     */
    public boolean sendTextMessage(SlackConfig config, String channel, String text) {
        try {
            log.info("发送Slack文本消息到频道: {}", channel);
            
            SlackMessage message = SlackMessage.builder()
                .channel(channel)
                .text(text)
                .build();
            
            return slackClient.sendMessage(config, message);
        } catch (Exception e) {
            log.error("发送Slack文本消息失败", e);
            return false;
        }
    }

    /**
     * 发送富文本消息
     */
    public boolean sendRichMessage(SlackConfig config, String channel, String text, 
                                 List<Map<String, Object>> attachments) {
        try {
            log.info("发送Slack富文本消息到频道: {}", channel);
            
            SlackMessage message = SlackMessage.builder()
                .channel(channel)
                .text(text)
                .attachments(attachments)
                .build();
            
            return slackClient.sendMessage(config, message);
        } catch (Exception e) {
            log.error("发送Slack富文本消息失败", e);
            return false;
        }
    }

    /**
     * 发送项目通知
     */
    public boolean sendProjectNotification(SlackConfig config, String channel, String projectName, 
                                         String action, String details, String userId) {
        try {
            String text = String.format("项目通知: %s", projectName);
            
            List<Map<String, Object>> attachments = List.of(
                Map.of(
                    "color", getColorByAction(action),
                    "title", String.format("项目: %s", projectName),
                    "fields", List.of(
                        Map.of("title", "操作", "value", action, "short", true),
                        Map.of("title", "详情", "value", details, "short", true),
                        Map.of("title", "时间", "value", getCurrentTimestamp(), "short", true)
                    ),
                    "footer", "AI项目管理平台",
                    "ts", System.currentTimeMillis() / 1000
                )
            );
            
            return sendRichMessage(config, channel, text, attachments);
        } catch (Exception e) {
            log.error("发送项目通知失败", e);
            return false;
        }
    }

    /**
     * 发送任务通知
     */
    public boolean sendTaskNotification(SlackConfig config, String channel, String taskName, 
                                      String status, String assignee) {
        try {
            String text = String.format("任务更新: %s", taskName);
            
            List<Map<String, Object>> attachments = List.of(
                Map.of(
                    "color", getColorByStatus(status),
                    "title", String.format("任务: %s", taskName),
                    "fields", List.of(
                        Map.of("title", "状态", "value", status, "short", true),
                        Map.of("title", "负责人", "value", assignee, "short", true),
                        Map.of("title", "更新时间", "value", getCurrentTimestamp(), "short", true)
                    ),
                    "actions", List.of(
                        Map.of(
                            "type", "button",
                            "text", "查看详情",
                            "url", String.format("https://ai-pm.com/tasks/%s", taskName)
                        )
                    ),
                    "footer", "AI项目管理平台",
                    "ts", System.currentTimeMillis() / 1000
                )
            );
            
            return sendRichMessage(config, channel, text, attachments);
        } catch (Exception e) {
            log.error("发送任务通知失败", e);
            return false;
        }
    }

    /**
     * 发送代码审查通知
     */
    public boolean sendCodeReviewNotification(SlackConfig config, String channel, String prTitle, 
                                            String author, String reviewer, String status) {
        try {
            String text = String.format("代码审查: %s", prTitle);
            
            List<Map<String, Object>> attachments = List.of(
                Map.of(
                    "color", getColorByStatus(status),
                    "title", String.format("Pull Request: %s", prTitle),
                    "fields", List.of(
                        Map.of("title", "作者", "value", author, "short", true),
                        Map.of("title", "审查者", "value", reviewer, "short", true),
                        Map.of("title", "状态", "value", status, "short", true)
                    ),
                    "actions", List.of(
                        Map.of(
                            "type", "button",
                            "text", "查看PR",
                            "url", "https://github.com/ai-pm/project/pulls"
                        )
                    ),
                    "footer", "AI项目管理平台",
                    "ts", System.currentTimeMillis() / 1000
                )
            );
            
            return sendRichMessage(config, channel, text, attachments);
        } catch (Exception e) {
            log.error("发送代码审查通知失败", e);
            return false;
        }
    }

    /**
     * 发送每日报告
     */
    public boolean sendDailyReport(SlackConfig config, String channel, Map<String, Object> reportData) {
        try {
            String text = "项目每日报告";
            
            List<Map<String, Object>> attachments = List.of(
                Map.of(
                    "color", "good",
                    "title", "项目每日报告",
                    "fields", List.of(
                        Map.of("title", "总项目数", "value", reportData.getOrDefault("totalProjects", 0), "short", true),
                        Map.of("title", "活跃项目", "value", reportData.getOrDefault("activeProjects", 0), "short", true),
                        Map.of("title", "完成任务", "value", reportData.getOrDefault("completedTasks", 0), "short", true),
                        Map.of("title", "进行中任务", "value", reportData.getOrDefault("inProgressTasks", 0), "short", true),
                        Map.of("title", "活跃成员", "value", reportData.getOrDefault("activeMembers", 0), "short", true),
                        Map.of("title", "今日提交", "value", reportData.getOrDefault("todayCommits", 0), "short", true)
                    ),
                    "footer", "AI项目管理平台",
                    "ts", System.currentTimeMillis() / 1000
                )
            );
            
            return sendRichMessage(config, channel, text, attachments);
        } catch (Exception e) {
            log.error("发送每日报告失败", e);
            return false;
        }
    }

    /**
     * 获取Slack频道列表
     */
    public List<Map<String, Object>> getChannels(SlackConfig config) {
        try {
            log.info("获取Slack频道列表");
            return slackClient.getChannels(config);
        } catch (Exception e) {
            log.error("获取Slack频道列表失败", e);
            throw new RuntimeException("获取Slack频道列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取Slack用户列表
     */
    public List<Map<String, Object>> getUsers(SlackConfig config) {
        try {
            log.info("获取Slack用户列表");
            return slackClient.getUsers(config);
        } catch (Exception e) {
            log.error("获取Slack用户列表失败", e);
            throw new RuntimeException("获取Slack用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建Slack频道
     */
    public String createChannel(SlackConfig config, String channelName, boolean isPrivate) {
        try {
            log.info("创建Slack频道: {}", channelName);
            return slackClient.createChannel(config, channelName, isPrivate);
        } catch (Exception e) {
            log.error("创建Slack频道失败", e);
            throw new RuntimeException("创建Slack频道失败: " + e.getMessage());
        }
    }

    /**
     * 邀请用户到频道
     */
    public boolean inviteUserToChannel(SlackConfig config, String channelId, String userId) {
        try {
            log.info("邀请用户到Slack频道，频道: {}, 用户: {}", channelId, userId);
            return slackClient.inviteUserToChannel(config, channelId, userId);
        } catch (Exception e) {
            log.error("邀请用户到Slack频道失败", e);
            return false;
        }
    }

    /**
     * 设置Slack Webhook
     */
    public boolean setupWebhook(SlackConfig config, String webhookUrl) {
        try {
            log.info("设置Slack Webhook");
            return slackClient.setupWebhook(config, webhookUrl);
        } catch (Exception e) {
            log.error("设置Slack Webhook失败", e);
            return false;
        }
    }

    /**
     * 处理Slack事件回调
     */
    public void handleEventCallback(Map<String, Object> eventData) {
        try {
            String eventType = (String) eventData.get("type");
            log.info("处理Slack事件: {}", eventType);
            
            switch (eventType) {
                case "url_verification":
                    handleUrlVerification(eventData);
                    break;
                case "event_callback":
                    handleEvent(eventData);
                    break;
                default:
                    log.debug("未处理的Slack事件类型: {}", eventType);
            }
        } catch (Exception e) {
            log.error("处理Slack事件回调失败", e);
        }
    }

    /**
     * 根据操作类型获取颜色
     */
    private String getColorByAction(String action) {
        switch (action.toLowerCase()) {
            case "created":
            case "started":
                return "good";
            case "updated":
            case "modified":
                return "warning";
            case "deleted":
            case "failed":
                return "danger";
            default:
                return "#36a64f";
        }
    }

    /**
     * 根据状态获取颜色
     */
    private String getColorByStatus(String status) {
        switch (status.toLowerCase()) {
            case "completed":
            case "done":
            case "approved":
                return "good";
            case "in progress":
            case "pending":
                return "warning";
            case "failed":
            case "rejected":
                return "danger";
            default:
                return "#36a64f";
        }
    }

    /**
     * 获取当前时间戳
     */
    private String getCurrentTimestamp() {
        return java.time.LocalDateTime.now()
            .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private void handleUrlVerification(Map<String, Object> eventData) {
        // TODO: 处理URL验证
        log.info("处理Slack URL验证");
    }

    private void handleEvent(Map<String, Object> eventData) {
        // TODO: 处理具体事件
        log.info("处理Slack事件回调");
    }
}
