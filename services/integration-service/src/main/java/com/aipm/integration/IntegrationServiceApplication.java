/**
 * AI项目管理平台 - 集成服务主应用
 * 提供第三方工具集成功能，包括Jira、钉钉、Slack等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package com.aipm.integration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootApplication
@EnableFeignClients
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class IntegrationServiceApplication {

    public static void main(String[] args) {
        try {
            log.info("正在启动AI项目管理平台集成服务...");
            SpringApplication.run(IntegrationServiceApplication.class, args);
            log.info("AI项目管理平台集成服务启动成功！");
        } catch (Exception e) {
            log.error("集成服务启动失败", e);
            System.exit(1);
        }
    }
}
