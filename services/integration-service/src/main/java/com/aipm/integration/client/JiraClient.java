/**
 * Jira API客户端
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package com.aipm.integration.client;

import com.aipm.integration.model.JiraConfig;
import com.aipm.integration.model.JiraIssue;
import com.aipm.integration.model.JiraProject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class JiraClient {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    /**
     * 测试Jira连接
     */
    public boolean testConnection(JiraConfig config) {
        try {
            String url = config.getServerUrl() + "/rest/api/2/myself";
            HttpHeaders headers = createHeaders(config);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);
            
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.error("Jira连接测试失败", e);
            return false;
        }
    }

    /**
     * 获取Jira项目列表
     */
    public List<JiraProject> getProjects(JiraConfig config) {
        try {
            String url = config.getServerUrl() + "/rest/api/2/project";
            HttpHeaders headers = createHeaders(config);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<List> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, List.class);
            
            List<JiraProject> projects = new ArrayList<>();
            List<Map<String, Object>> projectData = response.getBody();
            
            if (projectData != null) {
                for (Map<String, Object> data : projectData) {
                    JiraProject project = mapToJiraProject(data);
                    projects.add(project);
                }
            }
            
            return projects;
        } catch (Exception e) {
            log.error("获取Jira项目列表失败", e);
            throw new RuntimeException("获取Jira项目列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取Jira问题列表
     */
    public List<JiraIssue> getIssues(JiraConfig config, String projectKey) {
        try {
            String jql = "project = " + projectKey + " ORDER BY created DESC";
            String url = config.getServerUrl() + "/rest/api/2/search?jql=" + 
                         java.net.URLEncoder.encode(jql, "UTF-8") + "&maxResults=100";
            
            HttpHeaders headers = createHeaders(config);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);
            
            List<JiraIssue> issues = new ArrayList<>();
            Map<String, Object> responseData = response.getBody();
            
            if (responseData != null && responseData.containsKey("issues")) {
                List<Map<String, Object>> issuesData = (List<Map<String, Object>>) responseData.get("issues");
                for (Map<String, Object> data : issuesData) {
                    JiraIssue issue = mapToJiraIssue(data);
                    issues.add(issue);
                }
            }
            
            return issues;
        } catch (Exception e) {
            log.error("获取Jira问题列表失败", e);
            throw new RuntimeException("获取Jira问题列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建Jira问题
     */
    public JiraIssue createIssue(JiraConfig config, JiraIssue issue) {
        try {
            String url = config.getServerUrl() + "/rest/api/2/issue";
            HttpHeaders headers = createHeaders(config);
            
            Map<String, Object> issueData = createIssuePayload(issue);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(issueData, headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, Map.class);
            
            Map<String, Object> responseData = response.getBody();
            if (responseData != null) {
                issue.setKey((String) responseData.get("key"));
                issue.setId((String) responseData.get("id"));
            }
            
            return issue;
        } catch (Exception e) {
            log.error("创建Jira问题失败", e);
            throw new RuntimeException("创建Jira问题失败: " + e.getMessage());
        }
    }

    /**
     * 更新Jira问题
     */
    public JiraIssue updateIssue(JiraConfig config, String issueKey, Map<String, Object> updates) {
        try {
            String url = config.getServerUrl() + "/rest/api/2/issue/" + issueKey;
            HttpHeaders headers = createHeaders(config);
            
            Map<String, Object> updateData = Map.of("fields", updates);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(updateData, headers);
            
            restTemplate.exchange(url, HttpMethod.PUT, entity, Void.class);
            
            // 获取更新后的问题
            return getIssue(config, issueKey);
        } catch (Exception e) {
            log.error("更新Jira问题失败: {}", issueKey, e);
            throw new RuntimeException("更新Jira问题失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个Jira问题
     */
    public JiraIssue getIssue(JiraConfig config, String issueKey) {
        try {
            String url = config.getServerUrl() + "/rest/api/2/issue/" + issueKey;
            HttpHeaders headers = createHeaders(config);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);
            
            Map<String, Object> issueData = response.getBody();
            return mapToJiraIssue(issueData);
        } catch (Exception e) {
            log.error("获取Jira问题失败: {}", issueKey, e);
            throw new RuntimeException("获取Jira问题失败: " + e.getMessage());
        }
    }

    /**
     * 获取问题历史
     */
    public List<Map<String, Object>> getIssueHistory(JiraConfig config, String issueKey) {
        try {
            String url = config.getServerUrl() + "/rest/api/2/issue/" + issueKey + "?expand=changelog";
            HttpHeaders headers = createHeaders(config);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);
            
            Map<String, Object> issueData = response.getBody();
            if (issueData != null && issueData.containsKey("changelog")) {
                Map<String, Object> changelog = (Map<String, Object>) issueData.get("changelog");
                return (List<Map<String, Object>>) changelog.get("histories");
            }
            
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取Jira问题历史失败: {}", issueKey, e);
            throw new RuntimeException("获取Jira问题历史失败: " + e.getMessage());
        }
    }

    /**
     * 设置Webhook
     */
    public boolean setupWebhook(JiraConfig config, String webhookUrl) {
        try {
            String url = config.getServerUrl() + "/rest/webhooks/1.0/webhook";
            HttpHeaders headers = createHeaders(config);
            
            Map<String, Object> webhookData = Map.of(
                "name", "AI-PM Integration Webhook",
                "url", webhookUrl,
                "events", Arrays.asList(
                    "jira:issue_created",
                    "jira:issue_updated",
                    "jira:issue_deleted"
                )
            );
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(webhookData, headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, Map.class);
            
            return response.getStatusCode() == HttpStatus.CREATED;
        } catch (Exception e) {
            log.error("设置Jira Webhook失败", e);
            return false;
        }
    }

    /**
     * 创建HTTP请求头
     */
    private HttpHeaders createHeaders(JiraConfig config) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 使用Basic认证
        String auth = config.getUsername() + ":" + config.getApiToken();
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        headers.set("Authorization", "Basic " + encodedAuth);
        
        return headers;
    }

    /**
     * 映射Jira项目数据
     */
    private JiraProject mapToJiraProject(Map<String, Object> data) {
        JiraProject project = new JiraProject();
        project.setId((String) data.get("id"));
        project.setKey((String) data.get("key"));
        project.setName((String) data.get("name"));
        project.setDescription((String) data.get("description"));
        
        if (data.containsKey("projectTypeKey")) {
            project.setProjectType((String) data.get("projectTypeKey"));
        }
        
        return project;
    }

    /**
     * 映射Jira问题数据
     */
    private JiraIssue mapToJiraIssue(Map<String, Object> data) {
        JiraIssue issue = new JiraIssue();
        issue.setId((String) data.get("id"));
        issue.setKey((String) data.get("key"));
        
        if (data.containsKey("fields")) {
            Map<String, Object> fields = (Map<String, Object>) data.get("fields");
            issue.setSummary((String) fields.get("summary"));
            issue.setDescription((String) fields.get("description"));
            
            if (fields.containsKey("issuetype")) {
                Map<String, Object> issueType = (Map<String, Object>) fields.get("issuetype");
                issue.setIssueType((String) issueType.get("name"));
            }
            
            if (fields.containsKey("status")) {
                Map<String, Object> status = (Map<String, Object>) fields.get("status");
                issue.setStatus((String) status.get("name"));
            }
            
            if (fields.containsKey("priority")) {
                Map<String, Object> priority = (Map<String, Object>) fields.get("priority");
                issue.setPriority((String) priority.get("name"));
            }
            
            if (fields.containsKey("assignee") && fields.get("assignee") != null) {
                Map<String, Object> assignee = (Map<String, Object>) fields.get("assignee");
                issue.setAssignee((String) assignee.get("displayName"));
            }
        }
        
        return issue;
    }

    /**
     * 创建问题创建请求体
     */
    private Map<String, Object> createIssuePayload(JiraIssue issue) {
        Map<String, Object> fields = new HashMap<>();
        fields.put("summary", issue.getSummary());
        fields.put("description", issue.getDescription());
        fields.put("project", Map.of("key", issue.getProjectKey()));
        fields.put("issuetype", Map.of("name", issue.getIssueType()));
        
        if (issue.getPriority() != null) {
            fields.put("priority", Map.of("name", issue.getPriority()));
        }
        
        return Map.of("fields", fields);
    }
}
