/**
 * 钉钉集成服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package com.aipm.integration.service;

import com.aipm.integration.client.DingTalkClient;
import com.aipm.integration.model.DingTalkConfig;
import com.aipm.integration.model.DingTalkMessage;
import com.aipm.integration.model.DingTalkUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class DingTalkIntegrationService {

    private final DingTalkClient dingTalkClient;

    /**
     * 测试钉钉连接
     */
    public boolean testConnection(DingTalkConfig config) {
        try {
            log.info("测试钉钉连接");
            return dingTalkClient.testConnection(config);
        } catch (Exception e) {
            log.error("钉钉连接测试失败", e);
            return false;
        }
    }

    /**
     * 发送文本消息
     */
    public boolean sendTextMessage(DingTalkConfig config, String content, List<String> userIds) {
        try {
            log.info("发送钉钉文本消息，接收人数: {}", userIds.size());
            
            DingTalkMessage message = DingTalkMessage.builder()
                .msgType("text")
                .content(content)
                .userIds(userIds)
                .build();
            
            return dingTalkClient.sendMessage(config, message);
        } catch (Exception e) {
            log.error("发送钉钉文本消息失败", e);
            return false;
        }
    }

    /**
     * 发送Markdown消息
     */
    public boolean sendMarkdownMessage(DingTalkConfig config, String title, String content, List<String> userIds) {
        try {
            log.info("发送钉钉Markdown消息: {}", title);
            
            DingTalkMessage message = DingTalkMessage.builder()
                .msgType("markdown")
                .title(title)
                .content(content)
                .userIds(userIds)
                .build();
            
            return dingTalkClient.sendMessage(config, message);
        } catch (Exception e) {
            log.error("发送钉钉Markdown消息失败", e);
            return false;
        }
    }

    /**
     * 发送卡片消息
     */
    public boolean sendActionCardMessage(DingTalkConfig config, String title, String content, 
                                       List<Map<String, String>> buttons, List<String> userIds) {
        try {
            log.info("发送钉钉卡片消息: {}", title);
            
            DingTalkMessage message = DingTalkMessage.builder()
                .msgType("actionCard")
                .title(title)
                .content(content)
                .buttons(buttons)
                .userIds(userIds)
                .build();
            
            return dingTalkClient.sendMessage(config, message);
        } catch (Exception e) {
            log.error("发送钉钉卡片消息失败", e);
            return false;
        }
    }

    /**
     * 发送项目通知
     */
    public boolean sendProjectNotification(DingTalkConfig config, String projectName, 
                                         String action, String details, List<String> userIds) {
        try {
            String title = String.format("项目通知 - %s", projectName);
            String content = String.format(
                "### %s\n\n" +
                "**项目**: %s\n\n" +
                "**操作**: %s\n\n" +
                "**详情**: %s\n\n" +
                "**时间**: %s",
                title, projectName, action, details, 
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
            
            return sendMarkdownMessage(config, title, content, userIds);
        } catch (Exception e) {
            log.error("发送项目通知失败", e);
            return false;
        }
    }

    /**
     * 发送任务通知
     */
    public boolean sendTaskNotification(DingTalkConfig config, String taskName, String status, 
                                      String assignee, List<String> userIds) {
        try {
            String title = String.format("任务通知 - %s", taskName);
            String content = String.format(
                "### %s\n\n" +
                "**任务**: %s\n\n" +
                "**状态**: %s\n\n" +
                "**负责人**: %s\n\n" +
                "**时间**: %s",
                title, taskName, status, assignee,
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
            
            List<Map<String, String>> buttons = List.of(
                Map.of("title", "查看详情", "actionURL", "https://ai-pm.com/tasks/" + taskName)
            );
            
            return sendActionCardMessage(config, title, content, buttons, userIds);
        } catch (Exception e) {
            log.error("发送任务通知失败", e);
            return false;
        }
    }

    /**
     * 发送每日报告
     */
    public boolean sendDailyReport(DingTalkConfig config, Map<String, Object> reportData, List<String> userIds) {
        try {
            String title = "项目每日报告";
            String content = buildDailyReportContent(reportData);
            
            return sendMarkdownMessage(config, title, content, userIds);
        } catch (Exception e) {
            log.error("发送每日报告失败", e);
            return false;
        }
    }

    /**
     * 获取钉钉用户列表
     */
    public List<DingTalkUser> getUsers(DingTalkConfig config) {
        try {
            log.info("获取钉钉用户列表");
            return dingTalkClient.getUsers(config);
        } catch (Exception e) {
            log.error("获取钉钉用户列表失败", e);
            throw new RuntimeException("获取钉钉用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取钉钉部门列表
     */
    public List<Map<String, Object>> getDepartments(DingTalkConfig config) {
        try {
            log.info("获取钉钉部门列表");
            return dingTalkClient.getDepartments(config);
        } catch (Exception e) {
            log.error("获取钉钉部门列表失败", e);
            throw new RuntimeException("获取钉钉部门列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建钉钉群聊
     */
    public String createGroup(DingTalkConfig config, String groupName, List<String> userIds) {
        try {
            log.info("创建钉钉群聊: {}", groupName);
            return dingTalkClient.createGroup(config, groupName, userIds);
        } catch (Exception e) {
            log.error("创建钉钉群聊失败", e);
            throw new RuntimeException("创建钉钉群聊失败: " + e.getMessage());
        }
    }

    /**
     * 发送群聊消息
     */
    public boolean sendGroupMessage(DingTalkConfig config, String groupId, String content) {
        try {
            log.info("发送钉钉群聊消息，群ID: {}", groupId);
            return dingTalkClient.sendGroupMessage(config, groupId, content);
        } catch (Exception e) {
            log.error("发送钉钉群聊消息失败", e);
            return false;
        }
    }

    /**
     * 设置钉钉机器人Webhook
     */
    public boolean setupRobotWebhook(DingTalkConfig config, String webhookUrl, String secret) {
        try {
            log.info("设置钉钉机器人Webhook");
            return dingTalkClient.setupRobotWebhook(config, webhookUrl, secret);
        } catch (Exception e) {
            log.error("设置钉钉机器人Webhook失败", e);
            return false;
        }
    }

    /**
     * 处理钉钉回调事件
     */
    public void handleCallback(Map<String, Object> callbackData) {
        try {
            String eventType = (String) callbackData.get("EventType");
            log.info("处理钉钉回调事件: {}", eventType);
            
            switch (eventType) {
                case "check_url":
                    handleUrlVerification(callbackData);
                    break;
                case "user_add_org":
                    handleUserAddOrg(callbackData);
                    break;
                case "user_modify_org":
                    handleUserModifyOrg(callbackData);
                    break;
                case "user_leave_org":
                    handleUserLeaveOrg(callbackData);
                    break;
                default:
                    log.debug("未处理的钉钉回调事件: {}", eventType);
            }
        } catch (Exception e) {
            log.error("处理钉钉回调事件失败", e);
        }
    }

    /**
     * 构建每日报告内容
     */
    private String buildDailyReportContent(Map<String, Object> reportData) {
        StringBuilder content = new StringBuilder();
        content.append("### 项目每日报告\n\n");
        
        // 项目概况
        content.append("**项目概况**\n");
        content.append(String.format("- 总项目数: %s\n", reportData.getOrDefault("totalProjects", 0)));
        content.append(String.format("- 活跃项目: %s\n", reportData.getOrDefault("activeProjects", 0)));
        content.append(String.format("- 完成项目: %s\n\n", reportData.getOrDefault("completedProjects", 0)));
        
        // 任务统计
        content.append("**任务统计**\n");
        content.append(String.format("- 总任务数: %s\n", reportData.getOrDefault("totalTasks", 0)));
        content.append(String.format("- 已完成: %s\n", reportData.getOrDefault("completedTasks", 0)));
        content.append(String.format("- 进行中: %s\n", reportData.getOrDefault("inProgressTasks", 0)));
        content.append(String.format("- 待开始: %s\n\n", reportData.getOrDefault("todoTasks", 0)));
        
        // 团队活跃度
        content.append("**团队活跃度**\n");
        content.append(String.format("- 活跃成员: %s\n", reportData.getOrDefault("activeMembers", 0)));
        content.append(String.format("- 今日提交: %s\n", reportData.getOrDefault("todayCommits", 0)));
        content.append(String.format("- 代码审查: %s\n\n", reportData.getOrDefault("codeReviews", 0)));
        
        content.append(String.format("**报告时间**: %s", 
            java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        
        return content.toString();
    }

    private void handleUrlVerification(Map<String, Object> callbackData) {
        // TODO: 处理URL验证
        log.info("处理钉钉URL验证");
    }

    private void handleUserAddOrg(Map<String, Object> callbackData) {
        // TODO: 处理用户加入组织事件
        log.info("处理用户加入组织事件");
    }

    private void handleUserModifyOrg(Map<String, Object> callbackData) {
        // TODO: 处理用户信息修改事件
        log.info("处理用户信息修改事件");
    }

    private void handleUserLeaveOrg(Map<String, Object> callbackData) {
        // TODO: 处理用户离开组织事件
        log.info("处理用户离开组织事件");
    }
}
