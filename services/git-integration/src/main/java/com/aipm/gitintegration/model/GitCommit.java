/**
 * Git提交记录实体模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Git提交记录实体
 * 
 * 存储Git提交的详细信息和统计数据
 */
@Entity
@Table(name = "git_commits")
@Data
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class GitCommit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的Git仓库
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "repository_id", nullable = false)
    private GitRepository repository;

    /**
     * 提交SHA值
     */
    @Column(name = "sha", nullable = false, unique = true)
    private String sha;

    /**
     * 提交消息
     */
    @Column(name = "message", nullable = false, columnDefinition = "TEXT")
    private String message;

    /**
     * 提交作者姓名
     */
    @Column(name = "author_name", nullable = false)
    private String authorName;

    /**
     * 提交作者邮箱
     */
    @Column(name = "author_email", nullable = false)
    private String authorEmail;

    /**
     * 提交者姓名
     */
    @Column(name = "committer_name")
    private String committerName;

    /**
     * 提交者邮箱
     */
    @Column(name = "committer_email")
    private String committerEmail;

    /**
     * 提交时间
     */
    @Column(name = "commit_date", nullable = false)
    private LocalDateTime commitDate;

    /**
     * 分支名称
     */
    @Column(name = "branch_name")
    private String branchName;

    /**
     * 父提交SHA (多个父提交用逗号分隔)
     */
    @Column(name = "parent_shas")
    private String parentShas;

    /**
     * 提交类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "commit_type")
    private CommitType commitType;

    /**
     * 新增行数
     */
    @Column(name = "additions")
    private Integer additions = 0;

    /**
     * 删除行数
     */
    @Column(name = "deletions")
    private Integer deletions = 0;

    /**
     * 修改文件数
     */
    @Column(name = "changed_files")
    private Integer changedFiles = 0;

    /**
     * 关联的任务ID
     */
    @Column(name = "task_id")
    private String taskId;

    /**
     * 关联的Pull Request ID
     */
    @Column(name = "pull_request_id")
    private String pullRequestId;

    /**
     * 提交标签 (JSON格式)
     */
    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags;

    /**
     * 代码质量评分
     */
    @Column(name = "quality_score")
    private Double qualityScore;

    /**
     * 复杂度评分
     */
    @Column(name = "complexity_score")
    private Double complexityScore;

    /**
     * 测试覆盖率
     */
    @Column(name = "test_coverage")
    private Double testCoverage;

    /**
     * 是否为合并提交
     */
    @Column(name = "is_merge")
    private Boolean isMerge = false;

    /**
     * 是否为回滚提交
     */
    @Column(name = "is_revert")
    private Boolean isRevert = false;

    /**
     * 提交URL
     */
    @Column(name = "commit_url")
    private String commitUrl;

    /**
     * 差异统计 (JSON格式)
     */
    @Column(name = "diff_stats", columnDefinition = "TEXT")
    private String diffStats;

    /**
     * 是否激活
     */
    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 提交类型枚举
     */
    public enum CommitType {
        FEATURE("新功能"),
        BUGFIX("缺陷修复"),
        HOTFIX("热修复"),
        REFACTOR("重构"),
        DOCS("文档"),
        STYLE("样式"),
        TEST("测试"),
        CHORE("构建/工具"),
        PERF("性能优化"),
        REVERT("回滚"),
        MERGE("合并"),
        INITIAL("初始提交");

        private final String description;

        CommitType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 根据提交消息推断提交类型
         */
        public static CommitType inferFromMessage(String message) {
            if (message == null || message.trim().isEmpty()) {
                return CHORE;
            }
            
            String lowerMessage = message.toLowerCase();
            
            if (lowerMessage.startsWith("feat") || lowerMessage.contains("feature")) {
                return FEATURE;
            } else if (lowerMessage.startsWith("fix") || lowerMessage.contains("bug")) {
                return BUGFIX;
            } else if (lowerMessage.startsWith("hotfix")) {
                return HOTFIX;
            } else if (lowerMessage.startsWith("refactor")) {
                return REFACTOR;
            } else if (lowerMessage.startsWith("docs")) {
                return DOCS;
            } else if (lowerMessage.startsWith("style")) {
                return STYLE;
            } else if (lowerMessage.startsWith("test")) {
                return TEST;
            } else if (lowerMessage.startsWith("perf")) {
                return PERF;
            } else if (lowerMessage.startsWith("revert")) {
                return REVERT;
            } else if (lowerMessage.startsWith("merge")) {
                return MERGE;
            } else if (lowerMessage.contains("initial")) {
                return INITIAL;
            } else {
                return CHORE;
            }
        }
    }
}
