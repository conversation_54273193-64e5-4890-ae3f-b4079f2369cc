/**
 * Git分支实体模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Git分支实体
 * 
 * 存储Git分支的信息和状态
 */
@Entity
@Table(name = "git_branches")
@Data
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class GitBranch {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的Git仓库
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "repository_id", nullable = false)
    private GitRepository repository;

    /**
     * 分支名称
     */
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 分支类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "branch_type")
    private BranchType branchType;

    /**
     * 是否为默认分支
     */
    @Column(name = "is_default")
    private Boolean isDefault = false;

    /**
     * 是否受保护
     */
    @Column(name = "is_protected")
    private Boolean isProtected = false;

    /**
     * 最新提交SHA
     */
    @Column(name = "latest_commit_sha")
    private String latestCommitSha;

    /**
     * 最新提交消息
     */
    @Column(name = "latest_commit_message")
    private String latestCommitMessage;

    /**
     * 最新提交作者
     */
    @Column(name = "latest_commit_author")
    private String latestCommitAuthor;

    /**
     * 最新提交时间
     */
    @Column(name = "latest_commit_at")
    private LocalDateTime latestCommitAt;

    /**
     * 分支创建时间
     */
    @Column(name = "branch_created_at")
    private LocalDateTime branchCreatedAt;

    /**
     * 领先主分支的提交数
     */
    @Column(name = "ahead_count")
    private Integer aheadCount = 0;

    /**
     * 落后主分支的提交数
     */
    @Column(name = "behind_count")
    private Integer behindCount = 0;

    /**
     * 分支状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private BranchStatus status = BranchStatus.ACTIVE;

    /**
     * 关联的任务ID
     */
    @Column(name = "task_id")
    private String taskId;

    /**
     * 关联的功能ID
     */
    @Column(name = "feature_id")
    private String featureId;

    /**
     * 分支描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 是否激活
     */
    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 分支类型枚举
     */
    public enum BranchType {
        MAIN("主分支"),
        DEVELOP("开发分支"),
        FEATURE("功能分支"),
        RELEASE("发布分支"),
        HOTFIX("热修复分支"),
        BUGFIX("缺陷修复分支"),
        EXPERIMENTAL("实验分支");

        private final String description;

        BranchType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 分支状态枚举
     */
    public enum BranchStatus {
        ACTIVE("活跃"),
        MERGED("已合并"),
        DELETED("已删除"),
        STALE("过期"),
        ABANDONED("已废弃");

        private final String description;

        BranchStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
