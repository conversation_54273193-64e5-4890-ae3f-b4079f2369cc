/**
 * Git仓库DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.dto;

import com.aipm.gitintegration.model.GitRepository;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Git仓库数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitRepositoryDto {

    private Long id;
    private String projectId;
    private String name;
    private String description;
    private GitRepository.GitPlatform platform;
    private String repositoryUrl;
    private String externalId;
    private String defaultBranch;
    private Boolean isPrivate;
    private Boolean syncEnabled;
    private GitRepository.SyncStatus syncStatus;
    private LocalDateTime lastSyncAt;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 统计信息
    private Integer branchCount;
    private Integer commitCount;
    private Integer pullRequestCount;
    private Integer contributorCount;
    
    // 最新活动
    private String latestCommitSha;
    private String latestCommitMessage;
    private String latestCommitAuthor;
    private LocalDateTime latestCommitAt;
}
