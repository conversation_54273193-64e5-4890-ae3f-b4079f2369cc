/**
 * Git分支DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.dto;

import com.aipm.gitintegration.model.GitBranch;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Git分支数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitBranchDto {

    private Long id;
    private String name;
    private GitBranch.BranchType branchType;
    private Boolean isDefault;
    private Boolean isProtected;
    private String latestCommitSha;
    private String latestCommitMessage;
    private String latestCommitAuthor;
    private LocalDateTime latestCommitAt;
    private LocalDateTime branchCreatedAt;
    private Integer aheadCount;
    private Integer behindCount;
    private GitBranch.BranchStatus status;
    private String taskId;
    private String featureId;
    private String description;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
