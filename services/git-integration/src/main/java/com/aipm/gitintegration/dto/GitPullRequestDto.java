/**
 * Git Pull Request DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Git Pull Request数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitPullRequestDto {

    private Long id;
    private Integer number;
    private String externalId;
    private String title;
    private String description;
    private PullRequestStatus status;
    private String sourceBranch;
    private String targetBranch;
    private String authorUsername;
    private String authorEmail;
    private String assigneeUsername;
    private List<String> reviewers;
    private List<String> labels;
    private String milestone;
    private Boolean isDraft;
    private Boolean isMergeable;
    private MergeStatus mergeStatus;
    private Integer conflictsCount;
    private Integer additions;
    private Integer deletions;
    private Integer changedFiles;
    private Integer commitsCount;
    private Integer commentsCount;
    private Integer reviewCommentsCount;
    private String taskId;
    private String featureId;
    private String prUrl;
    private LocalDateTime prCreatedAt;
    private LocalDateTime prUpdatedAt;
    private LocalDateTime mergedAt;
    private LocalDateTime closedAt;
    private String mergeCommitSha;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * Pull Request状态枚举
     */
    public enum PullRequestStatus {
        OPEN, CLOSED, MERGED, DRAFT
    }

    /**
     * 合并状态枚举
     */
    public enum MergeStatus {
        CLEAN, UNSTABLE, DIRTY, UNKNOWN, BLOCKED
    }
}
