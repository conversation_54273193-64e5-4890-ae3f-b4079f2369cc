/**
 * Git集成控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.controller;

import com.aipm.gitintegration.dto.*;
import com.aipm.gitintegration.service.GitIntegrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Git集成REST API控制器
 * 
 * 提供Git仓库管理、数据同步、统计分析等功能的REST接口
 */
@RestController
@RequestMapping("/api/v1/git")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Git集成", description = "Git版本控制集成API")
public class GitIntegrationController {

    private final GitIntegrationService gitIntegrationService;

    @Operation(summary = "添加Git仓库", description = "添加新的Git仓库到项目中")
    @PostMapping("/repositories")
    public ResponseEntity<GitRepositoryDto> addRepository(
            @Valid @RequestBody CreateRepositoryRequest request) {
        log.info("添加Git仓库: {}", request.getRepositoryUrl());
        GitRepositoryDto repository = gitIntegrationService.addRepository(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(repository);
    }

    @Operation(summary = "获取Git仓库列表", description = "分页查询Git仓库列表")
    @GetMapping("/repositories")
    public ResponseEntity<Page<GitRepositoryDto>> getRepositories(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<GitRepositoryDto> repositories = gitIntegrationService.getRepositories(pageable);
        return ResponseEntity.ok(repositories);
    }

    @Operation(summary = "获取Git仓库详情", description = "根据ID获取Git仓库详细信息")
    @GetMapping("/repositories/{repositoryId}")
    public ResponseEntity<GitRepositoryDto> getRepository(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId) {
        return gitIntegrationService.getRepository(repositoryId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "更新Git仓库", description = "更新Git仓库配置信息")
    @PutMapping("/repositories/{repositoryId}")
    public ResponseEntity<GitRepositoryDto> updateRepository(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId,
            @Valid @RequestBody UpdateRepositoryRequest request) {
        GitRepositoryDto repository = gitIntegrationService.updateRepository(repositoryId, request);
        return ResponseEntity.ok(repository);
    }

    @Operation(summary = "删除Git仓库", description = "删除Git仓库及其相关数据")
    @DeleteMapping("/repositories/{repositoryId}")
    public ResponseEntity<Void> deleteRepository(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId) {
        gitIntegrationService.deleteRepository(repositoryId);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "获取项目的Git仓库", description = "获取指定项目的所有Git仓库")
    @GetMapping("/projects/{projectId}/repositories")
    public ResponseEntity<List<GitRepositoryDto>> getProjectRepositories(
            @Parameter(description = "项目ID") @PathVariable String projectId) {
        List<GitRepositoryDto> repositories = gitIntegrationService.getRepositoriesByProject(projectId);
        return ResponseEntity.ok(repositories);
    }

    @Operation(summary = "同步仓库数据", description = "手动触发仓库数据同步")
    @PostMapping("/repositories/{repositoryId}/sync")
    public ResponseEntity<SyncResultDto> syncRepository(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId) {
        log.info("同步仓库数据: {}", repositoryId);
        SyncResultDto result = gitIntegrationService.syncRepository(repositoryId);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "批量同步所有仓库", description = "同步所有启用的仓库数据")
    @PostMapping("/repositories/sync-all")
    public ResponseEntity<List<SyncResultDto>> syncAllRepositories() {
        log.info("批量同步所有仓库数据");
        List<SyncResultDto> results = gitIntegrationService.syncAllRepositories();
        return ResponseEntity.ok(results);
    }

    @Operation(summary = "获取仓库分支", description = "获取仓库的所有分支信息")
    @GetMapping("/repositories/{repositoryId}/branches")
    public ResponseEntity<List<GitBranchDto>> getRepositoryBranches(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId) {
        List<GitBranchDto> branches = gitIntegrationService.getRepositoryBranches(repositoryId);
        return ResponseEntity.ok(branches);
    }

    @Operation(summary = "获取仓库提交记录", description = "分页查询仓库的提交记录")
    @GetMapping("/repositories/{repositoryId}/commits")
    public ResponseEntity<Page<GitCommitDto>> getRepositoryCommits(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId,
            @Parameter(description = "分支名称") @RequestParam(required = false) String branch,
            @Parameter(description = "作者") @RequestParam(required = false) String author,
            @Parameter(description = "开始日期") @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @PageableDefault(size = 50) Pageable pageable) {
        
        CommitQueryRequest request = CommitQueryRequest.builder()
                .branch(branch)
                .author(author)
                .startDate(startDate)
                .endDate(endDate)
                .build();
                
        Page<GitCommitDto> commits = gitIntegrationService.getRepositoryCommits(repositoryId, request, pageable);
        return ResponseEntity.ok(commits);
    }

    @Operation(summary = "获取仓库Pull Request", description = "分页查询仓库的Pull Request")
    @GetMapping("/repositories/{repositoryId}/pull-requests")
    public ResponseEntity<Page<GitPullRequestDto>> getRepositoryPullRequests(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            @Parameter(description = "作者") @RequestParam(required = false) String author,
            @Parameter(description = "目标分支") @RequestParam(required = false) String targetBranch,
            @PageableDefault(size = 20) Pageable pageable) {
        
        PullRequestQueryRequest request = PullRequestQueryRequest.builder()
                .status(status)
                .author(author)
                .targetBranch(targetBranch)
                .build();
                
        Page<GitPullRequestDto> pullRequests = gitIntegrationService.getRepositoryPullRequests(repositoryId, request, pageable);
        return ResponseEntity.ok(pullRequests);
    }

    @Operation(summary = "获取提交统计", description = "获取仓库的提交统计数据")
    @GetMapping("/repositories/{repositoryId}/stats/commits")
    public ResponseEntity<CommitStatsDto> getCommitStats(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId,
            @Parameter(description = "开始日期") @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        CommitStatsDto stats = gitIntegrationService.getCommitStats(repositoryId, startDate, endDate);
        return ResponseEntity.ok(stats);
    }

    @Operation(summary = "获取开发者贡献统计", description = "获取仓库的开发者贡献统计")
    @GetMapping("/repositories/{repositoryId}/stats/contributors")
    public ResponseEntity<List<DeveloperContributionDto>> getDeveloperContributions(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId,
            @Parameter(description = "开始日期") @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        List<DeveloperContributionDto> contributions = gitIntegrationService.getDeveloperContributions(repositoryId, startDate, endDate);
        return ResponseEntity.ok(contributions);
    }

    @Operation(summary = "测试仓库连接", description = "测试Git仓库连接是否正常")
    @PostMapping("/test-connection")
    public ResponseEntity<ConnectionTestResult> testConnection(
            @Valid @RequestBody TestConnectionRequest request) {
        log.info("测试Git仓库连接: {}", request.getRepositoryUrl());
        ConnectionTestResult result = gitIntegrationService.testConnection(request);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "设置Webhook", description = "为仓库设置Webhook")
    @PostMapping("/repositories/{repositoryId}/webhook")
    public ResponseEntity<WebhookConfigDto> setupWebhook(
            @Parameter(description = "仓库ID") @PathVariable Long repositoryId,
            @Valid @RequestBody WebhookSetupRequest request) {
        log.info("设置仓库Webhook: {}", repositoryId);
        WebhookConfigDto config = gitIntegrationService.setupWebhook(repositoryId, request);
        return ResponseEntity.ok(config);
    }

    @Operation(summary = "处理Webhook事件", description = "处理来自Git平台的Webhook事件")
    @PostMapping("/webhook/{platform}")
    public ResponseEntity<Void> handleWebhook(
            @Parameter(description = "Git平台") @PathVariable String platform,
            @Parameter(description = "事件类型") @RequestHeader("X-Event-Type") String event,
            @Parameter(description = "签名") @RequestHeader(value = "X-Hub-Signature-256", required = false) String signature,
            @RequestBody String payload) {
        log.info("处理{}平台Webhook事件: {}", platform, event);
        gitIntegrationService.handleWebhookEvent(platform, event, payload, signature);
        return ResponseEntity.ok().build();
    }
}
