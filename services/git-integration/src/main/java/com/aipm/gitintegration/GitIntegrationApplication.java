/**
 * Git集成服务主应用类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Git集成服务启动类
 * 
 * 功能特性：
 * - GitHub/GitLab API集成
 * - 代码提交数据同步
 * - 分支和PR状态跟踪
 * - 代码质量分析集成
 * - Webhook事件处理
 * - 实时数据同步
 */
@SpringBootApplication
@EnableFeignClients
@EnableJpaAuditing
@EnableAsync
@EnableScheduling
public class GitIntegrationApplication {

    public static void main(String[] args) {
        SpringApplication.run(GitIntegrationApplication.class, args);
    }
}
