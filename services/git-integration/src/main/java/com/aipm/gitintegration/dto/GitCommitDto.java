/**
 * Git提交DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.dto;

import com.aipm.gitintegration.model.GitCommit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Git提交数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitCommitDto {

    private Long id;
    private String sha;
    private String message;
    private String authorName;
    private String authorEmail;
    private String committerName;
    private String committerEmail;
    private LocalDateTime commitDate;
    private String branchName;
    private String parentShas;
    private GitCommit.CommitType commitType;
    private Integer additions;
    private Integer deletions;
    private Integer changedFiles;
    private String taskId;
    private String pullRequestId;
    private List<String> tags;
    private Double qualityScore;
    private Double complexityScore;
    private Double testCoverage;
    private Boolean isMerge;
    private Boolean isRevert;
    private String commitUrl;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
