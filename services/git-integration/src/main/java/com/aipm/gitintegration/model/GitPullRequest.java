/**
 * Git Pull Request实体模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Git Pull Request实体
 * 
 * 存储Pull Request的信息和状态
 */
@Entity
@Table(name = "git_pull_requests")
@Data
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class GitPullRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的Git仓库
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "repository_id", nullable = false)
    private GitRepository repository;

    /**
     * PR编号 (GitHub PR number, GitLab MR iid等)
     */
    @Column(name = "number", nullable = false)
    private Integer number;

    /**
     * 外部ID (GitHub PR ID, GitLab MR ID等)
     */
    @Column(name = "external_id", nullable = false)
    private String externalId;

    /**
     * PR标题
     */
    @Column(name = "title", nullable = false)
    private String title;

    /**
     * PR描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * PR状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PullRequestStatus status;

    /**
     * 源分支
     */
    @Column(name = "source_branch", nullable = false)
    private String sourceBranch;

    /**
     * 目标分支
     */
    @Column(name = "target_branch", nullable = false)
    private String targetBranch;

    /**
     * 创建者用户名
     */
    @Column(name = "author_username", nullable = false)
    private String authorUsername;

    /**
     * 创建者邮箱
     */
    @Column(name = "author_email")
    private String authorEmail;

    /**
     * 分配给的用户
     */
    @Column(name = "assignee_username")
    private String assigneeUsername;

    /**
     * 审查者列表 (JSON格式)
     */
    @Column(name = "reviewers", columnDefinition = "TEXT")
    private String reviewers;

    /**
     * 标签列表 (JSON格式)
     */
    @Column(name = "labels", columnDefinition = "TEXT")
    private String labels;

    /**
     * 里程碑
     */
    @Column(name = "milestone")
    private String milestone;

    /**
     * 是否为草稿
     */
    @Column(name = "is_draft")
    private Boolean isDraft = false;

    /**
     * 是否可合并
     */
    @Column(name = "is_mergeable")
    private Boolean isMergeable;

    /**
     * 合并状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "merge_status")
    private MergeStatus mergeStatus;

    /**
     * 冲突文件数
     */
    @Column(name = "conflicts_count")
    private Integer conflictsCount = 0;

    /**
     * 新增行数
     */
    @Column(name = "additions")
    private Integer additions = 0;

    /**
     * 删除行数
     */
    @Column(name = "deletions")
    private Integer deletions = 0;

    /**
     * 修改文件数
     */
    @Column(name = "changed_files")
    private Integer changedFiles = 0;

    /**
     * 提交数量
     */
    @Column(name = "commits_count")
    private Integer commitsCount = 0;

    /**
     * 评论数量
     */
    @Column(name = "comments_count")
    private Integer commentsCount = 0;

    /**
     * 审查评论数量
     */
    @Column(name = "review_comments_count")
    private Integer reviewCommentsCount = 0;

    /**
     * 关联的任务ID
     */
    @Column(name = "task_id")
    private String taskId;

    /**
     * 关联的功能ID
     */
    @Column(name = "feature_id")
    private String featureId;

    /**
     * PR URL
     */
    @Column(name = "pr_url")
    private String prUrl;

    /**
     * 创建时间
     */
    @Column(name = "pr_created_at")
    private LocalDateTime prCreatedAt;

    /**
     * 更新时间
     */
    @Column(name = "pr_updated_at")
    private LocalDateTime prUpdatedAt;

    /**
     * 合并时间
     */
    @Column(name = "merged_at")
    private LocalDateTime mergedAt;

    /**
     * 关闭时间
     */
    @Column(name = "closed_at")
    private LocalDateTime closedAt;

    /**
     * 合并提交SHA
     */
    @Column(name = "merge_commit_sha")
    private String mergeCommitSha;

    /**
     * 是否激活
     */
    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * Pull Request状态枚举
     */
    public enum PullRequestStatus {
        OPEN("开放"),
        CLOSED("已关闭"),
        MERGED("已合并"),
        DRAFT("草稿");

        private final String description;

        PullRequestStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 合并状态枚举
     */
    public enum MergeStatus {
        CLEAN("无冲突"),
        UNSTABLE("不稳定"),
        DIRTY("有冲突"),
        UNKNOWN("未知"),
        BLOCKED("被阻止");

        private final String description;

        MergeStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
