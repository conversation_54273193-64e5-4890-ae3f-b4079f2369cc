/**
 * 创建仓库请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.dto;

import com.aipm.gitintegration.model.GitRepository;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建Git仓库请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateRepositoryRequest {

    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    @NotBlank(message = "仓库名称不能为空")
    private String name;

    private String description;

    @NotNull(message = "Git平台不能为空")
    private GitRepository.GitPlatform platform;

    @NotBlank(message = "仓库URL不能为空")
    private String repositoryUrl;

    @NotBlank(message = "访问令牌不能为空")
    private String accessToken;

    private String webhookSecret;

    private Boolean syncEnabled = true;

    private String syncConfig;
}
