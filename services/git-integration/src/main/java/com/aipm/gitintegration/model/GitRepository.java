/**
 * Git仓库实体模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Git仓库实体
 * 
 * 存储Git仓库的基本信息和配置
 */
@Entity
@Table(name = "git_repositories")
@Data
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class GitRepository {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 项目ID - 关联到项目管理系统
     */
    @Column(name = "project_id", nullable = false)
    private String projectId;

    /**
     * 仓库名称
     */
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 仓库描述
     */
    @Column(name = "description")
    private String description;

    /**
     * Git平台类型 (github, gitlab, gitee等)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "platform", nullable = false)
    private GitPlatform platform;

    /**
     * 仓库URL
     */
    @Column(name = "repository_url", nullable = false)
    private String repositoryUrl;

    /**
     * 仓库的唯一标识符 (GitHub repo ID, GitLab project ID等)
     */
    @Column(name = "external_id", nullable = false)
    private String externalId;

    /**
     * 默认分支
     */
    @Column(name = "default_branch")
    private String defaultBranch;

    /**
     * 是否为私有仓库
     */
    @Column(name = "is_private")
    private Boolean isPrivate;

    /**
     * 访问令牌 (加密存储)
     */
    @Column(name = "access_token")
    private String accessToken;

    /**
     * Webhook密钥
     */
    @Column(name = "webhook_secret")
    private String webhookSecret;

    /**
     * Webhook URL
     */
    @Column(name = "webhook_url")
    private String webhookUrl;

    /**
     * 是否启用同步
     */
    @Column(name = "sync_enabled")
    private Boolean syncEnabled = true;

    /**
     * 同步配置 (JSON格式)
     */
    @Column(name = "sync_config", columnDefinition = "TEXT")
    private String syncConfig;

    /**
     * 最后同步时间
     */
    @Column(name = "last_sync_at")
    private LocalDateTime lastSyncAt;

    /**
     * 同步状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "sync_status")
    private SyncStatus syncStatus = SyncStatus.PENDING;

    /**
     * 仓库统计信息 (JSON格式)
     */
    @Column(name = "statistics", columnDefinition = "TEXT")
    private String statistics;

    /**
     * 是否激活
     */
    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 关联的分支列表
     */
    @OneToMany(mappedBy = "repository", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GitBranch> branches;

    /**
     * 关联的提交记录
     */
    @OneToMany(mappedBy = "repository", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GitCommit> commits;

    /**
     * 关联的Pull Request
     */
    @OneToMany(mappedBy = "repository", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GitPullRequest> pullRequests;

    /**
     * Git平台枚举
     */
    public enum GitPlatform {
        GITHUB("GitHub"),
        GITLAB("GitLab"),
        GITEE("Gitee"),
        BITBUCKET("Bitbucket"),
        AZURE_DEVOPS("Azure DevOps");

        private final String displayName;

        GitPlatform(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING("待同步"),
        SYNCING("同步中"),
        SUCCESS("同步成功"),
        FAILED("同步失败"),
        DISABLED("已禁用");

        private final String description;

        SyncStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
