/**
 * GitHub API服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.service;

import com.aipm.gitintegration.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.kohsuke.github.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * GitHub API服务实现
 * 
 * 封装GitHub API调用，提供仓库数据获取功能
 */
@Service
@Slf4j
public class GitHubApiService {

    /**
     * 测试GitHub连接
     */
    public ConnectionTestResult testConnection(String token, String repositoryUrl) {
        try {
            GitHub github = new GitHubBuilder().withOAuthToken(token).build();
            
            // 解析仓库URL获取owner和repo
            String[] parts = parseRepositoryUrl(repositoryUrl);
            String owner = parts[0];
            String repo = parts[1];
            
            // 尝试获取仓库信息
            GHRepository repository = github.getRepository(owner + "/" + repo);
            
            return ConnectionTestResult.builder()
                    .success(true)
                    .message("连接成功")
                    .repositoryName(repository.getName())
                    .repositoryDescription(repository.getDescription())
                    .isPrivate(repository.isPrivate())
                    .defaultBranch(repository.getDefaultBranch())
                    .build();
                    
        } catch (IOException e) {
            log.error("GitHub连接测试失败: {}", e.getMessage());
            return ConnectionTestResult.builder()
                    .success(false)
                    .message("连接失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 获取仓库基本信息
     */
    public RepositoryInfoDto getRepositoryInfo(String token, String repositoryUrl) {
        try {
            GitHub github = new GitHubBuilder().withOAuthToken(token).build();
            String[] parts = parseRepositoryUrl(repositoryUrl);
            GHRepository repository = github.getRepository(parts[0] + "/" + parts[1]);
            
            return RepositoryInfoDto.builder()
                    .externalId(String.valueOf(repository.getId()))
                    .name(repository.getName())
                    .fullName(repository.getFullName())
                    .description(repository.getDescription())
                    .isPrivate(repository.isPrivate())
                    .defaultBranch(repository.getDefaultBranch())
                    .language(repository.getLanguage())
                    .size(repository.getSize())
                    .stargazersCount(repository.getStargazersCount())
                    .forksCount(repository.getForksCount())
                    .openIssuesCount(repository.getOpenIssueCount())
                    .createdAt(convertToLocalDateTime(repository.getCreatedAt()))
                    .updatedAt(convertToLocalDateTime(repository.getUpdatedAt()))
                    .pushedAt(convertToLocalDateTime(repository.getPushedAt()))
                    .build();
                    
        } catch (IOException e) {
            log.error("获取GitHub仓库信息失败: {}", e.getMessage());
            throw new RuntimeException("获取仓库信息失败", e);
        }
    }

    /**
     * 获取仓库分支列表
     */
    public List<GitBranchDto> getRepositoryBranches(String token, String repositoryUrl) {
        try {
            GitHub github = new GitHubBuilder().withOAuthToken(token).build();
            String[] parts = parseRepositoryUrl(repositoryUrl);
            GHRepository repository = github.getRepository(parts[0] + "/" + parts[1]);
            
            List<GitBranchDto> branches = new ArrayList<>();
            
            for (GHBranch branch : repository.getBranches().values()) {
                GitBranchDto branchDto = GitBranchDto.builder()
                        .name(branch.getName())
                        .isDefault(branch.getName().equals(repository.getDefaultBranch()))
                        .isProtected(branch.isProtected())
                        .latestCommitSha(branch.getSHA1())
                        .build();
                        
                // 获取最新提交信息
                try {
                    GHCommit commit = repository.getCommit(branch.getSHA1());
                    branchDto.setLatestCommitMessage(commit.getCommitShortInfo().getMessage());
                    branchDto.setLatestCommitAuthor(commit.getCommitShortInfo().getAuthor().getName());
                    branchDto.setLatestCommitAt(convertToLocalDateTime(commit.getCommitDate()));
                } catch (IOException e) {
                    log.warn("获取分支{}最新提交信息失败: {}", branch.getName(), e.getMessage());
                }
                
                branches.add(branchDto);
            }
            
            return branches;
            
        } catch (IOException e) {
            log.error("获取GitHub仓库分支失败: {}", e.getMessage());
            throw new RuntimeException("获取仓库分支失败", e);
        }
    }

    /**
     * 获取仓库提交记录
     */
    public List<GitCommitDto> getRepositoryCommits(String token, String repositoryUrl, String branch, int limit) {
        try {
            GitHub github = new GitHubBuilder().withOAuthToken(token).build();
            String[] parts = parseRepositoryUrl(repositoryUrl);
            GHRepository repository = github.getRepository(parts[0] + "/" + parts[1]);
            
            List<GitCommitDto> commits = new ArrayList<>();
            
            PagedIterable<GHCommit> commitList = repository.queryCommits()
                    .from(branch != null ? branch : repository.getDefaultBranch())
                    .list();
                    
            int count = 0;
            for (GHCommit commit : commitList) {
                if (count >= limit) break;
                
                GitCommitDto commitDto = GitCommitDto.builder()
                        .sha(commit.getSHA1())
                        .message(commit.getCommitShortInfo().getMessage())
                        .authorName(commit.getCommitShortInfo().getAuthor().getName())
                        .authorEmail(commit.getCommitShortInfo().getAuthor().getEmail())
                        .committerName(commit.getCommitShortInfo().getCommitter().getName())
                        .committerEmail(commit.getCommitShortInfo().getCommitter().getEmail())
                        .commitDate(convertToLocalDateTime(commit.getCommitDate()))
                        .commitUrl(commit.getHtmlUrl().toString())
                        .build();
                        
                // 获取提交统计信息
                try {
                    GHCommit.Stats stats = commit.getStats();
                    commitDto.setAdditions(stats.getAdditions());
                    commitDto.setDeletions(stats.getDeletions());
                    commitDto.setChangedFiles(stats.getTotal());
                } catch (IOException e) {
                    log.warn("获取提交{}统计信息失败: {}", commit.getSHA1(), e.getMessage());
                }
                
                // 获取父提交
                try {
                    List<String> parentShas = commit.getParents().stream()
                            .map(GHCommit::getSHA1)
                            .collect(Collectors.toList());
                    commitDto.setParentShas(String.join(",", parentShas));
                    commitDto.setIsMerge(parentShas.size() > 1);
                } catch (IOException e) {
                    log.warn("获取提交{}父提交信息失败: {}", commit.getSHA1(), e.getMessage());
                }
                
                commits.add(commitDto);
                count++;
            }
            
            return commits;
            
        } catch (IOException e) {
            log.error("获取GitHub仓库提交记录失败: {}", e.getMessage());
            throw new RuntimeException("获取仓库提交记录失败", e);
        }
    }

    /**
     * 获取Pull Request列表
     */
    public List<GitPullRequestDto> getRepositoryPullRequests(String token, String repositoryUrl, GHIssueState state, int limit) {
        try {
            GitHub github = new GitHubBuilder().withOAuthToken(token).build();
            String[] parts = parseRepositoryUrl(repositoryUrl);
            GHRepository repository = github.getRepository(parts[0] + "/" + parts[1]);
            
            List<GitPullRequestDto> pullRequests = new ArrayList<>();
            
            PagedIterable<GHPullRequest> prList = repository.queryPullRequests()
                    .state(state)
                    .list();
                    
            int count = 0;
            for (GHPullRequest pr : prList) {
                if (count >= limit) break;
                
                GitPullRequestDto prDto = GitPullRequestDto.builder()
                        .number(pr.getNumber())
                        .externalId(String.valueOf(pr.getId()))
                        .title(pr.getTitle())
                        .description(pr.getBody())
                        .sourceBranch(pr.getHead().getRef())
                        .targetBranch(pr.getBase().getRef())
                        .authorUsername(pr.getUser().getLogin())
                        .isDraft(pr.isDraft())
                        .isMergeable(pr.getMergeable())
                        .prUrl(pr.getHtmlUrl().toString())
                        .prCreatedAt(convertToLocalDateTime(pr.getCreatedAt()))
                        .prUpdatedAt(convertToLocalDateTime(pr.getUpdatedAt()))
                        .build();
                        
                // 设置状态
                if (pr.isMerged()) {
                    prDto.setStatus(GitPullRequestDto.PullRequestStatus.MERGED);
                    prDto.setMergedAt(convertToLocalDateTime(pr.getMergedAt()));
                } else if (pr.getState() == GHIssueState.CLOSED) {
                    prDto.setStatus(GitPullRequestDto.PullRequestStatus.CLOSED);
                    prDto.setClosedAt(convertToLocalDateTime(pr.getClosedAt()));
                } else {
                    prDto.setStatus(GitPullRequestDto.PullRequestStatus.OPEN);
                }
                
                // 获取统计信息
                try {
                    prDto.setAdditions(pr.getAdditions());
                    prDto.setDeletions(pr.getDeletions());
                    prDto.setChangedFiles(pr.getChangedFiles());
                    prDto.setCommitsCount(pr.getCommits());
                    prDto.setCommentsCount(pr.getCommentsCount());
                    prDto.setReviewCommentsCount(pr.getReviewComments());
                } catch (IOException e) {
                    log.warn("获取PR{}统计信息失败: {}", pr.getNumber(), e.getMessage());
                }
                
                pullRequests.add(prDto);
                count++;
            }
            
            return pullRequests;
            
        } catch (IOException e) {
            log.error("获取GitHub仓库Pull Request失败: {}", e.getMessage());
            throw new RuntimeException("获取仓库Pull Request失败", e);
        }
    }

    /**
     * 解析仓库URL获取owner和repo
     */
    private String[] parseRepositoryUrl(String repositoryUrl) {
        // 支持多种URL格式
        // https://github.com/owner/repo
        // https://github.com/owner/repo.git
        // **************:owner/repo.git
        
        String cleanUrl = repositoryUrl;
        if (cleanUrl.startsWith("**************:")) {
            cleanUrl = cleanUrl.substring("**************:".length());
        } else if (cleanUrl.contains("github.com/")) {
            cleanUrl = cleanUrl.substring(cleanUrl.indexOf("github.com/") + "github.com/".length());
        }
        
        if (cleanUrl.endsWith(".git")) {
            cleanUrl = cleanUrl.substring(0, cleanUrl.length() - 4);
        }
        
        String[] parts = cleanUrl.split("/");
        if (parts.length < 2) {
            throw new IllegalArgumentException("无效的GitHub仓库URL: " + repositoryUrl);
        }
        
        return new String[]{parts[0], parts[1]};
    }

    /**
     * 转换Date到LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Date date) {
        if (date == null) return null;
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}
