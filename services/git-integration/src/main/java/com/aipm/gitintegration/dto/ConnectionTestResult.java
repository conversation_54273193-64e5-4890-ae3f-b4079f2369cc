/**
 * 连接测试结果DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Git仓库连接测试结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConnectionTestResult {

    private Boolean success;
    private String message;
    private String repositoryName;
    private String repositoryDescription;
    private Boolean isPrivate;
    private String defaultBranch;
    private String language;
    private Integer stargazersCount;
    private Integer forksCount;
    private Integer openIssuesCount;
}
