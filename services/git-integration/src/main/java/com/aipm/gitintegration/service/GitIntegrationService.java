/**
 * Git集成服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
package com.aipm.gitintegration.service;

import com.aipm.gitintegration.dto.*;
import com.aipm.gitintegration.model.GitRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Git集成服务接口
 * 
 * 提供Git仓库管理、数据同步、分析等功能
 */
public interface GitIntegrationService {

    /**
     * 添加Git仓库
     */
    GitRepositoryDto addRepository(CreateRepositoryRequest request);

    /**
     * 更新Git仓库配置
     */
    GitRepositoryDto updateRepository(Long repositoryId, UpdateRepositoryRequest request);

    /**
     * 删除Git仓库
     */
    void deleteRepository(Long repositoryId);

    /**
     * 获取Git仓库详情
     */
    Optional<GitRepositoryDto> getRepository(Long repositoryId);

    /**
     * 获取项目的Git仓库列表
     */
    List<GitRepositoryDto> getRepositoriesByProject(String projectId);

    /**
     * 分页查询Git仓库
     */
    Page<GitRepositoryDto> getRepositories(Pageable pageable);

    /**
     * 同步仓库数据
     */
    SyncResultDto syncRepository(Long repositoryId);

    /**
     * 批量同步仓库数据
     */
    List<SyncResultDto> syncAllRepositories();

    /**
     * 获取仓库分支列表
     */
    List<GitBranchDto> getRepositoryBranches(Long repositoryId);

    /**
     * 获取仓库提交记录
     */
    Page<GitCommitDto> getRepositoryCommits(Long repositoryId, CommitQueryRequest request, Pageable pageable);

    /**
     * 获取仓库Pull Request列表
     */
    Page<GitPullRequestDto> getRepositoryPullRequests(Long repositoryId, PullRequestQueryRequest request, Pageable pageable);

    /**
     * 获取提交统计数据
     */
    CommitStatsDto getCommitStats(Long repositoryId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取开发者贡献统计
     */
    List<DeveloperContributionDto> getDeveloperContributions(Long repositoryId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取代码质量分析
     */
    CodeQualityAnalysisDto getCodeQualityAnalysis(Long repositoryId);

    /**
     * 获取分支分析
     */
    BranchAnalysisDto getBranchAnalysis(Long repositoryId);

    /**
     * 测试仓库连接
     */
    ConnectionTestResult testConnection(TestConnectionRequest request);

    /**
     * 设置Webhook
     */
    WebhookConfigDto setupWebhook(Long repositoryId, WebhookSetupRequest request);

    /**
     * 处理Webhook事件
     */
    void handleWebhookEvent(String platform, String event, String payload, String signature);

    /**
     * 获取仓库活动时间线
     */
    List<RepositoryActivityDto> getRepositoryActivity(Long repositoryId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 搜索提交记录
     */
    Page<GitCommitDto> searchCommits(CommitSearchRequest request, Pageable pageable);

    /**
     * 获取文件变更历史
     */
    List<FileChangeHistoryDto> getFileChangeHistory(Long repositoryId, String filePath);

    /**
     * 获取热点文件分析
     */
    List<HotFileDto> getHotFiles(Long repositoryId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取代码审查统计
     */
    CodeReviewStatsDto getCodeReviewStats(Long repositoryId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取发布统计
     */
    ReleaseStatsDto getReleaseStats(Long repositoryId);

    /**
     * 导出仓库数据
     */
    byte[] exportRepositoryData(Long repositoryId, ExportRequest request);
}
