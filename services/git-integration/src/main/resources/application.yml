# Git集成服务配置文件
# AI项目管理平台 - Git集成服务
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-16

server:
  port: 8083
  servlet:
    context-path: /api/git

spring:
  application:
    name: git-integration-service
  
  # 数据库配置
  datasource:
    url: ******************************************
    username: ${DB_USERNAME:ai_pm_user}
    password: ${DB_PASSWORD:ai_pm_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 3
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false
  
  # 安全配置
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:http://localhost:8080/auth/realms/ai-pm}

# 日志配置
logging:
  level:
    com.aipm.gitintegration: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/git-integration-service.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Git集成配置
git:
  integration:
    # GitHub配置
    github:
      api-url: https://api.github.com
      timeout: 30000
      rate-limit:
        requests-per-hour: 5000
    
    # GitLab配置
    gitlab:
      api-url: https://gitlab.com/api/v4
      timeout: 30000
      rate-limit:
        requests-per-hour: 2000
    
    # Gitee配置
    gitee:
      api-url: https://gitee.com/api/v5
      timeout: 30000
      rate-limit:
        requests-per-hour: 1000
    
    # 同步配置
    sync:
      # 自动同步间隔（分钟）
      auto-sync-interval: 30
      # 批量同步大小
      batch-size: 10
      # 最大重试次数
      max-retries: 3
      # 重试间隔（秒）
      retry-interval: 60
      # 同步超时（分钟）
      timeout: 10
    
    # Webhook配置
    webhook:
      # Webhook密钥
      secret: ${WEBHOOK_SECRET:your-webhook-secret}
      # 支持的事件类型
      supported-events:
        - push
        - pull_request
        - issues
        - create
        - delete
        - release
    
    # 数据保留配置
    retention:
      # 提交记录保留天数
      commits-retention-days: 365
      # 分支记录保留天数
      branches-retention-days: 90
      # 同步日志保留天数
      sync-logs-retention-days: 30

# 外部服务配置
external:
  services:
    # 用户服务
    user-service:
      url: ${USER_SERVICE_URL:http://localhost:8081}
      timeout: 5000
    
    # 项目服务
    project-service:
      url: ${PROJECT_SERVICE_URL:http://localhost:8082}
      timeout: 5000
    
    # 通知服务
    notification-service:
      url: ${NOTIFICATION_SERVICE_URL:http://localhost:8084}
      timeout: 5000

# 线程池配置
async:
  core-pool-size: 5
  max-pool-size: 20
  queue-capacity: 100
  thread-name-prefix: git-async-

# 分页配置
pagination:
  default-page-size: 20
  max-page-size: 100
