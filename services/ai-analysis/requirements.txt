# AI分析服务依赖包
# 基础框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# 机器学习框架
tensorflow==2.15.0
torch==2.1.0
torchvision==0.16.0
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0

# 数据处理
pandas==2.1.4
numpy==1.24.3
scipy==1.11.4
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# 模型管理和实验跟踪
mlflow==2.8.1
optuna==3.4.0
joblib==1.3.2

# 数据库连接
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
redis==5.0.1

# HTTP客户端
httpx==0.25.2
requests==2.31.0

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 数据验证
marshmallow==3.20.1

# 异步任务
celery==5.3.4
redis==5.0.1

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# API文档
swagger-ui-bundle==0.0.9

# 安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# 其他工具
tqdm==4.66.1
click==8.1.7
