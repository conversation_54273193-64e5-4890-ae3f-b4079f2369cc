"""
AI分析服务主入口

提供项目管理相关的AI分析功能，包括：
- 项目进度预测
- 风险识别和预警
- 团队绩效分析
- 智能推荐

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from loguru import logger

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.v1.api import api_router
from app.core.exceptions import AIAnalysisException


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 AI分析服务启动中...")
    
    # 初始化机器学习模型
    from app.ml.model_manager import ModelManager
    model_manager = ModelManager()
    await model_manager.initialize()
    app.state.model_manager = model_manager
    
    logger.info("✅ AI分析服务启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("🛑 AI分析服务关闭中...")
    
    # 清理资源
    if hasattr(app.state, 'model_manager'):
        await app.state.model_manager.cleanup()
    
    logger.info("✅ AI分析服务关闭完成")


def create_application() -> FastAPI:
    """创建FastAPI应用实例"""
    
    # 设置日志
    setup_logging()
    
    # 创建应用
    app = FastAPI(
        title="AI项目管理分析服务",
        description="提供项目管理相关的AI分析功能",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        openapi_url="/openapi.json" if settings.DEBUG else None,
        lifespan=lifespan
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 配置可信主机
    if settings.ALLOWED_HOSTS:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.ALLOWED_HOSTS
        )
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 全局异常处理
    @app.exception_handler(AIAnalysisException)
    async def ai_analysis_exception_handler(request: Request, exc: AIAnalysisException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.error_code,
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "INTERNAL_SERVER_ERROR",
                "message": "服务器内部错误",
                "details": str(exc) if settings.DEBUG else None
            }
        )
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "service": "ai-analysis",
            "version": "1.0.0"
        }
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "AI项目管理分析服务",
            "version": "1.0.0",
            "docs": "/docs" if settings.DEBUG else "文档已禁用"
        }
    
    return app


# 创建应用实例
app = create_application()


if __name__ == "__main__":
    """直接运行时的入口"""
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
