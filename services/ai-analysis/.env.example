# AI分析服务环境配置示例

# 基础配置
DEBUG=true
HOST=0.0.0.0
PORT=8003

# 安全配置
SECRET_KEY=ai-analysis-secret-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 数据库配置
DATABASE_URL=postgresql://aipm:aipm123@localhost:5432/aipm_ai_analysis
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://localhost:6379/2
REDIS_PASSWORD=

# 外部服务配置
PROJECT_MANAGEMENT_SERVICE_URL=http://localhost:8001
USER_SERVICE_URL=http://localhost:8002

# 机器学习配置
ML_MODEL_PATH=./models
ML_CACHE_SIZE=100
ML_BATCH_SIZE=32
ML_PREDICTION_TIMEOUT=30

# 模型文件配置
PROGRESS_PREDICTION_MODEL=progress_predictor_v1.pkl
RISK_ANALYSIS_MODEL=risk_analyzer_v1.pkl
TEAM_PERFORMANCE_MODEL=team_performance_v1.pkl
RECOMMENDATION_MODEL=recommendation_engine_v1.pkl

# 数据处理配置
DATA_FETCH_INTERVAL=300
DATA_RETENTION_DAYS=365
FEATURE_CACHE_TTL=3600

# 预测配置
PREDICTION_CONFIDENCE_THRESHOLD=0.7
RISK_THRESHOLD_HIGH=0.8
RISK_THRESHOLD_MEDIUM=0.5

# 推荐配置
RECOMMENDATION_COUNT=5
RECOMMENDATION_SCORE_THRESHOLD=0.6

# 日志配置
LOG_LEVEL=INFO
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9003

# 异步任务配置
CELERY_BROKER_URL=redis://localhost:6379/3
CELERY_RESULT_BACKEND=redis://localhost:6379/3

# 特征工程配置
FEATURE_WINDOW_DAYS=30
FEATURE_MIN_SAMPLES=10
FEATURE_SCALING_METHOD=standard

# 模型训练配置
MODEL_RETRAIN_INTERVAL_DAYS=7
MODEL_VALIDATION_SPLIT=0.2
MODEL_TEST_SPLIT=0.1
MODEL_CROSS_VALIDATION_FOLDS=5

# 性能配置
MAX_CONCURRENT_PREDICTIONS=10
PREDICTION_QUEUE_SIZE=100
MODEL_WARM_UP_SAMPLES=5
