"""
日志配置

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import sys
from pathlib import Path
from loguru import logger
from app.core.config import settings


def setup_logging():
    """设置日志配置"""
    
    # 移除默认处理器
    logger.remove()
    
    # 控制台输出
    logger.add(
        sys.stdout,
        format=settings.LOG_FORMAT,
        level=settings.LOG_LEVEL,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 文件输出
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 应用日志
    logger.add(
        log_dir / "ai-analysis.log",
        format=settings.LOG_FORMAT,
        level=settings.LOG_LEVEL,
        rotation=settings.LOG_ROTATION,
        retention=settings.LOG_RETENTION,
        compression="gz",
        backtrace=True,
        diagnose=True
    )
    
    # 错误日志
    logger.add(
        log_dir / "ai-analysis-error.log",
        format=settings.LOG_FORMAT,
        level="ERROR",
        rotation=settings.LOG_ROTATION,
        retention=settings.LOG_RETENTION,
        compression="gz",
        backtrace=True,
        diagnose=True
    )
    
    # ML模型日志
    logger.add(
        log_dir / "ml-models.log",
        format=settings.LOG_FORMAT,
        level="INFO",
        rotation=settings.LOG_ROTATION,
        retention=settings.LOG_RETENTION,
        compression="gz",
        filter=lambda record: "ml" in record["name"].lower()
    )
    
    logger.info("日志系统初始化完成")
