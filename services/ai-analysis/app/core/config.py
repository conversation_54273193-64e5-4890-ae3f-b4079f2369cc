"""
AI分析服务配置管理

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "AI项目管理分析服务"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8003
    
    # 安全配置
    SECRET_KEY: str = "ai-analysis-secret-key-change-in-production"
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://aipm:aipm123@localhost:5432/aipm_ai_analysis"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/2"
    REDIS_PASSWORD: Optional[str] = None
    
    # 项目管理服务配置
    PROJECT_MANAGEMENT_SERVICE_URL: str = "http://localhost:8001"
    USER_SERVICE_URL: str = "http://localhost:8002"
    
    # 机器学习配置
    ML_MODEL_PATH: str = "./models"
    ML_CACHE_SIZE: int = 100
    ML_BATCH_SIZE: int = 32
    ML_PREDICTION_TIMEOUT: int = 30  # 秒
    
    # 模型配置
    PROGRESS_PREDICTION_MODEL: str = "progress_predictor_v1.pkl"
    RISK_ANALYSIS_MODEL: str = "risk_analyzer_v1.pkl"
    TEAM_PERFORMANCE_MODEL: str = "team_performance_v1.pkl"
    RECOMMENDATION_MODEL: str = "recommendation_engine_v1.pkl"
    
    # 数据处理配置
    DATA_FETCH_INTERVAL: int = 300  # 5分钟
    DATA_RETENTION_DAYS: int = 365
    FEATURE_CACHE_TTL: int = 3600  # 1小时
    
    # 预测配置
    PREDICTION_CONFIDENCE_THRESHOLD: float = 0.7
    RISK_THRESHOLD_HIGH: float = 0.8
    RISK_THRESHOLD_MEDIUM: float = 0.5
    
    # 推荐配置
    RECOMMENDATION_COUNT: int = 5
    RECOMMENDATION_SCORE_THRESHOLD: float = 0.6
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9003
    
    # 异步任务配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/3"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/3"
    
    # API配置
    API_V1_PREFIX: str = "/api/v1"
    MAX_REQUEST_SIZE: int = 10 * 1024 * 1024  # 10MB
    REQUEST_TIMEOUT: int = 60  # 秒
    
    # 特征工程配置
    FEATURE_WINDOW_DAYS: int = 30
    FEATURE_MIN_SAMPLES: int = 10
    FEATURE_SCALING_METHOD: str = "standard"  # standard, minmax, robust
    
    # 模型训练配置
    MODEL_RETRAIN_INTERVAL_DAYS: int = 7
    MODEL_VALIDATION_SPLIT: float = 0.2
    MODEL_TEST_SPLIT: float = 0.1
    MODEL_CROSS_VALIDATION_FOLDS: int = 5
    
    # 性能配置
    MAX_CONCURRENT_PREDICTIONS: int = 10
    PREDICTION_QUEUE_SIZE: int = 100
    MODEL_WARM_UP_SAMPLES: int = 5
    
    @validator("DEBUG", pre=True)
    def parse_debug(cls, v):
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("PREDICTION_CONFIDENCE_THRESHOLD")
    def validate_confidence_threshold(cls, v):
        if not 0 <= v <= 1:
            raise ValueError("置信度阈值必须在0-1之间")
        return v
    
    @validator("RISK_THRESHOLD_HIGH", "RISK_THRESHOLD_MEDIUM")
    def validate_risk_threshold(cls, v):
        if not 0 <= v <= 1:
            raise ValueError("风险阈值必须在0-1之间")
        return v
    
    @validator("MODEL_VALIDATION_SPLIT", "MODEL_TEST_SPLIT")
    def validate_split_ratio(cls, v):
        if not 0 < v < 1:
            raise ValueError("数据分割比例必须在0-1之间")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


class MLConfig:
    """机器学习模型配置"""
    
    # 进度预测模型配置
    PROGRESS_PREDICTION = {
        "model_type": "xgboost",
        "features": [
            "task_completion_rate",
            "team_velocity",
            "sprint_burndown_rate",
            "historical_progress",
            "team_size",
            "project_complexity",
            "days_elapsed",
            "remaining_tasks"
        ],
        "target": "progress_percentage",
        "hyperparameters": {
            "n_estimators": 100,
            "max_depth": 6,
            "learning_rate": 0.1,
            "subsample": 0.8,
            "colsample_bytree": 0.8
        }
    }
    
    # 风险分析模型配置
    RISK_ANALYSIS = {
        "model_type": "random_forest",
        "features": [
            "schedule_variance",
            "budget_variance",
            "team_turnover_rate",
            "bug_density",
            "velocity_trend",
            "blocked_tasks_ratio",
            "overdue_tasks_ratio",
            "communication_frequency"
        ],
        "target": "risk_level",
        "hyperparameters": {
            "n_estimators": 200,
            "max_depth": 10,
            "min_samples_split": 5,
            "min_samples_leaf": 2,
            "random_state": 42
        }
    }
    
    # 团队绩效模型配置
    TEAM_PERFORMANCE = {
        "model_type": "neural_network",
        "features": [
            "task_completion_velocity",
            "code_quality_score",
            "collaboration_index",
            "knowledge_sharing_score",
            "meeting_efficiency",
            "response_time",
            "innovation_index",
            "satisfaction_score"
        ],
        "target": "performance_score",
        "architecture": {
            "hidden_layers": [64, 32, 16],
            "activation": "relu",
            "dropout": 0.2,
            "optimizer": "adam",
            "learning_rate": 0.001
        }
    }
    
    # 推荐系统配置
    RECOMMENDATION_ENGINE = {
        "model_type": "collaborative_filtering",
        "features": [
            "user_behavior",
            "project_similarity",
            "task_patterns",
            "team_preferences",
            "historical_success",
            "context_factors"
        ],
        "algorithms": {
            "content_based": {
                "similarity_metric": "cosine",
                "min_similarity": 0.3
            },
            "collaborative": {
                "method": "matrix_factorization",
                "factors": 50,
                "regularization": 0.01
            },
            "hybrid": {
                "content_weight": 0.4,
                "collaborative_weight": 0.6
            }
        }
    }


# 创建ML配置实例
ml_config = MLConfig()
