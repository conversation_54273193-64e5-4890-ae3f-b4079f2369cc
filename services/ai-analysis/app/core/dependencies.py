"""
依赖注入

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from fastapi import Depends, Request
from typing import Generator

from app.ml.model_manager import ModelManager
from app.ml.feature_extractor import FeatureExtractor
from app.services.data_service import DataService
from app.services.prediction_service import PredictionService
from app.services.risk_service import RiskService


def get_model_manager(request: Request) -> ModelManager:
    """获取模型管理器"""
    return request.app.state.model_manager


def get_data_service() -> DataService:
    """获取数据服务"""
    return DataService()


def get_feature_extractor(
    data_service: DataService = Depends(get_data_service)
) -> FeatureExtractor:
    """获取特征提取器"""
    return FeatureExtractor(data_service)


def get_prediction_service(
    model_manager: ModelManager = Depends(get_model_manager),
    feature_extractor: FeatureExtractor = Depends(get_feature_extractor),
    data_service: DataService = Depends(get_data_service)
) -> PredictionService:
    """获取预测服务"""
    return PredictionService(model_manager, feature_extractor, data_service)


def get_risk_service(
    model_manager: ModelManager = Depends(get_model_manager),
    feature_extractor: FeatureExtractor = Depends(get_feature_extractor),
    data_service: DataService = Depends(get_data_service)
) -> RiskService:
    """获取风险服务"""
    return RiskService(model_manager, feature_extractor, data_service)
