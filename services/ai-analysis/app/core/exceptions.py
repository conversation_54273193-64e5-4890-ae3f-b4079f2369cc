"""
自定义异常类

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from typing import Optional, Dict, Any


class AIAnalysisException(Exception):
    """AI分析服务基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "AI_ANALYSIS_ERROR",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ModelNotFoundError(AIAnalysisException):
    """模型未找到异常"""
    
    def __init__(self, model_name: str):
        super().__init__(
            message=f"模型 '{model_name}' 未找到",
            error_code="MODEL_NOT_FOUND",
            status_code=404,
            details={"model_name": model_name}
        )


class ModelLoadError(AIAnalysisException):
    """模型加载异常"""
    
    def __init__(self, model_name: str, error: str):
        super().__init__(
            message=f"模型 '{model_name}' 加载失败: {error}",
            error_code="MODEL_LOAD_ERROR",
            status_code=500,
            details={"model_name": model_name, "error": error}
        )


class PredictionError(AIAnalysisException):
    """预测异常"""
    
    def __init__(self, message: str, model_name: Optional[str] = None):
        super().__init__(
            message=f"预测失败: {message}",
            error_code="PREDICTION_ERROR",
            status_code=500,
            details={"model_name": model_name} if model_name else {}
        )


class DataValidationError(AIAnalysisException):
    """数据验证异常"""
    
    def __init__(self, message: str, field: Optional[str] = None):
        super().__init__(
            message=f"数据验证失败: {message}",
            error_code="DATA_VALIDATION_ERROR",
            status_code=400,
            details={"field": field} if field else {}
        )


class FeatureExtractionError(AIAnalysisException):
    """特征提取异常"""
    
    def __init__(self, message: str):
        super().__init__(
            message=f"特征提取失败: {message}",
            error_code="FEATURE_EXTRACTION_ERROR",
            status_code=500
        )


class InsufficientDataError(AIAnalysisException):
    """数据不足异常"""
    
    def __init__(self, required_samples: int, actual_samples: int):
        super().__init__(
            message=f"数据不足，需要至少 {required_samples} 个样本，实际只有 {actual_samples} 个",
            error_code="INSUFFICIENT_DATA",
            status_code=400,
            details={
                "required_samples": required_samples,
                "actual_samples": actual_samples
            }
        )


class ModelTrainingError(AIAnalysisException):
    """模型训练异常"""
    
    def __init__(self, message: str, model_type: Optional[str] = None):
        super().__init__(
            message=f"模型训练失败: {message}",
            error_code="MODEL_TRAINING_ERROR",
            status_code=500,
            details={"model_type": model_type} if model_type else {}
        )


class ExternalServiceError(AIAnalysisException):
    """外部服务异常"""
    
    def __init__(self, service_name: str, error: str):
        super().__init__(
            message=f"外部服务 '{service_name}' 调用失败: {error}",
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=503,
            details={"service_name": service_name, "error": error}
        )


class ConfigurationError(AIAnalysisException):
    """配置异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(
            message=f"配置错误: {message}",
            error_code="CONFIGURATION_ERROR",
            status_code=500,
            details={"config_key": config_key} if config_key else {}
        )


class RateLimitError(AIAnalysisException):
    """速率限制异常"""
    
    def __init__(self, limit: int, window: int):
        super().__init__(
            message=f"请求频率过高，限制为 {limit} 次/{window} 秒",
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=429,
            details={"limit": limit, "window": window}
        )


class ResourceNotFoundError(AIAnalysisException):
    """资源未找到异常"""
    
    def __init__(self, resource_type: str, resource_id: str):
        super().__init__(
            message=f"{resource_type} '{resource_id}' 未找到",
            error_code="RESOURCE_NOT_FOUND",
            status_code=404,
            details={"resource_type": resource_type, "resource_id": resource_id}
        )


class UnauthorizedError(AIAnalysisException):
    """未授权异常"""
    
    def __init__(self, message: str = "未授权访问"):
        super().__init__(
            message=message,
            error_code="UNAUTHORIZED",
            status_code=401
        )


class ForbiddenError(AIAnalysisException):
    """禁止访问异常"""
    
    def __init__(self, message: str = "禁止访问"):
        super().__init__(
            message=message,
            error_code="FORBIDDEN",
            status_code=403
        )
