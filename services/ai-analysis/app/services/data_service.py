"""
数据服务

负责从项目管理服务获取数据

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import httpx
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from uuid import UUID
from loguru import logger

from app.core.config import settings
from app.core.exceptions import ExternalServiceError, ResourceNotFoundError
from app.models.schemas import ProjectData


class DataService:
    """数据服务"""
    
    def __init__(self):
        self.project_service_url = settings.PROJECT_MANAGEMENT_SERVICE_URL
        self.user_service_url = settings.USER_SERVICE_URL
        self.timeout = settings.REQUEST_TIMEOUT
        
        # HTTP客户端配置
        self.client_config = {
            "timeout": self.timeout,
            "follow_redirects": True,
            "verify": False  # 开发环境，生产环境应该设置为True
        }
    
    async def get_project_data(self, project_id: UUID) -> Optional[ProjectData]:
        """获取项目基础数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.get(
                    f"{self.project_service_url}/api/v1/projects/{project_id}"
                )
                
                if response.status_code == 404:
                    return None
                
                if response.status_code != 200:
                    raise ExternalServiceError(
                        "project-management",
                        f"HTTP {response.status_code}: {response.text}"
                    )
                
                data = response.json()
                
                # 转换为ProjectData模型
                return ProjectData(
                    project_id=UUID(data["id"]),
                    name=data["name"],
                    description=data.get("description"),
                    start_date=datetime.fromisoformat(data["startDate"]).date(),
                    end_date=datetime.fromisoformat(data["endDate"]).date() if data.get("endDate") else None,
                    status=data["status"],
                    progress=float(data.get("progress", 0)),
                    team_size=int(data.get("teamSize", 1)),
                    budget=float(data["budget"]) if data.get("budget") else None,
                    complexity_score=float(data.get("complexityScore", 1))
                )
                
        except httpx.RequestError as e:
            logger.error(f"获取项目数据失败: {e}")
            raise ExternalServiceError("project-management", str(e))
        except Exception as e:
            logger.error(f"解析项目数据失败: {e}")
            raise ExternalServiceError("project-management", f"数据解析失败: {e}")
    
    async def get_task_statistics(self, project_id: UUID) -> Dict[str, Any]:
        """获取任务统计数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.get(
                    f"{self.project_service_url}/api/v1/projects/{project_id}/tasks/statistics"
                )
                
                if response.status_code == 404:
                    return {"total_tasks": 0, "completed_tasks": 0}
                
                if response.status_code != 200:
                    raise ExternalServiceError(
                        "project-management",
                        f"HTTP {response.status_code}: {response.text}"
                    )
                
                return response.json()
                
        except httpx.RequestError as e:
            logger.error(f"获取任务统计失败: {e}")
            # 返回默认值而不是抛出异常
            return {"total_tasks": 0, "completed_tasks": 0}
    
    async def get_progress_history(self, project_id: UUID, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取进度历史数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                params = {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat()
                }
                
                response = await client.get(
                    f"{self.project_service_url}/api/v1/analytics/project/{project_id}/trends/progress",
                    params=params
                )
                
                if response.status_code == 404:
                    return []
                
                if response.status_code != 200:
                    logger.warning(f"获取进度历史失败: HTTP {response.status_code}")
                    return []
                
                data = response.json()
                return data.get("trend", [])
                
        except httpx.RequestError as e:
            logger.warning(f"获取进度历史失败: {e}")
            return []
    
    async def get_team_data(self, project_id: UUID, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取团队数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.get(
                    f"{self.project_service_url}/api/v1/projects/{project_id}/team"
                )
                
                if response.status_code == 404:
                    return []
                
                if response.status_code != 200:
                    logger.warning(f"获取团队数据失败: HTTP {response.status_code}")
                    return []
                
                data = response.json()
                
                # 模拟团队数据结构
                team_members = []
                for member in data.get("members", []):
                    team_members.append({
                        "user_id": member.get("userId"),
                        "role": member.get("role", "developer"),
                        "experience_years": member.get("experienceYears", 2),
                        "join_date": member.get("joinDate"),
                        "leave_date": member.get("leaveDate"),
                        "performance_score": member.get("performanceScore", 75)
                    })
                
                return team_members
                
        except httpx.RequestError as e:
            logger.warning(f"获取团队数据失败: {e}")
            return []
    
    async def get_quality_data(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """获取质量数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                params = {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat()
                }
                
                response = await client.get(
                    f"{self.project_service_url}/api/v1/analytics/project/{project_id}/trends/quality",
                    params=params
                )
                
                if response.status_code == 404:
                    return {}
                
                if response.status_code != 200:
                    logger.warning(f"获取质量数据失败: HTTP {response.status_code}")
                    return {}
                
                data = response.json()
                
                # 从趋势数据中提取最新的质量指标
                trend = data.get("trend", [])
                if trend:
                    latest = trend[-1]
                    return {
                        "total_bugs": latest.get("bugCount", 0),
                        "lines_of_code": latest.get("linesOfCode", 1000),
                        "code_review_coverage": latest.get("codeReviewPassRate", 80)
                    }
                
                return {}
                
        except httpx.RequestError as e:
            logger.warning(f"获取质量数据失败: {e}")
            return {}
    
    async def get_time_data(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """获取时间数据"""
        try:
            # 获取项目基础数据
            project_data = await self.get_project_data(project_id)
            if not project_data:
                return {}
            
            # 获取进度趋势
            async with httpx.AsyncClient(**self.client_config) as client:
                params = {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat()
                }
                
                response = await client.get(
                    f"{self.project_service_url}/api/v1/analytics/project/{project_id}/trends/progress",
                    params=params
                )
                
                if response.status_code != 200:
                    logger.warning(f"获取时间数据失败: HTTP {response.status_code}")
                    return {}
                
                data = response.json()
                trend = data.get("trend", [])
                
                if trend:
                    latest = trend[-1]
                    return {
                        "planned_progress": latest.get("plannedProgress", 0),
                        "actual_progress": latest.get("actualProgress", 0),
                        "end_date": project_data.end_date
                    }
                
                return {
                    "planned_progress": 0,
                    "actual_progress": project_data.progress,
                    "end_date": project_data.end_date
                }
                
        except httpx.RequestError as e:
            logger.warning(f"获取时间数据失败: {e}")
            return {}
    
    async def get_risk_data(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """获取风险数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.get(
                    f"{self.project_service_url}/api/v1/analytics/project/{project_id}/risk-analysis"
                )
                
                if response.status_code == 404:
                    return {}
                
                if response.status_code != 200:
                    logger.warning(f"获取风险数据失败: HTTP {response.status_code}")
                    return {}
                
                data = response.json()
                
                return {
                    "complexity_score": data.get("complexityScore", 1.0),
                    "risk_factors": data.get("riskFactors", []),
                    "stakeholder_satisfaction": data.get("stakeholderSatisfaction", 50.0)
                }
                
        except httpx.RequestError as e:
            logger.warning(f"获取风险数据失败: {e}")
            return {}
    
    async def get_communication_data(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """获取沟通数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                params = {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat()
                }
                
                response = await client.get(
                    f"{self.project_service_url}/api/v1/analytics/project/{project_id}/trends/collaboration",
                    params=params
                )
                
                if response.status_code == 404:
                    return {"total_count": 0}
                
                if response.status_code != 200:
                    logger.warning(f"获取沟通数据失败: HTTP {response.status_code}")
                    return {"total_count": 0}
                
                data = response.json()
                trend = data.get("trend", [])
                
                # 计算总沟通次数
                total_count = sum(point.get("communicationCount", 0) for point in trend)
                
                return {"total_count": total_count}
                
        except httpx.RequestError as e:
            logger.warning(f"获取沟通数据失败: {e}")
            return {"total_count": 0}
    
    async def get_analytics_data(self, project_id: UUID, analytics_type: str) -> Dict[str, Any]:
        """获取分析数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.get(
                    f"{self.project_service_url}/api/v1/analytics/project/{project_id}/dashboard"
                )
                
                if response.status_code == 404:
                    return {}
                
                if response.status_code != 200:
                    logger.warning(f"获取分析数据失败: HTTP {response.status_code}")
                    return {}
                
                return response.json()
                
        except httpx.RequestError as e:
            logger.warning(f"获取分析数据失败: {e}")
            return {}
    
    async def get_user_data(self, user_id: UUID) -> Dict[str, Any]:
        """获取用户数据"""
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.get(
                    f"{self.user_service_url}/api/v1/users/{user_id}"
                )
                
                if response.status_code == 404:
                    return {}
                
                if response.status_code != 200:
                    logger.warning(f"获取用户数据失败: HTTP {response.status_code}")
                    return {}
                
                return response.json()
                
        except httpx.RequestError as e:
            logger.warning(f"获取用户数据失败: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        results = {}
        
        # 检查项目管理服务
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.get(f"{self.project_service_url}/health")
                results["project_management"] = response.status_code == 200
        except Exception:
            results["project_management"] = False
        
        # 检查用户服务
        try:
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.get(f"{self.user_service_url}/health")
                results["user_service"] = response.status_code == 200
        except Exception:
            results["user_service"] = False
        
        return results
