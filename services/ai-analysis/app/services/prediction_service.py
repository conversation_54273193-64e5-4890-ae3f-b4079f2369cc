"""
预测服务

提供项目进度预测功能

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
from uuid import UUID
from loguru import logger

from app.core.config import settings
from app.core.exceptions import PredictionError, InsufficientDataError
from app.models.schemas import (
    ProgressPredictionRequest, ProgressPredictionResult,
    PredictionConfidence, ProjectFeatures
)
from app.ml.model_manager import ModelManager
from app.ml.feature_extractor import FeatureExtractor
from app.services.data_service import DataService


class PredictionService:
    """预测服务"""
    
    def __init__(self, model_manager: ModelManager, feature_extractor: FeatureExtractor, data_service: DataService):
        self.model_manager = model_manager
        self.feature_extractor = feature_extractor
        self.data_service = data_service
        self.confidence_threshold = settings.PREDICTION_CONFIDENCE_THRESHOLD
    
    async def predict_progress(self, request: ProgressPredictionRequest) -> ProgressPredictionResult:
        """预测项目进度"""
        try:
            logger.info(f"开始预测项目 {request.project_id} 的进度")
            
            # 提取特征
            features = await self.feature_extractor.extract_project_features(request.project_id)
            
            # 验证特征
            if not self.feature_extractor.validate_features(features):
                raise PredictionError("特征数据验证失败")
            
            # 准备模型输入
            feature_vector = self._prepare_progress_features(features)
            
            # 执行预测
            prediction = await self.model_manager.predict("progress_predictor", feature_vector)
            confidence_scores = await self.model_manager.predict_proba("progress_predictor", feature_vector)
            
            # 处理预测结果
            predicted_progress = float(prediction[0])
            confidence_score = float(np.max(confidence_scores[0]))
            
            # 计算预测完成日期
            predicted_completion_date = self._calculate_completion_date(
                features, predicted_progress, request.target_date
            )
            
            # 确定置信度等级
            confidence_level = self._determine_confidence_level(confidence_score)
            
            # 生成影响因子和建议
            factors = self._analyze_prediction_factors(features, predicted_progress)
            recommendations = self._generate_progress_recommendations(features, predicted_progress)
            
            result = ProgressPredictionResult(
                project_id=request.project_id,
                current_progress=features.progress_percentage,
                predicted_completion_date=predicted_completion_date,
                predicted_final_progress=predicted_progress,
                confidence_score=confidence_score,
                confidence_level=confidence_level,
                factors=factors,
                recommendations=recommendations
            )
            
            logger.info(f"项目 {request.project_id} 进度预测完成: {predicted_progress:.1f}%")
            return result
            
        except Exception as e:
            logger.error(f"项目 {request.project_id} 进度预测失败: {e}")
            raise PredictionError(str(e))
    
    def _prepare_progress_features(self, features: ProjectFeatures) -> np.ndarray:
        """准备进度预测特征"""
        feature_vector = np.array([
            features.progress_percentage,
            features.velocity,
            features.burndown_rate,
            features.team_size,
            features.team_experience_avg,
            features.schedule_variance,
            features.complexity_score,
            features.bug_density,
            features.communication_frequency,
            features.deadline_pressure
        ]).reshape(1, -1)
        
        return feature_vector
    
    def _calculate_completion_date(self, features: ProjectFeatures, predicted_progress: float, target_date: Optional[date]) -> date:
        """计算预测完成日期"""
        try:
            current_progress = features.progress_percentage
            remaining_progress = max(0, predicted_progress - current_progress)
            
            if remaining_progress <= 0:
                return datetime.now().date()
            
            # 基于当前速度计算
            velocity = max(features.velocity, 0.1)  # 避免除零
            estimated_days = remaining_progress / velocity
            
            # 考虑团队效率和复杂度
            efficiency_factor = min(features.team_experience_avg / 5.0, 1.0)  # 标准化到0-1
            complexity_factor = features.complexity_score / 5.0  # 标准化
            
            adjusted_days = estimated_days * complexity_factor / efficiency_factor
            
            # 添加缓冲时间（10-30%）
            buffer_factor = 1.2 if features.risk_factors_count > 3 else 1.1
            final_days = adjusted_days * buffer_factor
            
            completion_date = datetime.now().date() + timedelta(days=int(final_days))
            
            # 如果有目标日期，进行比较
            if target_date and completion_date > target_date:
                logger.warning(f"预测完成日期 {completion_date} 晚于目标日期 {target_date}")
            
            return completion_date
            
        except Exception as e:
            logger.warning(f"计算完成日期失败: {e}")
            # 返回一个合理的默认值
            return datetime.now().date() + timedelta(days=30)
    
    def _determine_confidence_level(self, confidence_score: float) -> PredictionConfidence:
        """确定置信度等级"""
        if confidence_score >= 0.9:
            return PredictionConfidence.VERY_HIGH
        elif confidence_score >= 0.75:
            return PredictionConfidence.HIGH
        elif confidence_score >= 0.6:
            return PredictionConfidence.MEDIUM
        else:
            return PredictionConfidence.LOW
    
    def _analyze_prediction_factors(self, features: ProjectFeatures, predicted_progress: float) -> List[Dict[str, Any]]:
        """分析预测影响因子"""
        factors = []
        
        # 团队因子
        if features.team_size < 3:
            factors.append({
                "category": "team",
                "factor": "small_team",
                "impact": "negative",
                "description": "团队规模较小可能影响进度",
                "value": features.team_size
            })
        
        # 速度因子
        if features.velocity < 1.0:
            factors.append({
                "category": "velocity",
                "factor": "low_velocity",
                "impact": "negative", 
                "description": "项目速度较慢",
                "value": features.velocity
            })
        
        # 质量因子
        if features.bug_density > 5.0:
            factors.append({
                "category": "quality",
                "factor": "high_bug_density",
                "impact": "negative",
                "description": "缺陷密度较高可能影响进度",
                "value": features.bug_density
            })
        
        # 进度偏差因子
        if abs(features.schedule_variance) > 10:
            factors.append({
                "category": "schedule",
                "factor": "schedule_variance",
                "impact": "negative" if features.schedule_variance < 0 else "positive",
                "description": "进度偏差较大",
                "value": features.schedule_variance
            })
        
        # 复杂度因子
        if features.complexity_score > 7:
            factors.append({
                "category": "complexity",
                "factor": "high_complexity",
                "impact": "negative",
                "description": "项目复杂度较高",
                "value": features.complexity_score
            })
        
        return factors
    
    def _generate_progress_recommendations(self, features: ProjectFeatures, predicted_progress: float) -> List[str]:
        """生成进度改进建议"""
        recommendations = []
        
        # 基于预测结果的建议
        if predicted_progress < 90:
            recommendations.append("建议增加资源投入或调整项目范围以确保按时完成")
        
        # 基于团队的建议
        if features.team_size < 5 and features.task_count > 50:
            recommendations.append("考虑增加团队成员以提高开发效率")
        
        # 基于速度的建议
        if features.velocity < 1.0:
            recommendations.append("分析并解决影响团队速度的障碍因素")
        
        # 基于质量的建议
        if features.bug_density > 3.0:
            recommendations.append("加强代码审查和测试以减少缺陷数量")
        
        # 基于沟通的建议
        if features.communication_frequency < 2.0:
            recommendations.append("增加团队沟通频率以提高协作效率")
        
        # 基于风险的建议
        if features.risk_factors_count > 3:
            recommendations.append("制定风险缓解计划以降低项目风险")
        
        # 基于截止日期压力的建议
        if features.deadline_pressure > 50:
            recommendations.append("重新评估项目范围或延长截止日期以减少压力")
        
        return recommendations
    
    async def predict_completion_probability(self, project_id: UUID, target_date: date) -> Dict[str, Any]:
        """预测按时完成的概率"""
        try:
            # 提取特征
            features = await self.feature_extractor.extract_project_features(project_id)
            
            # 准备特征向量
            feature_vector = self._prepare_progress_features(features)
            
            # 预测进度
            predicted_progress = await self.model_manager.predict("progress_predictor", feature_vector)
            
            # 计算完成概率
            current_date = datetime.now().date()
            days_to_target = (target_date - current_date).days
            
            if days_to_target <= 0:
                probability = 0.0 if features.progress_percentage < 100 else 1.0
            else:
                # 基于速度和剩余工作计算
                remaining_work = 100 - features.progress_percentage
                required_velocity = remaining_work / days_to_target
                
                # 概率基于当前速度与所需速度的比较
                velocity_ratio = features.velocity / required_velocity if required_velocity > 0 else 1.0
                probability = min(velocity_ratio, 1.0)
                
                # 考虑其他因素调整概率
                if features.risk_factors_count > 3:
                    probability *= 0.8
                if features.bug_density > 5:
                    probability *= 0.9
                if features.team_turnover_rate > 20:
                    probability *= 0.85
            
            return {
                "project_id": str(project_id),
                "target_date": target_date.isoformat(),
                "completion_probability": float(probability),
                "current_progress": features.progress_percentage,
                "predicted_final_progress": float(predicted_progress[0]),
                "days_remaining": days_to_target,
                "required_velocity": required_velocity if days_to_target > 0 else 0,
                "current_velocity": features.velocity
            }
            
        except Exception as e:
            logger.error(f"预测完成概率失败: {e}")
            raise PredictionError(str(e))
    
    async def batch_predict_progress(self, project_ids: List[UUID]) -> List[Dict[str, Any]]:
        """批量预测项目进度"""
        results = []
        
        for project_id in project_ids:
            try:
                request = ProgressPredictionRequest(project_id=project_id)
                result = await self.predict_progress(request)
                results.append(result.dict())
            except Exception as e:
                logger.error(f"批量预测失败，项目 {project_id}: {e}")
                results.append({
                    "project_id": str(project_id),
                    "error": str(e),
                    "success": False
                })
        
        return results
    
    def get_prediction_metrics(self) -> Dict[str, Any]:
        """获取预测性能指标"""
        # 这里应该从数据库或缓存中获取实际的性能指标
        return {
            "total_predictions": 0,
            "average_confidence": 0.0,
            "accuracy_rate": 0.0,
            "last_model_update": datetime.now().isoformat()
        }
