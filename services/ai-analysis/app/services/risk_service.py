"""
风险分析服务

提供项目风险识别和预警功能

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from uuid import UUID
from loguru import logger

from app.core.config import settings
from app.core.exceptions import PredictionError
from app.models.schemas import (
    RiskAnalysisRequest, RiskAnalysisResult, RiskFactor, RiskLevel,
    ProjectFeatures
)
from app.ml.model_manager import ModelManager
from app.ml.feature_extractor import FeatureExtractor
from app.services.data_service import DataService


class RiskService:
    """风险分析服务"""
    
    def __init__(self, model_manager: ModelManager, feature_extractor: FeatureExtractor, data_service: DataService):
        self.model_manager = model_manager
        self.feature_extractor = feature_extractor
        self.data_service = data_service
        self.risk_threshold_high = settings.RISK_THRESHOLD_HIGH
        self.risk_threshold_medium = settings.RISK_THRESHOLD_MEDIUM
    
    async def analyze_risks(self, request: RiskAnalysisRequest) -> RiskAnalysisResult:
        """分析项目风险"""
        try:
            logger.info(f"开始分析项目 {request.project_id} 的风险")
            
            # 提取特征
            features = await self.feature_extractor.extract_project_features(request.project_id)
            
            # 准备模型输入
            feature_vector = self._prepare_risk_features(features)
            
            # 执行风险预测
            risk_scores = await self.model_manager.predict("risk_analyzer", feature_vector)
            risk_probabilities = await self.model_manager.predict_proba("risk_analyzer", feature_vector)
            
            # 分析各类风险
            risk_factors = []
            for category in request.risk_categories:
                category_risks = await self._analyze_category_risks(category, features, risk_scores[0])
                risk_factors.extend(category_risks)
            
            # 计算总体风险
            overall_risk_score = float(risk_scores[0])
            overall_risk_level = self._determine_risk_level(overall_risk_score)
            
            # 分析风险趋势
            risk_trend = await self._analyze_risk_trend(request.project_id)
            
            # 识别关键风险区域
            critical_areas = self._identify_critical_areas(risk_factors)
            
            # 生成建议
            recommendations = []
            if request.include_mitigation_suggestions:
                recommendations = self._generate_risk_recommendations(risk_factors, features)
            
            result = RiskAnalysisResult(
                project_id=request.project_id,
                overall_risk_level=overall_risk_level,
                overall_risk_score=overall_risk_score,
                risk_factors=risk_factors,
                risk_trend=risk_trend,
                critical_areas=critical_areas,
                recommendations=recommendations
            )
            
            logger.info(f"项目 {request.project_id} 风险分析完成: {overall_risk_level}")
            return result
            
        except Exception as e:
            logger.error(f"项目 {request.project_id} 风险分析失败: {e}")
            raise PredictionError(str(e))
    
    def _prepare_risk_features(self, features: ProjectFeatures) -> np.ndarray:
        """准备风险分析特征"""
        feature_vector = np.array([
            features.schedule_variance,
            features.team_turnover_rate,
            features.bug_density,
            features.velocity,
            features.deadline_pressure,
            features.complexity_score,
            features.communication_frequency,
            features.team_experience_avg,
            features.risk_factors_count,
            features.stakeholder_satisfaction
        ]).reshape(1, -1)
        
        return feature_vector
    
    async def _analyze_category_risks(self, category: str, features: ProjectFeatures, base_risk_score: float) -> List[RiskFactor]:
        """分析特定类别的风险"""
        risks = []
        
        if category == "schedule":
            risks.extend(self._analyze_schedule_risks(features, base_risk_score))
        elif category == "budget":
            risks.extend(self._analyze_budget_risks(features, base_risk_score))
        elif category == "quality":
            risks.extend(self._analyze_quality_risks(features, base_risk_score))
        elif category == "team":
            risks.extend(self._analyze_team_risks(features, base_risk_score))
        
        return risks
    
    def _analyze_schedule_risks(self, features: ProjectFeatures, base_risk_score: float) -> List[RiskFactor]:
        """分析进度风险"""
        risks = []
        
        # 进度偏差风险
        if features.schedule_variance < -10:
            risk_level = RiskLevel.HIGH if features.schedule_variance < -20 else RiskLevel.MEDIUM
            risks.append(RiskFactor(
                category="schedule",
                factor_name="schedule_delay",
                risk_level=risk_level,
                impact_score=min(abs(features.schedule_variance) / 50, 1.0),
                probability=0.8,
                description=f"项目进度落后 {abs(features.schedule_variance):.1f}%",
                mitigation_suggestions=[
                    "增加资源投入",
                    "重新评估项目范围",
                    "优化工作流程",
                    "加强项目监控"
                ]
            ))
        
        # 速度风险
        if features.velocity < 0.5:
            risks.append(RiskFactor(
                category="schedule",
                factor_name="low_velocity",
                risk_level=RiskLevel.MEDIUM,
                impact_score=0.6,
                probability=0.7,
                description="团队开发速度较慢",
                mitigation_suggestions=[
                    "分析速度瓶颈",
                    "提供技能培训",
                    "改进开发工具",
                    "优化团队协作"
                ]
            ))
        
        # 截止日期压力风险
        if features.deadline_pressure > 70:
            risks.append(RiskFactor(
                category="schedule",
                factor_name="deadline_pressure",
                risk_level=RiskLevel.HIGH,
                impact_score=features.deadline_pressure / 100,
                probability=0.9,
                description="截止日期压力过大",
                mitigation_suggestions=[
                    "重新协商截止日期",
                    "减少项目范围",
                    "增加团队规模",
                    "采用敏捷开发方法"
                ]
            ))
        
        return risks
    
    def _analyze_budget_risks(self, features: ProjectFeatures, base_risk_score: float) -> List[RiskFactor]:
        """分析预算风险"""
        risks = []
        
        # 基于团队规模和项目复杂度评估预算风险
        if features.team_size > 10 and features.complexity_score > 7:
            risks.append(RiskFactor(
                category="budget",
                factor_name="high_cost_complexity",
                risk_level=RiskLevel.MEDIUM,
                impact_score=0.6,
                probability=0.5,
                description="大团队高复杂度项目成本风险",
                mitigation_suggestions=[
                    "优化团队结构",
                    "分阶段交付",
                    "加强成本监控",
                    "评估外包可能性"
                ]
            ))
        
        return risks
    
    def _analyze_quality_risks(self, features: ProjectFeatures, base_risk_score: float) -> List[RiskFactor]:
        """分析质量风险"""
        risks = []
        
        # 缺陷密度风险
        if features.bug_density > 5:
            risk_level = RiskLevel.HIGH if features.bug_density > 10 else RiskLevel.MEDIUM
            risks.append(RiskFactor(
                category="quality",
                factor_name="high_bug_density",
                risk_level=risk_level,
                impact_score=min(features.bug_density / 20, 1.0),
                probability=0.8,
                description=f"缺陷密度过高: {features.bug_density:.1f}/千行",
                mitigation_suggestions=[
                    "加强代码审查",
                    "增加自动化测试",
                    "提供质量培训",
                    "建立质量门禁"
                ]
            ))
        
        # 代码审查覆盖率风险
        if features.code_review_coverage < 60:
            risks.append(RiskFactor(
                category="quality",
                factor_name="low_code_review",
                risk_level=RiskLevel.MEDIUM,
                impact_score=0.5,
                probability=0.6,
                description=f"代码审查覆盖率较低: {features.code_review_coverage:.1f}%",
                mitigation_suggestions=[
                    "强制代码审查流程",
                    "培训审查技能",
                    "使用审查工具",
                    "建立审查标准"
                ]
            ))
        
        return risks
    
    def _analyze_team_risks(self, features: ProjectFeatures, base_risk_score: float) -> List[RiskFactor]:
        """分析团队风险"""
        risks = []
        
        # 团队流失率风险
        if features.team_turnover_rate > 20:
            risk_level = RiskLevel.HIGH if features.team_turnover_rate > 40 else RiskLevel.MEDIUM
            risks.append(RiskFactor(
                category="team",
                factor_name="high_turnover",
                risk_level=risk_level,
                impact_score=features.team_turnover_rate / 100,
                probability=0.7,
                description=f"团队流失率过高: {features.team_turnover_rate:.1f}%",
                mitigation_suggestions=[
                    "改善工作环境",
                    "提供职业发展机会",
                    "调整薪酬待遇",
                    "加强团队建设"
                ]
            ))
        
        # 团队经验不足风险
        if features.team_experience_avg < 2:
            risks.append(RiskFactor(
                category="team",
                factor_name="low_experience",
                risk_level=RiskLevel.MEDIUM,
                impact_score=0.6,
                probability=0.6,
                description=f"团队平均经验不足: {features.team_experience_avg:.1f}年",
                mitigation_suggestions=[
                    "提供技能培训",
                    "安排导师指导",
                    "增加有经验的成员",
                    "采用结对编程"
                ]
            ))
        
        # 沟通频率风险
        if features.communication_frequency < 1:
            risks.append(RiskFactor(
                category="team",
                factor_name="poor_communication",
                risk_level=RiskLevel.MEDIUM,
                impact_score=0.5,
                probability=0.5,
                description="团队沟通频率较低",
                mitigation_suggestions=[
                    "建立定期会议制度",
                    "使用协作工具",
                    "改善沟通流程",
                    "培养沟通技能"
                ]
            ))
        
        return risks
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """确定风险等级"""
        if risk_score >= self.risk_threshold_high:
            return RiskLevel.HIGH
        elif risk_score >= self.risk_threshold_medium:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    async def _analyze_risk_trend(self, project_id: UUID) -> str:
        """分析风险趋势"""
        try:
            # 获取历史风险数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            # 这里应该从历史数据中分析趋势
            # 暂时返回模拟结果
            import random
            trends = ["increasing", "stable", "decreasing"]
            return random.choice(trends)
            
        except Exception as e:
            logger.warning(f"分析风险趋势失败: {e}")
            return "stable"
    
    def _identify_critical_areas(self, risk_factors: List[RiskFactor]) -> List[str]:
        """识别关键风险区域"""
        critical_areas = []
        
        # 按类别统计高风险因子
        category_risks = {}
        for factor in risk_factors:
            if factor.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                category = factor.category
                if category not in category_risks:
                    category_risks[category] = 0
                category_risks[category] += 1
        
        # 识别风险最高的区域
        for category, count in category_risks.items():
            if count >= 2:  # 有2个或以上高风险因子
                critical_areas.append(category)
        
        return critical_areas
    
    def _generate_risk_recommendations(self, risk_factors: List[RiskFactor], features: ProjectFeatures) -> List[str]:
        """生成风险缓解建议"""
        recommendations = []
        
        # 基于风险因子生成建议
        high_risk_factors = [f for f in risk_factors if f.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]]
        
        if high_risk_factors:
            recommendations.append("立即制定风险缓解计划，重点关注高风险因子")
        
        # 基于风险类别生成建议
        categories = set(f.category for f in high_risk_factors)
        
        if "schedule" in categories:
            recommendations.append("加强进度监控，考虑调整项目计划或增加资源")
        
        if "quality" in categories:
            recommendations.append("建立质量保证流程，增加测试和代码审查")
        
        if "team" in categories:
            recommendations.append("关注团队稳定性，提供必要的支持和培训")
        
        # 基于整体情况生成建议
        if len(risk_factors) > 5:
            recommendations.append("风险因子较多，建议进行全面的项目健康检查")
        
        return recommendations
    
    async def get_risk_alerts(self, project_id: UUID) -> List[Dict[str, Any]]:
        """获取风险预警"""
        try:
            request = RiskAnalysisRequest(project_id=project_id)
            result = await self.analyze_risks(request)
            
            alerts = []
            
            # 生成高风险预警
            for factor in result.risk_factors:
                if factor.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                    alerts.append({
                        "type": "risk_alert",
                        "level": factor.risk_level.value,
                        "category": factor.category,
                        "message": factor.description,
                        "impact_score": factor.impact_score,
                        "probability": factor.probability,
                        "suggestions": factor.mitigation_suggestions[:2]  # 只返回前2个建议
                    })
            
            return alerts
            
        except Exception as e:
            logger.error(f"获取风险预警失败: {e}")
            return []
    
    async def batch_analyze_risks(self, project_ids: List[UUID]) -> List[Dict[str, Any]]:
        """批量分析项目风险"""
        results = []
        
        for project_id in project_ids:
            try:
                request = RiskAnalysisRequest(project_id=project_id)
                result = await self.analyze_risks(request)
                results.append(result.dict())
            except Exception as e:
                logger.error(f"批量风险分析失败，项目 {project_id}: {e}")
                results.append({
                    "project_id": str(project_id),
                    "error": str(e),
                    "success": False
                })
        
        return results
