"""
进度预测API端点

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from datetime import date
from typing import List, Dict, Any
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query
from loguru import logger

from app.models.schemas import (
    ProgressPredictionRequest, ProgressPredictionResult,
    BatchAnalysisRequest, BatchAnalysisResult
)
from app.services.prediction_service import PredictionService
from app.core.dependencies import get_prediction_service

router = APIRouter()


@router.post("/progress", response_model=ProgressPredictionResult)
async def predict_progress(
    request: ProgressPredictionRequest,
    service: PredictionService = Depends(get_prediction_service)
):
    """预测项目进度"""
    try:
        result = await service.predict_progress(request)
        return result
    except Exception as e:
        logger.error(f"进度预测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/progress/{project_id}", response_model=ProgressPredictionResult)
async def predict_project_progress(
    project_id: UUID,
    target_date: date = Query(None, description="目标完成日期"),
    include_confidence_interval: bool = Query(True, description="是否包含置信区间"),
    service: PredictionService = Depends(get_prediction_service)
):
    """预测指定项目的进度"""
    try:
        request = ProgressPredictionRequest(
            project_id=project_id,
            target_date=target_date,
            include_confidence_interval=include_confidence_interval
        )
        result = await service.predict_progress(request)
        return result
    except Exception as e:
        logger.error(f"项目 {project_id} 进度预测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/completion-probability/{project_id}")
async def predict_completion_probability(
    project_id: UUID,
    target_date: date = Query(..., description="目标完成日期"),
    service: PredictionService = Depends(get_prediction_service)
):
    """预测项目按时完成的概率"""
    try:
        result = await service.predict_completion_probability(project_id, target_date)
        return result
    except Exception as e:
        logger.error(f"项目 {project_id} 完成概率预测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch-progress", response_model=BatchAnalysisResult)
async def batch_predict_progress(
    request: BatchAnalysisRequest,
    service: PredictionService = Depends(get_prediction_service)
):
    """批量预测项目进度"""
    try:
        results = await service.batch_predict_progress(request.project_ids)
        
        successful = sum(1 for r in results if r.get("success", True))
        failed = len(results) - successful
        
        return BatchAnalysisResult(
            total_projects=len(request.project_ids),
            successful_analyses=successful,
            failed_analyses=failed,
            results=results
        )
    except Exception as e:
        logger.error(f"批量进度预测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics")
async def get_prediction_metrics(
    service: PredictionService = Depends(get_prediction_service)
):
    """获取预测性能指标"""
    try:
        metrics = service.get_prediction_metrics()
        return metrics
    except Exception as e:
        logger.error(f"获取预测指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
