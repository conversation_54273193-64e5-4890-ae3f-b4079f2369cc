"""
智能分析API端点

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from typing import List, Dict, Any
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query
from loguru import logger

from app.models.schemas import ProjectFeatures
from app.ml.feature_extractor import FeatureExtractor
from app.services.data_service import DataService
from app.core.dependencies import get_feature_extractor, get_data_service

router = APIRouter()


@router.get("/features/{project_id}", response_model=ProjectFeatures)
async def extract_project_features(
    project_id: UUID,
    extractor: FeatureExtractor = Depends(get_feature_extractor)
):
    """提取项目特征"""
    try:
        features = await extractor.extract_project_features(project_id)
        return features
    except Exception as e:
        logger.error(f"提取项目 {project_id} 特征失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/features/batch")
async def extract_batch_features(
    project_ids: List[UUID],
    extractor: FeatureExtractor = Depends(get_feature_extractor)
):
    """批量提取项目特征"""
    try:
        features_list = await extractor.extract_batch_features(project_ids)
        return {
            "total_projects": len(project_ids),
            "extracted_count": len(features_list),
            "features": [f.dict() for f in features_list]
        }
    except Exception as e:
        logger.error(f"批量提取特征失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/project-data/{project_id}")
async def get_project_data(
    project_id: UUID,
    data_service: DataService = Depends(get_data_service)
):
    """获取项目数据"""
    try:
        project_data = await data_service.get_project_data(project_id)
        if not project_data:
            raise HTTPException(status_code=404, detail="项目不存在")
        return project_data.dict()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目 {project_id} 数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health-check")
async def health_check(
    data_service: DataService = Depends(get_data_service)
):
    """健康检查"""
    try:
        health_status = await data_service.health_check()
        return {
            "status": "healthy",
            "external_services": health_status,
            "all_services_healthy": all(health_status.values())
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "external_services": {},
            "all_services_healthy": False
        }
