"""
模型管理API端点

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from loguru import logger

from app.models.schemas import ModelInfo
from app.ml.model_manager import ModelManager
from app.core.dependencies import get_model_manager

router = APIRouter()


@router.get("/list")
async def list_models(
    model_manager: ModelManager = Depends(get_model_manager)
):
    """列出所有模型"""
    try:
        models = model_manager.list_models()
        return {
            "models": models,
            "total_count": len(models)
        }
    except Exception as e:
        logger.error(f"列出模型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/info/{model_name}")
async def get_model_info(
    model_name: str,
    model_manager: ModelManager = Depends(get_model_manager)
):
    """获取模型信息"""
    try:
        model_info = model_manager.get_model_info(model_name)
        if not model_info:
            raise HTTPException(status_code=404, detail="模型不存在")
        return model_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型 {model_name} 信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{model_name}")
async def get_model_status(
    model_name: str,
    model_manager: ModelManager = Depends(get_model_manager)
):
    """获取模型状态"""
    try:
        status = model_manager.get_model_status(model_name)
        return {
            "model_name": model_name,
            "status": status
        }
    except Exception as e:
        logger.error(f"获取模型 {model_name} 状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reload/{model_name}")
async def reload_model(
    model_name: str,
    model_manager: ModelManager = Depends(get_model_manager)
):
    """重新加载模型"""
    try:
        await model_manager.reload_model(model_name)
        return {
            "message": f"模型 {model_name} 重新加载成功",
            "model_name": model_name
        }
    except Exception as e:
        logger.error(f"重新加载模型 {model_name} 失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/warm-up")
async def warm_up_models(
    model_manager: ModelManager = Depends(get_model_manager)
):
    """预热模型"""
    try:
        await model_manager.warm_up_models()
        return {
            "message": "模型预热完成",
            "warmed_models": list(model_manager.models.keys())
        }
    except Exception as e:
        logger.error(f"模型预热失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cache-info")
async def get_cache_info(
    model_manager: ModelManager = Depends(get_model_manager)
):
    """获取缓存信息"""
    try:
        cache_info = model_manager.get_cache_info()
        return cache_info
    except Exception as e:
        logger.error(f"获取缓存信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
