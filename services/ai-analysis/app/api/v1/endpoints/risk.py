"""
风险分析API端点

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from typing import List, Dict, Any
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query
from loguru import logger

from app.models.schemas import (
    RiskAnalysisRequest, RiskAnalysisResult,
    BatchAnalysisRequest, BatchAnalysisResult
)
from app.services.risk_service import RiskService
from app.core.dependencies import get_risk_service

router = APIRouter()


@router.post("/analyze", response_model=RiskAnalysisResult)
async def analyze_risks(
    request: RiskAnalysisRequest,
    service: RiskService = Depends(get_risk_service)
):
    """分析项目风险"""
    try:
        result = await service.analyze_risks(request)
        return result
    except Exception as e:
        logger.error(f"风险分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/analyze/{project_id}", response_model=RiskAnalysisResult)
async def analyze_project_risks(
    project_id: UUID,
    risk_categories: List[str] = Query(
        default=["schedule", "budget", "quality", "team"],
        description="风险类别"
    ),
    include_mitigation_suggestions: bool = Query(True, description="是否包含缓解建议"),
    service: RiskService = Depends(get_risk_service)
):
    """分析指定项目的风险"""
    try:
        request = RiskAnalysisRequest(
            project_id=project_id,
            risk_categories=risk_categories,
            include_mitigation_suggestions=include_mitigation_suggestions
        )
        result = await service.analyze_risks(request)
        return result
    except Exception as e:
        logger.error(f"项目 {project_id} 风险分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts/{project_id}")
async def get_risk_alerts(
    project_id: UUID,
    service: RiskService = Depends(get_risk_service)
):
    """获取项目风险预警"""
    try:
        alerts = await service.get_risk_alerts(project_id)
        return {
            "project_id": str(project_id),
            "alerts": alerts,
            "alert_count": len(alerts)
        }
    except Exception as e:
        logger.error(f"获取项目 {project_id} 风险预警失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch-analyze", response_model=BatchAnalysisResult)
async def batch_analyze_risks(
    request: BatchAnalysisRequest,
    service: RiskService = Depends(get_risk_service)
):
    """批量分析项目风险"""
    try:
        results = await service.batch_analyze_risks(request.project_ids)
        
        successful = sum(1 for r in results if r.get("success", True))
        failed = len(results) - successful
        
        return BatchAnalysisResult(
            total_projects=len(request.project_ids),
            successful_analyses=successful,
            failed_analyses=failed,
            results=results
        )
    except Exception as e:
        logger.error(f"批量风险分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/categories")
async def get_risk_categories():
    """获取支持的风险类别"""
    return {
        "categories": [
            {
                "name": "schedule",
                "display_name": "进度风险",
                "description": "项目进度相关的风险因素"
            },
            {
                "name": "budget",
                "display_name": "预算风险", 
                "description": "项目成本和预算相关的风险"
            },
            {
                "name": "quality",
                "display_name": "质量风险",
                "description": "产品质量和技术债务风险"
            },
            {
                "name": "team",
                "display_name": "团队风险",
                "description": "团队人员和协作相关的风险"
            }
        ]
    }
