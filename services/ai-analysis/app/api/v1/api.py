"""
API路由汇总

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from fastapi import APIRouter

from app.api.v1.endpoints import prediction, risk, analytics, models

api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(prediction.router, prefix="/prediction", tags=["进度预测"])
api_router.include_router(risk.router, prefix="/risk", tags=["风险分析"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["智能分析"])
api_router.include_router(models.router, prefix="/models", tags=["模型管理"])
