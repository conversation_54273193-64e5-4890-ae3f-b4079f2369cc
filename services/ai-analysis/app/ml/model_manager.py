"""
机器学习模型管理器

负责模型的加载、缓存、预测和管理

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import os
import pickle
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from loguru import logger

from app.core.config import settings, ml_config
from app.core.exceptions import (
    ModelNotFoundError, ModelLoadError, PredictionError,
    ConfigurationError
)


class ModelManager:
    """机器学习模型管理器"""
    
    def __init__(self):
        self.models: Dict[str, Any] = {}
        self.model_metadata: Dict[str, Dict[str, Any]] = {}
        self.model_cache_size = settings.ML_CACHE_SIZE
        self.model_path = Path(settings.ML_MODEL_PATH)
        self.last_loaded: Dict[str, datetime] = {}
        
        # 确保模型目录存在
        self.model_path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self):
        """初始化模型管理器"""
        logger.info("初始化AI模型管理器...")
        
        try:
            # 加载预训练模型
            await self._load_default_models()
            
            # 验证模型
            await self._validate_models()
            
            logger.info(f"模型管理器初始化完成，已加载 {len(self.models)} 个模型")
            
        except Exception as e:
            logger.error(f"模型管理器初始化失败: {e}")
            raise ConfigurationError(f"模型管理器初始化失败: {e}")
    
    async def cleanup(self):
        """清理资源"""
        logger.info("清理模型管理器资源...")
        self.models.clear()
        self.model_metadata.clear()
        self.last_loaded.clear()
    
    async def _load_default_models(self):
        """加载默认模型"""
        default_models = [
            "progress_predictor",
            "risk_analyzer", 
            "team_performance_analyzer",
            "recommendation_engine"
        ]
        
        for model_name in default_models:
            try:
                await self.load_model(model_name)
            except Exception as e:
                logger.warning(f"加载默认模型 {model_name} 失败: {e}")
                # 创建模拟模型
                await self._create_mock_model(model_name)
    
    async def _create_mock_model(self, model_name: str):
        """创建模拟模型用于开发和测试"""
        logger.info(f"创建模拟模型: {model_name}")
        
        class MockModel:
            def __init__(self, name: str):
                self.name = name
                self.version = "mock_v1.0"
                self.created_at = datetime.now()
            
            def predict(self, features: np.ndarray) -> np.ndarray:
                """模拟预测"""
                if self.name == "progress_predictor":
                    # 返回进度预测 (0-100)
                    return np.random.uniform(50, 95, size=features.shape[0])
                elif self.name == "risk_analyzer":
                    # 返回风险分数 (0-1)
                    return np.random.uniform(0.1, 0.8, size=features.shape[0])
                elif self.name == "team_performance_analyzer":
                    # 返回绩效分数 (0-100)
                    return np.random.uniform(60, 90, size=features.shape[0])
                elif self.name == "recommendation_engine":
                    # 返回推荐分数 (0-1)
                    return np.random.uniform(0.3, 0.9, size=features.shape[0])
                else:
                    return np.random.random(features.shape[0])
            
            def predict_proba(self, features: np.ndarray) -> np.ndarray:
                """模拟概率预测"""
                n_samples = features.shape[0]
                n_classes = 3  # 低、中、高风险
                probs = np.random.dirichlet(np.ones(n_classes), size=n_samples)
                return probs
        
        mock_model = MockModel(model_name)
        self.models[model_name] = mock_model
        self.model_metadata[model_name] = {
            "name": model_name,
            "version": mock_model.version,
            "type": "mock",
            "created_at": mock_model.created_at,
            "status": "active",
            "is_mock": True
        }
        self.last_loaded[model_name] = datetime.now()
    
    async def load_model(self, model_name: str) -> bool:
        """加载指定模型"""
        try:
            model_file = self.model_path / f"{model_name}.pkl"
            
            if not model_file.exists():
                raise ModelNotFoundError(model_name)
            
            # 加载模型
            with open(model_file, 'rb') as f:
                model_data = pickle.load(f)
            
            # 提取模型和元数据
            if isinstance(model_data, dict):
                model = model_data.get('model')
                metadata = model_data.get('metadata', {})
            else:
                model = model_data
                metadata = {}
            
            # 存储模型
            self.models[model_name] = model
            self.model_metadata[model_name] = {
                "name": model_name,
                "version": metadata.get("version", "unknown"),
                "type": metadata.get("type", "unknown"),
                "created_at": metadata.get("created_at", datetime.now()),
                "status": "active",
                "is_mock": False,
                "file_path": str(model_file),
                "file_size": model_file.stat().st_size
            }
            self.last_loaded[model_name] = datetime.now()
            
            logger.info(f"模型 {model_name} 加载成功")
            return True
            
        except Exception as e:
            logger.error(f"加载模型 {model_name} 失败: {e}")
            raise ModelLoadError(model_name, str(e))
    
    async def unload_model(self, model_name: str):
        """卸载指定模型"""
        if model_name in self.models:
            del self.models[model_name]
            del self.model_metadata[model_name]
            if model_name in self.last_loaded:
                del self.last_loaded[model_name]
            logger.info(f"模型 {model_name} 已卸载")
    
    async def get_model(self, model_name: str) -> Any:
        """获取指定模型"""
        if model_name not in self.models:
            # 尝试加载模型
            await self.load_model(model_name)
        
        if model_name not in self.models:
            raise ModelNotFoundError(model_name)
        
        return self.models[model_name]
    
    async def predict(self, model_name: str, features: np.ndarray) -> np.ndarray:
        """使用指定模型进行预测"""
        try:
            model = await self.get_model(model_name)
            
            # 验证输入
            if not isinstance(features, np.ndarray):
                features = np.array(features)
            
            if features.ndim == 1:
                features = features.reshape(1, -1)
            
            # 执行预测
            predictions = model.predict(features)
            
            logger.debug(f"模型 {model_name} 预测完成，输入形状: {features.shape}, 输出形状: {predictions.shape}")
            return predictions
            
        except Exception as e:
            logger.error(f"模型 {model_name} 预测失败: {e}")
            raise PredictionError(str(e), model_name)
    
    async def predict_proba(self, model_name: str, features: np.ndarray) -> np.ndarray:
        """使用指定模型进行概率预测"""
        try:
            model = await self.get_model(model_name)
            
            # 验证输入
            if not isinstance(features, np.ndarray):
                features = np.array(features)
            
            if features.ndim == 1:
                features = features.reshape(1, -1)
            
            # 执行概率预测
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(features)
            else:
                # 如果模型不支持概率预测，返回确定性预测
                predictions = model.predict(features)
                probabilities = np.column_stack([1 - predictions, predictions])
            
            logger.debug(f"模型 {model_name} 概率预测完成")
            return probabilities
            
        except Exception as e:
            logger.error(f"模型 {model_name} 概率预测失败: {e}")
            raise PredictionError(str(e), model_name)
    
    async def _validate_models(self):
        """验证已加载的模型"""
        logger.info("验证已加载的模型...")
        
        for model_name, model in self.models.items():
            try:
                # 创建测试特征
                test_features = np.random.random((1, 10))
                
                # 测试预测
                predictions = model.predict(test_features)
                
                # 验证输出
                if not isinstance(predictions, np.ndarray):
                    raise ValueError(f"模型 {model_name} 输出类型错误")
                
                logger.debug(f"模型 {model_name} 验证通过")
                
            except Exception as e:
                logger.warning(f"模型 {model_name} 验证失败: {e}")
                # 标记为不可用
                if model_name in self.model_metadata:
                    self.model_metadata[model_name]["status"] = "invalid"
    
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        return self.model_metadata.get(model_name)
    
    def list_models(self) -> List[Dict[str, Any]]:
        """列出所有模型"""
        return list(self.model_metadata.values())
    
    def get_model_status(self, model_name: str) -> str:
        """获取模型状态"""
        metadata = self.model_metadata.get(model_name)
        if metadata:
            return metadata.get("status", "unknown")
        return "not_found"
    
    async def reload_model(self, model_name: str):
        """重新加载模型"""
        logger.info(f"重新加载模型: {model_name}")
        
        # 先卸载
        await self.unload_model(model_name)
        
        # 再加载
        await self.load_model(model_name)
    
    async def warm_up_models(self):
        """预热模型"""
        logger.info("预热模型...")
        
        warm_up_features = np.random.random((settings.MODEL_WARM_UP_SAMPLES, 10))
        
        for model_name in self.models.keys():
            try:
                await self.predict(model_name, warm_up_features)
                logger.debug(f"模型 {model_name} 预热完成")
            except Exception as e:
                logger.warning(f"模型 {model_name} 预热失败: {e}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            "loaded_models": len(self.models),
            "cache_size_limit": self.model_cache_size,
            "model_names": list(self.models.keys()),
            "last_loaded": {name: time.isoformat() for name, time in self.last_loaded.items()}
        }
