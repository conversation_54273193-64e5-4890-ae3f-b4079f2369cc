"""
特征提取器

从项目数据中提取机器学习特征

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from uuid import UUID
from loguru import logger

from app.core.config import settings
from app.core.exceptions import FeatureExtractionError, InsufficientDataError
from app.models.schemas import ProjectData, ProjectFeatures
from app.services.data_service import DataService


class FeatureExtractor:
    """特征提取器"""
    
    def __init__(self, data_service: DataService):
        self.data_service = data_service
        self.feature_window_days = settings.FEATURE_WINDOW_DAYS
        self.min_samples = settings.FEATURE_MIN_SAMPLES
    
    async def extract_project_features(self, project_id: UUID) -> ProjectFeatures:
        """提取项目特征"""
        try:
            logger.info(f"开始提取项目 {project_id} 的特征")
            
            # 获取项目基础数据
            project_data = await self.data_service.get_project_data(project_id)
            if not project_data:
                raise FeatureExtractionError(f"项目 {project_id} 数据不存在")
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.feature_window_days)
            
            # 提取各类特征
            basic_features = await self._extract_basic_features(project_data)
            progress_features = await self._extract_progress_features(project_id, start_date, end_date)
            team_features = await self._extract_team_features(project_id, start_date, end_date)
            quality_features = await self._extract_quality_features(project_id, start_date, end_date)
            time_features = await self._extract_time_features(project_id, start_date, end_date)
            risk_features = await self._extract_risk_features(project_id, start_date, end_date)
            
            # 合并特征
            features = ProjectFeatures(
                project_id=project_id,
                **basic_features,
                **progress_features,
                **team_features,
                **quality_features,
                **time_features,
                **risk_features
            )
            
            logger.info(f"项目 {project_id} 特征提取完成")
            return features
            
        except Exception as e:
            logger.error(f"项目 {project_id} 特征提取失败: {e}")
            raise FeatureExtractionError(str(e))
    
    async def _extract_basic_features(self, project_data: ProjectData) -> Dict[str, Any]:
        """提取基础特征"""
        start_date = project_data.start_date
        current_date = datetime.now().date()
        
        # 计算项目年龄
        project_age_days = (current_date - start_date).days
        
        # 获取任务统计
        task_stats = await self.data_service.get_task_statistics(project_data.project_id)
        
        return {
            "project_age_days": project_age_days,
            "team_size": project_data.team_size,
            "task_count": task_stats.get("total_tasks", 0),
            "completed_task_count": task_stats.get("completed_tasks", 0)
        }
    
    async def _extract_progress_features(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """提取进度相关特征"""
        try:
            # 获取进度历史数据
            progress_history = await self.data_service.get_progress_history(project_id, start_date, end_date)
            
            if not progress_history:
                return {
                    "progress_percentage": 0.0,
                    "velocity": 0.0,
                    "burndown_rate": 0.0
                }
            
            # 计算当前进度
            current_progress = progress_history[-1].get("progress", 0) if progress_history else 0
            
            # 计算速度（进度变化率）
            velocity = self._calculate_velocity(progress_history)
            
            # 计算燃尽率
            burndown_rate = self._calculate_burndown_rate(progress_history)
            
            return {
                "progress_percentage": float(current_progress),
                "velocity": velocity,
                "burndown_rate": burndown_rate
            }
            
        except Exception as e:
            logger.warning(f"提取进度特征失败: {e}")
            return {
                "progress_percentage": 0.0,
                "velocity": 0.0,
                "burndown_rate": 0.0
            }
    
    async def _extract_team_features(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """提取团队相关特征"""
        try:
            # 获取团队数据
            team_data = await self.data_service.get_team_data(project_id, start_date, end_date)
            
            if not team_data:
                return {
                    "team_experience_avg": 0.0,
                    "team_turnover_rate": 0.0,
                    "communication_frequency": 0.0
                }
            
            # 计算平均经验
            team_experience_avg = np.mean([member.get("experience_years", 0) for member in team_data])
            
            # 计算团队流失率
            team_turnover_rate = self._calculate_turnover_rate(team_data, start_date, end_date)
            
            # 计算沟通频率
            communication_frequency = await self._calculate_communication_frequency(project_id, start_date, end_date)
            
            return {
                "team_experience_avg": float(team_experience_avg),
                "team_turnover_rate": team_turnover_rate,
                "communication_frequency": communication_frequency
            }
            
        except Exception as e:
            logger.warning(f"提取团队特征失败: {e}")
            return {
                "team_experience_avg": 0.0,
                "team_turnover_rate": 0.0,
                "communication_frequency": 0.0
            }
    
    async def _extract_quality_features(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """提取质量相关特征"""
        try:
            # 获取质量数据
            quality_data = await self.data_service.get_quality_data(project_id, start_date, end_date)
            
            if not quality_data:
                return {
                    "bug_count": 0,
                    "bug_density": 0.0,
                    "code_review_coverage": 0.0
                }
            
            # 缺陷数量
            bug_count = quality_data.get("total_bugs", 0)
            
            # 缺陷密度（每千行代码的缺陷数）
            lines_of_code = quality_data.get("lines_of_code", 1)
            bug_density = (bug_count / lines_of_code) * 1000 if lines_of_code > 0 else 0
            
            # 代码审查覆盖率
            code_review_coverage = quality_data.get("code_review_coverage", 0)
            
            return {
                "bug_count": bug_count,
                "bug_density": float(bug_density),
                "code_review_coverage": float(code_review_coverage)
            }
            
        except Exception as e:
            logger.warning(f"提取质量特征失败: {e}")
            return {
                "bug_count": 0,
                "bug_density": 0.0,
                "code_review_coverage": 0.0
            }
    
    async def _extract_time_features(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """提取时间相关特征"""
        try:
            # 获取时间数据
            time_data = await self.data_service.get_time_data(project_id, start_date, end_date)
            
            if not time_data:
                return {
                    "schedule_variance": 0.0,
                    "deadline_pressure": 0.0
                }
            
            # 计算进度偏差
            planned_progress = time_data.get("planned_progress", 0)
            actual_progress = time_data.get("actual_progress", 0)
            schedule_variance = actual_progress - planned_progress
            
            # 计算截止日期压力
            deadline_pressure = self._calculate_deadline_pressure(project_id, time_data)
            
            return {
                "schedule_variance": float(schedule_variance),
                "deadline_pressure": deadline_pressure
            }
            
        except Exception as e:
            logger.warning(f"提取时间特征失败: {e}")
            return {
                "schedule_variance": 0.0,
                "deadline_pressure": 0.0
            }
    
    async def _extract_risk_features(self, project_id: UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """提取风险相关特征"""
        try:
            # 获取风险数据
            risk_data = await self.data_service.get_risk_data(project_id, start_date, end_date)
            
            if not risk_data:
                return {
                    "complexity_score": 1.0,
                    "risk_factors_count": 0,
                    "stakeholder_satisfaction": 50.0
                }
            
            # 复杂度评分
            complexity_score = risk_data.get("complexity_score", 1.0)
            
            # 风险因子数量
            risk_factors_count = len(risk_data.get("risk_factors", []))
            
            # 利益相关者满意度
            stakeholder_satisfaction = risk_data.get("stakeholder_satisfaction", 50.0)
            
            return {
                "complexity_score": float(complexity_score),
                "risk_factors_count": risk_factors_count,
                "stakeholder_satisfaction": float(stakeholder_satisfaction)
            }
            
        except Exception as e:
            logger.warning(f"提取风险特征失败: {e}")
            return {
                "complexity_score": 1.0,
                "risk_factors_count": 0,
                "stakeholder_satisfaction": 50.0
            }
    
    def _calculate_velocity(self, progress_history: List[Dict[str, Any]]) -> float:
        """计算项目速度"""
        if len(progress_history) < 2:
            return 0.0
        
        # 计算进度变化率
        progress_values = [p.get("progress", 0) for p in progress_history]
        time_diffs = []
        
        for i in range(1, len(progress_history)):
            prev_time = progress_history[i-1].get("timestamp")
            curr_time = progress_history[i].get("timestamp")
            if prev_time and curr_time:
                time_diff = (curr_time - prev_time).total_seconds() / 86400  # 转换为天
                time_diffs.append(time_diff)
        
        if not time_diffs:
            return 0.0
        
        # 计算平均每天的进度变化
        total_progress_change = progress_values[-1] - progress_values[0]
        total_time = sum(time_diffs)
        
        return total_progress_change / total_time if total_time > 0 else 0.0
    
    def _calculate_burndown_rate(self, progress_history: List[Dict[str, Any]]) -> float:
        """计算燃尽率"""
        if len(progress_history) < 2:
            return 0.0
        
        # 计算剩余工作的减少率
        remaining_work = [100 - p.get("progress", 0) for p in progress_history]
        
        if len(remaining_work) < 2:
            return 0.0
        
        # 线性回归计算斜率
        x = np.arange(len(remaining_work))
        y = np.array(remaining_work)
        
        if len(x) > 1:
            slope = np.polyfit(x, y, 1)[0]
            return abs(slope)  # 返回绝对值，表示燃尽速度
        
        return 0.0
    
    def _calculate_turnover_rate(self, team_data: List[Dict[str, Any]], start_date: datetime, end_date: datetime) -> float:
        """计算团队流失率"""
        if not team_data:
            return 0.0
        
        # 统计离职人员
        left_members = 0
        total_members = len(team_data)
        
        for member in team_data:
            leave_date = member.get("leave_date")
            if leave_date and start_date <= leave_date <= end_date:
                left_members += 1
        
        return (left_members / total_members) * 100 if total_members > 0 else 0.0
    
    async def _calculate_communication_frequency(self, project_id: UUID, start_date: datetime, end_date: datetime) -> float:
        """计算沟通频率"""
        try:
            # 获取沟通数据（评论、消息等）
            communication_data = await self.data_service.get_communication_data(project_id, start_date, end_date)
            
            if not communication_data:
                return 0.0
            
            total_communications = communication_data.get("total_count", 0)
            days = (end_date - start_date).days
            
            return total_communications / days if days > 0 else 0.0
            
        except Exception as e:
            logger.warning(f"计算沟通频率失败: {e}")
            return 0.0
    
    def _calculate_deadline_pressure(self, project_id: UUID, time_data: Dict[str, Any]) -> float:
        """计算截止日期压力"""
        try:
            end_date = time_data.get("end_date")
            current_progress = time_data.get("actual_progress", 0)
            
            if not end_date:
                return 0.0
            
            # 计算剩余时间
            current_date = datetime.now().date()
            if isinstance(end_date, datetime):
                end_date = end_date.date()
            
            remaining_days = (end_date - current_date).days
            
            if remaining_days <= 0:
                return 100.0  # 已过期，最高压力
            
            # 计算剩余工作量
            remaining_work = 100 - current_progress
            
            # 压力 = 剩余工作量 / 剩余时间
            pressure = (remaining_work / remaining_days) if remaining_days > 0 else 100.0
            
            # 标准化到0-100范围
            return min(pressure * 10, 100.0)
            
        except Exception as e:
            logger.warning(f"计算截止日期压力失败: {e}")
            return 0.0
    
    async def extract_batch_features(self, project_ids: List[UUID]) -> List[ProjectFeatures]:
        """批量提取特征"""
        features_list = []
        
        for project_id in project_ids:
            try:
                features = await self.extract_project_features(project_id)
                features_list.append(features)
            except Exception as e:
                logger.error(f"批量提取特征失败，项目 {project_id}: {e}")
                # 继续处理其他项目
                continue
        
        return features_list
    
    def validate_features(self, features: ProjectFeatures) -> bool:
        """验证特征数据"""
        try:
            # 检查必要字段
            required_fields = [
                "project_age_days", "team_size", "task_count",
                "progress_percentage", "velocity"
            ]
            
            for field in required_fields:
                if not hasattr(features, field):
                    logger.warning(f"缺少必要特征字段: {field}")
                    return False
            
            # 检查数值范围
            if features.progress_percentage < 0 or features.progress_percentage > 100:
                logger.warning(f"进度百分比超出范围: {features.progress_percentage}")
                return False
            
            if features.team_size <= 0:
                logger.warning(f"团队规模无效: {features.team_size}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"特征验证失败: {e}")
            return False
