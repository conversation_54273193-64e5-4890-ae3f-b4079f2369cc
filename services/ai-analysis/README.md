# AI分析服务

AI项目管理平台的智能分析服务，提供项目进度预测、风险识别、团队绩效分析等AI驱动的功能。

## 功能特性

### 🔮 项目进度预测
- **智能预测**: 基于历史数据和当前状态预测项目完成进度
- **完成时间估算**: 预测项目实际完成日期
- **置信度评估**: 提供预测结果的置信度评分
- **影响因子分析**: 识别影响项目进度的关键因素
- **改进建议**: 基于分析结果提供进度优化建议

### ⚠️ 风险识别和预警
- **多维度风险分析**: 从进度、预算、质量、团队等维度分析风险
- **实时风险监控**: 持续监控项目风险状态变化
- **风险等级评估**: 自动评估风险等级（低、中、高、严重）
- **预警通知**: 及时发现和通知高风险情况
- **缓解建议**: 为每个风险因子提供具体的缓解措施

### 📊 团队绩效分析
- **团队效率评估**: 分析团队整体工作效率
- **个人绩效分析**: 评估团队成员个人表现
- **协作质量评估**: 分析团队协作效果
- **技能匹配分析**: 评估团队技能与项目需求的匹配度
- **改进建议**: 提供团队和个人改进建议

### 🤖 智能推荐
- **任务分配推荐**: 基于技能和工作负载推荐最佳任务分配
- **资源优化建议**: 推荐资源配置优化方案
- **流程改进建议**: 识别并推荐流程改进机会
- **最佳实践推荐**: 基于成功项目经验提供最佳实践建议

## 技术架构

### 🏗️ 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI       │    │   ML Models     │    │   Data Sources  │
│   Web Server    │◄──►│   Manager       │◄──►│   (External)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Routes    │    │   Feature       │    │   Project Mgmt  │
│   & Controllers │    │   Extractor     │    │   Service       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Business      │    │   Prediction    │    │   User Service  │
│   Services      │    │   Services      │    │   (External)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🧠 机器学习模型
- **进度预测模型**: XGBoost回归模型，预测项目完成进度
- **风险分析模型**: 随机森林分类模型，识别和评估项目风险
- **团队绩效模型**: 神经网络模型，分析团队和个人绩效
- **推荐引擎**: 协同过滤和内容推荐混合模型

### 📊 特征工程
- **项目特征**: 项目年龄、团队规模、任务数量、复杂度等
- **进度特征**: 完成百分比、速度、燃尽率等
- **团队特征**: 经验水平、流失率、沟通频率等
- **质量特征**: 缺陷密度、代码审查覆盖率等
- **时间特征**: 进度偏差、截止日期压力等
- **风险特征**: 复杂度评分、风险因子数量、满意度等

## 快速开始

### 环境要求
- Python 3.11+
- Redis 6.0+
- PostgreSQL 13+ (可选)

### 安装依赖
```bash
cd services/ai-analysis
pip install -r requirements.txt
```

### 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和外部服务地址
```

### 启动服务
```bash
python main.py
```

### 使用Docker
```bash
docker build -t ai-analysis-service .
docker run -p 8003:8003 ai-analysis-service
```

## API文档

### 进度预测API
```http
POST /api/v1/prediction/progress
GET  /api/v1/prediction/progress/{project_id}
GET  /api/v1/prediction/completion-probability/{project_id}
POST /api/v1/prediction/batch-progress
```

### 风险分析API
```http
POST /api/v1/risk/analyze
GET  /api/v1/risk/analyze/{project_id}
GET  /api/v1/risk/alerts/{project_id}
POST /api/v1/risk/batch-analyze
```

### 智能分析API
```http
GET  /api/v1/analytics/features/{project_id}
POST /api/v1/analytics/features/batch
GET  /api/v1/analytics/project-data/{project_id}
GET  /api/v1/analytics/health-check
```

### 模型管理API
```http
GET  /api/v1/models/list
GET  /api/v1/models/info/{model_name}
GET  /api/v1/models/status/{model_name}
POST /api/v1/models/reload/{model_name}
POST /api/v1/models/warm-up
```

## 使用示例

### 预测项目进度
```python
import httpx

async def predict_progress(project_id: str):
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"http://localhost:8003/api/v1/prediction/progress/{project_id}"
        )
        return response.json()
```

### 分析项目风险
```python
async def analyze_risks(project_id: str):
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"http://localhost:8003/api/v1/risk/analyze/{project_id}"
        )
        return response.json()
```

### 获取风险预警
```python
async def get_risk_alerts(project_id: str):
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"http://localhost:8003/api/v1/risk/alerts/{project_id}"
        )
        return response.json()
```

## 配置说明

### 模型配置
- `ML_MODEL_PATH`: 模型文件存储路径
- `ML_CACHE_SIZE`: 模型缓存大小
- `PREDICTION_CONFIDENCE_THRESHOLD`: 预测置信度阈值

### 风险配置
- `RISK_THRESHOLD_HIGH`: 高风险阈值
- `RISK_THRESHOLD_MEDIUM`: 中风险阈值

### 特征工程配置
- `FEATURE_WINDOW_DAYS`: 特征提取时间窗口
- `FEATURE_MIN_SAMPLES`: 最小样本数量

## 监控和日志

### 日志配置
- 支持结构化日志输出
- 可配置日志级别和轮转策略
- 分离应用日志和ML模型日志

### 性能监控
- 预测响应时间监控
- 模型准确率跟踪
- 资源使用情况监控

### 健康检查
```http
GET /health
GET /api/v1/analytics/health-check
```

## 开发指南

### 添加新模型
1. 在 `app/ml/` 目录下创建模型文件
2. 更新 `ModelManager` 加载逻辑
3. 在相应的服务中添加预测逻辑
4. 创建对应的API端点

### 添加新特征
1. 在 `FeatureExtractor` 中添加特征提取逻辑
2. 更新 `ProjectFeatures` 模型
3. 确保模型能处理新特征

### 扩展风险分析
1. 在 `RiskService` 中添加新的风险类别
2. 实现对应的风险分析逻辑
3. 更新风险因子和缓解建议

## 部署说明

### 生产环境配置
- 设置 `DEBUG=false`
- 配置安全的 `SECRET_KEY`
- 限制 `ALLOWED_HOSTS`
- 配置SSL证书

### 性能优化
- 启用模型缓存
- 配置合适的并发数
- 使用负载均衡
- 监控资源使用

### 扩展性
- 支持水平扩展
- 模型版本管理
- A/B测试支持
- 多租户隔离

## 故障排除

### 常见问题
1. **模型加载失败**: 检查模型文件路径和权限
2. **预测超时**: 调整 `ML_PREDICTION_TIMEOUT` 配置
3. **外部服务连接失败**: 检查服务地址和网络连接
4. **内存不足**: 减少 `ML_CACHE_SIZE` 或增加服务器内存

### 调试技巧
- 启用DEBUG模式查看详细日志
- 使用健康检查端点验证服务状态
- 检查模型缓存信息
- 监控预测性能指标

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License
