"""
AI服务配置管理
管理环境变量和应用配置

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import os
from typing import List
from pydantic import BaseSettings, Field
from functools import lru_cache

class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = Field(default="AI项目管理平台 - AI分析服务", description="应用名称")
    VERSION: str = Field(default="1.0.0", description="版本号")
    DEBUG: bool = Field(default=False, description="调试模式")
    
    # 服务配置
    HOST: str = Field(default="0.0.0.0", description="服务主机")
    PORT: int = Field(default=8001, description="服务端口")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    
    # 数据库配置
    DATABASE_URL: str = Field(
        default="postgresql://ai_pm_user:ai_pm_password@localhost:5432/ai_pm_db",
        description="数据库连接URL"
    )
    
    # Redis配置
    REDIS_URL: str = Field(
        default="redis://localhost:6379/1",
        description="Redis连接URL"
    )
    
    # 模型配置
    MODEL_PATH: str = Field(
        default="./models",
        description="模型文件路径"
    )
    
    # CORS配置
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        description="允许的CORS源"
    )
    
    # 认证配置
    JWT_SECRET_KEY: str = Field(
        default="your-secret-key-here",
        description="JWT密钥"
    )
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT算法")
    JWT_EXPIRE_MINUTES: int = Field(default=30, description="JWT过期时间（分钟）")
    
    # 外部服务配置
    TENSORFLOW_SERVING_URL: str = Field(
        default="http://localhost:8501",
        description="TensorFlow Serving URL"
    )
    
    # 性能配置
    MAX_WORKERS: int = Field(default=4, description="最大工作进程数")
    REQUEST_TIMEOUT: int = Field(default=30, description="请求超时时间（秒）")
    
    # 缓存配置
    CACHE_TTL: int = Field(default=3600, description="缓存TTL（秒）")
    ENABLE_CACHE: bool = Field(default=True, description="启用缓存")
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, description="启用指标收集")
    METRICS_PORT: int = Field(default=9001, description="指标端口")
    
    # 安全配置
    ENABLE_RATE_LIMIT: bool = Field(default=True, description="启用速率限制")
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, description="每分钟请求限制")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()

# 环境检查
def validate_environment():
    """验证环境配置"""
    settings = get_settings()
    
    # 检查必要的环境变量
    required_vars = [
        "DATABASE_URL",
        "REDIS_URL",
        "JWT_SECRET_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not getattr(settings, var, None):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"缺少必要的环境变量: {', '.join(missing_vars)}")
    
    # 检查模型路径
    if not os.path.exists(settings.MODEL_PATH):
        os.makedirs(settings.MODEL_PATH, exist_ok=True)
    
    return True

# 开发环境配置
class DevelopmentSettings(Settings):
    """开发环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    ENABLE_CACHE: bool = False
    ENABLE_RATE_LIMIT: bool = False

# 生产环境配置
class ProductionSettings(Settings):
    """生产环境配置"""
    DEBUG: bool = False
    LOG_LEVEL: str = "WARNING"
    ENABLE_CACHE: bool = True
    ENABLE_RATE_LIMIT: bool = True
    REQUEST_TIMEOUT: int = 60

# 测试环境配置
class TestSettings(Settings):
    """测试环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    DATABASE_URL: str = "sqlite:///./test.db"
    REDIS_URL: str = "redis://localhost:6379/15"
    ENABLE_CACHE: bool = False
    ENABLE_RATE_LIMIT: bool = False

def get_settings_by_env(env: str = None) -> Settings:
    """根据环境获取配置"""
    if env is None:
        env = os.getenv("ENVIRONMENT", "development")
    
    if env == "production":
        return ProductionSettings()
    elif env == "test":
        return TestSettings()
    else:
        return DevelopmentSettings()
