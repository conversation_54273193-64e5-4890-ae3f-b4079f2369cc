"""
数据验证工具
提供数据验证和清洗功能

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)

class ValidationResult(BaseModel):
    """验证结果"""
    is_valid: bool
    error_message: Optional[str] = None
    warnings: List[str] = []

def validate_project_data(project_data: Dict[str, Any]) -> ValidationResult:
    """验证项目数据"""
    warnings = []
    
    try:
        # 检查必需字段
        required_fields = [
            'duration_days', 'team_size', 'complexity_score',
            'budget_amount', 'requirements_count', 'dependencies_count',
            'experience_score', 'technology_risk'
        ]
        
        for field in required_fields:
            if field not in project_data:
                return ValidationResult(
                    is_valid=False,
                    error_message=f"缺少必需字段: {field}"
                )
        
        # 验证数值范围
        if not (1 <= project_data['duration_days'] <= 1000):
            return ValidationResult(
                is_valid=False,
                error_message="项目持续时间必须在1-1000天之间"
            )
        
        if not (1 <= project_data['team_size'] <= 100):
            return ValidationResult(
                is_valid=False,
                error_message="团队规模必须在1-100人之间"
            )
        
        if not (1 <= project_data['complexity_score'] <= 10):
            return ValidationResult(
                is_valid=False,
                error_message="复杂度评分必须在1-10之间"
            )
        
        if project_data['budget_amount'] < 0:
            return ValidationResult(
                is_valid=False,
                error_message="预算金额不能为负数"
            )
        
        # 添加警告
        if project_data['duration_days'] > 365:
            warnings.append("项目持续时间超过一年，建议分阶段实施")
        
        if project_data['team_size'] > 20:
            warnings.append("团队规模较大，需要注意沟通协调")
        
        if project_data['complexity_score'] >= 8:
            warnings.append("项目复杂度很高，建议增加风险缓解措施")
        
        return ValidationResult(
            is_valid=True,
            warnings=warnings
        )
        
    except Exception as e:
        logger.error(f"项目数据验证失败: {e}")
        return ValidationResult(
            is_valid=False,
            error_message=f"数据验证过程中发生错误: {str(e)}"
        )
