"""
预测分析API路由
提供项目风险预测等预测分析功能

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import logging

from ..services.model_manager import ModelManager
from ..utils.auth import verify_token
from ..utils.validation import validate_project_data

logger = logging.getLogger(__name__)

prediction_router = APIRouter()

class ProjectRiskRequest(BaseModel):
    """项目风险预测请求"""
    project_id: str = Field(..., description="项目ID")
    duration_days: int = Field(..., ge=1, le=1000, description="项目持续时间（天）")
    team_size: int = Field(..., ge=1, le=100, description="团队规模")
    complexity_score: float = Field(..., ge=1, le=10, description="复杂度评分（1-10）")
    budget_amount: float = Field(..., ge=0, description="预算金额")
    requirements_count: int = Field(..., ge=0, description="需求数量")
    dependencies_count: int = Field(..., ge=0, description="依赖项数量")
    experience_score: float = Field(..., ge=1, le=10, description="团队经验评分（1-10）")
    technology_risk: float = Field(..., ge=1, le=10, description="技术风险评分（1-10）")
    
    class Config:
        schema_extra = {
            "example": {
                "project_id": "proj_123",
                "duration_days": 90,
                "team_size": 8,
                "complexity_score": 7.5,
                "budget_amount": 500000,
                "requirements_count": 25,
                "dependencies_count": 5,
                "experience_score": 6.5,
                "technology_risk": 6.0
            }
        }

class RiskPredictionResponse(BaseModel):
    """风险预测响应"""
    project_id: str
    risk_level: int = Field(..., description="风险等级（0-低，1-中，2-高）")
    risk_label: str = Field(..., description="风险标签")
    risk_score: float = Field(..., description="风险评分")
    probabilities: Dict[str, float] = Field(..., description="各风险等级概率")
    risk_factors: List[str] = Field(..., description="风险因子")
    recommendations: List[str] = Field(..., description="缓解建议")
    confidence: float = Field(..., description="预测置信度")

class BatchRiskRequest(BaseModel):
    """批量风险预测请求"""
    projects: List[ProjectRiskRequest] = Field(..., description="项目列表")

class BatchRiskResponse(BaseModel):
    """批量风险预测响应"""
    predictions: List[RiskPredictionResponse] = Field(..., description="预测结果列表")
    summary: Dict[str, Any] = Field(..., description="汇总信息")

@prediction_router.post("/risk/project", 
                       response_model=RiskPredictionResponse,
                       summary="项目风险预测",
                       description="基于项目特征预测风险等级和提供缓解建议")
async def predict_project_risk(
    request: ProjectRiskRequest,
    model_manager: ModelManager = Depends(),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """预测单个项目的风险"""
    try:
        logger.info(f"用户 {current_user.get('user_id')} 请求预测项目 {request.project_id} 的风险")
        
        # 验证项目数据
        project_data = request.dict()
        validation_result = validate_project_data(project_data)
        if not validation_result.is_valid:
            raise HTTPException(
                status_code=400, 
                detail=f"项目数据验证失败: {validation_result.error_message}"
            )
        
        # 调用AI模型进行预测
        prediction_result = await model_manager.predict_project_risk(project_data)
        
        # 构造响应
        response = RiskPredictionResponse(
            project_id=request.project_id,
            **prediction_result
        )
        
        logger.info(f"项目 {request.project_id} 风险预测完成，风险等级: {response.risk_label}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"项目风险预测失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="风险预测服务暂时不可用")

@prediction_router.post("/risk/batch",
                       response_model=BatchRiskResponse,
                       summary="批量项目风险预测",
                       description="批量预测多个项目的风险等级")
async def predict_batch_project_risk(
    request: BatchRiskRequest,
    background_tasks: BackgroundTasks,
    model_manager: ModelManager = Depends(),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """批量预测项目风险"""
    try:
        logger.info(f"用户 {current_user.get('user_id')} 请求批量预测 {len(request.projects)} 个项目的风险")
        
        if len(request.projects) > 50:
            raise HTTPException(status_code=400, detail="批量预测项目数量不能超过50个")
        
        predictions = []
        risk_distribution = {"低风险": 0, "中风险": 0, "高风险": 0}
        
        for project_request in request.projects:
            try:
                # 验证项目数据
                project_data = project_request.dict()
                validation_result = validate_project_data(project_data)
                if not validation_result.is_valid:
                    logger.warning(f"项目 {project_request.project_id} 数据验证失败: {validation_result.error_message}")
                    continue
                
                # 预测风险
                prediction_result = await model_manager.predict_project_risk(project_data)
                
                response = RiskPredictionResponse(
                    project_id=project_request.project_id,
                    **prediction_result
                )
                predictions.append(response)
                
                # 统计风险分布
                risk_distribution[response.risk_label] += 1
                
            except Exception as e:
                logger.error(f"项目 {project_request.project_id} 风险预测失败: {e}")
                continue
        
        # 计算汇总信息
        total_projects = len(predictions)
        high_risk_count = risk_distribution["高风险"]
        high_risk_percentage = (high_risk_count / total_projects * 100) if total_projects > 0 else 0
        
        summary = {
            "total_projects": total_projects,
            "risk_distribution": risk_distribution,
            "high_risk_percentage": round(high_risk_percentage, 2),
            "avg_confidence": round(
                sum(p.confidence for p in predictions) / total_projects, 3
            ) if total_projects > 0 else 0
        }
        
        # 异步记录批量预测日志
        background_tasks.add_task(
            log_batch_prediction, 
            current_user.get('user_id'), 
            total_projects, 
            summary
        )
        
        return BatchRiskResponse(predictions=predictions, summary=summary)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量风险预测失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="批量风险预测服务暂时不可用")

@prediction_router.get("/risk/history/{project_id}",
                      summary="获取项目风险预测历史",
                      description="获取指定项目的历史风险预测记录")
async def get_risk_prediction_history(
    project_id: str,
    limit: int = 10,
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """获取项目风险预测历史"""
    try:
        # 这里应该从数据库获取历史记录
        # 暂时返回模拟数据
        history = [
            {
                "prediction_id": f"pred_{i}",
                "project_id": project_id,
                "risk_level": i % 3,
                "risk_label": ["低风险", "中风险", "高风险"][i % 3],
                "risk_score": 0.3 + (i % 3) * 0.3,
                "predicted_at": f"2025-08-{16-i:02d}T10:00:00Z",
                "model_version": "1.0.0"
            }
            for i in range(min(limit, 5))
        ]
        
        return {
            "project_id": project_id,
            "history": history,
            "total_count": len(history)
        }
        
    except Exception as e:
        logger.error(f"获取风险预测历史失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取预测历史失败")

@prediction_router.get("/risk/trends",
                      summary="获取风险趋势分析",
                      description="获取组织级别的风险趋势分析")
async def get_risk_trends(
    days: int = 30,
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """获取风险趋势分析"""
    try:
        # 这里应该从数据库聚合风险趋势数据
        # 暂时返回模拟数据
        trends = {
            "period_days": days,
            "total_predictions": 150,
            "risk_trend": [
                {"date": f"2025-08-{i:02d}", "high_risk_count": 2 + i % 5, "total_count": 10 + i % 3}
                for i in range(1, min(days + 1, 16))
            ],
            "risk_factors_frequency": {
                "项目持续时间过长": 45,
                "团队经验不足": 38,
                "技术风险较高": 32,
                "项目复杂度很高": 28,
                "团队规模过大": 15
            },
            "improvement_suggestions": [
                "加强项目规划，合理控制项目周期",
                "增加技术培训，提升团队能力",
                "建立技术风险评估机制",
                "优化团队结构和沟通机制"
            ]
        }
        
        return trends
        
    except Exception as e:
        logger.error(f"获取风险趋势失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取风险趋势失败")

async def log_batch_prediction(user_id: str, project_count: int, summary: Dict[str, Any]):
    """异步记录批量预测日志"""
    try:
        logger.info(f"用户 {user_id} 完成批量预测 {project_count} 个项目，高风险比例: {summary.get('high_risk_percentage', 0)}%")
        # 这里可以将日志写入数据库或发送到监控系统
    except Exception as e:
        logger.error(f"记录批量预测日志失败: {e}")

def get_model_manager() -> ModelManager:
    """获取模型管理器依赖"""
    # 这个函数会被FastAPI的依赖注入系统调用
    # 实际的ModelManager实例会在main.py中设置
    pass
