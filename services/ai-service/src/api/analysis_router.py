"""
数据分析API路由
提供团队绩效分析、数据洞察等分析功能

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime, timedelta
import httpx
import asyncio

logger = logging.getLogger(__name__)

analysis_router = APIRouter()

class PerformanceAnalysisRequest(BaseModel):
    """绩效分析请求"""
    team_id: str = Field(..., description="团队ID")
    members: List[Dict[str, Any]] = Field(..., description="团队成员数据")
    project_metrics: Dict[str, Any] = Field(default={}, description="项目指标")
    time_period: str = Field(default="month", description="分析时间段")

class PerformanceAnalysisResponse(BaseModel):
    """绩效分析响应"""
    team_id: str
    overall_score: float = Field(..., description="整体评分")
    individual_analysis: List[Dict[str, Any]] = Field(..., description="个人分析")
    team_analysis: Dict[str, Any] = Field(..., description="团队分析")
    recommendations: List[str] = Field(..., description="改进建议")

@analysis_router.post("/performance",
                     response_model=PerformanceAnalysisResponse,
                     summary="团队绩效分析",
                     description="分析团队和个人绩效，提供改进建议")
async def analyze_performance(request: PerformanceAnalysisRequest):
    """分析团队绩效"""
    try:
        # 模拟分析逻辑
        individual_analysis = [
            {
                "member_id": member.get("id", f"member_{i}"),
                "member_name": member.get("name", f"成员{i}"),
                "overall_score": 0.8 - i * 0.05,
                "performance_level": "良好",
                "strengths": ["任务完成率高", "代码质量好"],
                "weaknesses": ["沟通需要改进"]
            }
            for i, member in enumerate(request.members[:5])
        ]
        
        team_analysis = {
            "team_size": len(request.members),
            "avg_performance": 0.75,
            "collaboration_score": 0.8,
            "delivery_metrics": 0.85
        }
        
        recommendations = [
            "加强团队沟通协作",
            "提供技能培训",
            "优化工作流程"
        ]
        
        return PerformanceAnalysisResponse(
            team_id=request.team_id,
            overall_score=0.78,
            individual_analysis=individual_analysis,
            team_analysis=team_analysis,
            recommendations=recommendations
        )
        
    except Exception as e:
        logger.error(f"绩效分析失败: {e}")
        raise HTTPException(status_code=500, detail="分析服务暂时不可用")

@analysis_router.get("/insights/{project_id}",
                    summary="获取项目洞察",
                    description="获取项目的数据洞察和趋势分析")
async def get_project_insights(project_id: str):
    """获取项目洞察 - 基于真实数据分析"""
    try:
        # 获取项目真实数据
        project_data = await get_project_data(project_id)
        if not project_data:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 计算真实的完成率
        total_tasks = project_data.get("total_tasks", 0)
        completed_tasks = project_data.get("completed_tasks", 0)
        completion_rate = completed_tasks / total_tasks if total_tasks > 0 else 0

        # 计算团队速度（基于最近30天的任务完成情况）
        recent_velocity = await calculate_team_velocity(project_id, days=30)

        # 计算质量分数（基于代码审查、测试覆盖率等）
        quality_metrics = await calculate_quality_score(project_id)

        # 分析趋势
        trends = await analyze_project_trends(project_id)

        # 生成智能洞察
        insights_list = await generate_intelligent_insights(
            completion_rate, recent_velocity, quality_metrics, trends
        )

        insights = {
            "project_id": project_id,
            "key_metrics": {
                "completion_rate": round(completion_rate, 3),
                "velocity": round(recent_velocity, 2),
                "quality_score": round(quality_metrics.get("overall_score", 0), 3)
            },
            "trends": trends,
            "insights": insights_list,
            "analysis_timestamp": datetime.utcnow().isoformat(),
            "confidence_level": calculate_confidence_level(project_data)
        }

        return insights

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目洞察失败: {e}")
        raise HTTPException(status_code=500, detail="获取洞察失败")


# ==================== 辅助函数 ====================

async def get_project_data(project_id: str) -> Dict[str, Any]:
    """获取项目基础数据"""
    try:
        # 调用项目管理服务API获取项目数据
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://project-management-service:8080/api/v1/projects/{project_id}/analytics"
            )
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return None
            else:
                logger.warning(f"获取项目数据失败: {response.status_code}")
                return {}
    except Exception as e:
        logger.error(f"调用项目服务失败: {e}")
        return {}


async def calculate_team_velocity(project_id: str, days: int = 30) -> float:
    """计算团队速度（最近N天完成的任务数/天）"""
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://project-management-service:8080/api/v1/projects/{project_id}/tasks/completed",
                params={
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                }
            )

            if response.status_code == 200:
                completed_tasks = response.json().get("count", 0)
                return completed_tasks / days
            else:
                logger.warning(f"获取任务完成数据失败: {response.status_code}")
                return 0.0

    except Exception as e:
        logger.error(f"计算团队速度失败: {e}")
        return 0.0


async def calculate_quality_score(project_id: str) -> Dict[str, float]:
    """计算质量分数"""
    try:
        # 获取代码质量数据
        quality_data = await get_code_quality_data(project_id)

        # 计算各项质量指标
        test_coverage = quality_data.get("test_coverage", 0) / 100.0
        code_review_rate = quality_data.get("code_review_rate", 0) / 100.0
        bug_density = max(0, 1 - (quality_data.get("bugs_per_kloc", 10) / 10.0))

        # 综合质量分数（加权平均）
        overall_score = (
            test_coverage * 0.4 +
            code_review_rate * 0.3 +
            bug_density * 0.3
        )

        return {
            "overall_score": overall_score,
            "test_coverage": test_coverage,
            "code_review_rate": code_review_rate,
            "bug_density": bug_density
        }

    except Exception as e:
        logger.error(f"计算质量分数失败: {e}")
        return {"overall_score": 0.5}  # 默认中等质量分数


async def get_code_quality_data(project_id: str) -> Dict[str, Any]:
    """获取代码质量数据"""
    try:
        # 调用Git集成服务获取代码质量数据
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://git-integration-service:8080/api/v1/repositories/{project_id}/quality"
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"获取代码质量数据失败: {response.status_code}")
                return {}

    except Exception as e:
        logger.error(f"获取代码质量数据失败: {e}")
        return {}


async def analyze_project_trends(project_id: str) -> Dict[str, str]:
    """分析项目趋势"""
    try:
        # 获取历史数据进行趋势分析
        historical_data = await get_historical_metrics(project_id)

        if not historical_data:
            return {
                "velocity_trend": "stable",
                "quality_trend": "stable",
                "team_satisfaction": "medium"
            }

        # 分析速度趋势
        velocity_trend = analyze_velocity_trend(historical_data.get("velocity", []))

        # 分析质量趋势
        quality_trend = analyze_quality_trend(historical_data.get("quality", []))

        # 分析团队满意度（基于任务完成率和工作负载）
        satisfaction = analyze_team_satisfaction(historical_data)

        return {
            "velocity_trend": velocity_trend,
            "quality_trend": quality_trend,
            "team_satisfaction": satisfaction
        }

    except Exception as e:
        logger.error(f"分析项目趋势失败: {e}")
        return {
            "velocity_trend": "stable",
            "quality_trend": "stable",
            "team_satisfaction": "medium"
        }


async def get_historical_metrics(project_id: str) -> Dict[str, List]:
    """获取历史指标数据"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://project-management-service:8080/api/v1/projects/{project_id}/metrics/history"
            )

            if response.status_code == 200:
                return response.json()
            else:
                return {}

    except Exception as e:
        logger.error(f"获取历史指标失败: {e}")
        return {}


def analyze_velocity_trend(velocity_data: List[float]) -> str:
    """分析速度趋势"""
    if len(velocity_data) < 2:
        return "stable"

    # 计算最近趋势（简单线性回归）
    recent_data = velocity_data[-5:]  # 最近5个数据点
    if len(recent_data) < 2:
        return "stable"

    # 简单趋势计算
    avg_early = sum(recent_data[:len(recent_data)//2]) / (len(recent_data)//2)
    avg_late = sum(recent_data[len(recent_data)//2:]) / (len(recent_data) - len(recent_data)//2)

    change_rate = (avg_late - avg_early) / avg_early if avg_early > 0 else 0

    if change_rate > 0.1:
        return "increasing"
    elif change_rate < -0.1:
        return "decreasing"
    else:
        return "stable"


def analyze_quality_trend(quality_data: List[float]) -> str:
    """分析质量趋势"""
    if len(quality_data) < 2:
        return "stable"

    recent_data = quality_data[-5:]
    if len(recent_data) < 2:
        return "stable"

    avg_early = sum(recent_data[:len(recent_data)//2]) / (len(recent_data)//2)
    avg_late = sum(recent_data[len(recent_data)//2:]) / (len(recent_data) - len(recent_data)//2)

    change_rate = (avg_late - avg_early) / avg_early if avg_early > 0 else 0

    if change_rate > 0.05:
        return "improving"
    elif change_rate < -0.05:
        return "declining"
    else:
        return "stable"


def analyze_team_satisfaction(historical_data: Dict[str, List]) -> str:
    """分析团队满意度"""
    try:
        # 基于任务完成率、工作负载等因素评估
        completion_rates = historical_data.get("completion_rate", [])
        workload_data = historical_data.get("workload", [])

        if not completion_rates:
            return "medium"

        avg_completion = sum(completion_rates[-5:]) / len(completion_rates[-5:])

        if avg_completion > 0.85:
            return "high"
        elif avg_completion > 0.65:
            return "medium"
        else:
            return "low"

    except Exception as e:
        logger.error(f"分析团队满意度失败: {e}")
        return "medium"


async def generate_intelligent_insights(
    completion_rate: float,
    velocity: float,
    quality_metrics: Dict[str, float],
    trends: Dict[str, str]
) -> List[str]:
    """生成智能洞察"""
    insights = []

    # 基于完成率的洞察
    if completion_rate > 0.9:
        insights.append("项目进展优秀，完成率超过90%")
    elif completion_rate > 0.7:
        insights.append("项目进展良好，建议关注剩余任务的优先级")
    else:
        insights.append("项目进展需要关注，建议分析延期原因并调整计划")

    # 基于速度趋势的洞察
    if trends.get("velocity_trend") == "increasing":
        insights.append("团队效率持续提升，当前开发节奏良好")
    elif trends.get("velocity_trend") == "decreasing":
        insights.append("团队效率有所下降，建议检查是否存在阻碍因素")

    # 基于质量的洞察
    overall_quality = quality_metrics.get("overall_score", 0)
    if overall_quality > 0.8:
        insights.append("代码质量优秀，测试覆盖率和审查率都很好")
    elif overall_quality < 0.6:
        insights.append("代码质量需要改进，建议加强测试和代码审查")

    # 基于团队满意度的洞察
    if trends.get("team_satisfaction") == "high":
        insights.append("团队协作状态良好，工作满意度较高")
    elif trends.get("team_satisfaction") == "low":
        insights.append("团队工作压力较大，建议关注工作负载分配")

    return insights


def calculate_confidence_level(project_data: Dict[str, Any]) -> float:
    """计算分析结果的置信度"""
    try:
        # 基于数据完整性和项目活跃度计算置信度
        data_completeness = 0.0

        # 检查关键数据字段
        if project_data.get("total_tasks", 0) > 0:
            data_completeness += 0.3
        if project_data.get("team_members", 0) > 0:
            data_completeness += 0.2
        if project_data.get("recent_activity", False):
            data_completeness += 0.3
        if project_data.get("historical_data", False):
            data_completeness += 0.2

        return min(1.0, data_completeness)

    except Exception as e:
        logger.error(f"计算置信度失败: {e}")
        return 0.5  # 默认中等置信度
