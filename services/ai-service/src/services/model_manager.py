"""
AI模型管理器
负责加载、管理和调用各种机器学习模型

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

import asyncio
import logging
import pickle
import joblib
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List
from pathlib import Path
import tensorflow as tf
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings

from ..utils.config import get_settings
from ..models.risk_predictor import RiskPredictor
from ..models.task_recommender import TaskRecommender
from ..models.performance_analyzer import PerformanceAnalyzer

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)
settings = get_settings()

class ModelManager:
    """AI模型管理器"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.model_versions = {}
        self.is_initialized = False
        
        # 模型路径
        self.model_dir = Path(settings.MODEL_PATH)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self):
        """初始化所有模型"""
        try:
            logger.info("开始初始化AI模型...")
            
            # 并行加载模型
            tasks = [
                self._load_risk_prediction_model(),
                self._load_task_recommendation_model(),
                self._load_performance_analysis_model(),
                self._load_preprocessing_components()
            ]
            
            await asyncio.gather(*tasks)
            
            self.is_initialized = True
            logger.info("AI模型初始化完成")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise
    
    async def _load_risk_prediction_model(self):
        """加载项目风险预测模型"""
        try:
            model_path = self.model_dir / "risk_predictor.pkl"
            
            if model_path.exists():
                # 加载已训练的模型
                with open(model_path, 'rb') as f:
                    self.models['risk_predictor'] = pickle.load(f)
                logger.info("已加载风险预测模型")
            else:
                # 创建并训练新模型
                self.models['risk_predictor'] = RiskPredictor()
                await self._train_risk_model()
                logger.info("已创建并训练风险预测模型")
                
            self.model_versions['risk_predictor'] = "1.0.0"
            
        except Exception as e:
            logger.error(f"加载风险预测模型失败: {e}")
            # 使用默认模型
            self.models['risk_predictor'] = RiskPredictor()
    
    async def _load_task_recommendation_model(self):
        """加载任务推荐模型"""
        try:
            model_path = self.model_dir / "task_recommender.pkl"
            
            if model_path.exists():
                with open(model_path, 'rb') as f:
                    self.models['task_recommender'] = pickle.load(f)
                logger.info("已加载任务推荐模型")
            else:
                self.models['task_recommender'] = TaskRecommender()
                await self._train_recommendation_model()
                logger.info("已创建并训练任务推荐模型")
                
            self.model_versions['task_recommender'] = "1.0.0"
            
        except Exception as e:
            logger.error(f"加载任务推荐模型失败: {e}")
            self.models['task_recommender'] = TaskRecommender()
    
    async def _load_performance_analysis_model(self):
        """加载绩效分析模型"""
        try:
            model_path = self.model_dir / "performance_analyzer.pkl"
            
            if model_path.exists():
                with open(model_path, 'rb') as f:
                    self.models['performance_analyzer'] = pickle.load(f)
                logger.info("已加载绩效分析模型")
            else:
                self.models['performance_analyzer'] = PerformanceAnalyzer()
                await self._train_performance_model()
                logger.info("已创建并训练绩效分析模型")
                
            self.model_versions['performance_analyzer'] = "1.0.0"
            
        except Exception as e:
            logger.error(f"加载绩效分析模型失败: {e}")
            self.models['performance_analyzer'] = PerformanceAnalyzer()
    
    async def _load_preprocessing_components(self):
        """加载预处理组件"""
        try:
            # 加载标准化器
            scaler_path = self.model_dir / "scaler.pkl"
            if scaler_path.exists():
                self.scalers['standard'] = joblib.load(scaler_path)
            else:
                self.scalers['standard'] = StandardScaler()
            
            # 加载编码器
            encoder_path = self.model_dir / "label_encoder.pkl"
            if encoder_path.exists():
                self.encoders['label'] = joblib.load(encoder_path)
            else:
                self.encoders['label'] = LabelEncoder()
                
            logger.info("预处理组件加载完成")
            
        except Exception as e:
            logger.error(f"加载预处理组件失败: {e}")
            self.scalers['standard'] = StandardScaler()
            self.encoders['label'] = LabelEncoder()
    
    async def _train_risk_model(self):
        """训练风险预测模型"""
        # 生成模拟训练数据
        np.random.seed(42)
        n_samples = 1000
        
        # 特征：项目持续时间、团队规模、复杂度、预算等
        X = np.random.rand(n_samples, 8)
        # 目标：风险等级（0-低风险，1-中风险，2-高风险）
        y = np.random.choice([0, 1, 2], n_samples, p=[0.6, 0.3, 0.1])
        
        await self.models['risk_predictor'].train(X, y)
        
        # 保存模型
        model_path = self.model_dir / "risk_predictor.pkl"
        with open(model_path, 'wb') as f:
            pickle.dump(self.models['risk_predictor'], f)
    
    async def _train_recommendation_model(self):
        """训练推荐模型"""
        # 生成模拟训练数据
        await self.models['task_recommender'].train_collaborative_filtering()
        
        # 保存模型
        model_path = self.model_dir / "task_recommender.pkl"
        with open(model_path, 'wb') as f:
            pickle.dump(self.models['task_recommender'], f)
    
    async def _train_performance_model(self):
        """训练绩效分析模型"""
        # 生成模拟训练数据
        await self.models['performance_analyzer'].train()
        
        # 保存模型
        model_path = self.model_dir / "performance_analyzer.pkl"
        with open(model_path, 'wb') as f:
            pickle.dump(self.models['performance_analyzer'], f)
    
    async def predict_project_risk(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """预测项目风险"""
        if 'risk_predictor' not in self.models:
            raise ValueError("风险预测模型未加载")
        
        return await self.models['risk_predictor'].predict(project_data)
    
    async def recommend_tasks(self, user_id: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推荐任务"""
        if 'task_recommender' not in self.models:
            raise ValueError("任务推荐模型未加载")
        
        return await self.models['task_recommender'].recommend(user_id, context)
    
    async def analyze_performance(self, team_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析团队绩效"""
        if 'performance_analyzer' not in self.models:
            raise ValueError("绩效分析模型未加载")
        
        return await self.models['performance_analyzer'].analyze(team_data)
    
    async def get_status(self) -> Dict[str, str]:
        """获取模型状态"""
        return {
            model_name: "已加载" if model_name in self.models else "未加载"
            for model_name in ['risk_predictor', 'task_recommender', 'performance_analyzer']
        }
    
    async def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细状态"""
        return {
            "initialized": self.is_initialized,
            "models": {
                name: {
                    "loaded": name in self.models,
                    "version": self.model_versions.get(name, "unknown"),
                    "type": type(self.models[name]).__name__ if name in self.models else None
                }
                for name in ['risk_predictor', 'task_recommender', 'performance_analyzer']
            },
            "preprocessing": {
                "scalers": list(self.scalers.keys()),
                "encoders": list(self.encoders.keys())
            }
        }
    
    async def reload_models(self):
        """重新加载所有模型"""
        logger.info("开始重新加载模型...")
        self.models.clear()
        self.scalers.clear()
        self.encoders.clear()
        self.model_versions.clear()
        self.is_initialized = False
        
        await self.initialize()
        logger.info("模型重新加载完成")
    
    async def cleanup(self):
        """清理资源"""
        logger.info("清理AI模型资源...")
        self.models.clear()
        self.scalers.clear()
        self.encoders.clear()
        self.is_initialized = False
