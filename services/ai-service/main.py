"""
AI分析服务主应用
AI项目管理平台 - AI分析服务

<AUTHOR>
@version 1.0.0
@since 2025-08-16
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager
import asyncio
from typing import Dict, Any

from src.api.prediction_router import prediction_router
from src.api.analysis_router import analysis_router
from src.api.recommendation_router import recommendation_router
from src.services.model_manager import ModelManager
from src.utils.config import get_settings
from src.utils.logger import setup_logger

# 设置日志
logger = setup_logger(__name__)
settings = get_settings()

# 全局模型管理器
model_manager = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global model_manager
    
    # 启动时初始化
    logger.info("正在启动AI分析服务...")
    try:
        model_manager = ModelManager()
        await model_manager.initialize()
        logger.info("AI模型加载完成")
        yield
    except Exception as e:
        logger.error(f"AI服务启动失败: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("正在关闭AI分析服务...")
        if model_manager:
            await model_manager.cleanup()

# 创建FastAPI应用
app = FastAPI(
    title="AI项目管理平台 - AI分析服务",
    description="提供项目风险预测、智能推荐、数据分析等AI功能",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(prediction_router, prefix="/api/v1/predictions", tags=["预测分析"])
app.include_router(analysis_router, prefix="/api/v1/analysis", tags=["数据分析"])
app.include_router(recommendation_router, prefix="/api/v1/recommendations", tags=["智能推荐"])

@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "AI项目管理平台 - AI分析服务",
        "version": "1.0.0",
        "status": "运行中",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查模型状态
        model_status = await model_manager.get_status() if model_manager else "未初始化"
        
        return {
            "status": "healthy",
            "service": "ai-service",
            "version": "1.0.0",
            "models": model_status,
            "timestamp": asyncio.get_event_loop().time()
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")

@app.get("/models/status")
async def get_models_status():
    """获取模型状态"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    
    try:
        status = await model_manager.get_detailed_status()
        return status
    except Exception as e:
        logger.error(f"获取模型状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取模型状态失败")

@app.post("/models/reload")
async def reload_models():
    """重新加载模型"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    
    try:
        await model_manager.reload_models()
        return {"message": "模型重新加载成功"}
    except Exception as e:
        logger.error(f"重新加载模型失败: {e}")
        raise HTTPException(status_code=500, detail="重新加载模型失败")

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": "AI服务遇到了未预期的错误",
            "type": type(exc).__name__
        }
    )

def get_model_manager() -> ModelManager:
    """获取模型管理器依赖"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="AI服务未就绪")
    return model_manager

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )
