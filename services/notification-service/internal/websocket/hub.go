/*
WebSocket Hub - 管理WebSocket连接和消息分发
实现高性能的实时通信功能

<AUTHOR>
@version 1.0.0
@since 2025-08-16
*/

package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// Message 表示WebSocket消息
type Message struct {
	Type      string      `json:"type"`
	UserID    string      `json:"user_id,omitempty"`
	ProjectID string      `json:"project_id,omitempty"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// Client 表示WebSocket客户端
type Client struct {
	ID       string
	UserID   string
	Conn     *websocket.Conn
	Send     chan Message
	Hub      *Hub
	Projects []string // 用户参与的项目列表
}

// Hub 管理WebSocket连接
type Hub struct {
	// 注册的客户端
	clients map[*Client]bool

	// 用户ID到客户端的映射
	userClients map[string][]*Client

	// 项目ID到客户端的映射
	projectClients map[string][]*Client

	// 广播消息通道
	broadcast chan Message

	// 注册客户端通道
	register chan *Client

	// 注销客户端通道
	unregister chan *Client

	// 互斥锁
	mutex sync.RWMutex

	// 日志记录器
	logger *log.Logger
}

// NewHub 创建新的Hub实例
func NewHub(logger *log.Logger) *Hub {
	return &Hub{
		clients:        make(map[*Client]bool),
		userClients:    make(map[string][]*Client),
		projectClients: make(map[string][]*Client),
		broadcast:      make(chan Message),
		register:       make(chan *Client),
		unregister:     make(chan *Client),
		logger:         logger,
	}
}

// Run 启动Hub
func (h *Hub) Run() {
	h.logger.Println("WebSocket Hub 启动")
	
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)
		}
	}
}

// registerClient 注册客户端
func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client] = true

	// 添加到用户客户端映射
	if client.UserID != "" {
		h.userClients[client.UserID] = append(h.userClients[client.UserID], client)
	}

	// 添加到项目客户端映射
	for _, projectID := range client.Projects {
		h.projectClients[projectID] = append(h.projectClients[projectID], client)
	}

	h.logger.Printf("客户端已注册: %s (用户: %s)", client.ID, client.UserID)

	// 发送欢迎消息
	welcomeMsg := Message{
		Type:      "welcome",
		Data:      map[string]string{"message": "连接成功"},
		Timestamp: time.Now(),
	}
	
	select {
	case client.Send <- welcomeMsg:
	default:
		close(client.Send)
		delete(h.clients, client)
	}
}

// unregisterClient 注销客户端
func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.Send)

		// 从用户客户端映射中移除
		if client.UserID != "" {
			clients := h.userClients[client.UserID]
			for i, c := range clients {
				if c == client {
					h.userClients[client.UserID] = append(clients[:i], clients[i+1:]...)
					break
				}
			}
			if len(h.userClients[client.UserID]) == 0 {
				delete(h.userClients, client.UserID)
			}
		}

		// 从项目客户端映射中移除
		for _, projectID := range client.Projects {
			clients := h.projectClients[projectID]
			for i, c := range clients {
				if c == client {
					h.projectClients[projectID] = append(clients[:i], clients[i+1:]...)
					break
				}
			}
			if len(h.projectClients[projectID]) == 0 {
				delete(h.projectClients, projectID)
			}
		}

		h.logger.Printf("客户端已注销: %s (用户: %s)", client.ID, client.UserID)
	}
}

// broadcastMessage 广播消息
func (h *Hub) broadcastMessage(message Message) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	// 根据消息类型选择目标客户端
	var targetClients []*Client

	if message.UserID != "" {
		// 发送给特定用户
		targetClients = h.userClients[message.UserID]
	} else if message.ProjectID != "" {
		// 发送给项目成员
		targetClients = h.projectClients[message.ProjectID]
	} else {
		// 广播给所有客户端
		for client := range h.clients {
			targetClients = append(targetClients, client)
		}
	}

	// 发送消息
	for _, client := range targetClients {
		select {
		case client.Send <- message:
		default:
			// 客户端发送缓冲区已满，关闭连接
			close(client.Send)
			delete(h.clients, client)
		}
	}

	h.logger.Printf("消息已广播: 类型=%s, 目标客户端数=%d", message.Type, len(targetClients))
}

// SendToUser 发送消息给特定用户
func (h *Hub) SendToUser(userID string, messageType string, data interface{}) {
	message := Message{
		Type:      messageType,
		UserID:    userID,
		Data:      data,
		Timestamp: time.Now(),
	}
	
	select {
	case h.broadcast <- message:
	default:
		h.logger.Printf("广播通道已满，消息丢失: 用户=%s, 类型=%s", userID, messageType)
	}
}

// SendToProject 发送消息给项目成员
func (h *Hub) SendToProject(projectID string, messageType string, data interface{}) {
	message := Message{
		Type:      messageType,
		ProjectID: projectID,
		Data:      data,
		Timestamp: time.Now(),
	}
	
	select {
	case h.broadcast <- message:
	default:
		h.logger.Printf("广播通道已满，消息丢失: 项目=%s, 类型=%s", projectID, messageType)
	}
}

// Broadcast 广播消息给所有客户端
func (h *Hub) Broadcast(messageType string, data interface{}) {
	message := Message{
		Type:      messageType,
		Data:      data,
		Timestamp: time.Now(),
	}
	
	select {
	case h.broadcast <- message:
	default:
		h.logger.Printf("广播通道已满，消息丢失: 类型=%s", messageType)
	}
}

// GetStats 获取连接统计信息
func (h *Hub) GetStats() map[string]interface{} {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	return map[string]interface{}{
		"total_clients":   len(h.clients),
		"user_clients":    len(h.userClients),
		"project_clients": len(h.projectClients),
		"timestamp":       time.Now(),
	}
}

// Shutdown 关闭Hub
func (h *Hub) Shutdown() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.logger.Println("正在关闭WebSocket Hub...")

	// 关闭所有客户端连接
	for client := range h.clients {
		close(client.Send)
		client.Conn.Close()
	}

	h.logger.Println("WebSocket Hub 已关闭")
}

// readPump 处理客户端消息读取
func (c *Client) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	// 设置读取超时
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		var message Message
		err := c.Conn.ReadJSON(&message)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.Hub.logger.Printf("WebSocket错误: %v", err)
			}
			break
		}

		// 处理客户端消息
		c.handleMessage(message)
	}
}

// writePump 处理客户端消息写入
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteJSON(message); err != nil {
				c.Hub.logger.Printf("写入消息失败: %v", err)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理客户端发送的消息
func (c *Client) handleMessage(message Message) {
	switch message.Type {
	case "ping":
		// 响应ping消息
		pongMsg := Message{
			Type:      "pong",
			Data:      map[string]string{"message": "pong"},
			Timestamp: time.Now(),
		}
		select {
		case c.Send <- pongMsg:
		default:
		}

	case "join_project":
		// 加入项目房间
		if projectID, ok := message.Data.(string); ok {
			c.joinProject(projectID)
		}

	case "leave_project":
		// 离开项目房间
		if projectID, ok := message.Data.(string); ok {
			c.leaveProject(projectID)
		}

	default:
		c.Hub.logger.Printf("未知消息类型: %s", message.Type)
	}
}

// joinProject 加入项目
func (c *Client) joinProject(projectID string) {
	c.Hub.mutex.Lock()
	defer c.Hub.mutex.Unlock()

	// 检查是否已经在项目中
	for _, pid := range c.Projects {
		if pid == projectID {
			return
		}
	}

	// 添加到项目列表
	c.Projects = append(c.Projects, projectID)
	c.Hub.projectClients[projectID] = append(c.Hub.projectClients[projectID], c)

	c.Hub.logger.Printf("客户端 %s 加入项目 %s", c.ID, projectID)
}

// leaveProject 离开项目
func (c *Client) leaveProject(projectID string) {
	c.Hub.mutex.Lock()
	defer c.Hub.mutex.Unlock()

	// 从项目列表中移除
	for i, pid := range c.Projects {
		if pid == projectID {
			c.Projects = append(c.Projects[:i], c.Projects[i+1:]...)
			break
		}
	}

	// 从项目客户端映射中移除
	clients := c.Hub.projectClients[projectID]
	for i, client := range clients {
		if client == c {
			c.Hub.projectClients[projectID] = append(clients[:i], clients[i+1:]...)
			break
		}
	}

	c.Hub.logger.Printf("客户端 %s 离开项目 %s", c.ID, projectID)
}
