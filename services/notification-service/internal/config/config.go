/**
 * 通知服务配置模块
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package config

import (
	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server       ServerConfig       `mapstructure:"server"`
	Database     DatabaseConfig     `mapstructure:"database"`
	Redis        RedisConfig        `mapstructure:"redis"`
	RabbitMQ     RabbitMQConfig     `mapstructure:"rabbitmq"`
	Email        EmailConfig        `mapstructure:"email"`
	SMS          SMSConfig          `mapstructure:"sms"`
	WebSocket    WebSocketConfig    `mapstructure:"websocket"`
	Notification NotificationConfig `mapstructure:"notification"`
	Log          LogConfig          `mapstructure:"log"`
	JWT          JWTConfig          `mapstructure:"jwt"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int    `mapstructure:"port"`
	Mode         string `mapstructure:"mode"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
	SSLMode  string `mapstructure:"sslmode"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

// RabbitMQConfig RabbitMQ配置
type RabbitMQConfig struct {
	URL      string `mapstructure:"url"`
	Exchange string `mapstructure:"exchange"`
	Queue    string `mapstructure:"queue"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	SMTP     SMTPConfig `mapstructure:"smtp"`
	From     string     `mapstructure:"from"`
	FromName string     `mapstructure:"from_name"`
}

// SMTPConfig SMTP配置
type SMTPConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	TLS      bool   `mapstructure:"tls"`
}

// SMSConfig 短信配置
type SMSConfig struct {
	Provider  string            `mapstructure:"provider"`
	AccessKey string            `mapstructure:"access_key"`
	SecretKey string            `mapstructure:"secret_key"`
	Region    string            `mapstructure:"region"`
	SignName  string            `mapstructure:"sign_name"`
	Templates map[string]string `mapstructure:"templates"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	ReadBufferSize  int `mapstructure:"read_buffer_size"`
	WriteBufferSize int `mapstructure:"write_buffer_size"`
	MaxConnections  int `mapstructure:"max_connections"`
	PingInterval    int `mapstructure:"ping_interval"`
	PongTimeout     int `mapstructure:"pong_timeout"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	MaxRetries      int `mapstructure:"max_retries"`
	RetryInterval   int `mapstructure:"retry_interval"`
	BatchSize       int `mapstructure:"batch_size"`
	QueueSize       int `mapstructure:"queue_size"`
	WorkerCount     int `mapstructure:"worker_count"`
	CleanupInterval int `mapstructure:"cleanup_interval"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
	File   string `mapstructure:"file"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	Expiration int    `mapstructure:"expiration"`
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("/etc/notification-service")

	// 设置默认值
	setDefaults()

	// 环境变量绑定
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.port", 8084)
	viper.SetDefault("server.mode", "development")
	viper.SetDefault("server.read_timeout", 30)
	viper.SetDefault("server.write_timeout", 30)

	// 数据库默认配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "ai_pm_user")
	viper.SetDefault("database.password", "ai_pm_password")
	viper.SetDefault("database.dbname", "ai_pm_notification")
	viper.SetDefault("database.sslmode", "disable")

	// Redis默认配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 4)

	// RabbitMQ默认配置
	viper.SetDefault("rabbitmq.url", "amqp://guest:guest@localhost:5672/")
	viper.SetDefault("rabbitmq.exchange", "notifications")
	viper.SetDefault("rabbitmq.queue", "notification_queue")

	// 邮件默认配置
	viper.SetDefault("email.smtp.host", "smtp.gmail.com")
	viper.SetDefault("email.smtp.port", 587)
	viper.SetDefault("email.smtp.tls", true)
	viper.SetDefault("email.from", "<EMAIL>")
	viper.SetDefault("email.from_name", "AI项目管理平台")

	// WebSocket默认配置
	viper.SetDefault("websocket.read_buffer_size", 1024)
	viper.SetDefault("websocket.write_buffer_size", 1024)
	viper.SetDefault("websocket.max_connections", 1000)
	viper.SetDefault("websocket.ping_interval", 30)
	viper.SetDefault("websocket.pong_timeout", 10)

	// 通知默认配置
	viper.SetDefault("notification.max_retries", 3)
	viper.SetDefault("notification.retry_interval", 60)
	viper.SetDefault("notification.batch_size", 100)
	viper.SetDefault("notification.queue_size", 1000)
	viper.SetDefault("notification.worker_count", 5)
	viper.SetDefault("notification.cleanup_interval", 3600)

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "text")
	viper.SetDefault("log.output", "stdout")

	// JWT默认配置
	viper.SetDefault("jwt.secret", "your-secret-key")
	viper.SetDefault("jwt.expiration", 3600)
}
