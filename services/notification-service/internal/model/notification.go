/**
 * 通知数据模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package model

import (
	"time"

	"gorm.io/gorm"
)

// NotificationType 通知类型
type NotificationType string

const (
	NotificationTypeInfo    NotificationType = "info"
	NotificationTypeWarning NotificationType = "warning"
	NotificationTypeError   NotificationType = "error"
	NotificationTypeSuccess NotificationType = "success"
)

// NotificationChannel 通知渠道
type NotificationChannel string

const (
	ChannelInApp     NotificationChannel = "in_app"
	ChannelEmail     NotificationChannel = "email"
	ChannelSMS       NotificationChannel = "sms"
	ChannelWebSocket NotificationChannel = "websocket"
	ChannelWebhook   NotificationChannel = "webhook"
)

// NotificationStatus 通知状态
type NotificationStatus string

const (
	StatusPending NotificationStatus = "pending"
	StatusSent    NotificationStatus = "sent"
	StatusFailed  NotificationStatus = "failed"
	StatusRead    NotificationStatus = "read"
)

// Notification 通知实体
type Notification struct {
	ID          uint                  `json:"id" gorm:"primaryKey"`
	UserID      string                `json:"user_id" gorm:"not null;index"`
	Type        NotificationType      `json:"type" gorm:"not null"`
	Channel     NotificationChannel   `json:"channel" gorm:"not null"`
	Title       string                `json:"title" gorm:"not null"`
	Content     string                `json:"content" gorm:"type:text"`
	Data        string                `json:"data,omitempty" gorm:"type:jsonb"`
	Status      NotificationStatus    `json:"status" gorm:"default:pending;index"`
	Priority    int                   `json:"priority" gorm:"default:0"`
	ScheduledAt *time.Time            `json:"scheduled_at,omitempty"`
	SentAt      *time.Time            `json:"sent_at,omitempty"`
	ReadAt      *time.Time            `json:"read_at,omitempty"`
	ExpiresAt   *time.Time            `json:"expires_at,omitempty"`
	Metadata    string                `json:"metadata,omitempty" gorm:"type:jsonb"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
	DeletedAt   gorm.DeletedAt        `json:"-" gorm:"index"`
}

// NotificationPreference 通知偏好设置
type NotificationPreference struct {
	ID                uint                `json:"id" gorm:"primaryKey"`
	UserID            string              `json:"user_id" gorm:"not null;uniqueIndex"`
	EmailEnabled      bool                `json:"email_enabled" gorm:"default:true"`
	SMSEnabled        bool                `json:"sms_enabled" gorm:"default:false"`
	InAppEnabled      bool                `json:"in_app_enabled" gorm:"default:true"`
	WebSocketEnabled  bool                `json:"websocket_enabled" gorm:"default:true"`
	QuietHoursStart   string              `json:"quiet_hours_start,omitempty"`
	QuietHoursEnd     string              `json:"quiet_hours_end,omitempty"`
	Timezone          string              `json:"timezone" gorm:"default:UTC"`
	Categories        string              `json:"categories,omitempty" gorm:"type:jsonb"`
	CreatedAt         time.Time           `json:"created_at"`
	UpdatedAt         time.Time           `json:"updated_at"`
	DeletedAt         gorm.DeletedAt      `json:"-" gorm:"index"`
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	ID          uint                `json:"id" gorm:"primaryKey"`
	Name        string              `json:"name" gorm:"not null;uniqueIndex"`
	Type        NotificationType    `json:"type" gorm:"not null"`
	Channel     NotificationChannel `json:"channel" gorm:"not null"`
	Subject     string              `json:"subject,omitempty"`
	Content     string              `json:"content" gorm:"type:text;not null"`
	Variables   string              `json:"variables,omitempty" gorm:"type:jsonb"`
	IsActive    bool                `json:"is_active" gorm:"default:true"`
	CreatedBy   string              `json:"created_by"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
	DeletedAt   gorm.DeletedAt      `json:"-" gorm:"index"`
}

// NotificationLog 通知日志
type NotificationLog struct {
	ID             uint               `json:"id" gorm:"primaryKey"`
	NotificationID uint               `json:"notification_id" gorm:"not null;index"`
	Channel        NotificationChannel `json:"channel" gorm:"not null"`
	Status         NotificationStatus `json:"status" gorm:"not null"`
	Message        string             `json:"message,omitempty"`
	ErrorCode      string             `json:"error_code,omitempty"`
	ErrorMessage   string             `json:"error_message,omitempty"`
	Attempts       int                `json:"attempts" gorm:"default:1"`
	ProcessedAt    time.Time          `json:"processed_at"`
	CreatedAt      time.Time          `json:"created_at"`
}

// NotificationRule 通知规则
type NotificationRule struct {
	ID          uint                `json:"id" gorm:"primaryKey"`
	Name        string              `json:"name" gorm:"not null"`
	Description string              `json:"description,omitempty"`
	EventType   string              `json:"event_type" gorm:"not null"`
	Conditions  string              `json:"conditions" gorm:"type:jsonb"`
	Actions     string              `json:"actions" gorm:"type:jsonb"`
	IsActive    bool                `json:"is_active" gorm:"default:true"`
	Priority    int                 `json:"priority" gorm:"default:0"`
	CreatedBy   string              `json:"created_by"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
	DeletedAt   gorm.DeletedAt      `json:"-" gorm:"index"`
}

// WebSocketConnection WebSocket连接记录
type WebSocketConnection struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	UserID      string    `json:"user_id" gorm:"not null;index"`
	SessionID   string    `json:"session_id,omitempty"`
	IPAddress   string    `json:"ip_address,omitempty"`
	UserAgent   string    `json:"user_agent,omitempty"`
	ConnectedAt time.Time `json:"connected_at"`
	LastPingAt  time.Time `json:"last_ping_at"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
}

// TableName 设置表名
func (Notification) TableName() string {
	return "notifications"
}

func (NotificationPreference) TableName() string {
	return "notification_preferences"
}

func (NotificationTemplate) TableName() string {
	return "notification_templates"
}

func (NotificationLog) TableName() string {
	return "notification_logs"
}

func (NotificationRule) TableName() string {
	return "notification_rules"
}

func (WebSocketConnection) TableName() string {
	return "websocket_connections"
}

// IsExpired 检查通知是否过期
func (n *Notification) IsExpired() bool {
	if n.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*n.ExpiresAt)
}

// CanSend 检查是否可以发送通知
func (n *Notification) CanSend() bool {
	if n.Status != StatusPending {
		return false
	}
	if n.IsExpired() {
		return false
	}
	if n.ScheduledAt != nil && time.Now().Before(*n.ScheduledAt) {
		return false
	}
	return true
}

// MarkAsSent 标记为已发送
func (n *Notification) MarkAsSent() {
	n.Status = StatusSent
	now := time.Now()
	n.SentAt = &now
}

// MarkAsRead 标记为已读
func (n *Notification) MarkAsRead() {
	n.Status = StatusRead
	now := time.Now()
	n.ReadAt = &now
}

// MarkAsFailed 标记为失败
func (n *Notification) MarkAsFailed() {
	n.Status = StatusFailed
}
