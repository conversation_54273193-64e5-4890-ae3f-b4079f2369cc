/*
通知处理器
处理通知相关的HTTP请求

<AUTHOR>
@version 1.0.0
@since 2025-08-16
*/

package handlers

import (
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"notification-service/internal/services"
	wsHub "notification-service/internal/websocket"
)

// NotificationHandler 通知处理器
type NotificationHandler struct {
	notificationService *services.NotificationService
	emailService        *services.EmailService
	websocketHub        *wsHub.Hub
	logger              *log.Logger
}

// NewNotificationHandler 创建通知处理器
func NewNotificationHandler(
	notificationService *services.NotificationService,
	emailService *services.EmailService,
	websocketHub *wsHub.Hub,
	logger *log.Logger,
) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
		emailService:        emailService,
		websocketHub:        websocketHub,
		logger:              logger,
	}
}

// SendNotificationRequest 发送通知请求
type SendNotificationRequest struct {
	UserID    string                 `json:"user_id" binding:"required"`
	Type      string                 `json:"type" binding:"required"`
	Title     string                 `json:"title" binding:"required"`
	Content   string                 `json:"content" binding:"required"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Priority  string                 `json:"priority,omitempty"`
	ProjectID string                 `json:"project_id,omitempty"`
	Channels  []string               `json:"channels,omitempty"`
}

// SendNotification 发送通知
func (h *NotificationHandler) SendNotification(c *gin.Context) {
	var req SendNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 创建通知
	notification := &services.Notification{
		UserID:    req.UserID,
		Type:      req.Type,
		Title:     req.Title,
		Content:   req.Content,
		Data:      req.Data,
		Priority:  req.Priority,
		ProjectID: req.ProjectID,
		Channels:  req.Channels,
		CreatedAt: time.Now(),
	}

	// 保存通知
	if err := h.notificationService.CreateNotification(notification); err != nil {
		h.logger.Printf("创建通知失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建通知失败"})
		return
	}

	// 通过WebSocket实时推送
	h.websocketHub.SendToUser(req.UserID, "notification", map[string]interface{}{
		"id":         notification.ID,
		"type":       notification.Type,
		"title":      notification.Title,
		"content":    notification.Content,
		"data":       notification.Data,
		"priority":   notification.Priority,
		"project_id": notification.ProjectID,
		"created_at": notification.CreatedAt,
	})

	// 根据渠道发送通知
	if contains(req.Channels, "email") {
		go h.sendEmailNotification(notification)
	}

	if contains(req.Channels, "sms") {
		go h.sendSMSNotification(notification)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "通知发送成功",
		"data":    notification,
	})
}

// BroadcastNotificationRequest 广播通知请求
type BroadcastNotificationRequest struct {
	Type      string                 `json:"type" binding:"required"`
	Title     string                 `json:"title" binding:"required"`
	Content   string                 `json:"content" binding:"required"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Priority  string                 `json:"priority,omitempty"`
	ProjectID string                 `json:"project_id,omitempty"`
	UserIDs   []string               `json:"user_ids,omitempty"`
	Channels  []string               `json:"channels,omitempty"`
}

// BroadcastNotification 广播通知
func (h *NotificationHandler) BroadcastNotification(c *gin.Context) {
	var req BroadcastNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 如果指定了用户列表，发送给特定用户
	if len(req.UserIDs) > 0 {
		for _, userID := range req.UserIDs {
			notification := &services.Notification{
				UserID:    userID,
				Type:      req.Type,
				Title:     req.Title,
				Content:   req.Content,
				Data:      req.Data,
				Priority:  req.Priority,
				ProjectID: req.ProjectID,
				Channels:  req.Channels,
				CreatedAt: time.Now(),
			}

			// 保存通知
			if err := h.notificationService.CreateNotification(notification); err != nil {
				h.logger.Printf("创建通知失败: %v", err)
				continue
			}

			// 实时推送
			h.websocketHub.SendToUser(userID, "notification", map[string]interface{}{
				"id":         notification.ID,
				"type":       notification.Type,
				"title":      notification.Title,
				"content":    notification.Content,
				"data":       notification.Data,
				"priority":   notification.Priority,
				"project_id": notification.ProjectID,
				"created_at": notification.CreatedAt,
			})
		}
	} else if req.ProjectID != "" {
		// 发送给项目成员
		h.websocketHub.SendToProject(req.ProjectID, "notification", map[string]interface{}{
			"type":       req.Type,
			"title":      req.Title,
			"content":    req.Content,
			"data":       req.Data,
			"priority":   req.Priority,
			"project_id": req.ProjectID,
			"created_at": time.Now(),
		})
	} else {
		// 广播给所有用户
		h.websocketHub.Broadcast("notification", map[string]interface{}{
			"type":       req.Type,
			"title":      req.Title,
			"content":    req.Content,
			"data":       req.Data,
			"priority":   req.Priority,
			"created_at": time.Now(),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "通知广播成功",
	})
}

// GetUserNotifications 获取用户通知
func (h *NotificationHandler) GetUserNotifications(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	unreadOnly := c.Query("unread_only") == "true"

	notifications, total, err := h.notificationService.GetUserNotifications(userID, page, limit, unreadOnly)
	if err != nil {
		h.logger.Printf("获取用户通知失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取通知失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"notifications": notifications,
			"total":         total,
			"page":          page,
			"limit":         limit,
		},
	})
}

// MarkAsRead 标记通知为已读
func (h *NotificationHandler) MarkAsRead(c *gin.Context) {
	notificationID := c.Param("id")
	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "通知ID不能为空"})
		return
	}

	if err := h.notificationService.MarkAsRead(notificationID); err != nil {
		h.logger.Printf("标记通知已读失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "标记已读失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "已标记为已读",
	})
}

// DeleteNotification 删除通知
func (h *NotificationHandler) DeleteNotification(c *gin.Context) {
	notificationID := c.Param("id")
	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "通知ID不能为空"})
		return
	}

	if err := h.notificationService.DeleteNotification(notificationID); err != nil {
		h.logger.Printf("删除通知失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除通知失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "通知已删除",
	})
}

// SendEmail 发送邮件
func (h *NotificationHandler) SendEmail(c *gin.Context) {
	var req struct {
		To      []string `json:"to" binding:"required"`
		Subject string   `json:"subject" binding:"required"`
		Body    string   `json:"body" binding:"required"`
		IsHTML  bool     `json:"is_html,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.emailService.SendEmail(req.To, req.Subject, req.Body, req.IsHTML); err != nil {
		h.logger.Printf("发送邮件失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "发送邮件失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "邮件发送成功",
	})
}

// SendTemplateEmail 发送模板邮件
func (h *NotificationHandler) SendTemplateEmail(c *gin.Context) {
	var req struct {
		To           []string               `json:"to" binding:"required"`
		TemplateName string                 `json:"template_name" binding:"required"`
		Data         map[string]interface{} `json:"data,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.emailService.SendTemplateEmail(req.To, req.TemplateName, req.Data); err != nil {
		h.logger.Printf("发送模板邮件失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "发送模板邮件失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "模板邮件发送成功",
	})
}

// GetUserPreferences 获取用户通知偏好
func (h *NotificationHandler) GetUserPreferences(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	preferences, err := h.notificationService.GetUserPreferences(userID)
	if err != nil {
		h.logger.Printf("获取用户偏好失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取偏好失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    preferences,
	})
}

// UpdateUserPreferences 更新用户通知偏好
func (h *NotificationHandler) UpdateUserPreferences(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	var preferences services.NotificationPreferences
	if err := c.ShouldBindJSON(&preferences); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	preferences.UserID = userID
	if err := h.notificationService.UpdateUserPreferences(&preferences); err != nil {
		h.logger.Printf("更新用户偏好失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新偏好失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "偏好设置已更新",
		"data":    preferences,
	})
}

// GetNotificationStats 获取通知统计
func (h *NotificationHandler) GetNotificationStats(c *gin.Context) {
	stats, err := h.notificationService.GetNotificationStats()
	if err != nil {
		h.logger.Printf("获取通知统计失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetUserNotificationStats 获取用户通知统计
func (h *NotificationHandler) GetUserNotificationStats(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	stats, err := h.notificationService.GetUserNotificationStats(userID)
	if err != nil {
		h.logger.Printf("获取用户通知统计失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户统计失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// 辅助函数
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// sendEmailNotification 发送邮件通知
func (h *NotificationHandler) sendEmailNotification(notification *services.Notification) {
	// 这里可以根据通知类型选择不同的邮件模板
	templateName := "notification"
	data := map[string]interface{}{
		"title":   notification.Title,
		"content": notification.Content,
		"data":    notification.Data,
	}

	// 获取用户邮箱地址
	userEmail, err := h.notificationService.GetUserEmail(notification.UserID)
	if err != nil {
		h.logger.Printf("获取用户邮箱失败: %v", err)
		return
	}

	if err := h.emailService.SendTemplateEmail([]string{userEmail}, templateName, data); err != nil {
		h.logger.Printf("发送邮件通知失败: %v", err)
	}
}

// sendSMSNotification 发送短信通知
func (h *NotificationHandler) sendSMSNotification(notification *services.Notification) {
	// 获取用户手机号
	userPhone, err := h.notificationService.GetUserPhone(notification.UserID)
	if err != nil {
		h.logger.Printf("获取用户手机号失败: %v", err)
		return
	}

	// 发送短信（这里需要集成短信服务）
	message := notification.Title + ": " + notification.Content
	h.logger.Printf("发送短信到 %s: %s", userPhone, message)
}
