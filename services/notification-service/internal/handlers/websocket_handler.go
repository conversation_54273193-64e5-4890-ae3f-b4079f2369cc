/*
WebSocket处理器
处理WebSocket连接和消息路由

<AUTHOR>
@version 1.0.0
@since 2025-08-16
*/

package handlers

import (
	"log"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/google/uuid"

	wsHub "notification-service/internal/websocket"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	hub      *wsHub.Hub
	upgrader websocket.Upgrader
	logger   *log.Logger
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(hub *wsHub.Hub, logger *log.Logger) *WebSocketHandler {
	return &WebSocketHandler{
		hub: hub,
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
		},
		logger: logger,
	}
}

// HandleWebSocket 处理WebSocket连接
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.Printf("WebSocket升级失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "WebSocket升级失败"})
		return
	}

	// 获取用户ID和项目列表
	userID := c.Query("user_id")
	projectsParam := c.Query("projects")
	
	var projects []string
	if projectsParam != "" {
		projects = strings.Split(projectsParam, ",")
	}

	// 创建客户端
	client := &wsHub.Client{
		ID:       uuid.New().String(),
		UserID:   userID,
		Conn:     conn,
		Send:     make(chan wsHub.Message, 256),
		Hub:      h.hub,
		Projects: projects,
	}

	// 注册客户端
	h.hub.register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump()

	h.logger.Printf("新的WebSocket连接: 用户=%s, 项目=%v", userID, projects)
}

// HandleUserWebSocket 处理特定用户的WebSocket连接
func (h *WebSocketHandler) HandleUserWebSocket(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.Printf("WebSocket升级失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "WebSocket升级失败"})
		return
	}

	// 获取项目列表
	projectsParam := c.Query("projects")
	var projects []string
	if projectsParam != "" {
		projects = strings.Split(projectsParam, ",")
	}

	// 创建客户端
	client := &wsHub.Client{
		ID:       uuid.New().String(),
		UserID:   userID,
		Conn:     conn,
		Send:     make(chan wsHub.Message, 256),
		Hub:      h.hub,
		Projects: projects,
	}

	// 注册客户端
	h.hub.register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump()

	h.logger.Printf("用户WebSocket连接: 用户=%s, 项目=%v", userID, projects)
}

// GetConnectionStats 获取连接统计信息
func (h *WebSocketHandler) GetConnectionStats(c *gin.Context) {
	stats := h.hub.GetStats()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// SendMessageToUser 发送消息给特定用户
func (h *WebSocketHandler) SendMessageToUser(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "用户ID不能为空"})
		return
	}

	var req struct {
		Type string      `json:"type" binding:"required"`
		Data interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 发送消息
	h.hub.SendToUser(userID, req.Type, req.Data)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "消息已发送",
	})
}

// SendMessageToProject 发送消息给项目成员
func (h *WebSocketHandler) SendMessageToProject(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "项目ID不能为空"})
		return
	}

	var req struct {
		Type string      `json:"type" binding:"required"`
		Data interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 发送消息
	h.hub.SendToProject(projectID, req.Type, req.Data)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "消息已发送",
	})
}

// BroadcastMessage 广播消息
func (h *WebSocketHandler) BroadcastMessage(c *gin.Context) {
	var req struct {
		Type string      `json:"type" binding:"required"`
		Data interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 广播消息
	h.hub.Broadcast(req.Type, req.Data)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "消息已广播",
	})
}
