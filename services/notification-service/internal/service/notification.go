/**
 * 通知服务核心业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/ai-pm/notification-service/internal/config"
	"github.com/ai-pm/notification-service/internal/model"
	"github.com/ai-pm/notification-service/pkg/email"
	"github.com/ai-pm/notification-service/pkg/sms"
)

// NotificationService 通知服务
type NotificationService struct {
	db          *gorm.DB
	redis       *redis.Client
	emailSender *email.Sender
	smsSender   *sms.Sender
	config      *config.Config
	workers     []*Worker
	stopChan    chan struct{}
}

// Worker 通知处理工作者
type Worker struct {
	id      int
	service *NotificationService
	queue   chan *model.Notification
	stop    chan struct{}
}

// SendNotificationRequest 发送通知请求
type SendNotificationRequest struct {
	UserID      string                     `json:"user_id" binding:"required"`
	Type        model.NotificationType     `json:"type" binding:"required"`
	Channel     model.NotificationChannel  `json:"channel" binding:"required"`
	Title       string                     `json:"title" binding:"required"`
	Content     string                     `json:"content" binding:"required"`
	Data        map[string]interface{}     `json:"data,omitempty"`
	Priority    int                        `json:"priority"`
	ScheduledAt *time.Time                 `json:"scheduled_at,omitempty"`
	ExpiresAt   *time.Time                 `json:"expires_at,omitempty"`
	Metadata    map[string]interface{}     `json:"metadata,omitempty"`
}

// NewNotificationService 创建通知服务实例
func NewNotificationService(cfg *config.Config) (*NotificationService, error) {
	// 初始化数据库连接
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Database.Host, cfg.Database.Port, cfg.Database.User,
		cfg.Database.Password, cfg.Database.DBName, cfg.Database.SSLMode)
	
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}
	
	// 自动迁移数据库表
	if err := db.AutoMigrate(
		&model.Notification{},
		&model.NotificationPreference{},
		&model.NotificationTemplate{},
		&model.NotificationLog{},
		&model.NotificationRule{},
		&model.WebSocketConnection{},
	); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %w", err)
	}
	
	// 初始化Redis连接
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})
	
	// 测试Redis连接
	if err := rdb.Ping(context.Background()).Err(); err != nil {
		return nil, fmt.Errorf("连接Redis失败: %w", err)
	}
	
	// 初始化邮件发送器
	emailSender := email.NewSender(&cfg.Email)
	
	// 初始化短信发送器
	smsSender := sms.NewSender(&cfg.SMS)
	
	service := &NotificationService{
		db:          db,
		redis:       rdb,
		emailSender: emailSender,
		smsSender:   smsSender,
		config:      cfg,
		stopChan:    make(chan struct{}),
	}
	
	// 启动工作者
	service.startWorkers()
	
	// 启动清理任务
	go service.startCleanupTask()
	
	logrus.Info("通知服务初始化完成")
	return service, nil
}

// SendNotification 发送通知
func (s *NotificationService) SendNotification(req *SendNotificationRequest) (*model.Notification, error) {
	// 创建通知记录
	notification := &model.Notification{
		UserID:      req.UserID,
		Type:        req.Type,
		Channel:     req.Channel,
		Title:       req.Title,
		Content:     req.Content,
		Priority:    req.Priority,
		ScheduledAt: req.ScheduledAt,
		ExpiresAt:   req.ExpiresAt,
		Status:      model.StatusPending,
	}
	
	// 序列化数据和元数据
	if req.Data != nil {
		if dataJSON, err := json.Marshal(req.Data); err == nil {
			notification.Data = string(dataJSON)
		}
	}
	
	if req.Metadata != nil {
		if metadataJSON, err := json.Marshal(req.Metadata); err == nil {
			notification.Metadata = string(metadataJSON)
		}
	}
	
	// 保存到数据库
	if err := s.db.Create(notification).Error; err != nil {
		return nil, fmt.Errorf("保存通知失败: %w", err)
	}
	
	// 如果是立即发送，加入处理队列
	if notification.CanSend() {
		s.enqueueNotification(notification)
	}
	
	logrus.Infof("创建通知成功，ID: %d, 用户: %s, 渠道: %s", 
		notification.ID, notification.UserID, notification.Channel)
	
	return notification, nil
}

// GetNotifications 获取用户通知列表
func (s *NotificationService) GetNotifications(userID string, limit, offset int) ([]*model.Notification, int64, error) {
	var notifications []*model.Notification
	var total int64
	
	query := s.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	// 获取总数
	if err := query.Model(&model.Notification{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	if err := query.Limit(limit).Offset(offset).Find(&notifications).Error; err != nil {
		return nil, 0, err
	}
	
	return notifications, total, nil
}

// MarkAsRead 标记通知为已读
func (s *NotificationService) MarkAsRead(notificationID uint, userID string) error {
	result := s.db.Model(&model.Notification{}).
		Where("id = ? AND user_id = ?", notificationID, userID).
		Updates(map[string]interface{}{
			"status":  model.StatusRead,
			"read_at": time.Now(),
		})
	
	if result.Error != nil {
		return result.Error
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("通知不存在或无权限")
	}
	
	logrus.Infof("标记通知 %d 为已读，用户: %s", notificationID, userID)
	return nil
}

// DeleteNotification 删除通知
func (s *NotificationService) DeleteNotification(notificationID uint, userID string) error {
	result := s.db.Where("id = ? AND user_id = ?", notificationID, userID).
		Delete(&model.Notification{})
	
	if result.Error != nil {
		return result.Error
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("通知不存在或无权限")
	}
	
	logrus.Infof("删除通知 %d，用户: %s", notificationID, userID)
	return nil
}

// GetPreferences 获取用户通知偏好
func (s *NotificationService) GetPreferences(userID string) (*model.NotificationPreference, error) {
	var preference model.NotificationPreference
	
	err := s.db.Where("user_id = ?", userID).First(&preference).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建默认偏好设置
			preference = model.NotificationPreference{
				UserID:           userID,
				EmailEnabled:     true,
				SMSEnabled:       false,
				InAppEnabled:     true,
				WebSocketEnabled: true,
				Timezone:         "UTC",
			}
			
			if err := s.db.Create(&preference).Error; err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	
	return &preference, nil
}

// UpdatePreferences 更新用户通知偏好
func (s *NotificationService) UpdatePreferences(userID string, updates map[string]interface{}) error {
	// 确保偏好设置存在
	if _, err := s.GetPreferences(userID); err != nil {
		return err
	}
	
	result := s.db.Model(&model.NotificationPreference{}).
		Where("user_id = ?", userID).
		Updates(updates)
	
	if result.Error != nil {
		return result.Error
	}
	
	logrus.Infof("更新用户 %s 的通知偏好", userID)
	return nil
}

// startWorkers 启动工作者
func (s *NotificationService) startWorkers() {
	workerCount := s.config.Notification.WorkerCount
	s.workers = make([]*Worker, workerCount)
	
	for i := 0; i < workerCount; i++ {
		worker := &Worker{
			id:      i,
			service: s,
			queue:   make(chan *model.Notification, s.config.Notification.QueueSize/workerCount),
			stop:    make(chan struct{}),
		}
		
		s.workers[i] = worker
		go worker.start()
	}
	
	logrus.Infof("启动 %d 个通知处理工作者", workerCount)
}

// enqueueNotification 将通知加入处理队列
func (s *NotificationService) enqueueNotification(notification *model.Notification) {
	// 简单的负载均衡：根据用户ID哈希选择工作者
	workerIndex := int(notification.ID) % len(s.workers)
	
	select {
	case s.workers[workerIndex].queue <- notification:
		// 成功加入队列
	default:
		// 队列满，记录错误
		logrus.Errorf("通知队列已满，丢弃通知 %d", notification.ID)
	}
}

// startCleanupTask 启动清理任务
func (s *NotificationService) startCleanupTask() {
	ticker := time.NewTicker(time.Duration(s.config.Notification.CleanupInterval) * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			s.cleanupExpiredNotifications()
		case <-s.stopChan:
			return
		}
	}
}

// cleanupExpiredNotifications 清理过期通知
func (s *NotificationService) cleanupExpiredNotifications() {
	result := s.db.Where("expires_at < ? AND status != ?", time.Now(), model.StatusRead).
		Delete(&model.Notification{})
	
	if result.Error != nil {
		logrus.Errorf("清理过期通知失败: %v", result.Error)
	} else if result.RowsAffected > 0 {
		logrus.Infof("清理了 %d 个过期通知", result.RowsAffected)
	}
}

// Close 关闭通知服务
func (s *NotificationService) Close() error {
	logrus.Info("正在关闭通知服务")
	
	// 停止清理任务
	close(s.stopChan)
	
	// 停止所有工作者
	for _, worker := range s.workers {
		close(worker.stop)
	}
	
	// 关闭Redis连接
	if err := s.redis.Close(); err != nil {
		logrus.Errorf("关闭Redis连接失败: %v", err)
	}
	
	// 关闭数据库连接
	if sqlDB, err := s.db.DB(); err == nil {
		if err := sqlDB.Close(); err != nil {
			logrus.Errorf("关闭数据库连接失败: %v", err)
		}
	}
	
	logrus.Info("通知服务已关闭")
	return nil
}

// start 启动工作者
func (w *Worker) start() {
	logrus.Infof("启动通知工作者 %d", w.id)

	for {
		select {
		case notification := <-w.queue:
			w.processNotification(notification)
		case <-w.stop:
			logrus.Infof("停止通知工作者 %d", w.id)
			return
		}
	}
}

// processNotification 处理通知
func (w *Worker) processNotification(notification *model.Notification) {
	logrus.Infof("工作者 %d 处理通知 %d", w.id, notification.ID)

	// 检查是否可以发送
	if !notification.CanSend() {
		logrus.Warnf("通知 %d 不能发送，状态: %s", notification.ID, notification.Status)
		return
	}

	// 获取用户偏好设置
	preferences, err := w.service.GetPreferences(notification.UserID)
	if err != nil {
		logrus.Errorf("获取用户 %s 偏好设置失败: %v", notification.UserID, err)
		w.markNotificationFailed(notification, "获取用户偏好设置失败")
		return
	}

	// 检查用户是否启用了该渠道
	if !w.isChannelEnabled(notification.Channel, preferences) {
		logrus.Infof("用户 %s 未启用渠道 %s，跳过通知 %d",
			notification.UserID, notification.Channel, notification.ID)
		return
	}

	// 根据渠道发送通知
	var err error
	switch notification.Channel {
	case model.ChannelEmail:
		err = w.sendEmailNotification(notification)
	case model.ChannelSMS:
		err = w.sendSMSNotification(notification)
	case model.ChannelInApp:
		err = w.sendInAppNotification(notification)
	case model.ChannelWebSocket:
		err = w.sendWebSocketNotification(notification)
	default:
		err = fmt.Errorf("不支持的通知渠道: %s", notification.Channel)
	}

	// 记录处理结果
	if err != nil {
		logrus.Errorf("发送通知 %d 失败: %v", notification.ID, err)
		w.markNotificationFailed(notification, err.Error())
	} else {
		logrus.Infof("发送通知 %d 成功", notification.ID)
		w.markNotificationSent(notification)
	}
}

// isChannelEnabled 检查渠道是否启用
func (w *Worker) isChannelEnabled(channel model.NotificationChannel, preferences *model.NotificationPreference) bool {
	switch channel {
	case model.ChannelEmail:
		return preferences.EmailEnabled
	case model.ChannelSMS:
		return preferences.SMSEnabled
	case model.ChannelInApp:
		return preferences.InAppEnabled
	case model.ChannelWebSocket:
		return preferences.WebSocketEnabled
	default:
		return true
	}
}

// sendEmailNotification 发送邮件通知
func (w *Worker) sendEmailNotification(notification *model.Notification) error {
	return w.service.emailSender.Send(
		notification.UserID,
		notification.Title,
		notification.Content,
	)
}

// sendSMSNotification 发送短信通知
func (w *Worker) sendSMSNotification(notification *model.Notification) error {
	return w.service.smsSender.Send(
		notification.UserID,
		notification.Content,
	)
}

// sendInAppNotification 发送应用内通知
func (w *Worker) sendInAppNotification(notification *model.Notification) error {
	// 应用内通知只需要更新状态，前端会通过API获取
	return nil
}

// sendWebSocketNotification 发送WebSocket通知
func (w *Worker) sendWebSocketNotification(notification *model.Notification) error {
	// TODO: 集成WebSocket管理器发送实时通知
	// 这里需要与WebSocket管理器集成
	return nil
}

// markNotificationSent 标记通知为已发送
func (w *Worker) markNotificationSent(notification *model.Notification) {
	now := time.Now()
	w.service.db.Model(notification).Updates(map[string]interface{}{
		"status":  model.StatusSent,
		"sent_at": now,
	})

	// 记录日志
	w.logNotificationResult(notification, model.StatusSent, "发送成功", "")
}

// markNotificationFailed 标记通知为失败
func (w *Worker) markNotificationFailed(notification *model.Notification, errorMsg string) {
	w.service.db.Model(notification).Update("status", model.StatusFailed)

	// 记录日志
	w.logNotificationResult(notification, model.StatusFailed, "发送失败", errorMsg)
}

// logNotificationResult 记录通知处理结果
func (w *Worker) logNotificationResult(notification *model.Notification, status model.NotificationStatus, message, errorMsg string) {
	log := &model.NotificationLog{
		NotificationID: notification.ID,
		Channel:        notification.Channel,
		Status:         status,
		Message:        message,
		ErrorMessage:   errorMsg,
		Attempts:       1,
		ProcessedAt:    time.Now(),
	}

	w.service.db.Create(log)
}
