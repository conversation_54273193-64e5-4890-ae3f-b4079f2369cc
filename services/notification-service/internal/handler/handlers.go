/**
 * HTTP处理器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"github.com/ai-pm/notification-service/internal/service"
	"github.com/ai-pm/notification-service/pkg/websocket"
)

// Handlers HTTP处理器集合
type Handlers struct {
	notificationService *service.NotificationService
	wsManager          *websocket.Manager
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginatedResponse 分页响应结构
type PaginatedResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Size    int         `json:"size"`
}

// NewHandlers 创建处理器实例
func NewHandlers(notificationService *service.NotificationService, wsManager *websocket.Manager) *Handlers {
	return &Handlers{
		notificationService: notificationService,
		wsManager:          wsManager,
	}
}

// HealthCheck 健康检查
func (h *Handlers) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "通知服务运行正常",
		Data: map[string]interface{}{
			"service":     "notification-service",
			"version":     "1.0.0",
			"connections": h.wsManager.GetConnectionCount(),
		},
	})
}

// SendNotification 发送通知
func (h *Handlers) SendNotification(c *gin.Context) {
	var req service.SendNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}
	
	notification, err := h.notificationService.SendNotification(&req)
	if err != nil {
		logrus.Errorf("发送通知失败: %v", err)
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "发送通知失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "通知发送成功",
		Data:    notification,
	})
}

// GetNotifications 获取通知列表
func (h *Handlers) GetNotifications(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}
	
	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))
	
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}
	
	offset := (page - 1) * size
	
	notifications, total, err := h.notificationService.GetNotifications(userID, size, offset)
	if err != nil {
		logrus.Errorf("获取通知列表失败: %v", err)
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取通知列表失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, PaginatedResponse{
		Code:    200,
		Message: "获取通知列表成功",
		Data:    notifications,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// MarkAsRead 标记通知为已读
func (h *Handlers) MarkAsRead(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的通知ID",
		})
		return
	}
	
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}
	
	if err := h.notificationService.MarkAsRead(uint(id), userID); err != nil {
		logrus.Errorf("标记通知已读失败: %v", err)
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "标记通知已读失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "标记已读成功",
	})
}

// DeleteNotification 删除通知
func (h *Handlers) DeleteNotification(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "无效的通知ID",
		})
		return
	}
	
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}
	
	if err := h.notificationService.DeleteNotification(uint(id), userID); err != nil {
		logrus.Errorf("删除通知失败: %v", err)
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "删除通知失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "删除通知成功",
	})
}

// GetPreferences 获取通知偏好设置
func (h *Handlers) GetPreferences(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}
	
	preferences, err := h.notificationService.GetPreferences(userID)
	if err != nil {
		logrus.Errorf("获取通知偏好失败: %v", err)
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "获取通知偏好失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取通知偏好成功",
		Data:    preferences,
	})
}

// UpdatePreferences 更新通知偏好设置
func (h *Handlers) UpdatePreferences(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}
	
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}
	
	if err := h.notificationService.UpdatePreferences(userID, updates); err != nil {
		logrus.Errorf("更新通知偏好失败: %v", err)
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "更新通知偏好失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "更新通知偏好成功",
	})
}

// HandleWebSocket 处理WebSocket连接
func (h *Handlers) HandleWebSocket(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}
	
	client, err := h.wsManager.UpgradeConnection(c.Writer, c.Request, userID)
	if err != nil {
		logrus.Errorf("WebSocket连接升级失败: %v", err)
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "WebSocket连接失败: " + err.Error(),
		})
		return
	}
	
	logrus.Infof("用户 %s 建立WebSocket连接，连接ID: %s", userID, client.ID)
}

// GetTemplates 获取通知模板列表
func (h *Handlers) GetTemplates(c *gin.Context) {
	// TODO: 实现获取模板列表
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "获取模板列表成功",
		Data:    []interface{}{},
	})
}

// CreateTemplate 创建通知模板
func (h *Handlers) CreateTemplate(c *gin.Context) {
	// TODO: 实现创建模板
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "创建模板成功",
	})
}

// UpdateTemplate 更新通知模板
func (h *Handlers) UpdateTemplate(c *gin.Context) {
	// TODO: 实现更新模板
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "更新模板成功",
	})
}

// DeleteTemplate 删除通知模板
func (h *Handlers) DeleteTemplate(c *gin.Context) {
	// TODO: 实现删除模板
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "删除模板成功",
	})
}
