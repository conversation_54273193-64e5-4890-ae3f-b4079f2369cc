/**
 * 通知服务主程序
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"github.com/ai-pm/notification-service/internal/config"
	"github.com/ai-pm/notification-service/internal/handler"
	"github.com/ai-pm/notification-service/internal/service"
	"github.com/ai-pm/notification-service/pkg/websocket"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		logrus.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	initLogger(cfg)

	logrus.Info("启动AI项目管理平台通知服务...")

	// 初始化服务
	notificationService, err := service.NewNotificationService(cfg)
	if err != nil {
		logrus.Fatalf("初始化通知服务失败: %v", err)
	}

	// 初始化WebSocket管理器
	wsManager := websocket.NewManager()
	go wsManager.Start()

	// 初始化HTTP处理器
	handlers := handler.NewHandlers(notificationService, wsManager)

	// 设置Gin模式
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := setupRoutes(handlers)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		logrus.Infof("通知服务启动在端口 %d", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("启动服务器失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("正在关闭通知服务...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logrus.Errorf("服务器关闭失败: %v", err)
	}

	// 关闭WebSocket管理器
	wsManager.Stop()

	// 关闭通知服务
	if err := notificationService.Close(); err != nil {
		logrus.Errorf("关闭通知服务失败: %v", err)
	}

	logrus.Info("通知服务已关闭")
}

// setupRoutes 设置路由
func setupRoutes(handlers *handler.Handlers) *gin.Engine {
	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	// API路由组
	api := router.Group("/api/v1")
	{
		// 健康检查
		api.GET("/health", handlers.HealthCheck)

		// 通知相关路由
		notifications := api.Group("/notifications")
		{
			notifications.POST("/send", handlers.SendNotification)
			notifications.GET("/", handlers.GetNotifications)
			notifications.PUT("/:id/read", handlers.MarkAsRead)
			notifications.DELETE("/:id", handlers.DeleteNotification)
		}

		// 通知偏好设置
		preferences := api.Group("/preferences")
		{
			preferences.GET("/", handlers.GetPreferences)
			preferences.PUT("/", handlers.UpdatePreferences)
		}

		// 通知模板
		templates := api.Group("/templates")
		{
			templates.GET("/", handlers.GetTemplates)
			templates.POST("/", handlers.CreateTemplate)
			templates.PUT("/:id", handlers.UpdateTemplate)
			templates.DELETE("/:id", handlers.DeleteTemplate)
		}

		// WebSocket连接
		api.GET("/ws", handlers.HandleWebSocket)
	}

	return router
}

// initLogger 初始化日志
func initLogger(cfg *config.Config) {
	level, err := logrus.ParseLevel(cfg.Log.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)

	if cfg.Log.Format == "json" {
		logrus.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	if cfg.Log.Output == "file" && cfg.Log.File != "" {
		file, err := os.OpenFile(cfg.Log.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			logrus.Warnf("无法打开日志文件 %s: %v", cfg.Log.File, err)
		} else {
			logrus.SetOutput(file)
		}
	}
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
