/**
 * 邮件发送模块
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package email

import (
	"fmt"
	"html/template"
	"strings"

	"github.com/sirupsen/logrus"
	"gopkg.in/gomail.v2"

	"github.com/ai-pm/notification-service/internal/config"
)

// Sender 邮件发送器
type Sender struct {
	config   *config.EmailConfig
	dialer   *gomail.Dialer
	template *template.Template
}

// EmailData 邮件数据
type EmailData struct {
	To       string
	Subject  string
	Content  string
	HTMLBody string
	Data     map[string]interface{}
}

// NewSender 创建邮件发送器
func NewSender(config *config.EmailConfig) *Sender {
	dialer := gomail.NewDialer(
		config.SMTP.Host,
		config.SMTP.Port,
		config.SMTP.Username,
		config.SMTP.Password,
	)
	
	if config.SMTP.TLS {
		dialer.TLSConfig = nil // 使用默认TLS配置
	}
	
	// 加载邮件模板
	tmpl := template.Must(template.New("email").Parse(defaultEmailTemplate))
	
	return &Sender{
		config:   config,
		dialer:   dialer,
		template: tmpl,
	}
}

// Send 发送邮件
func (s *Sender) Send(to, subject, content string) error {
	return s.SendWithData(&EmailData{
		To:      to,
		Subject: subject,
		Content: content,
	})
}

// SendWithData 使用数据发送邮件
func (s *Sender) SendWithData(data *EmailData) error {
	// 创建邮件消息
	m := gomail.NewMessage()
	
	// 设置发件人
	m.SetHeader("From", s.config.From)
	
	// 设置收件人
	m.SetHeader("To", data.To)
	
	// 设置主题
	m.SetHeader("Subject", data.Subject)
	
	// 设置邮件内容
	if data.HTMLBody != "" {
		m.SetBody("text/html", data.HTMLBody)
		m.AddAlternative("text/plain", data.Content)
	} else {
		// 使用模板生成HTML内容
		htmlBody, err := s.generateHTMLBody(data)
		if err != nil {
			logrus.Errorf("生成HTML邮件内容失败: %v", err)
			m.SetBody("text/plain", data.Content)
		} else {
			m.SetBody("text/html", htmlBody)
			m.AddAlternative("text/plain", data.Content)
		}
	}
	
	// 发送邮件
	if err := s.dialer.DialAndSend(m); err != nil {
		return fmt.Errorf("发送邮件失败: %w", err)
	}
	
	logrus.Infof("邮件发送成功，收件人: %s, 主题: %s", data.To, data.Subject)
	return nil
}

// SendBatch 批量发送邮件
func (s *Sender) SendBatch(emails []*EmailData) error {
	var errors []string
	
	for _, email := range emails {
		if err := s.SendWithData(email); err != nil {
			errors = append(errors, fmt.Sprintf("发送给 %s 失败: %v", email.To, err))
		}
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("批量发送邮件部分失败: %s", strings.Join(errors, "; "))
	}
	
	logrus.Infof("批量发送 %d 封邮件成功", len(emails))
	return nil
}

// generateHTMLBody 生成HTML邮件内容
func (s *Sender) generateHTMLBody(data *EmailData) (string, error) {
	var buf strings.Builder
	
	templateData := map[string]interface{}{
		"Subject": data.Subject,
		"Content": data.Content,
		"Data":    data.Data,
	}
	
	if err := s.template.Execute(&buf, templateData); err != nil {
		return "", err
	}
	
	return buf.String(), nil
}

// ValidateEmail 验证邮箱地址格式
func ValidateEmail(email string) bool {
	// 简单的邮箱格式验证
	return strings.Contains(email, "@") && strings.Contains(email, ".")
}

// defaultEmailTemplate 默认邮件模板
const defaultEmailTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Subject}}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #ffffff;
            padding: 30px 20px;
            border: 1px solid #e1e5e9;
            border-top: none;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border: 1px solid #e1e5e9;
            border-top: none;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
            color: #6c757d;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background: #e7f3ff;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI项目管理平台</h1>
        <p>{{.Subject}}</p>
    </div>
    
    <div class="content">
        {{.Content}}
        
        {{if .Data.ActionUrl}}
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{.Data.ActionUrl}}" class="button">{{.Data.ActionText | default "查看详情"}}</a>
        </div>
        {{end}}
        
        {{if .Data.Alert}}
        <div class="alert">
            {{.Data.Alert}}
        </div>
        {{end}}
    </div>
    
    <div class="footer">
        <p>此邮件由AI项目管理平台自动发送，请勿回复。</p>
        <p>如有疑问，请联系系统管理员。</p>
        <p>&copy; 2025 AI项目管理平台. 保留所有权利。</p>
    </div>
</body>
</html>
`
