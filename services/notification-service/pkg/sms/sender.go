/**
 * 短信发送模块
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package sms

import (
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"

	"github.com/ai-pm/notification-service/internal/config"
)

// Sender 短信发送器
type Sender struct {
	config   *config.SMSConfig
	provider Provider
}

// Provider 短信服务提供商接口
type Provider interface {
	Send(phone, content string) error
	SendTemplate(phone, templateCode string, params map[string]string) error
}

// SMSData 短信数据
type SMSData struct {
	Phone        string
	Content      string
	TemplateCode string
	Params       map[string]string
}

// NewSender 创建短信发送器
func NewSender(config *config.SMSConfig) *Sender {
	var provider Provider
	
	switch strings.ToLower(config.Provider) {
	case "aliyun":
		provider = NewAliyunProvider(config)
	case "tencent":
		provider = NewTencentProvider(config)
	case "mock":
		provider = NewMockProvider(config)
	default:
		logrus.Warnf("未知的短信服务提供商: %s, 使用Mock提供商", config.Provider)
		provider = NewMockProvider(config)
	}
	
	return &Sender{
		config:   config,
		provider: provider,
	}
}

// Send 发送短信
func (s *Sender) Send(phone, content string) error {
	// 验证手机号格式
	if !ValidatePhone(phone) {
		return fmt.Errorf("无效的手机号: %s", phone)
	}
	
	// 内容长度检查
	if len(content) > 500 {
		return fmt.Errorf("短信内容过长，最大500字符")
	}
	
	// 发送短信
	if err := s.provider.Send(phone, content); err != nil {
		return fmt.Errorf("发送短信失败: %w", err)
	}
	
	logrus.Infof("短信发送成功，手机号: %s", maskPhone(phone))
	return nil
}

// SendTemplate 发送模板短信
func (s *Sender) SendTemplate(phone, templateCode string, params map[string]string) error {
	// 验证手机号格式
	if !ValidatePhone(phone) {
		return fmt.Errorf("无效的手机号: %s", phone)
	}
	
	// 检查模板是否存在
	if _, exists := s.config.Templates[templateCode]; !exists {
		return fmt.Errorf("模板不存在: %s", templateCode)
	}
	
	// 发送模板短信
	if err := s.provider.SendTemplate(phone, templateCode, params); err != nil {
		return fmt.Errorf("发送模板短信失败: %w", err)
	}
	
	logrus.Infof("模板短信发送成功，手机号: %s, 模板: %s", maskPhone(phone), templateCode)
	return nil
}

// SendBatch 批量发送短信
func (s *Sender) SendBatch(messages []*SMSData) error {
	var errors []string
	
	for _, msg := range messages {
		var err error
		if msg.TemplateCode != "" {
			err = s.SendTemplate(msg.Phone, msg.TemplateCode, msg.Params)
		} else {
			err = s.Send(msg.Phone, msg.Content)
		}
		
		if err != nil {
			errors = append(errors, fmt.Sprintf("发送给 %s 失败: %v", maskPhone(msg.Phone), err))
		}
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("批量发送短信部分失败: %s", strings.Join(errors, "; "))
	}
	
	logrus.Infof("批量发送 %d 条短信成功", len(messages))
	return nil
}

// ValidatePhone 验证手机号格式
func ValidatePhone(phone string) bool {
	// 简单的手机号验证（中国大陆）
	if len(phone) != 11 {
		return false
	}
	
	if !strings.HasPrefix(phone, "1") {
		return false
	}
	
	// 检查是否全为数字
	for _, r := range phone {
		if r < '0' || r > '9' {
			return false
		}
	}
	
	return true
}

// maskPhone 手机号脱敏
func maskPhone(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}

// MockProvider Mock短信提供商（用于测试）
type MockProvider struct {
	config *config.SMSConfig
}

// NewMockProvider 创建Mock提供商
func NewMockProvider(config *config.SMSConfig) *MockProvider {
	return &MockProvider{config: config}
}

// Send 发送短信（Mock实现）
func (p *MockProvider) Send(phone, content string) error {
	logrus.Infof("[Mock SMS] 发送短信到 %s: %s", maskPhone(phone), content)
	return nil
}

// SendTemplate 发送模板短信（Mock实现）
func (p *MockProvider) SendTemplate(phone, templateCode string, params map[string]string) error {
	logrus.Infof("[Mock SMS] 发送模板短信到 %s, 模板: %s, 参数: %v", 
		maskPhone(phone), templateCode, params)
	return nil
}

// AliyunProvider 阿里云短信提供商
type AliyunProvider struct {
	config *config.SMSConfig
}

// NewAliyunProvider 创建阿里云提供商
func NewAliyunProvider(config *config.SMSConfig) *AliyunProvider {
	return &AliyunProvider{config: config}
}

// Send 发送短信（阿里云实现）
func (p *AliyunProvider) Send(phone, content string) error {
	// TODO: 实现阿里云短信发送
	logrus.Infof("[Aliyun SMS] 发送短信到 %s: %s", maskPhone(phone), content)
	return nil
}

// SendTemplate 发送模板短信（阿里云实现）
func (p *AliyunProvider) SendTemplate(phone, templateCode string, params map[string]string) error {
	// TODO: 实现阿里云模板短信发送
	logrus.Infof("[Aliyun SMS] 发送模板短信到 %s, 模板: %s, 参数: %v", 
		maskPhone(phone), templateCode, params)
	return nil
}

// TencentProvider 腾讯云短信提供商
type TencentProvider struct {
	config *config.SMSConfig
}

// NewTencentProvider 创建腾讯云提供商
func NewTencentProvider(config *config.SMSConfig) *TencentProvider {
	return &TencentProvider{config: config}
}

// Send 发送短信（腾讯云实现）
func (p *TencentProvider) Send(phone, content string) error {
	// TODO: 实现腾讯云短信发送
	logrus.Infof("[Tencent SMS] 发送短信到 %s: %s", maskPhone(phone), content)
	return nil
}

// SendTemplate 发送模板短信（腾讯云实现）
func (p *TencentProvider) SendTemplate(phone, templateCode string, params map[string]string) error {
	// TODO: 实现腾讯云模板短信发送
	logrus.Infof("[Tencent SMS] 发送模板短信到 %s, 模板: %s, 参数: %v", 
		maskPhone(phone), templateCode, params)
	return nil
}
