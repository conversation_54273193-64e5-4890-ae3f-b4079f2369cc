/**
 * WebSocket连接管理器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package websocket

import (
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// Manager WebSocket连接管理器
type Manager struct {
	clients    map[string]*Client
	register   chan *Client
	unregister chan *Client
	broadcast  chan []byte
	mutex      sync.RWMutex
	upgrader   websocket.Upgrader
}

// Client WebSocket客户端
type Client struct {
	ID       string
	UserID   string
	Conn     *websocket.Conn
	Send     chan []byte
	Manager  *Manager
	LastPing time.Time
}

// Message WebSocket消息
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// NewManager 创建新的WebSocket管理器
func NewManager() *Manager {
	return &Manager{
		clients:    make(map[string]*Client),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		broadcast:  make(chan []byte),
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
		},
	}
}

// Start 启动WebSocket管理器
func (m *Manager) Start() {
	logrus.Info("WebSocket管理器启动")
	
	// 启动心跳检查
	go m.heartbeat()
	
	for {
		select {
		case client := <-m.register:
			m.registerClient(client)
			
		case client := <-m.unregister:
			m.unregisterClient(client)
			
		case message := <-m.broadcast:
			m.broadcastMessage(message)
		}
	}
}

// Stop 停止WebSocket管理器
func (m *Manager) Stop() {
	logrus.Info("正在停止WebSocket管理器")
	
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	for _, client := range m.clients {
		close(client.Send)
		client.Conn.Close()
	}
	
	close(m.register)
	close(m.unregister)
	close(m.broadcast)
}

// UpgradeConnection 升级HTTP连接为WebSocket
func (m *Manager) UpgradeConnection(w http.ResponseWriter, r *http.Request, userID string) (*Client, error) {
	conn, err := m.upgrader.Upgrade(w, r, nil)
	if err != nil {
		return nil, err
	}
	
	client := &Client{
		ID:       generateClientID(),
		UserID:   userID,
		Conn:     conn,
		Send:     make(chan []byte, 256),
		Manager:  m,
		LastPing: time.Now(),
	}
	
	m.register <- client
	
	// 启动客户端读写协程
	go client.writePump()
	go client.readPump()
	
	return client, nil
}

// SendToUser 向指定用户发送消息
func (m *Manager) SendToUser(userID string, message interface{}) error {
	data, err := json.Marshal(Message{
		Type:      "notification",
		Data:      message,
		Timestamp: time.Now(),
	})
	if err != nil {
		return err
	}
	
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	for _, client := range m.clients {
		if client.UserID == userID {
			select {
			case client.Send <- data:
			default:
				// 发送缓冲区满，关闭连接
				m.unregisterClient(client)
			}
		}
	}
	
	return nil
}

// Broadcast 广播消息给所有连接
func (m *Manager) Broadcast(message interface{}) error {
	data, err := json.Marshal(Message{
		Type:      "broadcast",
		Data:      message,
		Timestamp: time.Now(),
	})
	if err != nil {
		return err
	}
	
	m.broadcast <- data
	return nil
}

// GetConnectedUsers 获取已连接的用户列表
func (m *Manager) GetConnectedUsers() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	users := make(map[string]bool)
	for _, client := range m.clients {
		users[client.UserID] = true
	}
	
	result := make([]string, 0, len(users))
	for userID := range users {
		result = append(result, userID)
	}
	
	return result
}

// GetConnectionCount 获取连接数
func (m *Manager) GetConnectionCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return len(m.clients)
}

// registerClient 注册客户端
func (m *Manager) registerClient(client *Client) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.clients[client.ID] = client
	logrus.Infof("用户 %s 连接WebSocket，连接ID: %s", client.UserID, client.ID)
	
	// 发送欢迎消息
	welcome := Message{
		Type:      "welcome",
		Data:      map[string]string{"message": "连接成功"},
		Timestamp: time.Now(),
	}
	
	if data, err := json.Marshal(welcome); err == nil {
		select {
		case client.Send <- data:
		default:
			close(client.Send)
			delete(m.clients, client.ID)
		}
	}
}

// unregisterClient 注销客户端
func (m *Manager) unregisterClient(client *Client) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if _, ok := m.clients[client.ID]; ok {
		delete(m.clients, client.ID)
		close(client.Send)
		client.Conn.Close()
		logrus.Infof("用户 %s 断开WebSocket连接，连接ID: %s", client.UserID, client.ID)
	}
}

// broadcastMessage 广播消息
func (m *Manager) broadcastMessage(message []byte) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	for _, client := range m.clients {
		select {
		case client.Send <- message:
		default:
			close(client.Send)
			delete(m.clients, client.ID)
		}
	}
}

// heartbeat 心跳检查
func (m *Manager) heartbeat() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for range ticker.C {
		m.mutex.RLock()
		for _, client := range m.clients {
			if time.Since(client.LastPing) > 60*time.Second {
				// 超时，关闭连接
				m.unregister <- client
			}
		}
		m.mutex.RUnlock()
	}
}

// generateClientID 生成客户端ID
func generateClientID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
