/**
 * WebSocket客户端处理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

package websocket

import (
	"encoding/json"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

const (
	// 写入等待时间
	writeWait = 10 * time.Second
	
	// Pong等待时间
	pongWait = 60 * time.Second
	
	// Ping发送间隔
	pingPeriod = (pongWait * 9) / 10
	
	// 最大消息大小
	maxMessageSize = 512
)

// readPump 处理从WebSocket连接读取消息
func (c *Client) readPump() {
	defer func() {
		c.Manager.unregister <- c
		c.Conn.Close()
	}()
	
	c.Conn.SetReadLimit(maxMessageSize)
	c.Conn.SetReadDeadline(time.Now().Add(pongWait))
	c.Conn.SetPongHandler(func(string) error {
		c.LastPing = time.Now()
		c.Conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})
	
	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logrus.Errorf("WebSocket读取错误: %v", err)
			}
			break
		}
		
		// 处理客户端消息
		c.handleMessage(message)
	}
}

// writePump 处理向WebSocket连接写入消息
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()
	
	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// 管理器关闭了通道
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}
			
			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)
			
			// 添加排队的消息到当前WebSocket消息
			n := len(c.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.Send)
			}
			
			if err := w.Close(); err != nil {
				return
			}
			
		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理客户端发送的消息
func (c *Client) handleMessage(message []byte) {
	var msg struct {
		Type string          `json:"type"`
		Data json.RawMessage `json:"data"`
	}
	
	if err := json.Unmarshal(message, &msg); err != nil {
		logrus.Errorf("解析WebSocket消息失败: %v", err)
		return
	}
	
	switch msg.Type {
	case "ping":
		c.handlePing()
	case "mark_read":
		c.handleMarkRead(msg.Data)
	case "subscribe":
		c.handleSubscribe(msg.Data)
	case "unsubscribe":
		c.handleUnsubscribe(msg.Data)
	default:
		logrus.Warnf("未知的WebSocket消息类型: %s", msg.Type)
	}
}

// handlePing 处理ping消息
func (c *Client) handlePing() {
	c.LastPing = time.Now()
	
	response := Message{
		Type:      "pong",
		Data:      map[string]interface{}{"timestamp": time.Now().Unix()},
		Timestamp: time.Now(),
	}
	
	if data, err := json.Marshal(response); err == nil {
		select {
		case c.Send <- data:
		default:
			// 发送缓冲区满
		}
	}
}

// handleMarkRead 处理标记已读消息
func (c *Client) handleMarkRead(data json.RawMessage) {
	var req struct {
		NotificationID uint `json:"notification_id"`
	}
	
	if err := json.Unmarshal(data, &req); err != nil {
		logrus.Errorf("解析mark_read消息失败: %v", err)
		return
	}
	
	// TODO: 调用通知服务标记消息为已读
	logrus.Infof("用户 %s 标记通知 %d 为已读", c.UserID, req.NotificationID)
	
	// 发送确认响应
	response := Message{
		Type: "mark_read_ack",
		Data: map[string]interface{}{
			"notification_id": req.NotificationID,
			"status":         "success",
		},
		Timestamp: time.Now(),
	}
	
	if data, err := json.Marshal(response); err == nil {
		select {
		case c.Send <- data:
		default:
			// 发送缓冲区满
		}
	}
}

// handleSubscribe 处理订阅消息
func (c *Client) handleSubscribe(data json.RawMessage) {
	var req struct {
		Channels []string `json:"channels"`
	}
	
	if err := json.Unmarshal(data, &req); err != nil {
		logrus.Errorf("解析subscribe消息失败: %v", err)
		return
	}
	
	// TODO: 实现订阅逻辑
	logrus.Infof("用户 %s 订阅频道: %v", c.UserID, req.Channels)
	
	// 发送确认响应
	response := Message{
		Type: "subscribe_ack",
		Data: map[string]interface{}{
			"channels": req.Channels,
			"status":   "success",
		},
		Timestamp: time.Now(),
	}
	
	if data, err := json.Marshal(response); err == nil {
		select {
		case c.Send <- data:
		default:
			// 发送缓冲区满
		}
	}
}

// handleUnsubscribe 处理取消订阅消息
func (c *Client) handleUnsubscribe(data json.RawMessage) {
	var req struct {
		Channels []string `json:"channels"`
	}
	
	if err := json.Unmarshal(data, &req); err != nil {
		logrus.Errorf("解析unsubscribe消息失败: %v", err)
		return
	}
	
	// TODO: 实现取消订阅逻辑
	logrus.Infof("用户 %s 取消订阅频道: %v", c.UserID, req.Channels)
	
	// 发送确认响应
	response := Message{
		Type: "unsubscribe_ack",
		Data: map[string]interface{}{
			"channels": req.Channels,
			"status":   "success",
		},
		Timestamp: time.Now(),
	}
	
	if data, err := json.Marshal(response); err == nil {
		select {
		case c.Send <- data:
		default:
			// 发送缓冲区满
		}
	}
}
