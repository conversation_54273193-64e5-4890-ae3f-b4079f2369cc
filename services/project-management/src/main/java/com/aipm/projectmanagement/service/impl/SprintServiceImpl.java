package com.aipm.projectmanagement.service.impl;

import com.aipm.projectmanagement.entity.Project;
import com.aipm.projectmanagement.entity.Sprint;
import com.aipm.projectmanagement.entity.Sprint.SprintStatus;
import com.aipm.projectmanagement.entity.Task;
import com.aipm.projectmanagement.repository.ProjectRepository;
import com.aipm.projectmanagement.repository.SprintRepository;
import com.aipm.projectmanagement.repository.TaskRepository;
import com.aipm.projectmanagement.service.SprintService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

/**
 * Sprint服务实现类
 * 
 * 实现Sprint管理相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Service
@Transactional
public class SprintServiceImpl implements SprintService {

    private static final Logger logger = LoggerFactory.getLogger(SprintServiceImpl.class);

    @Autowired
    private SprintRepository sprintRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Override
    public Sprint createSprint(Sprint sprint, UUID creatorId) {
        logger.info("创建Sprint: {}, 创建者: {}", sprint.getName(), creatorId);

        // 验证项目存在
        if (sprint.getProject() == null || sprint.getProject().getId() == null) {
            throw new RuntimeException("Sprint必须关联到项目");
        }

        Project project = projectRepository.findById(sprint.getProject().getId())
                .orElseThrow(() -> new RuntimeException("项目不存在: " + sprint.getProject().getId()));

        // 验证用户有项目访问权限
        // TODO: 实现权限检查
        // if (!hasProjectAccess(project.getId(), creatorId)) {
        //     throw new RuntimeException("用户无权限在此项目中创建Sprint");
        // }

        // 验证Sprint名称唯一性
        if (sprintRepository.existsByProjectIdAndNameAndIdNot(
                project.getId(), sprint.getName(), null)) {
            throw new RuntimeException("Sprint名称已存在: " + sprint.getName());
        }

        // 验证日期范围
        if (sprint.getStartDate().isAfter(sprint.getEndDate())) {
            throw new RuntimeException("Sprint开始日期不能晚于结束日期");
        }

        // 设置创建者信息
        sprint.setCreatedBy(creatorId);
        sprint.setUpdatedBy(creatorId);

        Sprint savedSprint = sprintRepository.save(sprint);
        logger.info("Sprint创建成功: {}", savedSprint.getId());
        return savedSprint;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Sprint> getSprintById(UUID sprintId) {
        return sprintRepository.findById(sprintId);
    }

    @Override
    public Sprint updateSprint(Sprint sprint, UUID updaterId) {
        logger.info("更新Sprint: {}, 更新者: {}", sprint.getId(), updaterId);

        Sprint existingSprint = sprintRepository.findById(sprint.getId())
                .orElseThrow(() -> new RuntimeException("Sprint不存在: " + sprint.getId()));

        // 验证权限
        if (!canManageSprint(sprint.getId(), updaterId)) {
            throw new RuntimeException("用户无权限更新此Sprint");
        }

        // 验证Sprint名称唯一性
        if (!existingSprint.getName().equals(sprint.getName()) &&
            sprintRepository.existsByProjectIdAndNameAndIdNot(
                existingSprint.getProject().getId(), sprint.getName(), sprint.getId())) {
            throw new RuntimeException("Sprint名称已存在: " + sprint.getName());
        }

        // 验证日期范围
        if (sprint.getStartDate().isAfter(sprint.getEndDate())) {
            throw new RuntimeException("Sprint开始日期不能晚于结束日期");
        }

        // 更新Sprint信息
        existingSprint.setName(sprint.getName());
        existingSprint.setDescription(sprint.getDescription());
        existingSprint.setStartDate(sprint.getStartDate());
        existingSprint.setEndDate(sprint.getEndDate());
        existingSprint.setGoal(sprint.getGoal());
        existingSprint.setUpdatedBy(updaterId);

        Sprint updatedSprint = sprintRepository.save(existingSprint);
        logger.info("Sprint更新成功: {}", updatedSprint.getId());
        return updatedSprint;
    }

    @Override
    public void deleteSprint(UUID sprintId, UUID deleterId) {
        logger.info("删除Sprint: {}, 删除者: {}", sprintId, deleterId);

        Sprint sprint = sprintRepository.findById(sprintId)
                .orElseThrow(() -> new RuntimeException("Sprint不存在: " + sprintId));

        // 验证权限
        if (!canManageSprint(sprintId, deleterId)) {
            throw new RuntimeException("用户无权限删除此Sprint");
        }

        // 检查Sprint状态
        if (sprint.getStatus() == SprintStatus.ACTIVE) {
            throw new RuntimeException("无法删除进行中的Sprint");
        }

        // 移除Sprint中的所有任务
        for (Task task : sprint.getTasks()) {
            task.setSprint(null);
            taskRepository.save(task);
        }

        sprintRepository.delete(sprint);
        logger.info("Sprint删除成功: {}", sprintId);
    }

    @Override
    public Sprint startSprint(UUID sprintId, UUID userId) {
        logger.info("开始Sprint: {}, 用户: {}", sprintId, userId);

        Sprint sprint = sprintRepository.findById(sprintId)
                .orElseThrow(() -> new RuntimeException("Sprint不存在: " + sprintId));

        // 验证权限
        if (!canManageSprint(sprintId, userId)) {
            throw new RuntimeException("用户无权限开始此Sprint");
        }

        // 检查是否有其他活跃的Sprint
        List<Sprint> activeSprints = sprintRepository.findActiveSprintsByProject(
                sprint.getProject().getId());
        if (!activeSprints.isEmpty()) {
            throw new RuntimeException("项目中已有活跃的Sprint，请先完成或取消现有Sprint");
        }

        sprint.start();
        sprint.setUpdatedBy(userId);

        Sprint updatedSprint = sprintRepository.save(sprint);
        logger.info("Sprint开始成功: {}", sprintId);
        return updatedSprint;
    }

    @Override
    public Sprint completeSprint(UUID sprintId, UUID userId) {
        logger.info("完成Sprint: {}, 用户: {}", sprintId, userId);

        Sprint sprint = sprintRepository.findById(sprintId)
                .orElseThrow(() -> new RuntimeException("Sprint不存在: " + sprintId));

        // 验证权限
        if (!canManageSprint(sprintId, userId)) {
            throw new RuntimeException("用户无权限完成此Sprint");
        }

        sprint.complete();
        sprint.setUpdatedBy(userId);

        Sprint updatedSprint = sprintRepository.save(sprint);
        logger.info("Sprint完成成功: {}", sprintId);
        return updatedSprint;
    }

    @Override
    public Sprint cancelSprint(UUID sprintId, UUID userId) {
        logger.info("取消Sprint: {}, 用户: {}", sprintId, userId);

        Sprint sprint = sprintRepository.findById(sprintId)
                .orElseThrow(() -> new RuntimeException("Sprint不存在: " + sprintId));

        // 验证权限
        if (!canManageSprint(sprintId, userId)) {
            throw new RuntimeException("用户无权限取消此Sprint");
        }

        sprint.cancel();
        sprint.setUpdatedBy(userId);

        Sprint updatedSprint = sprintRepository.save(sprint);
        logger.info("Sprint取消成功: {}", sprintId);
        return updatedSprint;
    }

    @Override
    public Sprint updateSprintStatus(UUID sprintId, SprintStatus status, UUID userId) {
        logger.info("更新Sprint状态: {} -> {}, 用户: {}", sprintId, status, userId);

        Sprint sprint = sprintRepository.findById(sprintId)
                .orElseThrow(() -> new RuntimeException("Sprint不存在: " + sprintId));

        // 验证权限
        if (!canManageSprint(sprintId, userId)) {
            throw new RuntimeException("用户无权限更新此Sprint状态");
        }

        // 验证状态转换
        if (!sprint.getStatus().canTransitionTo(status)) {
            throw new RuntimeException(String.format("无法从状态 %s 转换到 %s",
                    sprint.getStatus().getDisplayName(), status.getDisplayName()));
        }

        sprint.setStatus(status);
        sprint.setUpdatedBy(userId);

        Sprint updatedSprint = sprintRepository.save(sprint);
        logger.info("Sprint状态更新成功: {} -> {}", sprintId, status);
        return updatedSprint;
    }

    @Override
    public Sprint addTaskToSprint(UUID sprintId, UUID taskId, UUID userId) {
        logger.info("添加任务到Sprint: {} -> {}, 用户: {}", taskId, sprintId, userId);

        Sprint sprint = sprintRepository.findById(sprintId)
                .orElseThrow(() -> new RuntimeException("Sprint不存在: " + sprintId));

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!canManageSprint(sprintId, userId)) {
            throw new RuntimeException("用户无权限管理此Sprint");
        }

        // 验证任务属于同一项目
        if (!task.getProject().getId().equals(sprint.getProject().getId())) {
            throw new RuntimeException("任务和Sprint必须属于同一项目");
        }

        // 检查Sprint状态
        if (sprint.getStatus() == SprintStatus.COMPLETED) {
            throw new RuntimeException("无法向已完成的Sprint添加任务");
        }

        task.setSprint(sprint);
        task.setUpdatedBy(userId);
        taskRepository.save(task);

        // 重新加载Sprint以获取最新的任务列表
        Sprint updatedSprint = sprintRepository.findById(sprintId).orElse(sprint);
        logger.info("任务添加到Sprint成功: {} -> {}", taskId, sprintId);
        return updatedSprint;
    }

    @Override
    public Sprint removeTaskFromSprint(UUID sprintId, UUID taskId, UUID userId) {
        logger.info("从Sprint移除任务: {} <- {}, 用户: {}", sprintId, taskId, userId);

        Sprint sprint = sprintRepository.findById(sprintId)
                .orElseThrow(() -> new RuntimeException("Sprint不存在: " + sprintId));

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!canManageSprint(sprintId, userId)) {
            throw new RuntimeException("用户无权限管理此Sprint");
        }

        // 验证任务在Sprint中
        if (task.getSprint() == null || !task.getSprint().getId().equals(sprintId)) {
            throw new RuntimeException("任务不在此Sprint中");
        }

        task.setSprint(null);
        task.setUpdatedBy(userId);
        taskRepository.save(task);

        // 重新加载Sprint以获取最新的任务列表
        Sprint updatedSprint = sprintRepository.findById(sprintId).orElse(sprint);
        logger.info("任务从Sprint移除成功: {} <- {}", sprintId, taskId);
        return updatedSprint;
    }

    @Override
    public int batchAddTasksToSprint(UUID sprintId, List<UUID> taskIds, UUID userId) {
        logger.info("批量添加任务到Sprint: {} -> {}, 用户: {}", taskIds.size(), sprintId, userId);

        int addedCount = 0;
        for (UUID taskId : taskIds) {
            try {
                addTaskToSprint(sprintId, taskId, userId);
                addedCount++;
            } catch (Exception e) {
                logger.warn("添加任务到Sprint失败: {}, 错误: {}", taskId, e.getMessage());
            }
        }

        logger.info("批量添加任务到Sprint完成: {}/{}", addedCount, taskIds.size());
        return addedCount;
    }

    @Override
    public int batchRemoveTasksFromSprint(UUID sprintId, List<UUID> taskIds, UUID userId) {
        logger.info("批量从Sprint移除任务: {} <- {}, 用户: {}", sprintId, taskIds.size(), userId);

        int removedCount = 0;
        for (UUID taskId : taskIds) {
            try {
                removeTaskFromSprint(sprintId, taskId, userId);
                removedCount++;
            } catch (Exception e) {
                logger.warn("从Sprint移除任务失败: {}, 错误: {}", taskId, e.getMessage());
            }
        }

        logger.info("批量从Sprint移除任务完成: {}/{}", removedCount, taskIds.size());
        return removedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Sprint> getProjectSprints(UUID projectId, Pageable pageable) {
        return sprintRepository.findByProjectId(projectId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Sprint> getCurrentActiveSprint(UUID projectId) {
        return sprintRepository.findCurrentActiveSprint(projectId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Sprint> getActiveSprintsByProject(UUID projectId) {
        return sprintRepository.findActiveSprintsByProject(projectId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Sprint> searchSprints(UUID projectId, String keyword, SprintStatus status,
                                     LocalDate startDate, LocalDate endDate, Pageable pageable) {
        return sprintRepository.searchSprints(projectId, keyword, status, startDate, endDate, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Sprint> getUserSprints(UUID userId, Pageable pageable) {
        return sprintRepository.findUserSprints(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Sprint> getRecentSprints(UUID projectId, Pageable pageable) {
        return sprintRepository.findRecentSprints(projectId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Sprint> getOverdueSprints() {
        return sprintRepository.findOverdueSprints(LocalDate.now());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Sprint> getUpcomingSprints(int days) {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(days);
        return sprintRepository.findUpcomingSprints(startDate, endDate);
    }

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    @Override
    @Transactional(readOnly = true)
    public boolean hasSprintPermission(UUID sprintId, UUID userId) {
        Sprint sprint = sprintRepository.findById(sprintId).orElse(null);
        if (sprint == null) {
            return false;
        }

        // 项目负责人有权限
        if (userId.equals(sprint.getProject().getOwnerId())) {
            return true;
        }

        // Sprint创建者有权限
        if (userId.equals(sprint.getCreatedBy())) {
            return true;
        }

        // TODO: 检查项目成员权限
        // return projectMemberRepository.isProjectMember(sprint.getProject().getId(), userId);
        return true; // 临时实现
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canManageSprint(UUID sprintId, UUID userId) {
        Sprint sprint = sprintRepository.findById(sprintId).orElse(null);
        if (sprint == null) {
            return false;
        }

        // 项目负责人可以管理
        if (userId.equals(sprint.getProject().getOwnerId())) {
            return true;
        }

        // TODO: 检查项目成员管理权限
        // return projectMemberRepository.hasManagementPermission(sprint.getProject().getId(), userId);
        return true; // 临时实现
    }

    // ============================================================================
    // 其他方法的简化实现（待完善）
    // ============================================================================

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getSprintStatistics(UUID projectId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取Sprint状态统计
        List<Object[]> statusStats = sprintRepository.getSprintStatisticsByProject(projectId);
        Map<String, Long> statusCounts = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusCounts.put(((SprintStatus) stat[0]).name(), (Long) stat[1]);
        }
        statistics.put("statusCounts", statusCounts);
        
        // 获取总Sprint数
        statistics.put("totalSprints", sprintRepository.countByProjectId(projectId));
        
        // 获取平均速度
        Double avgVelocity = sprintRepository.getAverageVelocity(projectId, 5);
        statistics.put("averageVelocity", avgVelocity != null ? avgVelocity : 0.0);
        
        return statistics;
    }

    @Override
    public Map<String, Object> getSprintDetailStatistics(UUID sprintId) {
        // TODO: 实现Sprint详细统计
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getSprintBurndownData(UUID sprintId) {
        // TODO: 实现燃尽图数据
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getSprintVelocityData(UUID projectId, int limit) {
        // TODO: 实现速度图数据
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getSprintTaskDistribution(UUID sprintId) {
        // TODO: 实现任务分布统计
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getSprintUserStatistics(UUID sprintId) {
        // TODO: 实现用户统计
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> calculateSprintCapacity(UUID sprintId) {
        // TODO: 实现Sprint容量计算
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getSprintProgressReport(UUID sprintId) {
        // TODO: 实现进度报告
        return new HashMap<>();
    }

    @Override
    public Sprint copySprint(UUID sprintId, String newName, LocalDate newStartDate,
                            LocalDate newEndDate, boolean copyTasks, UUID userId) {
        // TODO: 实现Sprint复制
        throw new UnsupportedOperationException("Sprint复制功能待实现");
    }

    @Override
    public int autoCompleteOverdueSprints() {
        // TODO: 实现自动完成过期Sprint
        return 0;
    }

    @Override
    public Map<String, Object> generateSprintReport(UUID sprintId) {
        // TODO: 实现Sprint报告生成
        return new HashMap<>();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Task> getSprintTasks(UUID sprintId, Pageable pageable) {
        // TODO: 实现Sprint任务查询
        return Page.empty();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Task> getSprintBacklog(UUID sprintId) {
        // TODO: 实现Sprint待办事项查询
        return new ArrayList<>();
    }

    @Override
    public Task moveTaskToSprint(UUID taskId, UUID targetSprintId, UUID userId) {
        // TODO: 实现任务移动到Sprint
        throw new UnsupportedOperationException("任务移动到Sprint功能待实现");
    }
}
