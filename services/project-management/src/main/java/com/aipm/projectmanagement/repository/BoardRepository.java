package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.Board;
import com.aipm.projectmanagement.entity.Board.BoardType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 看板数据访问接口
 * 
 * 提供看板相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface BoardRepository extends JpaRepository<Board, UUID> {

    /**
     * 根据项目ID查询看板列表
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 看板分页结果
     */
    Page<Board> findByProjectId(UUID projectId, Pageable pageable);

    /**
     * 根据项目ID查询所有看板
     * 
     * @param projectId 项目ID
     * @return 看板列表
     */
    List<Board> findByProjectIdOrderByCreatedAtAsc(UUID projectId);

    /**
     * 根据项目ID和看板类型查询看板
     * 
     * @param projectId 项目ID
     * @param boardType 看板类型
     * @return 看板列表
     */
    List<Board> findByProjectIdAndBoardType(UUID projectId, BoardType boardType);

    /**
     * 查询项目的默认看板
     * 
     * @param projectId 项目ID
     * @return 默认看板
     */
    @Query("SELECT b FROM Board b WHERE b.project.id = :projectId AND b.isDefault = true")
    Optional<Board> findDefaultBoard(@Param("projectId") UUID projectId);

    /**
     * 查询项目的第一个看板（如果没有默认看板）
     * 
     * @param projectId 项目ID
     * @return 第一个看板
     */
    @Query("SELECT b FROM Board b WHERE b.project.id = :projectId ORDER BY b.createdAt ASC LIMIT 1")
    Optional<Board> findFirstBoard(@Param("projectId") UUID projectId);

    /**
     * 检查看板名称是否在项目中唯一
     * 
     * @param projectId 项目ID
     * @param name 看板名称
     * @param excludeId 排除的看板ID
     * @return 是否存在
     */
    @Query("SELECT COUNT(b) > 0 FROM Board b WHERE b.project.id = :projectId " +
           "AND b.name = :name AND (:excludeId IS NULL OR b.id != :excludeId)")
    boolean existsByProjectIdAndNameAndIdNot(@Param("projectId") UUID projectId,
                                            @Param("name") String name,
                                            @Param("excludeId") UUID excludeId);

    /**
     * 统计项目看板数量
     * 
     * @param projectId 项目ID
     * @return 看板数量
     */
    long countByProjectId(UUID projectId);

    /**
     * 统计项目各类型看板数量
     * 
     * @param projectId 项目ID
     * @return 类型统计结果
     */
    @Query("SELECT b.boardType, COUNT(b) FROM Board b WHERE b.project.id = :projectId GROUP BY b.boardType")
    List<Object[]> getBoardStatisticsByProject(@Param("projectId") UUID projectId);

    /**
     * 搜索看板
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param boardType 看板类型
     * @param pageable 分页参数
     * @return 看板分页结果
     */
    @Query("SELECT b FROM Board b WHERE b.project.id = :projectId " +
           "AND (:keyword IS NULL OR b.name LIKE %:keyword% OR b.description LIKE %:keyword%) " +
           "AND (:boardType IS NULL OR b.boardType = :boardType)")
    Page<Board> searchBoards(@Param("projectId") UUID projectId,
                            @Param("keyword") String keyword,
                            @Param("boardType") BoardType boardType,
                            Pageable pageable);

    /**
     * 查询用户创建的看板
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 看板分页结果
     */
    Page<Board> findByCreatedBy(UUID userId, Pageable pageable);

    /**
     * 查询最近更新的看板
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 看板分页结果
     */
    @Query("SELECT b FROM Board b WHERE b.project.id = :projectId ORDER BY b.updatedAt DESC")
    Page<Board> findRecentBoards(@Param("projectId") UUID projectId, Pageable pageable);

    /**
     * 查询看板的任务统计
     * 
     * @param boardId 看板ID
     * @return 任务统计结果
     */
    @Query("SELECT bc.id, bc.name, COUNT(t) " +
           "FROM BoardColumn bc LEFT JOIN Task t ON t.status = bc.taskStatus " +
           "AND t.project.id = bc.board.project.id " +
           "WHERE bc.board.id = :boardId " +
           "GROUP BY bc.id, bc.name ORDER BY bc.sortOrder")
    List<Object[]> getBoardTaskStatistics(@Param("boardId") UUID boardId);

    /**
     * 查询看板列的任务分布
     * 
     * @param boardId 看板ID
     * @return 任务分布统计
     */
    @Query("SELECT bc.name, bc.taskStatus, COUNT(t), SUM(COALESCE(t.storyPoints, 0)) " +
           "FROM BoardColumn bc LEFT JOIN Task t ON t.status = bc.taskStatus " +
           "AND t.project.id = bc.board.project.id " +
           "WHERE bc.board.id = :boardId " +
           "GROUP BY bc.id, bc.name, bc.taskStatus ORDER BY bc.sortOrder")
    List<Object[]> getBoardColumnDistribution(@Param("boardId") UUID boardId);

    /**
     * 查询看板的WIP限制违规情况
     * 
     * @param boardId 看板ID
     * @return WIP违规统计
     */
    @Query("SELECT bc.id, bc.name, bc.wipLimit, COUNT(t) " +
           "FROM BoardColumn bc LEFT JOIN Task t ON t.status = bc.taskStatus " +
           "AND t.project.id = bc.board.project.id " +
           "WHERE bc.board.id = :boardId AND bc.wipLimit IS NOT NULL " +
           "GROUP BY bc.id, bc.name, bc.wipLimit " +
           "HAVING COUNT(t) > bc.wipLimit " +
           "ORDER BY bc.sortOrder")
    List<Object[]> getWipViolations(@Param("boardId") UUID boardId);
}
