package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.Sprint;
import com.aipm.projectmanagement.entity.Sprint.SprintStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Sprint数据访问接口
 * 
 * 提供Sprint相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface SprintRepository extends JpaRepository<Sprint, UUID> {

    /**
     * 根据项目ID查询Sprint列表
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return Sprint分页结果
     */
    Page<Sprint> findByProjectId(UUID projectId, Pageable pageable);

    /**
     * 根据项目ID和状态查询Sprint列表
     * 
     * @param projectId 项目ID
     * @param status Sprint状态
     * @param pageable 分页参数
     * @return Sprint分页结果
     */
    Page<Sprint> findByProjectIdAndStatus(UUID projectId, SprintStatus status, Pageable pageable);

    /**
     * 查询项目的活跃Sprint
     * 
     * @param projectId 项目ID
     * @return 活跃Sprint列表
     */
    @Query("SELECT s FROM Sprint s WHERE s.project.id = :projectId AND s.status = 'ACTIVE'")
    List<Sprint> findActiveSprintsByProject(@Param("projectId") UUID projectId);

    /**
     * 查询项目当前活跃的Sprint
     * 
     * @param projectId 项目ID
     * @return 当前活跃Sprint
     */
    @Query("SELECT s FROM Sprint s WHERE s.project.id = :projectId AND s.status = 'ACTIVE' ORDER BY s.startDate DESC")
    Optional<Sprint> findCurrentActiveSprint(@Param("projectId") UUID projectId);

    /**
     * 查询指定日期范围内的Sprint
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return Sprint列表
     */
    @Query("SELECT s FROM Sprint s WHERE s.project.id = :projectId " +
           "AND ((s.startDate BETWEEN :startDate AND :endDate) " +
           "OR (s.endDate BETWEEN :startDate AND :endDate) " +
           "OR (s.startDate <= :startDate AND s.endDate >= :endDate))")
    List<Sprint> findSprintsInDateRange(@Param("projectId") UUID projectId,
                                       @Param("startDate") LocalDate startDate,
                                       @Param("endDate") LocalDate endDate);

    /**
     * 查询过期的Sprint
     * 
     * @param currentDate 当前日期
     * @return 过期Sprint列表
     */
    @Query("SELECT s FROM Sprint s WHERE s.status = 'ACTIVE' AND s.endDate < :currentDate")
    List<Sprint> findOverdueSprints(@Param("currentDate") LocalDate currentDate);

    /**
     * 查询即将开始的Sprint
     * 
     * @param startDate 开始日期
     * @param days 天数
     * @return 即将开始的Sprint列表
     */
    @Query("SELECT s FROM Sprint s WHERE s.status = 'PLANNING' " +
           "AND s.startDate BETWEEN :startDate AND :endDate")
    List<Sprint> findUpcomingSprints(@Param("startDate") LocalDate startDate,
                                    @Param("endDate") LocalDate endDate);

    /**
     * 查询项目最近完成的Sprint
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 最近完成的Sprint
     */
    @Query("SELECT s FROM Sprint s WHERE s.project.id = :projectId AND s.status = 'COMPLETED' " +
           "ORDER BY s.endDate DESC")
    Page<Sprint> findRecentCompletedSprints(@Param("projectId") UUID projectId, Pageable pageable);

    /**
     * 统计项目Sprint数量
     * 
     * @param projectId 项目ID
     * @return Sprint数量
     */
    long countByProjectId(UUID projectId);

    /**
     * 统计项目各状态Sprint数量
     * 
     * @param projectId 项目ID
     * @return 状态统计结果
     */
    @Query("SELECT s.status, COUNT(s) FROM Sprint s WHERE s.project.id = :projectId GROUP BY s.status")
    List<Object[]> getSprintStatisticsByProject(@Param("projectId") UUID projectId);

    /**
     * 查询Sprint的平均速度
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 平均速度
     */
    @Query("SELECT AVG(s.completedStoryPoints) FROM Sprint s " +
           "WHERE s.project.id = :projectId AND s.status = 'COMPLETED' " +
           "AND s.completedStoryPoints IS NOT NULL " +
           "ORDER BY s.endDate DESC LIMIT :limit")
    Double getAverageVelocity(@Param("projectId") UUID projectId, @Param("limit") int limit);

    /**
     * 查询项目Sprint历史速度
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 速度历史列表
     */
    @Query("SELECT s.name, s.completedStoryPoints, s.endDate FROM Sprint s " +
           "WHERE s.project.id = :projectId AND s.status = 'COMPLETED' " +
           "AND s.completedStoryPoints IS NOT NULL " +
           "ORDER BY s.endDate DESC LIMIT :limit")
    List<Object[]> getVelocityHistory(@Param("projectId") UUID projectId, @Param("limit") int limit);

    /**
     * 检查Sprint名称是否在项目中唯一
     * 
     * @param projectId 项目ID
     * @param name Sprint名称
     * @param excludeId 排除的Sprint ID
     * @return 是否存在
     */
    @Query("SELECT COUNT(s) > 0 FROM Sprint s WHERE s.project.id = :projectId " +
           "AND s.name = :name AND (:excludeId IS NULL OR s.id != :excludeId)")
    boolean existsByProjectIdAndNameAndIdNot(@Param("projectId") UUID projectId,
                                            @Param("name") String name,
                                            @Param("excludeId") UUID excludeId);

    /**
     * 查询Sprint的燃尽图数据
     * 
     * @param sprintId Sprint ID
     * @return 燃尽图数据
     */
    @Query("SELECT DATE(t.updatedAt) as date, " +
           "SUM(CASE WHEN t.status IN ('DONE') THEN COALESCE(t.storyPoints, 0) ELSE 0 END) as completedPoints " +
           "FROM Task t WHERE t.sprint.id = :sprintId " +
           "GROUP BY DATE(t.updatedAt) ORDER BY DATE(t.updatedAt)")
    List<Object[]> getBurndownData(@Param("sprintId") UUID sprintId);

    /**
     * 查询Sprint任务分布统计
     * 
     * @param sprintId Sprint ID
     * @return 任务分布统计
     */
    @Query("SELECT t.status, COUNT(t), SUM(COALESCE(t.storyPoints, 0)) " +
           "FROM Task t WHERE t.sprint.id = :sprintId GROUP BY t.status")
    List<Object[]> getSprintTaskDistribution(@Param("sprintId") UUID sprintId);

    /**
     * 查询用户在Sprint中的任务统计
     * 
     * @param sprintId Sprint ID
     * @return 用户任务统计
     */
    @Query("SELECT t.assigneeId, COUNT(t), SUM(COALESCE(t.storyPoints, 0)), " +
           "SUM(CASE WHEN t.status IN ('DONE') THEN 1 ELSE 0 END) " +
           "FROM Task t WHERE t.sprint.id = :sprintId AND t.assigneeId IS NOT NULL " +
           "GROUP BY t.assigneeId")
    List<Object[]> getSprintUserStatistics(@Param("sprintId") UUID sprintId);

    /**
     * 搜索Sprint
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param status 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return Sprint分页结果
     */
    @Query("SELECT s FROM Sprint s WHERE s.project.id = :projectId " +
           "AND (:keyword IS NULL OR s.name LIKE %:keyword% OR s.description LIKE %:keyword%) " +
           "AND (:status IS NULL OR s.status = :status) " +
           "AND (:startDate IS NULL OR s.startDate >= :startDate) " +
           "AND (:endDate IS NULL OR s.endDate <= :endDate)")
    Page<Sprint> searchSprints(@Param("projectId") UUID projectId,
                              @Param("keyword") String keyword,
                              @Param("status") SprintStatus status,
                              @Param("startDate") LocalDate startDate,
                              @Param("endDate") LocalDate endDate,
                              Pageable pageable);

    /**
     * 查询用户参与的Sprint
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return Sprint分页结果
     */
    @Query("SELECT DISTINCT s FROM Sprint s JOIN s.tasks t " +
           "WHERE t.assigneeId = :userId OR t.reporterId = :userId " +
           "ORDER BY s.updatedAt DESC")
    Page<Sprint> findUserSprints(@Param("userId") UUID userId, Pageable pageable);

    /**
     * 查询最近的Sprint
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 最近Sprint分页结果
     */
    @Query("SELECT s FROM Sprint s WHERE s.project.id = :projectId " +
           "ORDER BY s.updatedAt DESC")
    Page<Sprint> findRecentSprints(@Param("projectId") UUID projectId, Pageable pageable);
}
