package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Sprint实体类
 * 
 * 表示敏捷开发中的Sprint冲刺
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "sprints", indexes = {
    @Index(name = "idx_sprint_project", columnList = "project_id"),
    @Index(name = "idx_sprint_status", columnList = "status"),
    @Index(name = "idx_sprint_dates", columnList = "start_date, end_date"),
    @Index(name = "idx_sprint_created", columnList = "created_at")
})
public class Sprint {

    /**
     * Sprint状态枚举
     */
    public enum SprintStatus {
        PLANNING("规划中", "#6B7280", false),
        ACTIVE("进行中", "#10B981", true),
        COMPLETED("已完成", "#3B82F6", false),
        CANCELLED("已取消", "#EF4444", false);

        private final String displayName;
        private final String color;
        private final boolean active;

        SprintStatus(String displayName, String color, boolean active) {
            this.displayName = displayName;
            this.color = color;
            this.active = active;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getColor() {
            return color;
        }

        public boolean isActive() {
            return active;
        }

        /**
         * 检查是否可以转换到目标状态
         */
        public boolean canTransitionTo(SprintStatus targetStatus) {
            return switch (this) {
                case PLANNING -> targetStatus == ACTIVE || targetStatus == CANCELLED;
                case ACTIVE -> targetStatus == COMPLETED || targetStatus == CANCELLED;
                case COMPLETED, CANCELLED -> false;
            };
        }
    }

    /**
     * Sprint唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * Sprint名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * Sprint描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 所属项目
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    /**
     * Sprint状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SprintStatus status = SprintStatus.PLANNING;

    /**
     * Sprint开始日期
     */
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    /**
     * Sprint结束日期
     */
    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;

    /**
     * Sprint目标
     */
    @Column(name = "goal", columnDefinition = "TEXT")
    private String goal;

    /**
     * 计划故事点
     */
    @Column(name = "planned_story_points")
    private Integer plannedStoryPoints;

    /**
     * 完成故事点
     */
    @Column(name = "completed_story_points")
    private Integer completedStoryPoints;

    /**
     * Sprint任务列表
     */
    @OneToMany(mappedBy = "sprint", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Task> tasks = new ArrayList<>();

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by", nullable = false)
    private UUID createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by", nullable = false)
    private UUID updatedBy;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public Sprint() {
    }

    /**
     * 基础构造函数
     * 
     * @param name Sprint名称
     * @param project 所属项目
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    public Sprint(String name, Project project, LocalDate startDate, LocalDate endDate) {
        this.name = name;
        this.project = project;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 开始Sprint
     */
    public void start() {
        if (status != SprintStatus.PLANNING) {
            throw new IllegalStateException("只有规划中的Sprint才能开始");
        }
        this.status = SprintStatus.ACTIVE;
    }

    /**
     * 完成Sprint
     */
    public void complete() {
        if (status != SprintStatus.ACTIVE) {
            throw new IllegalStateException("只有进行中的Sprint才能完成");
        }
        this.status = SprintStatus.COMPLETED;
        
        // 计算完成的故事点
        this.completedStoryPoints = tasks.stream()
            .filter(task -> task.getStatus().isCompleted())
            .mapToInt(task -> task.getStoryPoints() != null ? task.getStoryPoints() : 0)
            .sum();
    }

    /**
     * 取消Sprint
     */
    public void cancel() {
        if (status == SprintStatus.COMPLETED) {
            throw new IllegalStateException("已完成的Sprint不能取消");
        }
        this.status = SprintStatus.CANCELLED;
    }

    /**
     * 检查Sprint是否处于活跃状态
     */
    public boolean isActive() {
        return status.isActive();
    }

    /**
     * 检查Sprint是否已完成
     */
    public boolean isCompleted() {
        return status == SprintStatus.COMPLETED;
    }

    /**
     * 获取Sprint持续天数
     */
    public long getDurationDays() {
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }

    /**
     * 获取剩余天数
     */
    public long getRemainingDays() {
        if (status == SprintStatus.COMPLETED || status == SprintStatus.CANCELLED) {
            return 0;
        }
        
        LocalDate today = LocalDate.now();
        if (today.isAfter(endDate)) {
            return 0;
        }
        
        return java.time.temporal.ChronoUnit.DAYS.between(today, endDate) + 1;
    }

    /**
     * 检查Sprint是否过期
     */
    public boolean isOverdue() {
        return status == SprintStatus.ACTIVE && LocalDate.now().isAfter(endDate);
    }

    /**
     * 获取Sprint进度百分比
     */
    public double getProgressPercentage() {
        if (plannedStoryPoints == null || plannedStoryPoints == 0) {
            return 0.0;
        }
        
        int completed = completedStoryPoints != null ? completedStoryPoints : 0;
        return (double) completed / plannedStoryPoints * 100;
    }

    /**
     * 获取任务完成率
     */
    public double getTaskCompletionRate() {
        if (tasks.isEmpty()) {
            return 0.0;
        }
        
        long completedTasks = tasks.stream()
            .filter(task -> task.getStatus().isCompleted())
            .count();
        
        return (double) completedTasks / tasks.size() * 100;
    }

    /**
     * 获取Sprint速度（完成的故事点）
     */
    public int getVelocity() {
        return completedStoryPoints != null ? completedStoryPoints : 0;
    }

    /**
     * 添加任务到Sprint
     */
    public void addTask(Task task) {
        if (task != null && !tasks.contains(task)) {
            tasks.add(task);
            task.setSprint(this);
            
            // 重新计算计划故事点
            recalculatePlannedStoryPoints();
        }
    }

    /**
     * 从Sprint移除任务
     */
    public void removeTask(Task task) {
        if (task != null && tasks.contains(task)) {
            tasks.remove(task);
            task.setSprint(null);
            
            // 重新计算计划故事点
            recalculatePlannedStoryPoints();
        }
    }

    /**
     * 重新计算计划故事点
     */
    private void recalculatePlannedStoryPoints() {
        this.plannedStoryPoints = tasks.stream()
            .mapToInt(task -> task.getStoryPoints() != null ? task.getStoryPoints() : 0)
            .sum();
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public SprintStatus getStatus() {
        return status;
    }

    public void setStatus(SprintStatus status) {
        this.status = status;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getGoal() {
        return goal;
    }

    public void setGoal(String goal) {
        this.goal = goal;
    }

    public Integer getPlannedStoryPoints() {
        return plannedStoryPoints;
    }

    public void setPlannedStoryPoints(Integer plannedStoryPoints) {
        this.plannedStoryPoints = plannedStoryPoints;
    }

    public Integer getCompletedStoryPoints() {
        return completedStoryPoints;
    }

    public void setCompletedStoryPoints(Integer completedStoryPoints) {
        this.completedStoryPoints = completedStoryPoints;
    }

    public List<Task> getTasks() {
        return tasks;
    }

    public void setTasks(List<Task> tasks) {
        this.tasks = tasks;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public UUID getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Sprint sprint)) return false;
        return id != null && id.equals(sprint.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "Sprint{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status=" + status +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", plannedStoryPoints=" + plannedStoryPoints +
                ", completedStoryPoints=" + completedStoryPoints +
                '}';
    }
}
