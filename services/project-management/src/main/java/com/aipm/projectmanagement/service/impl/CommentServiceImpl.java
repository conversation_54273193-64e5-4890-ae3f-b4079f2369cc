package com.aipm.projectmanagement.service.impl;

import com.aipm.projectmanagement.entity.Comment;
import com.aipm.projectmanagement.entity.Comment.TargetType;
import com.aipm.projectmanagement.repository.CommentRepository;
import com.aipm.projectmanagement.service.CommentService;
import com.aipm.projectmanagement.service.ActivityLogService;
import com.aipm.projectmanagement.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 评论服务实现类
 * 
 * 实现评论管理相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Service
@Transactional
public class CommentServiceImpl implements CommentService {

    private static final Logger logger = LoggerFactory.getLogger(CommentServiceImpl.class);

    @Autowired
    private CommentRepository commentRepository;

    @Autowired
    private ActivityLogService activityLogService;

    @Autowired
    private NotificationService notificationService;

    @Override
    public Comment createComment(Comment comment, UUID authorId) {
        logger.info("创建评论: 目标类型={}, 目标ID={}, 作者={}", 
                   comment.getTargetType(), comment.getTargetId(), authorId);

        // 设置作者信息
        comment.setAuthorId(authorId);

        // 保存评论
        Comment savedComment = commentRepository.save(comment);

        // 记录活动日志
        try {
            activityLogService.logCommentActivity(
                getProjectIdFromTarget(comment.getTargetType(), comment.getTargetId()),
                authorId,
                com.aipm.projectmanagement.entity.ActivityLog.ActionType.COMMENT_ADDED,
                savedComment.getId(),
                "添加了评论"
            );
        } catch (Exception e) {
            logger.warn("记录评论活动日志失败: {}", e.getMessage());
        }

        // 发送通知（异步）
        try {
            sendCommentNotifications(savedComment);
        } catch (Exception e) {
            logger.warn("发送评论通知失败: {}", e.getMessage());
        }

        logger.info("评论创建成功: {}", savedComment.getId());
        return savedComment;
    }

    @Override
    public Comment createReply(UUID parentCommentId, String content, UUID authorId) {
        logger.info("创建回复: 父评论={}, 作者={}", parentCommentId, authorId);

        Comment parentComment = commentRepository.findById(parentCommentId)
                .orElseThrow(() -> new RuntimeException("父评论不存在: " + parentCommentId));

        Comment reply = new Comment();
        reply.setContent(content);
        reply.setTargetType(parentComment.getTargetType());
        reply.setTargetId(parentComment.getTargetId());
        reply.setAuthorId(authorId);
        reply.setParentComment(parentComment);

        Comment savedReply = commentRepository.save(reply);

        // 记录活动日志
        try {
            activityLogService.logCommentActivity(
                getProjectIdFromTarget(reply.getTargetType(), reply.getTargetId()),
                authorId,
                com.aipm.projectmanagement.entity.ActivityLog.ActionType.COMMENT_REPLIED,
                savedReply.getId(),
                "回复了评论"
            );
        } catch (Exception e) {
            logger.warn("记录回复活动日志失败: {}", e.getMessage());
        }

        logger.info("回复创建成功: {}", savedReply.getId());
        return savedReply;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Comment> getCommentById(UUID commentId) {
        return commentRepository.findById(commentId);
    }

    @Override
    public Comment updateComment(UUID commentId, String content, UUID userId) {
        logger.info("更新评论: {}, 用户: {}", commentId, userId);

        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RuntimeException("评论不存在: " + commentId));

        // 验证权限
        if (!canEditComment(commentId, userId)) {
            throw new RuntimeException("用户无权限编辑此评论");
        }

        String oldContent = comment.getContent();
        comment.setContent(content);
        comment.markAsEdited();

        Comment updatedComment = commentRepository.save(comment);

        // 记录活动日志
        try {
            activityLogService.logActivityWithChanges(
                getProjectIdFromTarget(comment.getTargetType(), comment.getTargetId()),
                userId,
                com.aipm.projectmanagement.entity.ActivityLog.ActionType.COMMENT_UPDATED,
                com.aipm.projectmanagement.entity.ActivityLog.TargetType.COMMENT,
                commentId,
                null,
                "更新了评论",
                oldContent,
                content
            );
        } catch (Exception e) {
            logger.warn("记录评论更新活动日志失败: {}", e.getMessage());
        }

        logger.info("评论更新成功: {}", commentId);
        return updatedComment;
    }

    @Override
    public void deleteComment(UUID commentId, UUID userId) {
        logger.info("删除评论: {}, 用户: {}", commentId, userId);

        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RuntimeException("评论不存在: " + commentId));

        // 验证权限
        if (!canDeleteComment(commentId, userId)) {
            throw new RuntimeException("用户无权限删除此评论");
        }

        // 软删除
        comment.softDelete();
        commentRepository.save(comment);

        // 记录活动日志
        try {
            activityLogService.logCommentActivity(
                getProjectIdFromTarget(comment.getTargetType(), comment.getTargetId()),
                userId,
                com.aipm.projectmanagement.entity.ActivityLog.ActionType.COMMENT_DELETED,
                commentId,
                "删除了评论"
            );
        } catch (Exception e) {
            logger.warn("记录评论删除活动日志失败: {}", e.getMessage());
        }

        logger.info("评论删除成功: {}", commentId);
    }

    @Override
    public Comment restoreComment(UUID commentId, UUID userId) {
        logger.info("恢复评论: {}, 用户: {}", commentId, userId);

        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RuntimeException("评论不存在: " + commentId));

        // 验证权限
        if (!canEditComment(commentId, userId)) {
            throw new RuntimeException("用户无权限恢复此评论");
        }

        comment.restore();
        Comment restoredComment = commentRepository.save(comment);

        logger.info("评论恢复成功: {}", commentId);
        return restoredComment;
    }

    @Override
    public Comment likeComment(UUID commentId, UUID userId) {
        logger.info("点赞评论: {}, 用户: {}", commentId, userId);

        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RuntimeException("评论不存在: " + commentId));

        // TODO: 检查用户是否已经点赞过
        comment.incrementLikeCount();
        Comment likedComment = commentRepository.save(comment);

        logger.info("评论点赞成功: {}", commentId);
        return likedComment;
    }

    @Override
    public Comment unlikeComment(UUID commentId, UUID userId) {
        logger.info("取消点赞评论: {}, 用户: {}", commentId, userId);

        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RuntimeException("评论不存在: " + commentId));

        // TODO: 检查用户是否已经点赞过
        comment.decrementLikeCount();
        Comment unlikedComment = commentRepository.save(comment);

        logger.info("评论取消点赞成功: {}", commentId);
        return unlikedComment;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Comment> getCommentsByTarget(TargetType targetType, UUID targetId, Pageable pageable) {
        return commentRepository.findByTargetTypeAndTargetIdAndIsDeletedFalse(targetType, targetId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Comment> getTopLevelComments(TargetType targetType, UUID targetId, Pageable pageable) {
        return commentRepository.findTopLevelComments(targetType, targetId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Comment> getCommentReplies(UUID parentCommentId, Pageable pageable) {
        return commentRepository.findByParentCommentIdAndIsDeletedFalseOrderByCreatedAtAsc(parentCommentId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Comment> getUserComments(UUID authorId, Pageable pageable) {
        return commentRepository.findByAuthorIdAndIsDeletedFalseOrderByCreatedAtDesc(authorId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Comment> searchComments(TargetType targetType, UUID targetId, String keyword, Pageable pageable) {
        return commentRepository.searchComments(targetType, targetId, keyword, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Comment> getPopularComments(TargetType targetType, UUID targetId, Pageable pageable) {
        return commentRepository.findPopularComments(targetType, targetId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Comment> getRecentComments(TargetType targetType, UUID targetId, int limit) {
        return commentRepository.findRecentComments(targetType, targetId, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Comment> getCommentThread(UUID rootCommentId) {
        return commentRepository.findCommentThread(rootCommentId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countCommentsByTarget(TargetType targetType, UUID targetId) {
        return commentRepository.countByTargetTypeAndTargetIdAndIsDeletedFalse(targetType, targetId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countTopLevelComments(TargetType targetType, UUID targetId) {
        return commentRepository.countTopLevelComments(targetType, targetId);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getCommentStatistics(TargetType targetType, UUID targetId) {
        Map<String, Object> statistics = new HashMap<>();
        
        long totalComments = countCommentsByTarget(targetType, targetId);
        long topLevelComments = countTopLevelComments(targetType, targetId);
        
        statistics.put("totalComments", totalComments);
        statistics.put("topLevelComments", topLevelComments);
        statistics.put("replyComments", totalComments - topLevelComments);
        
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserCommentStatistics(UUID authorId) {
        Map<String, Object> statistics = new HashMap<>();
        
        List<Object[]> stats = commentRepository.getUserCommentStatistics(authorId);
        Map<String, Long> typeStats = new HashMap<>();
        
        for (Object[] stat : stats) {
            typeStats.put(((TargetType) stat[0]).name(), (Long) stat[1]);
        }
        
        statistics.put("commentsByType", typeStats);
        return statistics;
    }

    // ============================================================================
    // 其他方法的简化实现
    // ============================================================================

    @Override
    public Map<String, Object> getCommentActivity(TargetType targetType, UUID targetId, int days) {
        // TODO: 实现评论活跃度统计
        return new HashMap<>();
    }

    @Override
    public List<Map<String, Object>> getTopCommentAuthors(TargetType targetType, UUID targetId, int limit) {
        // TODO: 实现评论作者排行
        return new ArrayList<>();
    }

    @Override
    public int batchDeleteComments(List<UUID> commentIds, UUID userId) {
        // TODO: 实现批量删除
        return 0;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canEditComment(UUID commentId, UUID userId) {
        Comment comment = commentRepository.findById(commentId).orElse(null);
        if (comment == null) {
            return false;
        }
        
        // 评论作者可以编辑
        if (userId.equals(comment.getAuthorId())) {
            return true;
        }
        
        // TODO: 检查项目管理员权限
        return false;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canDeleteComment(UUID commentId, UUID userId) {
        return canEditComment(commentId, userId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canViewComment(UUID commentId, UUID userId) {
        // TODO: 实现查看权限检查
        return true;
    }

    @Override
    public void mentionUsers(UUID commentId, List<UUID> mentionedUserIds, UUID userId) {
        // TODO: 实现用户提及功能
    }

    @Override
    public Page<Comment> getMentionedComments(UUID userId, Pageable pageable) {
        // TODO: 实现提及评论查询
        return Page.empty();
    }

    @Override
    public int cleanupDeletedComments(int days) {
        // TODO: 实现已删除评论清理
        return 0;
    }

    @Override
    public String exportComments(TargetType targetType, UUID targetId) {
        // TODO: 实现评论导出
        return "";
    }

    @Override
    public Map<String, Object> generateCommentSummary(TargetType targetType, UUID targetId) {
        // TODO: 实现评论摘要生成
        return new HashMap<>();
    }

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    /**
     * 根据目标类型和ID获取项目ID
     */
    private UUID getProjectIdFromTarget(TargetType targetType, UUID targetId) {
        // TODO: 根据不同的目标类型查询对应的项目ID
        // 这里需要根据实际的业务逻辑来实现
        return targetId; // 临时实现
    }

    /**
     * 发送评论相关通知
     */
    private void sendCommentNotifications(Comment comment) {
        // TODO: 实现评论通知发送逻辑
        // 1. 通知目标对象的相关用户
        // 2. 通知被提及的用户
        // 3. 通知父评论的作者（如果是回复）
    }
}
