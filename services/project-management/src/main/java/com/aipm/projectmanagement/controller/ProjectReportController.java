package com.aipm.projectmanagement.controller;

import com.aipm.projectmanagement.entity.ProjectReport;
import com.aipm.projectmanagement.entity.ProjectReport.ReportFormat;
import com.aipm.projectmanagement.entity.ProjectReport.ReportStatus;
import com.aipm.projectmanagement.entity.ProjectReport.ReportType;
import com.aipm.projectmanagement.service.ProjectReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 项目报表管理控制器
 * 
 * 提供项目报表相关的REST API接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
@RestController
@RequestMapping("/api/v1/reports")
@Tag(name = "项目报表管理", description = "项目报表生成和管理相关的API接口")
public class ProjectReportController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectReportController.class);

    @Autowired
    private ProjectReportService projectReportService;

    @Operation(summary = "创建报表", description = "创建新的项目报表")
    @PostMapping
    public ResponseEntity<ProjectReport> createReport(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "报表类型") @RequestParam ReportType reportType,
            @Parameter(description = "报表标题") @RequestParam String title,
            @Parameter(description = "报表描述") @RequestParam(required = false) String description,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime periodStart,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime periodEnd,
            @Parameter(description = "报表格式") @RequestParam(defaultValue = "JSON") ReportFormat format,
            @Parameter(description = "生成者ID") @RequestParam UUID generatedBy) {
        
        logger.info("创建报表请求: 项目={}, 类型={}, 标题={}", projectId, reportType, title);

        ProjectReport report = projectReportService.createReport(projectId, reportType, title, description,
                                                               periodStart, periodEnd, format, generatedBy);
        return ResponseEntity.ok(report);
    }

    @Operation(summary = "生成日报", description = "生成项目日报")
    @PostMapping("/daily")
    public ResponseEntity<ProjectReport> generateDailyReport(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime date,
            @Parameter(description = "生成者ID") @RequestParam UUID generatedBy) {
        
        logger.info("生成日报请求: 项目={}, 日期={}", projectId, date);

        ProjectReport report = projectReportService.generateDailyReport(projectId, date, generatedBy);
        return ResponseEntity.ok(report);
    }

    @Operation(summary = "生成周报", description = "生成项目周报")
    @PostMapping("/weekly")
    public ResponseEntity<ProjectReport> generateWeeklyReport(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "周开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime weekStart,
            @Parameter(description = "生成者ID") @RequestParam UUID generatedBy) {
        
        logger.info("生成周报请求: 项目={}, 周开始={}", projectId, weekStart);

        ProjectReport report = projectReportService.generateWeeklyReport(projectId, weekStart, generatedBy);
        return ResponseEntity.ok(report);
    }

    @Operation(summary = "生成月报", description = "生成项目月报")
    @PostMapping("/monthly")
    public ResponseEntity<ProjectReport> generateMonthlyReport(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "月开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime monthStart,
            @Parameter(description = "生成者ID") @RequestParam UUID generatedBy) {
        
        logger.info("生成月报请求: 项目={}, 月开始={}", projectId, monthStart);

        ProjectReport report = projectReportService.generateMonthlyReport(projectId, monthStart, generatedBy);
        return ResponseEntity.ok(report);
    }

    @Operation(summary = "生成Sprint报表", description = "生成Sprint周期报表")
    @PostMapping("/sprint")
    public ResponseEntity<ProjectReport> generateSprintReport(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "Sprint ID") @RequestParam UUID sprintId,
            @Parameter(description = "Sprint名称") @RequestParam String sprintName,
            @Parameter(description = "Sprint开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime sprintStart,
            @Parameter(description = "Sprint结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime sprintEnd,
            @Parameter(description = "生成者ID") @RequestParam UUID generatedBy) {
        
        logger.info("生成Sprint报表请求: 项目={}, Sprint={}", projectId, sprintId);

        ProjectReport report = projectReportService.generateSprintReport(projectId, sprintId, sprintName,
                                                                        sprintStart, sprintEnd, generatedBy);
        return ResponseEntity.ok(report);
    }

    @Operation(summary = "生成团队绩效报表", description = "生成团队绩效分析报表")
    @PostMapping("/team-performance")
    public ResponseEntity<ProjectReport> generateTeamPerformanceReport(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime periodStart,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime periodEnd,
            @Parameter(description = "生成者ID") @RequestParam UUID generatedBy) {
        
        logger.info("生成团队绩效报表请求: 项目={}", projectId);

        ProjectReport report = projectReportService.generateTeamPerformanceReport(projectId, periodStart, periodEnd, generatedBy);
        return ResponseEntity.ok(report);
    }

    @Operation(summary = "生成燃尽图报表", description = "生成Sprint燃尽图报表")
    @PostMapping("/burndown")
    public ResponseEntity<ProjectReport> generateBurndownReport(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "Sprint ID") @RequestParam UUID sprintId,
            @Parameter(description = "生成者ID") @RequestParam UUID generatedBy) {
        
        logger.info("生成燃尽图报表请求: 项目={}, Sprint={}", projectId, sprintId);

        ProjectReport report = projectReportService.generateBurndownReport(projectId, sprintId, generatedBy);
        return ResponseEntity.ok(report);
    }

    @Operation(summary = "获取报表详情", description = "根据ID获取报表详细信息")
    @GetMapping("/{reportId}")
    public ResponseEntity<ProjectReport> getReport(
            @Parameter(description = "报表ID") @PathVariable UUID reportId) {
        
        return projectReportService.getReportById(reportId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "预览报表", description = "预览报表内容")
    @GetMapping("/{reportId}/preview")
    public ResponseEntity<Map<String, Object>> previewReport(
            @Parameter(description = "报表ID") @PathVariable UUID reportId,
            @Parameter(description = "用户ID") @RequestParam UUID userId) {
        
        Map<String, Object> preview = projectReportService.previewReport(reportId, userId);
        return ResponseEntity.ok(preview);
    }

    @Operation(summary = "下载报表", description = "下载报表文件")
    @GetMapping("/{reportId}/download")
    public ResponseEntity<String> downloadReport(
            @Parameter(description = "报表ID") @PathVariable UUID reportId,
            @Parameter(description = "用户ID") @RequestParam UUID userId) {
        
        // TODO: 实现文件下载
        logger.info("下载报表请求: 报表={}, 用户={}", reportId, userId);
        return ResponseEntity.ok("报表下载功能待实现");
    }

    @Operation(summary = "删除报表", description = "删除指定报表")
    @DeleteMapping("/{reportId}")
    public ResponseEntity<Void> deleteReport(
            @Parameter(description = "报表ID") @PathVariable UUID reportId,
            @Parameter(description = "用户ID") @RequestParam UUID userId) {
        
        logger.info("删除报表请求: 报表={}, 用户={}", reportId, userId);

        projectReportService.deleteReport(reportId, userId);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "获取项目报表列表", description = "获取指定项目的所有报表")
    @GetMapping("/project/{projectId}")
    public ResponseEntity<Page<ProjectReport>> getProjectReports(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProjectReport> reports = projectReportService.getProjectReports(projectId, pageable);
        return ResponseEntity.ok(reports);
    }

    @Operation(summary = "按类型获取报表", description = "获取指定项目和类型的报表")
    @GetMapping("/project/{projectId}/type/{reportType}")
    public ResponseEntity<Page<ProjectReport>> getReportsByType(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "报表类型") @PathVariable ReportType reportType,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProjectReport> reports = projectReportService.getReportsByType(projectId, reportType, pageable);
        return ResponseEntity.ok(reports);
    }

    @Operation(summary = "按状态获取报表", description = "获取指定项目和状态的报表")
    @GetMapping("/project/{projectId}/status/{status}")
    public ResponseEntity<Page<ProjectReport>> getReportsByStatus(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "报表状态") @PathVariable ReportStatus status,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProjectReport> reports = projectReportService.getReportsByStatus(projectId, status, pageable);
        return ResponseEntity.ok(reports);
    }

    @Operation(summary = "获取用户报表", description = "获取指定用户生成的报表")
    @GetMapping("/user/{generatedBy}")
    public ResponseEntity<Page<ProjectReport>> getUserReports(
            @Parameter(description = "生成者ID") @PathVariable UUID generatedBy,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProjectReport> reports = projectReportService.getUserReports(generatedBy, pageable);
        return ResponseEntity.ok(reports);
    }

    @Operation(summary = "搜索报表", description = "搜索项目报表")
    @GetMapping("/search")
    public ResponseEntity<Page<ProjectReport>> searchReports(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "报表类型") @RequestParam(required = false) ReportType reportType,
            @Parameter(description = "报表状态") @RequestParam(required = false) ReportStatus status,
            @Parameter(description = "开始日期") @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProjectReport> reports = projectReportService.searchReports(projectId, keyword, reportType,
                                                                        status, startDate, endDate, pageable);
        return ResponseEntity.ok(reports);
    }

    @Operation(summary = "获取最新报表", description = "获取指定项目和类型的最新报表")
    @GetMapping("/project/{projectId}/latest/{reportType}")
    public ResponseEntity<ProjectReport> getLatestReport(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "报表类型") @PathVariable ReportType reportType) {
        
        return projectReportService.getLatestReport(projectId, reportType)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "获取热门报表", description = "获取下载次数最多的报表")
    @GetMapping("/project/{projectId}/popular")
    public ResponseEntity<List<ProjectReport>> getPopularReports(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<ProjectReport> reports = projectReportService.getPopularReports(projectId, limit);
        return ResponseEntity.ok(reports);
    }

    @Operation(summary = "获取最近报表", description = "获取最近生成的报表")
    @GetMapping("/project/{projectId}/recent")
    public ResponseEntity<List<ProjectReport>> getRecentReports(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<ProjectReport> reports = projectReportService.getRecentReports(projectId, limit);
        return ResponseEntity.ok(reports);
    }

    @Operation(summary = "获取报表统计", description = "获取项目报表统计信息")
    @GetMapping("/project/{projectId}/statistics")
    public ResponseEntity<Map<String, Object>> getReportStatistics(
            @Parameter(description = "项目ID") @PathVariable UUID projectId) {
        
        Map<String, Object> statistics = projectReportService.getReportStatistics(projectId);
        return ResponseEntity.ok(statistics);
    }

    @Operation(summary = "批量生成报表", description = "批量生成待处理的报表")
    @PostMapping("/batch-generate")
    public ResponseEntity<Integer> batchGenerateReports(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") int limit) {
        
        logger.info("批量生成报表请求: 限制数量={}", limit);

        int generatedCount = projectReportService.batchGenerateReports(limit);
        return ResponseEntity.ok(generatedCount);
    }

    @Operation(summary = "重试失败报表", description = "重试生成失败的报表")
    @PostMapping("/retry-failed")
    public ResponseEntity<Integer> retryFailedReports(
            @Parameter(description = "重试间隔小时数") @RequestParam(defaultValue = "1") int retryHours) {
        
        logger.info("重试失败报表请求: 重试间隔={}小时", retryHours);

        int retryCount = projectReportService.retryFailedReports(retryHours);
        return ResponseEntity.ok(retryCount);
    }

    @Operation(summary = "处理过期报表", description = "处理已过期的报表")
    @PostMapping("/process-expired")
    public ResponseEntity<Integer> processExpiredReports() {
        logger.info("处理过期报表请求");

        int processedCount = projectReportService.processExpiredReports();
        return ResponseEntity.ok(processedCount);
    }

    @Operation(summary = "清理旧报表", description = "清理指定天数之前的旧报表")
    @DeleteMapping("/cleanup")
    public ResponseEntity<Integer> cleanupOldReports(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "90") int days) {
        
        logger.info("清理旧报表请求: 保留天数={}", days);

        int cleanedCount = projectReportService.cleanupOldReports(days);
        return ResponseEntity.ok(cleanedCount);
    }

    @Operation(summary = "获取报表模板", description = "获取指定类型的报表模板")
    @GetMapping("/template/{reportType}")
    public ResponseEntity<Map<String, Object>> getReportTemplate(
            @Parameter(description = "报表类型") @PathVariable ReportType reportType) {
        
        Map<String, Object> template = projectReportService.getReportTemplate(reportType);
        return ResponseEntity.ok(template);
    }

    @Operation(summary = "检查生成权限", description = "检查用户是否可以生成报表")
    @GetMapping("/project/{projectId}/can-generate")
    public ResponseEntity<Boolean> canGenerateReport(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "用户ID") @RequestParam UUID userId) {
        
        boolean canGenerate = projectReportService.canGenerateReport(projectId, userId);
        return ResponseEntity.ok(canGenerate);
    }

    @Operation(summary = "检查查看权限", description = "检查用户是否可以查看报表")
    @GetMapping("/{reportId}/can-view")
    public ResponseEntity<Boolean> canViewReport(
            @Parameter(description = "报表ID") @PathVariable UUID reportId,
            @Parameter(description = "用户ID") @RequestParam UUID userId) {
        
        boolean canView = projectReportService.canViewReport(reportId, userId);
        return ResponseEntity.ok(canView);
    }
}
