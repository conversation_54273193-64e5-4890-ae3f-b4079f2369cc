package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.ActivityLog;
import com.aipm.projectmanagement.entity.ActivityLog.ActionType;
import com.aipm.projectmanagement.entity.ActivityLog.TargetType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 活动日志服务接口
 * 
 * 定义活动日志管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface ActivityLogService {

    /**
     * 记录活动日志
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param description 描述
     * @return 创建的活动日志
     */
    ActivityLog logActivity(UUID projectId, UUID userId, ActionType actionType, String description);

    /**
     * 记录活动日志（带目标信息）
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param targetName 目标名称
     * @param description 描述
     * @return 创建的活动日志
     */
    ActivityLog logActivity(UUID projectId, UUID userId, ActionType actionType, 
                           TargetType targetType, UUID targetId, String targetName, String description);

    /**
     * 记录活动日志（带变更信息）
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param targetName 目标名称
     * @param description 描述
     * @param oldValue 旧值
     * @param newValue 新值
     * @return 创建的活动日志
     */
    ActivityLog logActivityWithChanges(UUID projectId, UUID userId, ActionType actionType, 
                                      TargetType targetType, UUID targetId, String targetName, 
                                      String description, String oldValue, String newValue);

    /**
     * 记录项目活动
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param projectName 项目名称
     * @param description 描述
     * @return 创建的活动日志
     */
    ActivityLog logProjectActivity(UUID projectId, UUID userId, ActionType actionType, 
                                  String projectName, String description);

    /**
     * 记录任务活动
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param taskId 任务ID
     * @param taskTitle 任务标题
     * @param description 描述
     * @return 创建的活动日志
     */
    ActivityLog logTaskActivity(UUID projectId, UUID userId, ActionType actionType, 
                               UUID taskId, String taskTitle, String description);

    /**
     * 记录Sprint活动
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param sprintId Sprint ID
     * @param sprintName Sprint名称
     * @param description 描述
     * @return 创建的活动日志
     */
    ActivityLog logSprintActivity(UUID projectId, UUID userId, ActionType actionType, 
                                 UUID sprintId, String sprintName, String description);

    /**
     * 记录评论活动
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param commentId 评论ID
     * @param description 描述
     * @return 创建的活动日志
     */
    ActivityLog logCommentActivity(UUID projectId, UUID userId, ActionType actionType, 
                                  UUID commentId, String description);

    /**
     * 获取项目活动日志
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> getProjectActivities(UUID projectId, Pageable pageable);

    /**
     * 获取用户活动日志
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> getUserActivities(UUID userId, Pageable pageable);

    /**
     * 获取目标活动日志
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> getTargetActivities(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 获取最近活动
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 最近活动列表
     */
    List<ActivityLog> getRecentActivities(UUID projectId, int limit);

    /**
     * 获取用户最近活动
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近活动列表
     */
    List<ActivityLog> getUserRecentActivities(UUID userId, int limit);

    /**
     * 搜索活动日志
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param actionType 活动类型
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> searchActivities(UUID projectId, String keyword, ActionType actionType, 
                                      UUID userId, LocalDateTime startDate, LocalDateTime endDate, 
                                      Pageable pageable);

    /**
     * 获取目标活动历史
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 活动历史列表
     */
    List<ActivityLog> getTargetActivityHistory(TargetType targetType, UUID targetId);

    /**
     * 获取活动类型统计
     * 
     * @param projectId 项目ID
     * @return 活动类型统计
     */
    Map<String, Object> getActivityTypeStatistics(UUID projectId);

    /**
     * 获取用户活动统计
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 用户活动统计
     */
    Map<String, Object> getUserActivityStatistics(UUID projectId, int days);

    /**
     * 获取活动时间线数据
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 时间线数据
     */
    Map<String, Object> getActivityTimeline(UUID projectId, int days);

    /**
     * 获取最活跃用户
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @param limit 限制数量
     * @return 活跃用户列表
     */
    List<Map<String, Object>> getMostActiveUsers(UUID projectId, int days, int limit);

    /**
     * 获取活动热力图数据
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 热力图数据
     */
    Map<String, Object> getActivityHeatmapData(UUID projectId, int days);

    /**
     * 获取活动摘要
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 活动摘要
     */
    Map<String, Object> getActivitySummary(UUID projectId, int days);

    /**
     * 获取用户在项目中的活动统计
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param days 天数
     * @return 用户活动统计
     */
    Map<String, Object> getUserActivityInProject(UUID projectId, UUID userId, int days);

    /**
     * 获取活动趋势数据
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 活动趋势数据
     */
    Map<String, Object> getActivityTrend(UUID projectId, int days);

    /**
     * 统计指定时间范围内的活动数量
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 活动数量
     */
    long countActivitiesByDateRange(UUID projectId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 清理旧的活动日志
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupOldActivities(int days);

    /**
     * 导出活动日志
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 导出数据
     */
    String exportActivities(UUID projectId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 生成活动报告
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 活动报告
     */
    Map<String, Object> generateActivityReport(UUID projectId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 设置请求上下文信息
     * 
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void setRequestContext(String ipAddress, String userAgent);

    /**
     * 批量记录活动日志
     * 
     * @param activityLogs 活动日志列表
     * @return 记录数量
     */
    int batchLogActivities(List<ActivityLog> activityLogs);

    /**
     * 异步记录活动日志
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param description 描述
     */
    void logActivityAsync(UUID projectId, UUID userId, ActionType actionType, String description);

    /**
     * 检查用户是否可以查看活动日志
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否可以查看
     */
    boolean canViewActivities(UUID projectId, UUID userId);
}
