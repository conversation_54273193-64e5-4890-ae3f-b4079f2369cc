package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.Board;
import com.aipm.projectmanagement.entity.Board.BoardType;
import com.aipm.projectmanagement.entity.BoardColumn;
import com.aipm.projectmanagement.entity.Task;
import com.aipm.projectmanagement.entity.TaskStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 看板服务接口
 * 
 * 定义看板管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface BoardService {

    /**
     * 创建看板
     * 
     * @param board 看板对象
     * @param creatorId 创建者ID
     * @return 创建的看板
     */
    Board createBoard(Board board, UUID creatorId);

    /**
     * 根据ID获取看板
     * 
     * @param boardId 看板ID
     * @return 看板对象（如果存在）
     */
    Optional<Board> getBoardById(UUID boardId);

    /**
     * 更新看板信息
     * 
     * @param board 看板对象
     * @param updaterId 更新者ID
     * @return 更新后的看板
     */
    Board updateBoard(Board board, UUID updaterId);

    /**
     * 删除看板
     * 
     * @param boardId 看板ID
     * @param deleterId 删除者ID
     */
    void deleteBoard(UUID boardId, UUID deleterId);

    /**
     * 获取项目的看板列表
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 看板分页结果
     */
    Page<Board> getProjectBoards(UUID projectId, Pageable pageable);

    /**
     * 获取项目的所有看板
     * 
     * @param projectId 项目ID
     * @return 看板列表
     */
    List<Board> getAllProjectBoards(UUID projectId);

    /**
     * 获取项目的默认看板
     * 
     * @param projectId 项目ID
     * @return 默认看板
     */
    Optional<Board> getDefaultBoard(UUID projectId);

    /**
     * 设置默认看板
     * 
     * @param boardId 看板ID
     * @param userId 操作用户ID
     * @return 更新后的看板
     */
    Board setDefaultBoard(UUID boardId, UUID userId);

    /**
     * 搜索看板
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param boardType 看板类型
     * @param pageable 分页参数
     * @return 看板分页结果
     */
    Page<Board> searchBoards(UUID projectId, String keyword, BoardType boardType, Pageable pageable);

    /**
     * 复制看板
     * 
     * @param boardId 源看板ID
     * @param newName 新看板名称
     * @param targetProjectId 目标项目ID
     * @param userId 操作用户ID
     * @return 新看板
     */
    Board copyBoard(UUID boardId, String newName, UUID targetProjectId, UUID userId);

    /**
     * 添加看板列
     * 
     * @param boardId 看板ID
     * @param column 看板列对象
     * @param userId 操作用户ID
     * @return 添加的看板列
     */
    BoardColumn addBoardColumn(UUID boardId, BoardColumn column, UUID userId);

    /**
     * 更新看板列
     * 
     * @param columnId 看板列ID
     * @param column 看板列对象
     * @param userId 操作用户ID
     * @return 更新后的看板列
     */
    BoardColumn updateBoardColumn(UUID columnId, BoardColumn column, UUID userId);

    /**
     * 删除看板列
     * 
     * @param columnId 看板列ID
     * @param userId 操作用户ID
     */
    void deleteBoardColumn(UUID columnId, UUID userId);

    /**
     * 移动看板列
     * 
     * @param columnId 看板列ID
     * @param newPosition 新位置
     * @param userId 操作用户ID
     * @return 更新后的看板
     */
    Board moveBoardColumn(UUID columnId, int newPosition, UUID userId);

    /**
     * 获取看板的任务
     * 
     * @param boardId 看板ID
     * @param columnId 看板列ID（可选）
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getBoardTasks(UUID boardId, UUID columnId, Pageable pageable);

    /**
     * 移动任务到看板列
     * 
     * @param taskId 任务ID
     * @param targetColumnId 目标看板列ID
     * @param userId 操作用户ID
     * @return 更新后的任务
     */
    Task moveTaskToColumn(UUID taskId, UUID targetColumnId, UUID userId);

    /**
     * 批量移动任务到看板列
     * 
     * @param taskIds 任务ID列表
     * @param targetColumnId 目标看板列ID
     * @param userId 操作用户ID
     * @return 成功移动的任务数量
     */
    int batchMoveTasksToColumn(List<UUID> taskIds, UUID targetColumnId, UUID userId);

    /**
     * 获取看板统计信息
     * 
     * @param boardId 看板ID
     * @return 统计信息Map
     */
    Map<String, Object> getBoardStatistics(UUID boardId);

    /**
     * 获取看板列统计信息
     * 
     * @param boardId 看板ID
     * @return 列统计信息列表
     */
    List<Map<String, Object>> getBoardColumnStatistics(UUID boardId);

    /**
     * 获取看板的WIP限制违规情况
     * 
     * @param boardId 看板ID
     * @return WIP违规信息列表
     */
    List<Map<String, Object>> getWipViolations(UUID boardId);

    /**
     * 获取看板的累积流图数据
     * 
     * @param boardId 看板ID
     * @param days 天数
     * @return 累积流图数据
     */
    Map<String, Object> getCumulativeFlowData(UUID boardId, int days);

    /**
     * 获取看板的周期时间数据
     * 
     * @param boardId 看板ID
     * @param days 天数
     * @return 周期时间数据
     */
    Map<String, Object> getCycleTimeData(UUID boardId, int days);

    /**
     * 获取看板的吞吐量数据
     * 
     * @param boardId 看板ID
     * @param days 天数
     * @return 吞吐量数据
     */
    Map<String, Object> getThroughputData(UUID boardId, int days);

    /**
     * 检查用户是否有看板权限
     * 
     * @param boardId 看板ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasBoardPermission(UUID boardId, UUID userId);

    /**
     * 检查用户是否可以管理看板
     * 
     * @param boardId 看板ID
     * @param userId 用户ID
     * @return 是否可以管理
     */
    boolean canManageBoard(UUID boardId, UUID userId);

    /**
     * 初始化项目默认看板
     * 
     * @param projectId 项目ID
     * @param boardType 看板类型
     * @param userId 操作用户ID
     * @return 创建的看板
     */
    Board initializeDefaultBoard(UUID projectId, BoardType boardType, UUID userId);

    /**
     * 获取看板模板
     * 
     * @param boardType 看板类型
     * @return 看板模板配置
     */
    Map<String, Object> getBoardTemplate(BoardType boardType);

    /**
     * 从模板创建看板
     * 
     * @param projectId 项目ID
     * @param templateName 模板名称
     * @param boardName 看板名称
     * @param userId 操作用户ID
     * @return 创建的看板
     */
    Board createBoardFromTemplate(UUID projectId, String templateName, String boardName, UUID userId);

    /**
     * 导出看板配置
     * 
     * @param boardId 看板ID
     * @return 看板配置JSON
     */
    String exportBoardConfiguration(UUID boardId);

    /**
     * 导入看板配置
     * 
     * @param projectId 项目ID
     * @param configurationJson 配置JSON
     * @param userId 操作用户ID
     * @return 导入的看板
     */
    Board importBoardConfiguration(UUID projectId, String configurationJson, UUID userId);

    /**
     * 获取看板活动日志
     * 
     * @param boardId 看板ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<Map<String, Object>> getBoardActivityLog(UUID boardId, Pageable pageable);

    /**
     * 获取用户创建的看板
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 看板分页结果
     */
    Page<Board> getUserBoards(UUID userId, Pageable pageable);

    /**
     * 获取最近更新的看板
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 看板分页结果
     */
    Page<Board> getRecentBoards(UUID projectId, Pageable pageable);

    /**
     * 归档看板
     * 
     * @param boardId 看板ID
     * @param userId 操作用户ID
     * @return 更新后的看板
     */
    Board archiveBoard(UUID boardId, UUID userId);

    /**
     * 恢复看板
     * 
     * @param boardId 看板ID
     * @param userId 操作用户ID
     * @return 更新后的看板
     */
    Board restoreBoard(UUID boardId, UUID userId);
}
