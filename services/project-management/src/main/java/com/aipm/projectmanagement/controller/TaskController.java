package com.aipm.projectmanagement.controller;

import com.aipm.projectmanagement.dto.*;
import com.aipm.projectmanagement.entity.Task;
import com.aipm.projectmanagement.entity.TaskPriority;
import com.aipm.projectmanagement.entity.TaskStatus;
import com.aipm.projectmanagement.mapper.TaskMapper;
import com.aipm.projectmanagement.service.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 任务管理控制器
 * 
 * 提供任务管理相关的REST API接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@RestController
@RequestMapping("/tasks")
@Tag(name = "任务管理", description = "任务管理相关API")
@SecurityRequirement(name = "bearerAuth")
public class TaskController {

    private static final Logger logger = LoggerFactory.getLogger(TaskController.class);

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskMapper taskMapper;

    /**
     * 创建新任务
     */
    @PostMapping
    @Operation(summary = "创建新任务", description = "在指定项目中创建一个新任务")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "201", 
            description = "任务创建成功",
            content = @Content(schema = @Schema(implementation = TaskDto.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "请求参数无效"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "403", 
            description = "无权限在此项目中创建任务"
        )
    })
    public ResponseEntity<ApiResponse<TaskDto>> createTask(
            @Valid @RequestBody CreateTaskRequest request,
            Authentication authentication) {
        
        logger.info("创建任务请求: {}", request.getTitle());

        try {
            UUID creatorId = getCurrentUserId(authentication);
            
            // 验证截止日期
            if (!request.isDueDateValid()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("截止日期不能早于当前时间", "INVALID_DUE_DATE"));
            }

            // 验证工时
            if (!request.isEstimatedHoursValid()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("预估工时不能为负数", "INVALID_ESTIMATED_HOURS"));
            }

            // Task task = taskMapper.fromCreateRequest(request);
            Task task = new Task(); // 临时实现
            
            // 检查是否为子任务
            if (request.getParentTaskId() != null) {
                task = taskService.createSubTask(request.getParentTaskId(), task, creatorId);
            } else {
                task = taskService.createTask(task, creatorId);
            }

            // TaskDto taskDto = taskMapper.toDto(task);
            TaskDto taskDto = new TaskDto(); // 临时实现
            
            logger.info("任务创建成功: {}", task.getId());
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("任务创建成功", taskDto));

        } catch (RuntimeException e) {
            logger.error("创建任务失败: {}", e.getMessage());
            
            if (e.getMessage().contains("无权限")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.getMessage(), "ACCESS_DENIED"));
            } else if (e.getMessage().contains("不存在")) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), "RESOURCE_NOT_FOUND"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "CREATE_TASK_FAILED"));
        } catch (Exception e) {
            logger.error("创建任务异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("创建任务失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 根据ID获取任务详情
     */
    @GetMapping("/{taskId}")
    @Operation(summary = "获取任务详情", description = "根据任务ID获取任务详细信息")
    public ResponseEntity<ApiResponse<TaskDto>> getTask(
            @Parameter(description = "任务ID", required = true) @PathVariable UUID taskId,
            Authentication authentication) {
        
        logger.debug("获取任务详情: {}", taskId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 检查访问权限
            if (!taskService.hasTaskPermission(taskId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限访问此任务", "ACCESS_DENIED"));
            }

            Optional<Task> taskOpt = taskService.getTaskById(taskId);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("任务不存在", "TASK_NOT_FOUND"));
            }

            Task task = taskOpt.get();
            TaskDto taskDto = taskMapper.toDetailDto(task);
            
            return ResponseEntity.ok(ApiResponse.success("获取任务详情成功", taskDto));

        } catch (Exception e) {
            logger.error("获取任务详情异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取任务详情失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 更新任务信息
     */
    @PutMapping("/{taskId}")
    @Operation(summary = "更新任务信息", description = "更新指定任务的基本信息")
    public ResponseEntity<ApiResponse<TaskDto>> updateTask(
            @Parameter(description = "任务ID", required = true) @PathVariable UUID taskId,
            @Valid @RequestBody UpdateTaskRequest request,
            Authentication authentication) {
        
        logger.info("更新任务信息: {}", taskId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 检查权限
            if (!taskService.hasTaskPermission(taskId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限更新此任务", "ACCESS_DENIED"));
            }

            // 验证截止日期
            if (!request.isDueDateValid()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("截止日期不能早于当前时间", "INVALID_DUE_DATE"));
            }

            Task task = taskMapper.fromUpdateRequest(request);
            task.setId(taskId);
            
            Task updatedTask = taskService.updateTask(task, userId);
            TaskDto taskDto = taskMapper.toDto(updatedTask);
            
            logger.info("任务信息更新成功: {}", taskId);
            return ResponseEntity.ok(ApiResponse.success("任务信息更新成功", taskDto));

        } catch (RuntimeException e) {
            logger.error("更新任务信息失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "TASK_NOT_FOUND"));
            } else if (e.getMessage().contains("无权限")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.getMessage(), "ACCESS_DENIED"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "UPDATE_TASK_FAILED"));
        } catch (Exception e) {
            logger.error("更新任务信息异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新任务信息失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{taskId}")
    @Operation(summary = "删除任务", description = "删除指定任务（软删除）")
    public ResponseEntity<ApiResponse<Void>> deleteTask(
            @Parameter(description = "任务ID", required = true) @PathVariable UUID taskId,
            Authentication authentication) {
        
        logger.info("删除任务: {}", taskId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 检查权限
            if (!taskService.hasTaskPermission(taskId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限删除此任务", "ACCESS_DENIED"));
            }

            taskService.deleteTask(taskId, userId);
            
            logger.info("任务删除成功: {}", taskId);
            return ResponseEntity.ok(ApiResponse.success("任务删除成功"));

        } catch (RuntimeException e) {
            logger.error("删除任务失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "TASK_NOT_FOUND"));
            } else if (e.getMessage().contains("无权限")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.getMessage(), "ACCESS_DENIED"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "DELETE_TASK_FAILED"));
        } catch (Exception e) {
            logger.error("删除任务异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("删除任务失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 分页查询任务列表
     */
    @GetMapping
    @Operation(summary = "分页查询任务列表", description = "分页查询任务列表，支持按项目、用户、状态等条件过滤")
    public ResponseEntity<ApiResponse<PagedResponse<TaskDto>>> getTasks(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "updatedAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "项目ID") @RequestParam(required = false) UUID projectId,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "任务状态") @RequestParam(required = false) TaskStatus status,
            @Parameter(description = "任务优先级") @RequestParam(required = false) TaskPriority priority,
            @Parameter(description = "分配人ID") @RequestParam(required = false) UUID assigneeId,
            @Parameter(description = "是否只查询我的任务") @RequestParam(defaultValue = "false") boolean myTasks,
            Authentication authentication) {
        
        logger.debug("查询任务列表: page={}, size={}, projectId={}", page, size, projectId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 创建分页和排序参数
            Sort.Direction direction = "desc".equalsIgnoreCase(sortDir) ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Page<Task> taskPage;
            
            if (myTasks) {
                // 查询用户的任务
                taskPage = taskService.getUserTasks(userId, keyword, status, pageable);
            } else if (projectId != null) {
                // 查询项目任务
                taskPage = taskService.searchTasks(projectId, keyword, status, priority, assigneeId, pageable);
            } else {
                // 查询用户相关的所有任务
                taskPage = taskService.getUserTasks(userId, keyword, status, pageable);
            }
            
            // 转换为DTO
            List<TaskDto> taskDtos = taskMapper.toDtoList(taskPage.getContent());
            PagedResponse<TaskDto> pagedResponse = PagedResponse.from(taskPage, taskDtos);
            
            return ResponseEntity.ok(ApiResponse.success("获取任务列表成功", pagedResponse));

        } catch (Exception e) {
            logger.error("查询任务列表异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询任务列表失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 更新任务状态
     */
    @PatchMapping("/{taskId}/status")
    @Operation(summary = "更新任务状态", description = "更新指定任务的状态")
    public ResponseEntity<ApiResponse<TaskDto>> updateTaskStatus(
            @Parameter(description = "任务ID", required = true) @PathVariable UUID taskId,
            @Parameter(description = "新状态", required = true) @RequestParam TaskStatus status,
            Authentication authentication) {
        
        logger.info("更新任务状态: {} -> {}", taskId, status);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            Task updatedTask = taskService.updateTaskStatus(taskId, status, userId);
            TaskDto taskDto = taskMapper.toDto(updatedTask);
            
            logger.info("任务状态更新成功: {} -> {}", taskId, status);
            return ResponseEntity.ok(ApiResponse.success("任务状态更新成功", taskDto));

        } catch (RuntimeException e) {
            logger.error("更新任务状态失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "TASK_NOT_FOUND"));
            } else if (e.getMessage().contains("无权限")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.getMessage(), "ACCESS_DENIED"));
            } else if (e.getMessage().contains("无法从状态")) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), "INVALID_STATUS_TRANSITION"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "UPDATE_STATUS_FAILED"));
        } catch (Exception e) {
            logger.error("更新任务状态异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新任务状态失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 分配任务
     */
    @PatchMapping("/{taskId}/assign")
    @Operation(summary = "分配任务", description = "将任务分配给指定用户")
    public ResponseEntity<ApiResponse<TaskDto>> assignTask(
            @Parameter(description = "任务ID", required = true) @PathVariable UUID taskId,
            @Parameter(description = "分配给的用户ID", required = true) @RequestParam UUID assigneeId,
            Authentication authentication) {
        
        logger.info("分配任务: {} -> {}", taskId, assigneeId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            Task updatedTask = taskService.assignTask(taskId, assigneeId, userId);
            TaskDto taskDto = taskMapper.toDto(updatedTask);
            
            logger.info("任务分配成功: {} -> {}", taskId, assigneeId);
            return ResponseEntity.ok(ApiResponse.success("任务分配成功", taskDto));

        } catch (RuntimeException e) {
            logger.error("分配任务失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "RESOURCE_NOT_FOUND"));
            } else if (e.getMessage().contains("无权限")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.getMessage(), "ACCESS_DENIED"));
            } else if (e.getMessage().contains("不是项目成员")) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), "NOT_PROJECT_MEMBER"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "ASSIGN_TASK_FAILED"));
        } catch (Exception e) {
            logger.error("分配任务异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("分配任务失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 取消任务分配
     */
    @PatchMapping("/{taskId}/unassign")
    @Operation(summary = "取消任务分配", description = "取消任务的当前分配")
    public ResponseEntity<ApiResponse<TaskDto>> unassignTask(
            @Parameter(description = "任务ID", required = true) @PathVariable UUID taskId,
            Authentication authentication) {
        
        logger.info("取消任务分配: {}", taskId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            Task updatedTask = taskService.unassignTask(taskId, userId);
            TaskDto taskDto = taskMapper.toDto(updatedTask);
            
            logger.info("任务分配取消成功: {}", taskId);
            return ResponseEntity.ok(ApiResponse.success("任务分配取消成功", taskDto));

        } catch (RuntimeException e) {
            logger.error("取消任务分配失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "TASK_NOT_FOUND"));
            } else if (e.getMessage().contains("无权限")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.getMessage(), "ACCESS_DENIED"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "UNASSIGN_TASK_FAILED"));
        } catch (Exception e) {
            logger.error("取消任务分配异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("取消任务分配失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取任务统计信息", description = "获取用户任务相关的统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTaskStatistics(
            @Parameter(description = "项目ID（可选）") @RequestParam(required = false) UUID projectId,
            Authentication authentication) {
        
        logger.debug("获取任务统计信息");

        try {
            UUID userId = getCurrentUserId(authentication);
            
            Map<String, Object> statistics;
            if (projectId != null) {
                statistics = taskService.getTaskStatistics(projectId);
            } else {
                statistics = taskService.getUserTaskStatistics(userId);
            }
            
            return ResponseEntity.ok(ApiResponse.success("获取任务统计信息成功", statistics));

        } catch (Exception e) {
            logger.error("获取任务统计信息异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取任务统计信息失败", "INTERNAL_ERROR"));
        }
    }

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    /**
     * 获取当前用户ID
     */
    private UUID getCurrentUserId(Authentication authentication) {
        // TODO: 从认证信息中获取用户ID
        // 这里需要根据实际的认证实现来获取用户ID
        return UUID.randomUUID(); // 临时实现
    }
}
