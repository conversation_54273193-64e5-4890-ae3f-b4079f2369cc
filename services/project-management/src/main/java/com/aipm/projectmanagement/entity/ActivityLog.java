package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 活动日志实体类
 * 
 * 记录项目中的各种活动和操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "activity_logs", indexes = {
    @Index(name = "idx_activity_project", columnList = "project_id"),
    @Index(name = "idx_activity_user", columnList = "user_id"),
    @Index(name = "idx_activity_target", columnList = "target_type, target_id"),
    @Index(name = "idx_activity_action", columnList = "action_type"),
    @Index(name = "idx_activity_created", columnList = "created_at")
})
public class ActivityLog {

    /**
     * 活动类型枚举
     */
    public enum ActionType {
        // 项目相关
        PROJECT_CREATED("创建项目", "创建了项目"),
        PROJECT_UPDATED("更新项目", "更新了项目信息"),
        PROJECT_DELETED("删除项目", "删除了项目"),
        PROJECT_ARCHIVED("归档项目", "归档了项目"),
        PROJECT_RESTORED("恢复项目", "恢复了项目"),
        
        // 任务相关
        TASK_CREATED("创建任务", "创建了任务"),
        TASK_UPDATED("更新任务", "更新了任务"),
        TASK_DELETED("删除任务", "删除了任务"),
        TASK_ASSIGNED("分配任务", "分配了任务"),
        TASK_UNASSIGNED("取消分配", "取消了任务分配"),
        TASK_STATUS_CHANGED("任务状态变更", "更改了任务状态"),
        TASK_PRIORITY_CHANGED("任务优先级变更", "更改了任务优先级"),
        TASK_MOVED_TO_SPRINT("任务移动到Sprint", "将任务移动到Sprint"),
        TASK_REMOVED_FROM_SPRINT("任务移出Sprint", "将任务移出Sprint"),
        
        // Sprint相关
        SPRINT_CREATED("创建Sprint", "创建了Sprint"),
        SPRINT_UPDATED("更新Sprint", "更新了Sprint"),
        SPRINT_DELETED("删除Sprint", "删除了Sprint"),
        SPRINT_STARTED("开始Sprint", "开始了Sprint"),
        SPRINT_COMPLETED("完成Sprint", "完成了Sprint"),
        SPRINT_CANCELLED("取消Sprint", "取消了Sprint"),
        
        // 看板相关
        BOARD_CREATED("创建看板", "创建了看板"),
        BOARD_UPDATED("更新看板", "更新了看板"),
        BOARD_DELETED("删除看板", "删除了看板"),
        BOARD_COLUMN_ADDED("添加看板列", "添加了看板列"),
        BOARD_COLUMN_UPDATED("更新看板列", "更新了看板列"),
        BOARD_COLUMN_DELETED("删除看板列", "删除了看板列"),
        
        // 成员相关
        MEMBER_ADDED("添加成员", "添加了项目成员"),
        MEMBER_REMOVED("移除成员", "移除了项目成员"),
        MEMBER_ROLE_CHANGED("成员角色变更", "更改了成员角色"),
        
        // 评论相关
        COMMENT_ADDED("添加评论", "添加了评论"),
        COMMENT_UPDATED("更新评论", "更新了评论"),
        COMMENT_DELETED("删除评论", "删除了评论"),
        COMMENT_REPLIED("回复评论", "回复了评论"),
        
        // 附件相关
        ATTACHMENT_UPLOADED("上传附件", "上传了附件"),
        ATTACHMENT_DOWNLOADED("下载附件", "下载了附件"),
        ATTACHMENT_DELETED("删除附件", "删除了附件"),
        
        // 其他
        USER_JOINED("用户加入", "加入了项目"),
        USER_LEFT("用户离开", "离开了项目"),
        NOTIFICATION_SENT("发送通知", "发送了通知");

        private final String displayName;
        private final String description;

        ActionType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 目标类型枚举
     */
    public enum TargetType {
        PROJECT("项目"),
        TASK("任务"),
        SPRINT("Sprint"),
        BOARD("看板"),
        BOARD_COLUMN("看板列"),
        COMMENT("评论"),
        ATTACHMENT("附件"),
        USER("用户");

        private final String displayName;

        TargetType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 活动日志唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 所属项目ID
     */
    @Column(name = "project_id", nullable = false)
    private UUID projectId;

    /**
     * 操作用户ID
     */
    @Column(name = "user_id", nullable = false)
    private UUID userId;

    /**
     * 活动类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "action_type", nullable = false)
    private ActionType actionType;

    /**
     * 目标类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "target_type")
    private TargetType targetType;

    /**
     * 目标ID
     */
    @Column(name = "target_id")
    private UUID targetId;

    /**
     * 目标名称
     */
    @Column(name = "target_name", length = 255)
    private String targetName;

    /**
     * 活动描述
     */
    @Column(name = "description", nullable = false, columnDefinition = "TEXT")
    private String description;

    /**
     * 详细信息（JSON格式）
     */
    @Column(name = "details", columnDefinition = "TEXT")
    private String details;

    /**
     * 变更前的值（JSON格式）
     */
    @Column(name = "old_value", columnDefinition = "TEXT")
    private String oldValue;

    /**
     * 变更后的值（JSON格式）
     */
    @Column(name = "new_value", columnDefinition = "TEXT")
    private String newValue;

    /**
     * IP地址
     */
    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public ActivityLog() {
    }

    /**
     * 基础构造函数
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param description 描述
     */
    public ActivityLog(UUID projectId, UUID userId, ActionType actionType, String description) {
        this.projectId = projectId;
        this.userId = userId;
        this.actionType = actionType;
        this.description = description;
    }

    /**
     * 完整构造函数
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param actionType 活动类型
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param targetName 目标名称
     * @param description 描述
     */
    public ActivityLog(UUID projectId, UUID userId, ActionType actionType, 
                      TargetType targetType, UUID targetId, String targetName, String description) {
        this.projectId = projectId;
        this.userId = userId;
        this.actionType = actionType;
        this.targetType = targetType;
        this.targetId = targetId;
        this.targetName = targetName;
        this.description = description;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 设置变更信息
     * 
     * @param oldValue 旧值
     * @param newValue 新值
     */
    public void setChangeValues(String oldValue, String newValue) {
        this.oldValue = oldValue;
        this.newValue = newValue;
    }

    /**
     * 设置请求信息
     * 
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    public void setRequestInfo(String ipAddress, String userAgent) {
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
    }

    /**
     * 检查是否有变更信息
     * 
     * @return true表示有变更信息
     */
    public boolean hasChangeInfo() {
        return oldValue != null || newValue != null;
    }

    /**
     * 检查是否有目标信息
     * 
     * @return true表示有目标信息
     */
    public boolean hasTargetInfo() {
        return targetType != null && targetId != null;
    }

    /**
     * 获取活动摘要
     * 
     * @return 活动摘要
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(actionType.getDescription());
        
        if (targetName != null && !targetName.isEmpty()) {
            summary.append(" \"").append(targetName).append("\"");
        }
        
        return summary.toString();
    }

    /**
     * 获取格式化的描述
     * 
     * @return 格式化描述
     */
    public String getFormattedDescription() {
        if (description == null || description.isEmpty()) {
            return getSummary();
        }
        return description;
    }

    // ============================================================================
    // 静态工厂方法
    // ============================================================================

    /**
     * 创建项目活动日志
     */
    public static ActivityLog createProjectLog(UUID projectId, UUID userId, ActionType actionType, 
                                              String projectName, String description) {
        return new ActivityLog(projectId, userId, actionType, TargetType.PROJECT, 
                              projectId, projectName, description);
    }

    /**
     * 创建任务活动日志
     */
    public static ActivityLog createTaskLog(UUID projectId, UUID userId, ActionType actionType, 
                                           UUID taskId, String taskTitle, String description) {
        return new ActivityLog(projectId, userId, actionType, TargetType.TASK, 
                              taskId, taskTitle, description);
    }

    /**
     * 创建Sprint活动日志
     */
    public static ActivityLog createSprintLog(UUID projectId, UUID userId, ActionType actionType, 
                                             UUID sprintId, String sprintName, String description) {
        return new ActivityLog(projectId, userId, actionType, TargetType.SPRINT, 
                              sprintId, sprintName, description);
    }

    /**
     * 创建评论活动日志
     */
    public static ActivityLog createCommentLog(UUID projectId, UUID userId, ActionType actionType, 
                                              UUID commentId, String description) {
        return new ActivityLog(projectId, userId, actionType, TargetType.COMMENT, 
                              commentId, null, description);
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getProjectId() {
        return projectId;
    }

    public void setProjectId(UUID projectId) {
        this.projectId = projectId;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public ActionType getActionType() {
        return actionType;
    }

    public void setActionType(ActionType actionType) {
        this.actionType = actionType;
    }

    public TargetType getTargetType() {
        return targetType;
    }

    public void setTargetType(TargetType targetType) {
        this.targetType = targetType;
    }

    public UUID getTargetId() {
        return targetId;
    }

    public void setTargetId(UUID targetId) {
        this.targetId = targetId;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getOldValue() {
        return oldValue;
    }

    public void setOldValue(String oldValue) {
        this.oldValue = oldValue;
    }

    public String getNewValue() {
        return newValue;
    }

    public void setNewValue(String newValue) {
        this.newValue = newValue;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ActivityLog that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "ActivityLog{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", userId=" + userId +
                ", actionType=" + actionType +
                ", targetType=" + targetType +
                ", targetId=" + targetId +
                ", targetName='" + targetName + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
