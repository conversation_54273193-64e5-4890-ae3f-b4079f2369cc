package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 项目实体类
 * 
 * 表示一个项目的完整信息，包括基本信息、状态、成员、任务等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "projects", indexes = {
    @Index(name = "idx_projects_name", columnList = "name"),
    @Index(name = "idx_projects_status", columnList = "status"),
    @Index(name = "idx_projects_owner_id", columnList = "owner_id"),
    @Index(name = "idx_projects_start_date", columnList = "start_date"),
    @Index(name = "idx_projects_end_date", columnList = "end_date"),
    @Index(name = "idx_projects_created_at", columnList = "created_at")
})
@EntityListeners(AuditingEntityListener.class)
public class Project {

    /**
     * 项目唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @Size(min = 2, max = 100, message = "项目名称长度必须在2-100个字符之间")
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * 项目描述
     */
    @Size(max = 2000, message = "项目描述长度不能超过2000个字符")
    @Column(name = "description", length = 2000)
    private String description;

    /**
     * 项目状态
     */
    @NotNull(message = "项目状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ProjectStatus status = ProjectStatus.PLANNING;

    /**
     * 项目负责人ID
     */
    @NotNull(message = "项目负责人不能为空")
    @Column(name = "owner_id", nullable = false)
    private UUID ownerId;

    /**
     * 项目开始日期
     */
    @Column(name = "start_date")
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    @Column(name = "end_date")
    private LocalDate endDate;

    /**
     * 项目预算
     */
    @Column(name = "budget", precision = 15, scale = 2)
    private BigDecimal budget;

    /**
     * 项目进度百分比 (0-100)
     */
    @Column(name = "progress", columnDefinition = "INTEGER DEFAULT 0")
    private Integer progress = 0;

    /**
     * 项目优先级
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "priority", length = 20)
    private TaskPriority priority = TaskPriority.NORMAL;

    /**
     * 项目标签（JSON格式存储）
     */
    @Column(name = "tags", length = 500)
    private String tags;

    /**
     * 项目配置（JSON格式存储）
     */
    @Column(name = "settings", columnDefinition = "TEXT")
    private String settings;

    /**
     * 是否为模板项目
     */
    @Column(name = "is_template", columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isTemplate = false;

    /**
     * 是否已归档
     */
    @Column(name = "is_archived", columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isArchived = false;

    /**
     * 项目成员列表
     */
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProjectMember> members = new ArrayList<>();

    /**
     * 项目任务列表
     */
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Task> tasks = new ArrayList<>();

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private UUID createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private UUID updatedBy;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public Project() {
    }

    /**
     * 基础构造函数
     * 
     * @param name 项目名称
     * @param description 项目描述
     * @param ownerId 项目负责人ID
     */
    public Project(String name, String description, UUID ownerId) {
        this.name = name;
        this.description = description;
        this.ownerId = ownerId;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查项目是否处于活跃状态
     * 
     * @return true表示项目活跃
     */
    public boolean isActive() {
        return status != null && status.isActive() && !Boolean.TRUE.equals(isArchived);
    }

    /**
     * 检查项目是否已完成
     * 
     * @return true表示项目已完成
     */
    public boolean isCompleted() {
        return status == ProjectStatus.COMPLETED;
    }

    /**
     * 检查项目是否已过期
     * 
     * @return true表示项目已过期
     */
    public boolean isOverdue() {
        return endDate != null && LocalDate.now().isAfter(endDate) && !isCompleted();
    }

    /**
     * 计算项目持续时间（天数）
     * 
     * @return 项目持续天数，如果日期不完整返回null
     */
    public Long getDurationDays() {
        if (startDate == null || endDate == null) {
            return null;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate);
    }

    /**
     * 计算项目剩余天数
     * 
     * @return 剩余天数，如果已过期返回负数
     */
    public Long getRemainingDays() {
        if (endDate == null) {
            return null;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(LocalDate.now(), endDate);
    }

    /**
     * 添加项目成员
     * 
     * @param member 项目成员
     */
    public void addMember(ProjectMember member) {
        if (members == null) {
            members = new ArrayList<>();
        }
        members.add(member);
        member.setProject(this);
    }

    /**
     * 移除项目成员
     * 
     * @param member 项目成员
     */
    public void removeMember(ProjectMember member) {
        if (members != null) {
            members.remove(member);
            member.setProject(null);
        }
    }

    /**
     * 添加任务
     * 
     * @param task 任务
     */
    public void addTask(Task task) {
        if (tasks == null) {
            tasks = new ArrayList<>();
        }
        tasks.add(task);
        task.setProject(this);
    }

    /**
     * 移除任务
     * 
     * @param task 任务
     */
    public void removeTask(Task task) {
        if (tasks != null) {
            tasks.remove(task);
            task.setProject(null);
        }
    }

    /**
     * 获取活跃任务数量
     * 
     * @return 活跃任务数量
     */
    public long getActiveTaskCount() {
        if (tasks == null) {
            return 0;
        }
        return tasks.stream().filter(Task::isActive).count();
    }

    /**
     * 获取已完成任务数量
     * 
     * @return 已完成任务数量
     */
    public long getCompletedTaskCount() {
        if (tasks == null) {
            return 0;
        }
        return tasks.stream().filter(Task::isCompleted).count();
    }

    /**
     * 计算任务完成率
     * 
     * @return 完成率百分比 (0-100)
     */
    public double getTaskCompletionRate() {
        if (tasks == null || tasks.isEmpty()) {
            return 0.0;
        }
        long completedCount = getCompletedTaskCount();
        return (double) completedCount / tasks.size() * 100;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ProjectStatus getStatus() {
        return status;
    }

    public void setStatus(ProjectStatus status) {
        this.status = status;
    }

    public UUID getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(UUID ownerId) {
        this.ownerId = ownerId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getSettings() {
        return settings;
    }

    public void setSettings(String settings) {
        this.settings = settings;
    }

    public Boolean getIsTemplate() {
        return isTemplate;
    }

    public void setIsTemplate(Boolean isTemplate) {
        this.isTemplate = isTemplate;
    }

    public Boolean getIsArchived() {
        return isArchived;
    }

    public void setIsArchived(Boolean isArchived) {
        this.isArchived = isArchived;
    }

    public List<ProjectMember> getMembers() {
        return members;
    }

    public void setMembers(List<ProjectMember> members) {
        this.members = members;
    }

    public List<Task> getTasks() {
        return tasks;
    }

    public void setTasks(List<Task> tasks) {
        this.tasks = tasks;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public UUID getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Project project)) return false;
        return id != null && id.equals(project.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "Project{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status=" + status +
                ", ownerId=" + ownerId +
                ", progress=" + progress +
                ", createdAt=" + createdAt +
                '}';
    }
}
