package com.aipm.projectmanagement.dto;

import com.aipm.projectmanagement.entity.ProjectStatus;
import com.aipm.projectmanagement.entity.TaskPriority;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 项目信息DTO
 * 
 * 用于API响应中返回项目信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "项目信息")
public class ProjectDto {

    /**
     * 项目ID
     */
    @Schema(description = "项目唯一标识", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称", example = "AI项目管理平台")
    private String name;

    /**
     * 项目描述
     */
    @Schema(description = "项目描述", example = "基于AI的智能项目管理平台")
    private String description;

    /**
     * 项目状态
     */
    @Schema(description = "项目状态", example = "IN_PROGRESS")
    private ProjectStatus status;

    /**
     * 项目负责人ID
     */
    @Schema(description = "项目负责人ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID ownerId;

    /**
     * 项目负责人信息
     */
    @Schema(description = "项目负责人信息")
    private UserSummaryDto owner;

    /**
     * 项目开始日期
     */
    @Schema(description = "项目开始日期", example = "2025-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    @Schema(description = "项目结束日期", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 项目预算
     */
    @Schema(description = "项目预算", example = "1000000.00")
    private BigDecimal budget;

    /**
     * 项目进度百分比
     */
    @Schema(description = "项目进度百分比", example = "75")
    private Integer progress;

    /**
     * 项目优先级
     */
    @Schema(description = "项目优先级", example = "HIGH")
    private TaskPriority priority;

    /**
     * 项目标签
     */
    @Schema(description = "项目标签", example = "[\"AI\", \"管理平台\", \"微服务\"]")
    private List<String> tags;

    /**
     * 是否为模板项目
     */
    @Schema(description = "是否为模板项目", example = "false")
    private Boolean isTemplate;

    /**
     * 是否已归档
     */
    @Schema(description = "是否已归档", example = "false")
    private Boolean isArchived;

    /**
     * 项目成员数量
     */
    @Schema(description = "项目成员数量", example = "8")
    private Long memberCount;

    /**
     * 项目任务数量
     */
    @Schema(description = "项目任务数量", example = "25")
    private Long taskCount;

    /**
     * 已完成任务数量
     */
    @Schema(description = "已完成任务数量", example = "18")
    private Long completedTaskCount;

    /**
     * 任务完成率
     */
    @Schema(description = "任务完成率", example = "72.0")
    private Double taskCompletionRate;

    /**
     * 项目健康度评分
     */
    @Schema(description = "项目健康度评分", example = "85")
    private Integer healthScore;

    /**
     * 是否过期
     */
    @Schema(description = "是否过期", example = "false")
    private Boolean isOverdue;

    /**
     * 剩余天数
     */
    @Schema(description = "剩余天数", example = "45")
    private Long remainingDays;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-08-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-08-15T15:45:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID createdBy;

    /**
     * 更新者ID
     */
    @Schema(description = "更新者ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID updatedBy;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public ProjectDto() {
    }

    /**
     * 基础构造函数
     * 
     * @param id 项目ID
     * @param name 项目名称
     * @param description 项目描述
     * @param status 项目状态
     */
    public ProjectDto(UUID id, String name, String description, ProjectStatus status) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.status = status;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查项目是否处于活跃状态
     * 
     * @return true表示项目活跃
     */
    public boolean isActive() {
        return status != null && status.isActive() && !Boolean.TRUE.equals(isArchived);
    }

    /**
     * 检查项目是否已完成
     * 
     * @return true表示项目已完成
     */
    public boolean isCompleted() {
        return status == ProjectStatus.COMPLETED;
    }

    /**
     * 获取项目状态显示名称
     * 
     * @return 状态显示名称
     */
    public String getStatusDisplayName() {
        return status != null ? status.getDisplayName() : "未知";
    }

    /**
     * 获取优先级显示名称
     * 
     * @return 优先级显示名称
     */
    public String getPriorityDisplayName() {
        return priority != null ? priority.getDisplayName() : "普通";
    }

    /**
     * 获取项目持续时间（天数）
     * 
     * @return 项目持续天数
     */
    public Long getDurationDays() {
        if (startDate == null || endDate == null) {
            return null;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate);
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ProjectStatus getStatus() {
        return status;
    }

    public void setStatus(ProjectStatus status) {
        this.status = status;
    }

    public UUID getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(UUID ownerId) {
        this.ownerId = ownerId;
    }

    public UserSummaryDto getOwner() {
        return owner;
    }

    public void setOwner(UserSummaryDto owner) {
        this.owner = owner;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Boolean getIsTemplate() {
        return isTemplate;
    }

    public void setIsTemplate(Boolean isTemplate) {
        this.isTemplate = isTemplate;
    }

    public Boolean getIsArchived() {
        return isArchived;
    }

    public void setIsArchived(Boolean isArchived) {
        this.isArchived = isArchived;
    }

    public Long getMemberCount() {
        return memberCount;
    }

    public void setMemberCount(Long memberCount) {
        this.memberCount = memberCount;
    }

    public Long getTaskCount() {
        return taskCount;
    }

    public void setTaskCount(Long taskCount) {
        this.taskCount = taskCount;
    }

    public Long getCompletedTaskCount() {
        return completedTaskCount;
    }

    public void setCompletedTaskCount(Long completedTaskCount) {
        this.completedTaskCount = completedTaskCount;
    }

    public Double getTaskCompletionRate() {
        return taskCompletionRate;
    }

    public void setTaskCompletionRate(Double taskCompletionRate) {
        this.taskCompletionRate = taskCompletionRate;
    }

    public Integer getHealthScore() {
        return healthScore;
    }

    public void setHealthScore(Integer healthScore) {
        this.healthScore = healthScore;
    }

    public Boolean getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(Boolean isOverdue) {
        this.isOverdue = isOverdue;
    }

    public Long getRemainingDays() {
        return remainingDays;
    }

    public void setRemainingDays(Long remainingDays) {
        this.remainingDays = remainingDays;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public UUID getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ProjectDto that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "ProjectDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status=" + status +
                ", progress=" + progress +
                ", memberCount=" + memberCount +
                ", taskCount=" + taskCount +
                '}';
    }
}
