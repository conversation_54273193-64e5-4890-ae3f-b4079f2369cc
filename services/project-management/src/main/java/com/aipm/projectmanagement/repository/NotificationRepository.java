package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.Notification;
import com.aipm.projectmanagement.entity.Notification.NotificationStatus;
import com.aipm.projectmanagement.entity.Notification.NotificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 通知数据访问接口
 * 
 * 提供通知相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface NotificationRepository extends JpaRepository<Notification, UUID> {

    /**
     * 根据接收者ID查询通知
     * 
     * @param recipientId 接收者ID
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    Page<Notification> findByRecipientIdOrderByCreatedAtDesc(UUID recipientId, Pageable pageable);

    /**
     * 根据接收者ID和已读状态查询通知
     * 
     * @param recipientId 接收者ID
     * @param isRead 是否已读
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    Page<Notification> findByRecipientIdAndIsReadOrderByCreatedAtDesc(UUID recipientId, Boolean isRead, Pageable pageable);

    /**
     * 根据接收者ID和通知类型查询通知
     * 
     * @param recipientId 接收者ID
     * @param notificationType 通知类型
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    Page<Notification> findByRecipientIdAndNotificationTypeOrderByCreatedAtDesc(UUID recipientId, 
                                                                               NotificationType notificationType, 
                                                                               Pageable pageable);

    /**
     * 根据项目ID查询通知
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    Page<Notification> findByProjectIdOrderByCreatedAtDesc(UUID projectId, Pageable pageable);

    /**
     * 统计用户未读通知数量
     * 
     * @param recipientId 接收者ID
     * @return 未读通知数量
     */
    long countByRecipientIdAndIsReadFalse(UUID recipientId);

    /**
     * 统计用户指定类型的未读通知数量
     * 
     * @param recipientId 接收者ID
     * @param notificationType 通知类型
     * @return 未读通知数量
     */
    long countByRecipientIdAndNotificationTypeAndIsReadFalse(UUID recipientId, NotificationType notificationType);

    /**
     * 查询用户最近的通知
     * 
     * @param recipientId 接收者ID
     * @param limit 限制数量
     * @return 最近通知列表
     */
    @Query("SELECT n FROM Notification n WHERE n.recipientId = :recipientId " +
           "ORDER BY n.createdAt DESC LIMIT :limit")
    List<Notification> findRecentNotifications(@Param("recipientId") UUID recipientId, @Param("limit") int limit);

    /**
     * 查询高优先级未读通知
     * 
     * @param recipientId 接收者ID
     * @param minPriority 最小优先级
     * @param pageable 分页参数
     * @return 高优先级通知分页结果
     */
    @Query("SELECT n FROM Notification n WHERE n.recipientId = :recipientId " +
           "AND n.isRead = false AND n.priority >= :minPriority " +
           "ORDER BY n.priority DESC, n.createdAt DESC")
    Page<Notification> findHighPriorityUnreadNotifications(@Param("recipientId") UUID recipientId, 
                                                           @Param("minPriority") Integer minPriority, 
                                                           Pageable pageable);

    /**
     * 查询待发送的通知
     * 
     * @param status 通知状态
     * @param limit 限制数量
     * @return 待发送通知列表
     */
    @Query("SELECT n FROM Notification n WHERE n.status = :status " +
           "ORDER BY n.priority DESC, n.createdAt ASC LIMIT :limit")
    List<Notification> findPendingNotifications(@Param("status") NotificationStatus status, @Param("limit") int limit);

    /**
     * 查询需要邮件通知的通知
     * 
     * @param status 通知状态
     * @return 邮件通知列表
     */
    @Query("SELECT n FROM Notification n WHERE n.status = :status AND n.emailNotification = true " +
           "ORDER BY n.priority DESC, n.createdAt ASC")
    List<Notification> findEmailNotifications(@Param("status") NotificationStatus status);

    /**
     * 查询需要推送通知的通知
     * 
     * @param status 通知状态
     * @return 推送通知列表
     */
    @Query("SELECT n FROM Notification n WHERE n.status = :status AND n.pushNotification = true " +
           "ORDER BY n.priority DESC, n.createdAt ASC")
    List<Notification> findPushNotifications(@Param("status") NotificationStatus status);

    /**
     * 查询已过期的通知
     * 
     * @param currentTime 当前时间
     * @return 过期通知列表
     */
    @Query("SELECT n FROM Notification n WHERE n.expiresAt IS NOT NULL AND n.expiresAt < :currentTime")
    List<Notification> findExpiredNotifications(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 搜索通知
     * 
     * @param recipientId 接收者ID
     * @param keyword 关键词
     * @param notificationType 通知类型
     * @param isRead 是否已读
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    @Query("SELECT n FROM Notification n WHERE n.recipientId = :recipientId " +
           "AND (:keyword IS NULL OR n.title LIKE %:keyword% OR n.content LIKE %:keyword%) " +
           "AND (:notificationType IS NULL OR n.notificationType = :notificationType) " +
           "AND (:isRead IS NULL OR n.isRead = :isRead) " +
           "AND (:startDate IS NULL OR n.createdAt >= :startDate) " +
           "AND (:endDate IS NULL OR n.createdAt <= :endDate) " +
           "ORDER BY n.createdAt DESC")
    Page<Notification> searchNotifications(@Param("recipientId") UUID recipientId,
                                          @Param("keyword") String keyword,
                                          @Param("notificationType") NotificationType notificationType,
                                          @Param("isRead") Boolean isRead,
                                          @Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate,
                                          Pageable pageable);

    /**
     * 批量标记通知为已读
     * 
     * @param notificationIds 通知ID列表
     * @param readTime 阅读时间
     * @return 更新数量
     */
    @Query("UPDATE Notification n SET n.isRead = true, n.readAt = :readTime, n.status = 'READ' " +
           "WHERE n.id IN :notificationIds AND n.isRead = false")
    int batchMarkAsRead(@Param("notificationIds") List<UUID> notificationIds, @Param("readTime") LocalDateTime readTime);

    /**
     * 批量标记用户所有通知为已读
     * 
     * @param recipientId 接收者ID
     * @param readTime 阅读时间
     * @return 更新数量
     */
    @Query("UPDATE Notification n SET n.isRead = true, n.readAt = :readTime, n.status = 'read' " +
           "WHERE n.recipientId = :recipientId AND n.isRead = false")
    int markAllAsReadForUser(@Param("recipientId") UUID recipientId, @Param("readTime") LocalDateTime readTime);

    /**
     * 统计通知类型分布
     * 
     * @param recipientId 接收者ID
     * @return 类型分布统计
     */
    @Query("SELECT n.notificationType, COUNT(n), SUM(CASE WHEN n.isRead = false THEN 1 ELSE 0 END) " +
           "FROM Notification n WHERE n.recipientId = :recipientId " +
           "GROUP BY n.notificationType ORDER BY COUNT(n) DESC")
    List<Object[]> getNotificationTypeStatistics(@Param("recipientId") UUID recipientId);

    /**
     * 查询通知发送统计
     * 
     * @param startDate 开始日期
     * @return 发送统计
     */
    @Query("SELECT DATE(n.createdAt) as date, COUNT(n) as total, " +
           "SUM(CASE WHEN n.status = 'SENT' THEN 1 ELSE 0 END) as sent, " +
           "SUM(CASE WHEN n.status = 'DELIVERED' THEN 1 ELSE 0 END) as delivered, " +
           "SUM(CASE WHEN n.status = 'READ' THEN 1 ELSE 0 END) as read " +
           "FROM Notification n WHERE n.createdAt >= :startDate " +
           "GROUP BY DATE(n.createdAt) ORDER BY DATE(n.createdAt)")
    List<Object[]> getNotificationSendingStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 查询用户通知活跃度
     * 
     * @param recipientId 接收者ID
     * @param startDate 开始日期
     * @return 活跃度统计
     */
    @Query("SELECT DATE(n.createdAt) as date, COUNT(n) as received, " +
           "SUM(CASE WHEN n.isRead = true THEN 1 ELSE 0 END) as read " +
           "FROM Notification n WHERE n.recipientId = :recipientId " +
           "AND n.createdAt >= :startDate " +
           "GROUP BY DATE(n.createdAt) ORDER BY DATE(n.createdAt)")
    List<Object[]> getUserNotificationActivity(@Param("recipientId") UUID recipientId, 
                                              @Param("startDate") LocalDateTime startDate);

    /**
     * 清理旧的已读通知
     * 
     * @param beforeDate 清理日期之前的记录
     * @return 清理数量
     */
    @Query("DELETE FROM Notification n WHERE n.isRead = true AND n.readAt < :beforeDate")
    int cleanupOldReadNotifications(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 查询发送失败的通知
     * 
     * @param retryBefore 重试时间之前
     * @return 失败通知列表
     */
    @Query("SELECT n FROM Notification n WHERE n.status = 'FAILED' " +
           "AND n.updatedAt < :retryBefore ORDER BY n.priority DESC, n.createdAt ASC")
    List<Notification> findFailedNotificationsForRetry(@Param("retryBefore") LocalDateTime retryBefore);

    /**
     * 统计用户通知摘要
     * 
     * @param recipientId 接收者ID
     * @param days 天数
     * @return 通知摘要
     */
    @Query("SELECT " +
           "COUNT(n) as total, " +
           "SUM(CASE WHEN n.isRead = false THEN 1 ELSE 0 END) as unread, " +
           "SUM(CASE WHEN n.priority >= 4 THEN 1 ELSE 0 END) as highPriority, " +
           "COUNT(DISTINCT n.notificationType) as typeCount " +
           "FROM Notification n WHERE n.recipientId = :recipientId " +
           "AND n.createdAt >= :startDate")
    Object[] getNotificationSummary(@Param("recipientId") UUID recipientId, 
                                   @Param("startDate") LocalDateTime startDate);

    /**
     * 查询项目通知统计
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @return 项目通知统计
     */
    @Query("SELECT n.notificationType, COUNT(n), COUNT(DISTINCT n.recipientId) " +
           "FROM Notification n WHERE n.projectId = :projectId " +
           "AND (:startDate IS NULL OR n.createdAt >= :startDate) " +
           "GROUP BY n.notificationType ORDER BY COUNT(n) DESC")
    List<Object[]> getProjectNotificationStatistics(@Param("projectId") UUID projectId, 
                                                    @Param("startDate") LocalDateTime startDate);
}
