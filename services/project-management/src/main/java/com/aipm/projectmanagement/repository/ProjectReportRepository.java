package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.ProjectReport;
import com.aipm.projectmanagement.entity.ProjectReport.ReportStatus;
import com.aipm.projectmanagement.entity.ProjectReport.ReportType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 项目报表数据访问接口
 * 
 * 提供项目报表相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
@Repository
public interface ProjectReportRepository extends JpaRepository<ProjectReport, UUID> {

    /**
     * 根据项目ID查询报表
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> findByProjectIdOrderByCreatedAtDesc(UUID projectId, Pageable pageable);

    /**
     * 根据项目ID和报表类型查询报表
     * 
     * @param projectId 项目ID
     * @param reportType 报表类型
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> findByProjectIdAndReportTypeOrderByCreatedAtDesc(UUID projectId, ReportType reportType, Pageable pageable);

    /**
     * 根据项目ID和状态查询报表
     * 
     * @param projectId 项目ID
     * @param status 报表状态
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> findByProjectIdAndStatusOrderByCreatedAtDesc(UUID projectId, ReportStatus status, Pageable pageable);

    /**
     * 根据生成者查询报表
     * 
     * @param generatedBy 生成者ID
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> findByGeneratedByOrderByCreatedAtDesc(UUID generatedBy, Pageable pageable);

    /**
     * 查询指定时间范围内的报表
     * 
     * @param projectId 项目ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.projectId = :projectId " +
           "AND r.periodStart >= :startTime AND r.periodEnd <= :endTime " +
           "ORDER BY r.createdAt DESC")
    Page<ProjectReport> findByProjectIdAndPeriodRange(@Param("projectId") UUID projectId,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime,
                                                     Pageable pageable);

    /**
     * 查询最新的报表
     * 
     * @param projectId 项目ID
     * @param reportType 报表类型
     * @return 最新报表
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.projectId = :projectId AND r.reportType = :reportType " +
           "AND r.status = 'COMPLETED' ORDER BY r.createdAt DESC LIMIT 1")
    Optional<ProjectReport> findLatestReport(@Param("projectId") UUID projectId, @Param("reportType") ReportType reportType);

    /**
     * 查询待生成的报表
     * 
     * @param limit 限制数量
     * @return 待生成报表列表
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.status = 'PENDING' " +
           "ORDER BY r.createdAt ASC LIMIT :limit")
    List<ProjectReport> findPendingReports(@Param("limit") int limit);

    /**
     * 查询生成失败的报表
     * 
     * @param retryBefore 重试时间之前
     * @return 失败报表列表
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.status = 'FAILED' " +
           "AND r.updatedAt < :retryBefore ORDER BY r.createdAt ASC")
    List<ProjectReport> findFailedReportsForRetry(@Param("retryBefore") LocalDateTime retryBefore);

    /**
     * 查询已过期的报表
     * 
     * @param currentTime 当前时间
     * @return 过期报表列表
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.expiresAt IS NOT NULL AND r.expiresAt < :currentTime")
    List<ProjectReport> findExpiredReports(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询需要自动生成的报表
     * 
     * @param currentTime 当前时间
     * @return 需要生成的报表列表
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.isAutoGenerated = true " +
           "AND r.nextGenerationTime IS NOT NULL AND r.nextGenerationTime <= :currentTime")
    List<ProjectReport> findReportsForAutoGeneration(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 统计项目报表数量
     * 
     * @param projectId 项目ID
     * @return 报表数量统计
     */
    @Query("SELECT r.reportType, COUNT(r) FROM ProjectReport r WHERE r.projectId = :projectId " +
           "GROUP BY r.reportType ORDER BY COUNT(r) DESC")
    List<Object[]> getReportCountByType(@Param("projectId") UUID projectId);

    /**
     * 统计报表状态分布
     * 
     * @param projectId 项目ID
     * @return 状态分布统计
     */
    @Query("SELECT r.status, COUNT(r) FROM ProjectReport r WHERE r.projectId = :projectId " +
           "GROUP BY r.status ORDER BY COUNT(r) DESC")
    List<Object[]> getReportStatusDistribution(@Param("projectId") UUID projectId);

    /**
     * 查询报表生成趋势
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @return 生成趋势数据
     */
    @Query("SELECT DATE(r.createdAt) as date, COUNT(r) as count " +
           "FROM ProjectReport r WHERE r.projectId = :projectId " +
           "AND r.createdAt >= :startDate " +
           "GROUP BY DATE(r.createdAt) ORDER BY DATE(r.createdAt)")
    List<Object[]> getReportGenerationTrend(@Param("projectId") UUID projectId, 
                                           @Param("startDate") LocalDateTime startDate);

    /**
     * 查询热门报表
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 热门报表列表
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.projectId = :projectId " +
           "AND r.status = 'COMPLETED' ORDER BY r.downloadCount DESC, r.createdAt DESC LIMIT :limit")
    List<ProjectReport> findPopularReports(@Param("projectId") UUID projectId, @Param("limit") int limit);

    /**
     * 查询最近的报表
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 最近报表列表
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.projectId = :projectId " +
           "ORDER BY r.createdAt DESC LIMIT :limit")
    List<ProjectReport> findRecentReports(@Param("projectId") UUID projectId, @Param("limit") int limit);

    /**
     * 搜索报表
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param reportType 报表类型
     * @param status 报表状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    @Query("SELECT r FROM ProjectReport r WHERE r.projectId = :projectId " +
           "AND (:keyword IS NULL OR r.title LIKE %:keyword% OR r.description LIKE %:keyword%) " +
           "AND (:reportType IS NULL OR r.reportType = :reportType) " +
           "AND (:status IS NULL OR r.status = :status) " +
           "AND (:startDate IS NULL OR r.createdAt >= :startDate) " +
           "AND (:endDate IS NULL OR r.createdAt <= :endDate) " +
           "ORDER BY r.createdAt DESC")
    Page<ProjectReport> searchReports(@Param("projectId") UUID projectId,
                                     @Param("keyword") String keyword,
                                     @Param("reportType") ReportType reportType,
                                     @Param("status") ReportStatus status,
                                     @Param("startDate") LocalDateTime startDate,
                                     @Param("endDate") LocalDateTime endDate,
                                     Pageable pageable);

    /**
     * 统计报表文件总大小
     * 
     * @param projectId 项目ID
     * @return 总大小（字节）
     */
    @Query("SELECT COALESCE(SUM(r.fileSize), 0) FROM ProjectReport r WHERE r.projectId = :projectId")
    long getTotalFileSize(@Param("projectId") UUID projectId);

    /**
     * 统计用户生成的报表数量
     * 
     * @param generatedBy 生成者ID
     * @return 报表数量
     */
    long countByGeneratedBy(UUID generatedBy);

    /**
     * 查询平均生成时间
     * 
     * @param projectId 项目ID
     * @param reportType 报表类型
     * @return 平均生成时间（毫秒）
     */
    @Query("SELECT AVG(r.generationDuration) FROM ProjectReport r WHERE r.projectId = :projectId " +
           "AND (:reportType IS NULL OR r.reportType = :reportType) " +
           "AND r.generationDuration IS NOT NULL")
    Double getAverageGenerationTime(@Param("projectId") UUID projectId, @Param("reportType") ReportType reportType);

    /**
     * 查询生成成功率
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @return 成功率统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN r.status = 'COMPLETED' THEN 1 END) as completed, " +
           "COUNT(CASE WHEN r.status = 'FAILED' THEN 1 END) as failed, " +
           "COUNT(r) as total " +
           "FROM ProjectReport r WHERE r.projectId = :projectId " +
           "AND (:startDate IS NULL OR r.createdAt >= :startDate)")
    Object[] getGenerationSuccessRate(@Param("projectId") UUID projectId, @Param("startDate") LocalDateTime startDate);

    /**
     * 删除过期的报表
     * 
     * @param expiredBefore 过期时间之前
     * @return 删除数量
     */
    @Query("DELETE FROM ProjectReport r WHERE r.expiresAt IS NOT NULL AND r.expiresAt < :expiredBefore")
    int deleteExpiredReports(@Param("expiredBefore") LocalDateTime expiredBefore);

    /**
     * 更新下载次数
     * 
     * @param reportId 报表ID
     * @return 更新数量
     */
    @Query("UPDATE ProjectReport r SET r.downloadCount = r.downloadCount + 1 WHERE r.id = :reportId")
    int incrementDownloadCount(@Param("reportId") UUID reportId);

    /**
     * 查询项目报表摘要
     * 
     * @param projectId 项目ID
     * @return 报表摘要
     */
    @Query("SELECT " +
           "COUNT(r) as totalReports, " +
           "COUNT(CASE WHEN r.status = 'COMPLETED' THEN 1 END) as completedReports, " +
           "COUNT(CASE WHEN r.status = 'PENDING' THEN 1 END) as pendingReports, " +
           "COUNT(CASE WHEN r.status = 'FAILED' THEN 1 END) as failedReports, " +
           "COALESCE(SUM(r.fileSize), 0) as totalFileSize, " +
           "COALESCE(SUM(r.downloadCount), 0) as totalDownloads " +
           "FROM ProjectReport r WHERE r.projectId = :projectId")
    Object[] getProjectReportSummary(@Param("projectId") UUID projectId);

    /**
     * 查询用户报表统计
     * 
     * @param generatedBy 生成者ID
     * @return 用户报表统计
     */
    @Query("SELECT r.reportType, COUNT(r), SUM(r.downloadCount) " +
           "FROM ProjectReport r WHERE r.generatedBy = :generatedBy " +
           "GROUP BY r.reportType ORDER BY COUNT(r) DESC")
    List<Object[]> getUserReportStatistics(@Param("generatedBy") UUID generatedBy);
}
