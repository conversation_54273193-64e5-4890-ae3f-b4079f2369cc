package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.ActivityLog;
import com.aipm.projectmanagement.entity.ActivityLog.ActionType;
import com.aipm.projectmanagement.entity.ActivityLog.TargetType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 活动日志数据访问接口
 * 
 * 提供活动日志相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface ActivityLogRepository extends JpaRepository<ActivityLog, UUID> {

    /**
     * 根据项目ID查询活动日志
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByProjectIdOrderByCreatedAtDesc(UUID projectId, Pageable pageable);

    /**
     * 根据用户ID查询活动日志
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByUserIdOrderByCreatedAtDesc(UUID userId, Pageable pageable);

    /**
     * 根据目标类型和目标ID查询活动日志
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByTargetTypeAndTargetIdOrderByCreatedAtDesc(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 根据活动类型查询活动日志
     * 
     * @param projectId 项目ID
     * @param actionType 活动类型
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    Page<ActivityLog> findByProjectIdAndActionTypeOrderByCreatedAtDesc(UUID projectId, ActionType actionType, Pageable pageable);

    /**
     * 查询项目最近的活动日志
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 最近活动日志列表
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.projectId = :projectId " +
           "ORDER BY a.createdAt DESC LIMIT :limit")
    List<ActivityLog> findRecentActivities(@Param("projectId") UUID projectId, @Param("limit") int limit);

    /**
     * 查询用户最近的活动日志
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近活动日志列表
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.userId = :userId " +
           "ORDER BY a.createdAt DESC LIMIT :limit")
    List<ActivityLog> findUserRecentActivities(@Param("userId") UUID userId, @Param("limit") int limit);

    /**
     * 搜索活动日志
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param actionType 活动类型
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 活动日志分页结果
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.projectId = :projectId " +
           "AND (:keyword IS NULL OR a.description LIKE %:keyword% OR a.targetName LIKE %:keyword%) " +
           "AND (:actionType IS NULL OR a.actionType = :actionType) " +
           "AND (:userId IS NULL OR a.userId = :userId) " +
           "AND (:startDate IS NULL OR a.createdAt >= :startDate) " +
           "AND (:endDate IS NULL OR a.createdAt <= :endDate) " +
           "ORDER BY a.createdAt DESC")
    Page<ActivityLog> searchActivities(@Param("projectId") UUID projectId,
                                      @Param("keyword") String keyword,
                                      @Param("actionType") ActionType actionType,
                                      @Param("userId") UUID userId,
                                      @Param("startDate") LocalDateTime startDate,
                                      @Param("endDate") LocalDateTime endDate,
                                      Pageable pageable);

    /**
     * 统计项目活动类型分布
     * 
     * @param projectId 项目ID
     * @return 活动类型统计
     */
    @Query("SELECT a.actionType, COUNT(a) FROM ActivityLog a WHERE a.projectId = :projectId " +
           "GROUP BY a.actionType ORDER BY COUNT(a) DESC")
    List<Object[]> getActivityTypeStatistics(@Param("projectId") UUID projectId);

    /**
     * 统计用户活动分布
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @return 用户活动统计
     */
    @Query("SELECT a.userId, COUNT(a) FROM ActivityLog a WHERE a.projectId = :projectId " +
           "AND (:startDate IS NULL OR a.createdAt >= :startDate) " +
           "GROUP BY a.userId ORDER BY COUNT(a) DESC")
    List<Object[]> getUserActivityStatistics(@Param("projectId") UUID projectId, 
                                            @Param("startDate") LocalDateTime startDate);

    /**
     * 查询活动时间线数据
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @return 时间线数据
     */
    @Query("SELECT DATE(a.createdAt) as date, COUNT(a) as count " +
           "FROM ActivityLog a WHERE a.projectId = :projectId " +
           "AND a.createdAt >= :startDate " +
           "GROUP BY DATE(a.createdAt) ORDER BY DATE(a.createdAt)")
    List<Object[]> getActivityTimeline(@Param("projectId") UUID projectId, 
                                      @Param("startDate") LocalDateTime startDate);

    /**
     * 查询目标的活动历史
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 活动历史列表
     */
    @Query("SELECT a FROM ActivityLog a WHERE a.targetType = :targetType AND a.targetId = :targetId " +
           "ORDER BY a.createdAt ASC")
    List<ActivityLog> getTargetActivityHistory(@Param("targetType") TargetType targetType, 
                                              @Param("targetId") UUID targetId);

    /**
     * 统计指定时间范围内的活动数量
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 活动数量
     */
    @Query("SELECT COUNT(a) FROM ActivityLog a WHERE a.projectId = :projectId " +
           "AND a.createdAt BETWEEN :startDate AND :endDate")
    long countActivitiesByDateRange(@Param("projectId") UUID projectId,
                                   @Param("startDate") LocalDateTime startDate,
                                   @Param("endDate") LocalDateTime endDate);

    /**
     * 查询最活跃的用户
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param limit 限制数量
     * @return 活跃用户列表
     */
    @Query("SELECT a.userId, COUNT(a) as activityCount " +
           "FROM ActivityLog a WHERE a.projectId = :projectId " +
           "AND (:startDate IS NULL OR a.createdAt >= :startDate) " +
           "GROUP BY a.userId ORDER BY activityCount DESC LIMIT :limit")
    List<Object[]> getMostActiveUsers(@Param("projectId") UUID projectId, 
                                     @Param("startDate") LocalDateTime startDate, 
                                     @Param("limit") int limit);

    /**
     * 查询活动热力图数据
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @return 热力图数据
     */
    @Query("SELECT HOUR(a.createdAt) as hour, DAYOFWEEK(a.createdAt) as dayOfWeek, COUNT(a) as count " +
           "FROM ActivityLog a WHERE a.projectId = :projectId " +
           "AND a.createdAt >= :startDate " +
           "GROUP BY HOUR(a.createdAt), DAYOFWEEK(a.createdAt) " +
           "ORDER BY dayOfWeek, hour")
    List<Object[]> getActivityHeatmapData(@Param("projectId") UUID projectId, 
                                         @Param("startDate") LocalDateTime startDate);

    /**
     * 清理旧的活动日志
     * 
     * @param beforeDate 清理日期之前的记录
     * @return 清理数量
     */
    @Query("DELETE FROM ActivityLog a WHERE a.createdAt < :beforeDate")
    int cleanupOldActivities(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 查询项目活动摘要
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 活动摘要
     */
    @Query("SELECT " +
           "COUNT(a) as totalActivities, " +
           "COUNT(DISTINCT a.userId) as activeUsers, " +
           "COUNT(DISTINCT a.targetId) as affectedTargets " +
           "FROM ActivityLog a WHERE a.projectId = :projectId " +
           "AND a.createdAt >= :startDate")
    Object[] getActivitySummary(@Param("projectId") UUID projectId, 
                               @Param("startDate") LocalDateTime startDate);

    /**
     * 查询用户在项目中的活动统计
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param startDate 开始日期
     * @return 用户活动统计
     */
    @Query("SELECT a.actionType, COUNT(a) " +
           "FROM ActivityLog a WHERE a.projectId = :projectId AND a.userId = :userId " +
           "AND (:startDate IS NULL OR a.createdAt >= :startDate) " +
           "GROUP BY a.actionType ORDER BY COUNT(a) DESC")
    List<Object[]> getUserActivityInProject(@Param("projectId") UUID projectId, 
                                           @Param("userId") UUID userId, 
                                           @Param("startDate") LocalDateTime startDate);

    /**
     * 查询项目活动趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 活动趋势数据
     */
    @Query("SELECT DATE(a.createdAt) as date, " +
           "COUNT(a) as totalCount, " +
           "COUNT(DISTINCT a.userId) as userCount, " +
           "COUNT(DISTINCT CASE WHEN a.actionType LIKE 'TASK_%' THEN a.targetId END) as taskCount " +
           "FROM ActivityLog a WHERE a.projectId = :projectId " +
           "AND a.createdAt >= :startDate " +
           "GROUP BY DATE(a.createdAt) ORDER BY DATE(a.createdAt)")
    List<Object[]> getActivityTrend(@Param("projectId") UUID projectId, 
                                   @Param("startDate") LocalDateTime startDate);
}
