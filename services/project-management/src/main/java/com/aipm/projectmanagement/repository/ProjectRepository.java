package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.Project;
import com.aipm.projectmanagement.entity.ProjectStatus;
import com.aipm.projectmanagement.entity.TaskPriority;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 项目数据访问接口
 * 
 * 提供项目相关的数据库操作方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface ProjectRepository extends JpaRepository<Project, UUID> {

    /**
     * 根据项目名称查找项目
     * 
     * @param name 项目名称
     * @return 项目列表
     */
    List<Project> findByNameContainingIgnoreCase(String name);

    /**
     * 根据项目状态查找项目
     * 
     * @param status 项目状态
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByStatus(ProjectStatus status, Pageable pageable);

    /**
     * 根据项目负责人查找项目
     * 
     * @param ownerId 负责人ID
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    Page<Project> findByOwnerId(UUID ownerId, Pageable pageable);

    /**
     * 查找用户参与的所有项目
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p " +
           "LEFT JOIN p.members m " +
           "WHERE p.ownerId = :userId OR (m.userId = :userId AND m.isActive = true)")
    Page<Project> findProjectsByUserId(@Param("userId") UUID userId, Pageable pageable);

    /**
     * 根据多个条件搜索项目
     * 
     * @param keyword 关键词（搜索名称和描述）
     * @param status 项目状态
     * @param ownerId 负责人ID
     * @param priority 优先级
     * @param isArchived 是否已归档
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT p FROM Project p WHERE " +
           "(:keyword IS NULL OR LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           " LOWER(p.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:status IS NULL OR p.status = :status) AND " +
           "(:ownerId IS NULL OR p.ownerId = :ownerId) AND " +
           "(:priority IS NULL OR p.priority = :priority) AND " +
           "(:isArchived IS NULL OR p.isArchived = :isArchived)")
    Page<Project> searchProjects(@Param("keyword") String keyword,
                                 @Param("status") ProjectStatus status,
                                 @Param("ownerId") UUID ownerId,
                                 @Param("priority") TaskPriority priority,
                                 @Param("isArchived") Boolean isArchived,
                                 Pageable pageable);

    /**
     * 查找即将到期的项目
     * 
     * @param endDate 截止日期
     * @param statuses 项目状态列表
     * @return 项目列表
     */
    @Query("SELECT p FROM Project p WHERE " +
           "p.endDate <= :endDate AND p.status IN :statuses")
    List<Project> findProjectsDueSoon(@Param("endDate") LocalDate endDate,
                                     @Param("statuses") List<ProjectStatus> statuses);

    /**
     * 查找已过期的项目
     * 
     * @param currentDate 当前日期
     * @param statuses 项目状态列表
     * @return 项目列表
     */
    @Query("SELECT p FROM Project p WHERE " +
           "p.endDate < :currentDate AND p.status IN :statuses")
    List<Project> findOverdueProjects(@Param("currentDate") LocalDate currentDate,
                                     @Param("statuses") List<ProjectStatus> statuses);

    /**
     * 统计用户的项目数量
     * 
     * @param userId 用户ID
     * @return 项目数量统计
     */
    @Query("SELECT COUNT(DISTINCT p) FROM Project p " +
           "LEFT JOIN p.members m " +
           "WHERE p.ownerId = :userId OR (m.userId = :userId AND m.isActive = true)")
    long countProjectsByUserId(@Param("userId") UUID userId);

    /**
     * 根据状态统计项目数量
     * 
     * @param status 项目状态
     * @return 项目数量
     */
    long countByStatus(ProjectStatus status);

    /**
     * 统计活跃项目数量
     * 
     * @return 活跃项目数量
     */
    @Query("SELECT COUNT(p) FROM Project p WHERE " +
           "p.status IN ('PLANNING', 'IN_PROGRESS') AND p.isArchived = false")
    long countActiveProjects();

    /**
     * 查找项目模板
     * 
     * @param pageable 分页参数
     * @return 项目模板分页结果
     */
    Page<Project> findByIsTemplateTrue(Pageable pageable);

    /**
     * 根据标签查找项目
     * 
     * @param tag 标签
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT p FROM Project p WHERE " +
           "p.tags IS NOT NULL AND p.tags LIKE CONCAT('%', :tag, '%')")
    Page<Project> findByTag(@Param("tag") String tag, Pageable pageable);

    /**
     * 查找最近创建的项目
     * 
     * @param limit 限制数量
     * @return 项目列表
     */
    @Query("SELECT p FROM Project p ORDER BY p.createdAt DESC")
    List<Project> findRecentProjects(Pageable pageable);

    /**
     * 查找最近更新的项目
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p " +
           "LEFT JOIN p.members m " +
           "WHERE (p.ownerId = :userId OR (m.userId = :userId AND m.isActive = true)) " +
           "ORDER BY p.updatedAt DESC")
    Page<Project> findRecentlyUpdatedProjects(@Param("userId") UUID userId, Pageable pageable);

    /**
     * 检查项目名称是否已存在
     * 
     * @param name 项目名称
     * @param excludeId 排除的项目ID（用于更新时检查）
     * @return 是否存在
     */
    @Query("SELECT COUNT(p) > 0 FROM Project p WHERE " +
           "LOWER(p.name) = LOWER(:name) AND (:excludeId IS NULL OR p.id != :excludeId)")
    boolean existsByNameIgnoreCase(@Param("name") String name, @Param("excludeId") UUID excludeId);

    /**
     * 查找用户有权限访问的项目
     * 
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param pageable 分页参数
     * @return 项目分页结果
     */
    @Query("SELECT DISTINCT p FROM Project p " +
           "LEFT JOIN p.members m " +
           "WHERE (p.ownerId = :userId OR (m.userId = :userId AND m.isActive = true)) " +
           "AND (:keyword IS NULL OR LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Project> findAccessibleProjects(@Param("userId") UUID userId,
                                        @Param("keyword") String keyword,
                                        Pageable pageable);

    /**
     * 获取项目统计信息
     * 
     * @return 统计信息对象数组 [status, count]
     */
    @Query("SELECT p.status, COUNT(p) FROM Project p " +
           "WHERE p.isArchived = false GROUP BY p.status")
    List<Object[]> getProjectStatistics();

    /**
     * 获取用户项目统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息对象数组 [status, count]
     */
    @Query("SELECT p.status, COUNT(DISTINCT p) FROM Project p " +
           "LEFT JOIN p.members m " +
           "WHERE (p.ownerId = :userId OR (m.userId = :userId AND m.isActive = true)) " +
           "AND p.isArchived = false " +
           "GROUP BY p.status")
    List<Object[]> getUserProjectStatistics(@Param("userId") UUID userId);

    /**
     * 查找指定日期范围内创建的项目
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 项目列表
     */
    @Query("SELECT p FROM Project p WHERE " +
           "p.createdAt >= :startDate AND p.createdAt <= :endDate " +
           "ORDER BY p.createdAt DESC")
    List<Project> findProjectsCreatedBetween(@Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate);
}
