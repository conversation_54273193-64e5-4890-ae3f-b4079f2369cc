package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 项目成员实体类
 * 
 * 表示项目中的成员信息，包括角色和权限
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "project_members", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"project_id", "user_id"}),
       indexes = {
           @Index(name = "idx_project_members_project_id", columnList = "project_id"),
           @Index(name = "idx_project_members_user_id", columnList = "user_id"),
           @Index(name = "idx_project_members_role", columnList = "role")
       })
@EntityListeners(AuditingEntityListener.class)
public class ProjectMember {

    /**
     * 项目成员角色枚举
     */
    public enum Role {
        /**
         * 项目经理 - 拥有项目的完全管理权限
         */
        PROJECT_MANAGER("项目经理", "拥有项目的完全管理权限"),
        
        /**
         * 技术负责人 - 负责技术决策和架构
         */
        TECH_LEAD("技术负责人", "负责技术决策和架构设计"),
        
        /**
         * 开发者 - 参与项目开发工作
         */
        DEVELOPER("开发者", "参与项目的开发工作"),
        
        /**
         * 测试工程师 - 负责项目测试工作
         */
        TESTER("测试工程师", "负责项目的测试工作"),
        
        /**
         * 设计师 - 负责UI/UX设计
         */
        DESIGNER("设计师", "负责项目的UI/UX设计"),
        
        /**
         * 产品经理 - 负责产品需求和规划
         */
        PRODUCT_MANAGER("产品经理", "负责产品需求分析和规划"),
        
        /**
         * 观察者 - 只能查看项目信息
         */
        OBSERVER("观察者", "只能查看项目信息，无编辑权限");

        private final String displayName;
        private final String description;

        Role(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 检查是否有管理权限
         */
        public boolean hasManagementPermission() {
            return this == PROJECT_MANAGER || this == TECH_LEAD;
        }

        /**
         * 检查是否有编辑权限
         */
        public boolean hasEditPermission() {
            return this != OBSERVER;
        }

        /**
         * 检查是否可以分配任务
         */
        public boolean canAssignTasks() {
            return this == PROJECT_MANAGER || this == TECH_LEAD || this == PRODUCT_MANAGER;
        }
    }

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 关联的项目
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    @NotNull(message = "项目不能为空")
    private Project project;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private UUID userId;

    /**
     * 项目中的角色
     */
    @NotNull(message = "角色不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false, length = 30)
    private Role role;

    /**
     * 是否为活跃成员
     */
    @Column(name = "is_active", columnDefinition = "BOOLEAN DEFAULT TRUE")
    private Boolean isActive = true;

    /**
     * 加入项目时间
     */
    @CreatedDate
    @Column(name = "joined_at", nullable = false, updatable = false)
    private LocalDateTime joinedAt;

    /**
     * 最后更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 添加成员的用户ID
     */
    @Column(name = "added_by")
    private UUID addedBy;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public ProjectMember() {
    }

    /**
     * 构造函数
     * 
     * @param project 项目
     * @param userId 用户ID
     * @param role 角色
     */
    public ProjectMember(Project project, UUID userId, Role role) {
        this.project = project;
        this.userId = userId;
        this.role = role;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查是否有管理权限
     * 
     * @return true表示有管理权限
     */
    public boolean hasManagementPermission() {
        return role != null && role.hasManagementPermission() && Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查是否有编辑权限
     * 
     * @return true表示有编辑权限
     */
    public boolean hasEditPermission() {
        return role != null && role.hasEditPermission() && Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查是否可以分配任务
     * 
     * @return true表示可以分配任务
     */
    public boolean canAssignTasks() {
        return role != null && role.canAssignTasks() && Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查是否为项目经理
     * 
     * @return true表示是项目经理
     */
    public boolean isProjectManager() {
        return role == Role.PROJECT_MANAGER && Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查是否为技术负责人
     * 
     * @return true表示是技术负责人
     */
    public boolean isTechLead() {
        return role == Role.TECH_LEAD && Boolean.TRUE.equals(isActive);
    }

    /**
     * 激活成员
     */
    public void activate() {
        this.isActive = true;
    }

    /**
     * 停用成员
     */
    public void deactivate() {
        this.isActive = false;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getJoinedAt() {
        return joinedAt;
    }

    public void setJoinedAt(LocalDateTime joinedAt) {
        this.joinedAt = joinedAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public UUID getAddedBy() {
        return addedBy;
    }

    public void setAddedBy(UUID addedBy) {
        this.addedBy = addedBy;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ProjectMember that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "ProjectMember{" +
                "id=" + id +
                ", userId=" + userId +
                ", role=" + role +
                ", isActive=" + isActive +
                ", joinedAt=" + joinedAt +
                '}';
    }
}
