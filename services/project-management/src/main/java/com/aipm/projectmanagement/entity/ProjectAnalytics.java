package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 项目分析数据实体类
 * 
 * 存储项目的各种分析指标和统计数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
@Entity
@Table(name = "project_analytics", indexes = {
    @Index(name = "idx_analytics_project", columnList = "project_id"),
    @Index(name = "idx_analytics_date", columnList = "analytics_date"),
    @Index(name = "idx_analytics_type", columnList = "analytics_type"),
    @Index(name = "idx_analytics_project_date", columnList = "project_id, analytics_date"),
    @Index(name = "idx_analytics_created", columnList = "created_at")
})
public class ProjectAnalytics {

    /**
     * 分析类型枚举
     */
    public enum AnalyticsType {
        DAILY("日度分析", "每日项目数据分析"),
        WEEKLY("周度分析", "每周项目数据分析"),
        MONTHLY("月度分析", "每月项目数据分析"),
        SPRINT("Sprint分析", "Sprint周期数据分析"),
        MILESTONE("里程碑分析", "项目里程碑数据分析");

        private final String displayName;
        private final String description;

        AnalyticsType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 分析数据唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 所属项目ID
     */
    @Column(name = "project_id", nullable = false)
    private UUID projectId;

    /**
     * 分析类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "analytics_type", nullable = false)
    private AnalyticsType analyticsType;

    /**
     * 分析日期
     */
    @Column(name = "analytics_date", nullable = false)
    private LocalDate analyticsDate;

    // ============================================================================
    // 任务相关指标
    // ============================================================================

    /**
     * 总任务数
     */
    @Column(name = "total_tasks")
    private Integer totalTasks = 0;

    /**
     * 已完成任务数
     */
    @Column(name = "completed_tasks")
    private Integer completedTasks = 0;

    /**
     * 进行中任务数
     */
    @Column(name = "in_progress_tasks")
    private Integer inProgressTasks = 0;

    /**
     * 待开始任务数
     */
    @Column(name = "todo_tasks")
    private Integer todoTasks = 0;

    /**
     * 已阻塞任务数
     */
    @Column(name = "blocked_tasks")
    private Integer blockedTasks = 0;

    /**
     * 新增任务数
     */
    @Column(name = "new_tasks")
    private Integer newTasks = 0;

    /**
     * 任务完成率
     */
    @Column(name = "task_completion_rate", precision = 5, scale = 2)
    private BigDecimal taskCompletionRate = BigDecimal.ZERO;

    /**
     * 平均任务完成时间（小时）
     */
    @Column(name = "avg_task_completion_time", precision = 8, scale = 2)
    private BigDecimal avgTaskCompletionTime = BigDecimal.ZERO;

    // ============================================================================
    // 团队相关指标
    // ============================================================================

    /**
     * 活跃成员数
     */
    @Column(name = "active_members")
    private Integer activeMembers = 0;

    /**
     * 总成员数
     */
    @Column(name = "total_members")
    private Integer totalMembers = 0;

    /**
     * 团队效率分数
     */
    @Column(name = "team_efficiency_score", precision = 5, scale = 2)
    private BigDecimal teamEfficiencyScore = BigDecimal.ZERO;

    /**
     * 团队协作分数
     */
    @Column(name = "team_collaboration_score", precision = 5, scale = 2)
    private BigDecimal teamCollaborationScore = BigDecimal.ZERO;

    // ============================================================================
    // 时间相关指标
    // ============================================================================

    /**
     * 计划工时
     */
    @Column(name = "planned_hours", precision = 8, scale = 2)
    private BigDecimal plannedHours = BigDecimal.ZERO;

    /**
     * 实际工时
     */
    @Column(name = "actual_hours", precision = 8, scale = 2)
    private BigDecimal actualHours = BigDecimal.ZERO;

    /**
     * 工时偏差率
     */
    @Column(name = "time_variance_rate", precision = 5, scale = 2)
    private BigDecimal timeVarianceRate = BigDecimal.ZERO;

    // ============================================================================
    // 质量相关指标
    // ============================================================================

    /**
     * 缺陷数量
     */
    @Column(name = "bug_count")
    private Integer bugCount = 0;

    /**
     * 已修复缺陷数
     */
    @Column(name = "fixed_bugs")
    private Integer fixedBugs = 0;

    /**
     * 代码审查通过率
     */
    @Column(name = "code_review_pass_rate", precision = 5, scale = 2)
    private BigDecimal codeReviewPassRate = BigDecimal.ZERO;

    /**
     * 质量分数
     */
    @Column(name = "quality_score", precision = 5, scale = 2)
    private BigDecimal qualityScore = BigDecimal.ZERO;

    // ============================================================================
    // 进度相关指标
    // ============================================================================

    /**
     * 项目进度百分比
     */
    @Column(name = "project_progress", precision = 5, scale = 2)
    private BigDecimal projectProgress = BigDecimal.ZERO;

    /**
     * 计划进度百分比
     */
    @Column(name = "planned_progress", precision = 5, scale = 2)
    private BigDecimal plannedProgress = BigDecimal.ZERO;

    /**
     * 进度偏差
     */
    @Column(name = "progress_variance", precision = 5, scale = 2)
    private BigDecimal progressVariance = BigDecimal.ZERO;

    /**
     * 预计完成日期
     */
    @Column(name = "estimated_completion_date")
    private LocalDate estimatedCompletionDate;

    // ============================================================================
    // 活动相关指标
    // ============================================================================

    /**
     * 评论数量
     */
    @Column(name = "comment_count")
    private Integer commentCount = 0;

    /**
     * 附件数量
     */
    @Column(name = "attachment_count")
    private Integer attachmentCount = 0;

    /**
     * 活动日志数量
     */
    @Column(name = "activity_count")
    private Integer activityCount = 0;

    /**
     * 通知数量
     */
    @Column(name = "notification_count")
    private Integer notificationCount = 0;

    // ============================================================================
    // 风险相关指标
    // ============================================================================

    /**
     * 风险等级（1-5，5最高）
     */
    @Column(name = "risk_level")
    private Integer riskLevel = 1;

    /**
     * 延期风险分数
     */
    @Column(name = "delay_risk_score", precision = 5, scale = 2)
    private BigDecimal delayRiskScore = BigDecimal.ZERO;

    /**
     * 资源风险分数
     */
    @Column(name = "resource_risk_score", precision = 5, scale = 2)
    private BigDecimal resourceRiskScore = BigDecimal.ZERO;

    // ============================================================================
    // 其他字段
    // ============================================================================

    /**
     * 扩展数据（JSON格式）
     */
    @Column(name = "extended_data", columnDefinition = "TEXT")
    private String extendedData;

    /**
     * 备注
     */
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public ProjectAnalytics() {
    }

    /**
     * 基础构造函数
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param analyticsDate 分析日期
     */
    public ProjectAnalytics(UUID projectId, AnalyticsType analyticsType, LocalDate analyticsDate) {
        this.projectId = projectId;
        this.analyticsType = analyticsType;
        this.analyticsDate = analyticsDate;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 计算任务完成率
     */
    public void calculateTaskCompletionRate() {
        if (totalTasks != null && totalTasks > 0 && completedTasks != null) {
            this.taskCompletionRate = BigDecimal.valueOf(completedTasks)
                    .divide(BigDecimal.valueOf(totalTasks), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        } else {
            this.taskCompletionRate = BigDecimal.ZERO;
        }
    }

    /**
     * 计算工时偏差率
     */
    public void calculateTimeVarianceRate() {
        if (plannedHours != null && plannedHours.compareTo(BigDecimal.ZERO) > 0 && actualHours != null) {
            this.timeVarianceRate = actualHours.subtract(plannedHours)
                    .divide(plannedHours, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        } else {
            this.timeVarianceRate = BigDecimal.ZERO;
        }
    }

    /**
     * 计算进度偏差
     */
    public void calculateProgressVariance() {
        if (projectProgress != null && plannedProgress != null) {
            this.progressVariance = projectProgress.subtract(plannedProgress);
        } else {
            this.progressVariance = BigDecimal.ZERO;
        }
    }

    /**
     * 计算团队效率分数
     */
    public void calculateTeamEfficiencyScore() {
        // 基于任务完成率、工时偏差率等计算团队效率
        BigDecimal completionScore = taskCompletionRate != null ? taskCompletionRate : BigDecimal.ZERO;
        BigDecimal timeScore = BigDecimal.valueOf(100).subtract(
            timeVarianceRate != null ? timeVarianceRate.abs() : BigDecimal.ZERO);
        
        this.teamEfficiencyScore = completionScore.add(timeScore)
                .divide(BigDecimal.valueOf(2), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算质量分数
     */
    public void calculateQualityScore() {
        // 基于缺陷率、代码审查通过率等计算质量分数
        BigDecimal bugScore = BigDecimal.valueOf(100);
        if (totalTasks != null && totalTasks > 0 && bugCount != null) {
            BigDecimal bugRate = BigDecimal.valueOf(bugCount)
                    .divide(BigDecimal.valueOf(totalTasks), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            bugScore = BigDecimal.valueOf(100).subtract(bugRate);
        }
        
        BigDecimal reviewScore = codeReviewPassRate != null ? codeReviewPassRate : BigDecimal.valueOf(80);
        
        this.qualityScore = bugScore.add(reviewScore)
                .divide(BigDecimal.valueOf(2), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算风险等级
     */
    public void calculateRiskLevel() {
        int risk = 1;
        
        // 基于进度偏差计算风险
        if (progressVariance != null) {
            if (progressVariance.compareTo(BigDecimal.valueOf(-20)) < 0) {
                risk += 2; // 进度严重滞后
            } else if (progressVariance.compareTo(BigDecimal.valueOf(-10)) < 0) {
                risk += 1; // 进度轻微滞后
            }
        }
        
        // 基于质量分数计算风险
        if (qualityScore != null && qualityScore.compareTo(BigDecimal.valueOf(60)) < 0) {
            risk += 1; // 质量较低
        }
        
        // 基于阻塞任务计算风险
        if (blockedTasks != null && totalTasks != null && totalTasks > 0) {
            BigDecimal blockedRate = BigDecimal.valueOf(blockedTasks)
                    .divide(BigDecimal.valueOf(totalTasks), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            if (blockedRate.compareTo(BigDecimal.valueOf(20)) > 0) {
                risk += 1; // 阻塞任务较多
            }
        }
        
        this.riskLevel = Math.min(risk, 5); // 最高风险等级为5
    }

    /**
     * 更新所有计算字段
     */
    public void updateCalculatedFields() {
        calculateTaskCompletionRate();
        calculateTimeVarianceRate();
        calculateProgressVariance();
        calculateTeamEfficiencyScore();
        calculateQualityScore();
        calculateRiskLevel();
    }

    /**
     * 检查是否为高风险项目
     * 
     * @return true表示高风险
     */
    public boolean isHighRisk() {
        return riskLevel != null && riskLevel >= 4;
    }

    /**
     * 检查进度是否滞后
     * 
     * @return true表示进度滞后
     */
    public boolean isBehindSchedule() {
        return progressVariance != null && progressVariance.compareTo(BigDecimal.valueOf(-5)) < 0;
    }

    /**
     * 检查团队效率是否良好
     * 
     * @return true表示效率良好
     */
    public boolean isTeamEfficient() {
        return teamEfficiencyScore != null && teamEfficiencyScore.compareTo(BigDecimal.valueOf(80)) >= 0;
    }

    // ============================================================================
    // 静态工厂方法
    // ============================================================================

    /**
     * 创建日度分析数据
     */
    public static ProjectAnalytics createDailyAnalytics(UUID projectId, LocalDate date) {
        return new ProjectAnalytics(projectId, AnalyticsType.DAILY, date);
    }

    /**
     * 创建周度分析数据
     */
    public static ProjectAnalytics createWeeklyAnalytics(UUID projectId, LocalDate weekStart) {
        return new ProjectAnalytics(projectId, AnalyticsType.WEEKLY, weekStart);
    }

    /**
     * 创建月度分析数据
     */
    public static ProjectAnalytics createMonthlyAnalytics(UUID projectId, LocalDate monthStart) {
        return new ProjectAnalytics(projectId, AnalyticsType.MONTHLY, monthStart);
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getProjectId() {
        return projectId;
    }

    public void setProjectId(UUID projectId) {
        this.projectId = projectId;
    }

    public AnalyticsType getAnalyticsType() {
        return analyticsType;
    }

    public void setAnalyticsType(AnalyticsType analyticsType) {
        this.analyticsType = analyticsType;
    }

    public LocalDate getAnalyticsDate() {
        return analyticsDate;
    }

    public void setAnalyticsDate(LocalDate analyticsDate) {
        this.analyticsDate = analyticsDate;
    }

    public Integer getTotalTasks() {
        return totalTasks;
    }

    public void setTotalTasks(Integer totalTasks) {
        this.totalTasks = totalTasks;
    }

    public Integer getCompletedTasks() {
        return completedTasks;
    }

    public void setCompletedTasks(Integer completedTasks) {
        this.completedTasks = completedTasks;
    }

    public Integer getInProgressTasks() {
        return inProgressTasks;
    }

    public void setInProgressTasks(Integer inProgressTasks) {
        this.inProgressTasks = inProgressTasks;
    }

    public Integer getTodoTasks() {
        return todoTasks;
    }

    public void setTodoTasks(Integer todoTasks) {
        this.todoTasks = todoTasks;
    }

    public Integer getBlockedTasks() {
        return blockedTasks;
    }

    public void setBlockedTasks(Integer blockedTasks) {
        this.blockedTasks = blockedTasks;
    }

    public Integer getNewTasks() {
        return newTasks;
    }

    public void setNewTasks(Integer newTasks) {
        this.newTasks = newTasks;
    }

    public BigDecimal getTaskCompletionRate() {
        return taskCompletionRate;
    }

    public void setTaskCompletionRate(BigDecimal taskCompletionRate) {
        this.taskCompletionRate = taskCompletionRate;
    }

    public BigDecimal getAvgTaskCompletionTime() {
        return avgTaskCompletionTime;
    }

    public void setAvgTaskCompletionTime(BigDecimal avgTaskCompletionTime) {
        this.avgTaskCompletionTime = avgTaskCompletionTime;
    }

    public Integer getActiveMembers() {
        return activeMembers;
    }

    public void setActiveMembers(Integer activeMembers) {
        this.activeMembers = activeMembers;
    }

    public Integer getTotalMembers() {
        return totalMembers;
    }

    public void setTotalMembers(Integer totalMembers) {
        this.totalMembers = totalMembers;
    }

    public BigDecimal getTeamEfficiencyScore() {
        return teamEfficiencyScore;
    }

    public void setTeamEfficiencyScore(BigDecimal teamEfficiencyScore) {
        this.teamEfficiencyScore = teamEfficiencyScore;
    }

    public BigDecimal getTeamCollaborationScore() {
        return teamCollaborationScore;
    }

    public void setTeamCollaborationScore(BigDecimal teamCollaborationScore) {
        this.teamCollaborationScore = teamCollaborationScore;
    }

    public BigDecimal getPlannedHours() {
        return plannedHours;
    }

    public void setPlannedHours(BigDecimal plannedHours) {
        this.plannedHours = plannedHours;
    }

    public BigDecimal getActualHours() {
        return actualHours;
    }

    public void setActualHours(BigDecimal actualHours) {
        this.actualHours = actualHours;
    }

    public BigDecimal getTimeVarianceRate() {
        return timeVarianceRate;
    }

    public void setTimeVarianceRate(BigDecimal timeVarianceRate) {
        this.timeVarianceRate = timeVarianceRate;
    }

    public Integer getBugCount() {
        return bugCount;
    }

    public void setBugCount(Integer bugCount) {
        this.bugCount = bugCount;
    }

    public Integer getFixedBugs() {
        return fixedBugs;
    }

    public void setFixedBugs(Integer fixedBugs) {
        this.fixedBugs = fixedBugs;
    }

    public BigDecimal getCodeReviewPassRate() {
        return codeReviewPassRate;
    }

    public void setCodeReviewPassRate(BigDecimal codeReviewPassRate) {
        this.codeReviewPassRate = codeReviewPassRate;
    }

    public BigDecimal getQualityScore() {
        return qualityScore;
    }

    public void setQualityScore(BigDecimal qualityScore) {
        this.qualityScore = qualityScore;
    }

    public BigDecimal getProjectProgress() {
        return projectProgress;
    }

    public void setProjectProgress(BigDecimal projectProgress) {
        this.projectProgress = projectProgress;
    }

    public BigDecimal getPlannedProgress() {
        return plannedProgress;
    }

    public void setPlannedProgress(BigDecimal plannedProgress) {
        this.plannedProgress = plannedProgress;
    }

    public BigDecimal getProgressVariance() {
        return progressVariance;
    }

    public void setProgressVariance(BigDecimal progressVariance) {
        this.progressVariance = progressVariance;
    }

    public LocalDate getEstimatedCompletionDate() {
        return estimatedCompletionDate;
    }

    public void setEstimatedCompletionDate(LocalDate estimatedCompletionDate) {
        this.estimatedCompletionDate = estimatedCompletionDate;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public Integer getAttachmentCount() {
        return attachmentCount;
    }

    public void setAttachmentCount(Integer attachmentCount) {
        this.attachmentCount = attachmentCount;
    }

    public Integer getActivityCount() {
        return activityCount;
    }

    public void setActivityCount(Integer activityCount) {
        this.activityCount = activityCount;
    }

    public Integer getNotificationCount() {
        return notificationCount;
    }

    public void setNotificationCount(Integer notificationCount) {
        this.notificationCount = notificationCount;
    }

    public Integer getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(Integer riskLevel) {
        this.riskLevel = riskLevel;
    }

    public BigDecimal getDelayRiskScore() {
        return delayRiskScore;
    }

    public void setDelayRiskScore(BigDecimal delayRiskScore) {
        this.delayRiskScore = delayRiskScore;
    }

    public BigDecimal getResourceRiskScore() {
        return resourceRiskScore;
    }

    public void setResourceRiskScore(BigDecimal resourceRiskScore) {
        this.resourceRiskScore = resourceRiskScore;
    }

    public String getExtendedData() {
        return extendedData;
    }

    public void setExtendedData(String extendedData) {
        this.extendedData = extendedData;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ProjectAnalytics that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "ProjectAnalytics{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", analyticsType=" + analyticsType +
                ", analyticsDate=" + analyticsDate +
                ", taskCompletionRate=" + taskCompletionRate +
                ", teamEfficiencyScore=" + teamEfficiencyScore +
                ", riskLevel=" + riskLevel +
                '}';
    }
}
