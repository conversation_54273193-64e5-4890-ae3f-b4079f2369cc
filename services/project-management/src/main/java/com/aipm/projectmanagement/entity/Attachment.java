package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 附件实体类
 * 
 * 表示项目、任务、评论等对象的附件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "attachments", indexes = {
    @Index(name = "idx_attachment_target", columnList = "target_type, target_id"),
    @Index(name = "idx_attachment_uploader", columnList = "uploaded_by"),
    @Index(name = "idx_attachment_comment", columnList = "comment_id"),
    @Index(name = "idx_attachment_created", columnList = "created_at")
})
public class Attachment {

    /**
     * 附件目标类型枚举
     */
    public enum TargetType {
        PROJECT("项目", "项目相关附件"),
        TASK("任务", "任务相关附件"),
        COMMENT("评论", "评论相关附件"),
        SPRINT("Sprint", "Sprint相关附件"),
        BOARD("看板", "看板相关附件");

        private final String displayName;
        private final String description;

        TargetType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 附件类型枚举
     */
    public enum AttachmentType {
        IMAGE("图片", new String[]{"jpg", "jpeg", "png", "gif", "bmp", "webp"}),
        DOCUMENT("文档", new String[]{"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"}),
        ARCHIVE("压缩包", new String[]{"zip", "rar", "7z", "tar", "gz"}),
        VIDEO("视频", new String[]{"mp4", "avi", "mov", "wmv", "flv", "mkv"}),
        AUDIO("音频", new String[]{"mp3", "wav", "flac", "aac", "ogg"}),
        CODE("代码", new String[]{"java", "js", "html", "css", "py", "cpp", "c", "h"}),
        OTHER("其他", new String[]{});

        private final String displayName;
        private final String[] extensions;

        AttachmentType(String displayName, String[] extensions) {
            this.displayName = displayName;
            this.extensions = extensions;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String[] getExtensions() {
            return extensions;
        }

        /**
         * 根据文件扩展名获取附件类型
         */
        public static AttachmentType fromExtension(String extension) {
            if (extension == null || extension.isEmpty()) {
                return OTHER;
            }
            
            String ext = extension.toLowerCase();
            for (AttachmentType type : values()) {
                for (String typeExt : type.getExtensions()) {
                    if (typeExt.equals(ext)) {
                        return type;
                    }
                }
            }
            return OTHER;
        }
    }

    /**
     * 附件唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 原始文件名
     */
    @Column(name = "original_filename", nullable = false, length = 255)
    private String originalFilename;

    /**
     * 存储文件名
     */
    @Column(name = "stored_filename", nullable = false, length = 255)
    private String storedFilename;

    /**
     * 文件路径
     */
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size", nullable = false)
    private Long fileSize;

    /**
     * 文件扩展名
     */
    @Column(name = "file_extension", length = 10)
    private String fileExtension;

    /**
     * MIME类型
     */
    @Column(name = "mime_type", length = 100)
    private String mimeType;

    /**
     * 附件类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "attachment_type", nullable = false)
    private AttachmentType attachmentType;

    /**
     * 附件目标类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "target_type", nullable = false)
    private TargetType targetType;

    /**
     * 附件目标ID
     */
    @Column(name = "target_id", nullable = false)
    private UUID targetId;

    /**
     * 关联的评论（如果是评论附件）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "comment_id")
    private Comment comment;

    /**
     * 文件描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 上传者ID
     */
    @Column(name = "uploaded_by", nullable = false)
    private UUID uploadedBy;

    /**
     * 下载次数
     */
    @Column(name = "download_count", nullable = false)
    private Integer downloadCount = 0;

    /**
     * 是否为公开附件
     */
    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;

    /**
     * 缩略图路径（图片附件）
     */
    @Column(name = "thumbnail_path", length = 500)
    private String thumbnailPath;

    /**
     * 文件哈希值（用于去重）
     */
    @Column(name = "file_hash", length = 64)
    private String fileHash;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public Attachment() {
    }

    /**
     * 基础构造函数
     * 
     * @param originalFilename 原始文件名
     * @param storedFilename 存储文件名
     * @param filePath 文件路径
     * @param fileSize 文件大小
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param uploadedBy 上传者ID
     */
    public Attachment(String originalFilename, String storedFilename, String filePath, 
                     Long fileSize, TargetType targetType, UUID targetId, UUID uploadedBy) {
        this.originalFilename = originalFilename;
        this.storedFilename = storedFilename;
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.targetType = targetType;
        this.targetId = targetId;
        this.uploadedBy = uploadedBy;
        
        // 自动设置文件扩展名和附件类型
        this.fileExtension = extractFileExtension(originalFilename);
        this.attachmentType = AttachmentType.fromExtension(this.fileExtension);
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 增加下载次数
     */
    public void incrementDownloadCount() {
        this.downloadCount++;
    }

    /**
     * 检查是否为图片附件
     * 
     * @return true表示是图片
     */
    public boolean isImage() {
        return attachmentType == AttachmentType.IMAGE;
    }

    /**
     * 检查是否为文档附件
     * 
     * @return true表示是文档
     */
    public boolean isDocument() {
        return attachmentType == AttachmentType.DOCUMENT;
    }

    /**
     * 检查是否有缩略图
     * 
     * @return true表示有缩略图
     */
    public boolean hasThumbnail() {
        return thumbnailPath != null && !thumbnailPath.isEmpty();
    }

    /**
     * 获取格式化的文件大小
     * 
     * @return 格式化的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "0 B";
        }
        
        long size = fileSize;
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", (double) size, units[unitIndex]);
    }

    /**
     * 获取文件显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return originalFilename != null ? originalFilename : storedFilename;
    }

    /**
     * 检查文件是否可以预览
     * 
     * @return true表示可以预览
     */
    public boolean isPreviewable() {
        return attachmentType == AttachmentType.IMAGE || 
               attachmentType == AttachmentType.DOCUMENT ||
               (attachmentType == AttachmentType.CODE && fileSize < 1024 * 1024); // 1MB以下的代码文件
    }

    /**
     * 提取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名
     */
    private String extractFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }

    /**
     * 获取文件图标CSS类
     * 
     * @return CSS类名
     */
    public String getIconClass() {
        return switch (attachmentType) {
            case IMAGE -> "fa-image";
            case DOCUMENT -> "fa-file-text";
            case ARCHIVE -> "fa-file-archive";
            case VIDEO -> "fa-file-video";
            case AUDIO -> "fa-file-audio";
            case CODE -> "fa-file-code";
            default -> "fa-file";
        };
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public String getStoredFilename() {
        return storedFilename;
    }

    public void setStoredFilename(String storedFilename) {
        this.storedFilename = storedFilename;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public AttachmentType getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(AttachmentType attachmentType) {
        this.attachmentType = attachmentType;
    }

    public TargetType getTargetType() {
        return targetType;
    }

    public void setTargetType(TargetType targetType) {
        this.targetType = targetType;
    }

    public UUID getTargetId() {
        return targetId;
    }

    public void setTargetId(UUID targetId) {
        this.targetId = targetId;
    }

    public Comment getComment() {
        return comment;
    }

    public void setComment(Comment comment) {
        this.comment = comment;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UUID getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(UUID uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public Integer getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public String getThumbnailPath() {
        return thumbnailPath;
    }

    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }

    public String getFileHash() {
        return fileHash;
    }

    public void setFileHash(String fileHash) {
        this.fileHash = fileHash;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Attachment that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "Attachment{" +
                "id=" + id +
                ", originalFilename='" + originalFilename + '\'' +
                ", fileSize=" + fileSize +
                ", attachmentType=" + attachmentType +
                ", targetType=" + targetType +
                ", targetId=" + targetId +
                ", downloadCount=" + downloadCount +
                '}';
    }
}
