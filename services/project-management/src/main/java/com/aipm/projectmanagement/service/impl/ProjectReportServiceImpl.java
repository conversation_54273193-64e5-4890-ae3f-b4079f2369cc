package com.aipm.projectmanagement.service.impl;

import com.aipm.projectmanagement.entity.ProjectReport;
import com.aipm.projectmanagement.entity.ProjectReport.ReportFormat;
import com.aipm.projectmanagement.entity.ProjectReport.ReportStatus;
import com.aipm.projectmanagement.entity.ProjectReport.ReportType;
import com.aipm.projectmanagement.repository.ProjectReportRepository;
import com.aipm.projectmanagement.service.ProjectReportService;
import com.aipm.projectmanagement.service.ActivityLogService;
import com.aipm.projectmanagement.service.ProjectAnalyticsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 项目报表服务实现类
 * 
 * 实现项目报表管理相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
@Service
@Transactional
public class ProjectReportServiceImpl implements ProjectReportService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectReportServiceImpl.class);

    @Autowired
    private ProjectReportRepository projectReportRepository;

    @Autowired
    private ProjectAnalyticsService projectAnalyticsService;

    @Autowired
    private ActivityLogService activityLogService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public ProjectReport createReport(UUID projectId, ReportType reportType, String title, String description,
                                     LocalDateTime periodStart, LocalDateTime periodEnd, 
                                     ReportFormat format, UUID generatedBy) {
        logger.info("创建报表: 项目={}, 类型={}, 标题={}", projectId, reportType, title);

        ProjectReport report = new ProjectReport(projectId, reportType, title, periodStart, periodEnd, generatedBy);
        report.setDescription(description);
        report.setFormat(format);
        report.setExpiresInDays(30); // 默认30天过期

        ProjectReport savedReport = projectReportRepository.save(report);

        // 记录活动日志
        try {
            activityLogService.logActivity(
                projectId, generatedBy,
                com.aipm.projectmanagement.entity.ActivityLog.ActionType.REPORT_CREATED,
                com.aipm.projectmanagement.entity.ActivityLog.TargetType.REPORT,
                savedReport.getId(), title,
                "创建了报表: " + title
            );
        } catch (Exception e) {
            logger.warn("记录报表创建活动日志失败: {}", e.getMessage());
        }

        // 异步生成报表
        generateReportAsync(savedReport.getId());

        logger.info("报表创建成功: {}", savedReport.getId());
        return savedReport;
    }

    @Override
    public ProjectReport generateDailyReport(UUID projectId, LocalDateTime date, UUID generatedBy) {
        String title = "项目日报 - " + date.toLocalDate();
        LocalDateTime start = date.toLocalDate().atStartOfDay();
        LocalDateTime end = start.plusDays(1).minusNanos(1);

        return createReport(projectId, ReportType.DAILY, title, "项目每日进度和活动报表",
                          start, end, ReportFormat.JSON, generatedBy);
    }

    @Override
    public ProjectReport generateWeeklyReport(UUID projectId, LocalDateTime weekStart, UUID generatedBy) {
        String title = "项目周报 - " + weekStart.toLocalDate();
        LocalDateTime end = weekStart.plusWeeks(1).minusNanos(1);

        return createReport(projectId, ReportType.WEEKLY, title, "项目每周总结和分析报表",
                          weekStart, end, ReportFormat.JSON, generatedBy);
    }

    @Override
    public ProjectReport generateMonthlyReport(UUID projectId, LocalDateTime monthStart, UUID generatedBy) {
        String title = "项目月报 - " + monthStart.toLocalDate();
        LocalDateTime end = monthStart.plusMonths(1).minusNanos(1);

        return createReport(projectId, ReportType.MONTHLY, title, "项目每月分析和总结报表",
                          monthStart, end, ReportFormat.JSON, generatedBy);
    }

    @Override
    public ProjectReport generateSprintReport(UUID projectId, UUID sprintId, String sprintName,
                                             LocalDateTime sprintStart, LocalDateTime sprintEnd, UUID generatedBy) {
        String title = "Sprint报表 - " + sprintName;

        ProjectReport report = createReport(projectId, ReportType.SPRINT, title, "Sprint周期报表",
                                          sprintStart, sprintEnd, ReportFormat.JSON, generatedBy);

        // 设置Sprint相关配置
        Map<String, Object> config = new HashMap<>();
        config.put("sprintId", sprintId);
        config.put("sprintName", sprintName);
        
        try {
            report.setConfig(objectMapper.writeValueAsString(config));
            projectReportRepository.save(report);
        } catch (Exception e) {
            logger.warn("设置Sprint报表配置失败: {}", e.getMessage());
        }

        return report;
    }

    @Override
    public ProjectReport generateTeamPerformanceReport(UUID projectId, LocalDateTime periodStart, 
                                                      LocalDateTime periodEnd, UUID generatedBy) {
        String title = "团队绩效报表 - " + periodStart.toLocalDate() + " 至 " + periodEnd.toLocalDate();

        return createReport(projectId, ReportType.TEAM_PERFORMANCE, title, "团队绩效分析报表",
                          periodStart, periodEnd, ReportFormat.JSON, generatedBy);
    }

    @Override
    public ProjectReport generateBurndownReport(UUID projectId, UUID sprintId, UUID generatedBy) {
        String title = "燃尽图报表 - Sprint " + sprintId;

        // 获取Sprint时间范围（这里需要从Sprint服务获取）
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start = now.minusWeeks(2); // 假设Sprint为2周
        LocalDateTime end = now;

        ProjectReport report = createReport(projectId, ReportType.BURNDOWN, title, "Sprint燃尽图报表",
                                          start, end, ReportFormat.JSON, generatedBy);

        // 设置燃尽图相关配置
        Map<String, Object> config = new HashMap<>();
        config.put("sprintId", sprintId);
        config.put("chartType", "burndown");
        
        try {
            report.setConfig(objectMapper.writeValueAsString(config));
            projectReportRepository.save(report);
        } catch (Exception e) {
            logger.warn("设置燃尽图报表配置失败: {}", e.getMessage());
        }

        return report;
    }

    @Override
    @Async("fileProcessingExecutor")
    public void generateReportAsync(UUID reportId) {
        logger.info("异步生成报表: {}", reportId);

        try {
            Optional<ProjectReport> reportOpt = projectReportRepository.findById(reportId);
            if (reportOpt.isEmpty()) {
                logger.warn("报表不存在: {}", reportId);
                return;
            }

            ProjectReport report = reportOpt.get();
            report.startGeneration();
            projectReportRepository.save(report);

            // 生成报表数据
            Map<String, Object> reportData = generateReportData(report);
            String dataJson = objectMapper.writeValueAsString(reportData);

            // 完成报表生成
            report.completeGeneration(dataJson, null, (long) dataJson.length());
            projectReportRepository.save(report);

            logger.info("报表生成完成: {}", reportId);

        } catch (Exception e) {
            logger.error("报表生成失败: {}", reportId, e);
            
            try {
                Optional<ProjectReport> reportOpt = projectReportRepository.findById(reportId);
                if (reportOpt.isPresent()) {
                    ProjectReport report = reportOpt.get();
                    report.markAsFailed(e.getMessage());
                    projectReportRepository.save(report);
                }
            } catch (Exception ex) {
                logger.error("标记报表失败状态时出错: {}", reportId, ex);
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ProjectReport> getReportById(UUID reportId) {
        return projectReportRepository.findById(reportId);
    }

    @Override
    public InputStream downloadReport(UUID reportId, UUID userId) {
        logger.info("下载报表: {}, 用户: {}", reportId, userId);

        ProjectReport report = projectReportRepository.findById(reportId)
                .orElseThrow(() -> new RuntimeException("报表不存在: " + reportId));

        // 验证权限
        if (!canViewReport(reportId, userId)) {
            throw new RuntimeException("用户无权限下载此报表");
        }

        if (!report.isCompleted()) {
            throw new RuntimeException("报表尚未生成完成");
        }

        // 增加下载次数
        report.incrementDownloadCount();
        projectReportRepository.save(report);

        // 记录活动日志
        try {
            activityLogService.logActivity(
                report.getProjectId(), userId,
                com.aipm.projectmanagement.entity.ActivityLog.ActionType.REPORT_DOWNLOADED,
                com.aipm.projectmanagement.entity.ActivityLog.TargetType.REPORT,
                reportId, report.getTitle(),
                "下载了报表: " + report.getTitle()
            );
        } catch (Exception e) {
            logger.warn("记录报表下载活动日志失败: {}", e.getMessage());
        }

        // TODO: 返回实际的文件流
        // 这里应该根据report.getFilePath()返回文件流
        throw new UnsupportedOperationException("文件下载功能待实现");
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> previewReport(UUID reportId, UUID userId) {
        logger.info("预览报表: {}, 用户: {}", reportId, userId);

        ProjectReport report = projectReportRepository.findById(reportId)
                .orElseThrow(() -> new RuntimeException("报表不存在: " + reportId));

        // 验证权限
        if (!canViewReport(reportId, userId)) {
            throw new RuntimeException("用户无权限预览此报表");
        }

        if (!report.isCompleted()) {
            throw new RuntimeException("报表尚未生成完成");
        }

        try {
            // 解析报表数据
            if (report.getData() != null) {
                return objectMapper.readValue(report.getData(), Map.class);
            } else {
                return new HashMap<>();
            }
        } catch (Exception e) {
            logger.error("解析报表数据失败: {}", reportId, e);
            throw new RuntimeException("报表数据解析失败");
        }
    }

    @Override
    public void deleteReport(UUID reportId, UUID userId) {
        logger.info("删除报表: {}, 用户: {}", reportId, userId);

        ProjectReport report = projectReportRepository.findById(reportId)
                .orElseThrow(() -> new RuntimeException("报表不存在: " + reportId));

        // 验证权限
        if (!canDeleteReport(reportId, userId)) {
            throw new RuntimeException("用户无权限删除此报表");
        }

        // 删除报表
        projectReportRepository.delete(report);

        // 记录活动日志
        try {
            activityLogService.logActivity(
                report.getProjectId(), userId,
                com.aipm.projectmanagement.entity.ActivityLog.ActionType.REPORT_DELETED,
                com.aipm.projectmanagement.entity.ActivityLog.TargetType.REPORT,
                reportId, report.getTitle(),
                "删除了报表: " + report.getTitle()
            );
        } catch (Exception e) {
            logger.warn("记录报表删除活动日志失败: {}", e.getMessage());
        }

        logger.info("报表删除成功: {}", reportId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProjectReport> getProjectReports(UUID projectId, Pageable pageable) {
        return projectReportRepository.findByProjectIdOrderByCreatedAtDesc(projectId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProjectReport> getReportsByType(UUID projectId, ReportType reportType, Pageable pageable) {
        return projectReportRepository.findByProjectIdAndReportTypeOrderByCreatedAtDesc(projectId, reportType, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProjectReport> getReportsByStatus(UUID projectId, ReportStatus status, Pageable pageable) {
        return projectReportRepository.findByProjectIdAndStatusOrderByCreatedAtDesc(projectId, status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProjectReport> getUserReports(UUID generatedBy, Pageable pageable) {
        return projectReportRepository.findByGeneratedByOrderByCreatedAtDesc(generatedBy, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProjectReport> searchReports(UUID projectId, String keyword, ReportType reportType,
                                            ReportStatus status, LocalDateTime startDate, LocalDateTime endDate,
                                            Pageable pageable) {
        return projectReportRepository.searchReports(projectId, keyword, reportType, status, 
                                                    startDate, endDate, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ProjectReport> getLatestReport(UUID projectId, ReportType reportType) {
        return projectReportRepository.findLatestReport(projectId, reportType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProjectReport> getPopularReports(UUID projectId, int limit) {
        return projectReportRepository.findPopularReports(projectId, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProjectReport> getRecentReports(UUID projectId, int limit) {
        return projectReportRepository.findRecentReports(projectId, limit);
    }

    @Override
    public int batchGenerateReports(int limit) {
        List<ProjectReport> pendingReports = projectReportRepository.findPendingReports(limit);
        
        for (ProjectReport report : pendingReports) {
            generateReportAsync(report.getId());
        }
        
        logger.info("批量生成报表: {}", pendingReports.size());
        return pendingReports.size();
    }

    @Override
    public int retryFailedReports(int retryHours) {
        LocalDateTime retryBefore = LocalDateTime.now().minusHours(retryHours);
        List<ProjectReport> failedReports = projectReportRepository.findFailedReportsForRetry(retryBefore);
        
        for (ProjectReport report : failedReports) {
            report.setStatus(ReportStatus.PENDING);
            projectReportRepository.save(report);
            generateReportAsync(report.getId());
        }
        
        logger.info("重试失败报表: {}", failedReports.size());
        return failedReports.size();
    }

    @Override
    public int processExpiredReports() {
        List<ProjectReport> expiredReports = projectReportRepository.findExpiredReports(LocalDateTime.now());
        
        for (ProjectReport report : expiredReports) {
            report.setStatus(ReportStatus.EXPIRED);
            projectReportRepository.save(report);
        }
        
        logger.info("处理过期报表: {}", expiredReports.size());
        return expiredReports.size();
    }

    @Override
    public int cleanupOldReports(int days) {
        LocalDateTime expiredBefore = LocalDateTime.now().minusDays(days);
        int cleanedCount = projectReportRepository.deleteExpiredReports(expiredBefore);
        logger.info("清理旧报表: {}", cleanedCount);
        return cleanedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getReportStatistics(UUID projectId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取报表摘要
        Object[] summary = projectReportRepository.getProjectReportSummary(projectId);
        if (summary != null && summary.length >= 6) {
            statistics.put("totalReports", summary[0]);
            statistics.put("completedReports", summary[1]);
            statistics.put("pendingReports", summary[2]);
            statistics.put("failedReports", summary[3]);
            statistics.put("totalFileSize", summary[4]);
            statistics.put("totalDownloads", summary[5]);
        }
        
        // 获取类型分布
        List<Object[]> typeStats = projectReportRepository.getReportCountByType(projectId);
        Map<String, Long> typeCounts = new HashMap<>();
        for (Object[] stat : typeStats) {
            typeCounts.put(((ReportType) stat[0]).name(), (Long) stat[1]);
        }
        statistics.put("typeDistribution", typeCounts);
        
        // 获取状态分布
        List<Object[]> statusStats = projectReportRepository.getReportStatusDistribution(projectId);
        Map<String, Long> statusCounts = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusCounts.put(((ReportStatus) stat[0]).name(), (Long) stat[1]);
        }
        statistics.put("statusDistribution", statusCounts);
        
        return statistics;
    }

    // ============================================================================
    // 其他方法的简化实现
    // ============================================================================

    @Override
    public Map<String, Object> getReportGenerationTrend(UUID projectId, int days) {
        // TODO: 实现报表生成趋势
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getUserReportStatistics(UUID generatedBy) {
        // TODO: 实现用户报表统计
        return new HashMap<>();
    }

    @Override
    public boolean setAutoGeneration(UUID projectId, ReportType reportType, boolean isAutoGenerated,
                                    int generationInterval, UUID generatedBy) {
        // TODO: 实现自动生成设置
        return true;
    }

    @Override
    public int processAutoGenerationReports() {
        // TODO: 实现自动生成处理
        return 0;
    }

    @Override
    public ProjectReport exportReport(UUID reportId, ReportFormat format, UUID userId) {
        // TODO: 实现报表格式转换
        throw new UnsupportedOperationException("报表格式转换功能待实现");
    }

    @Override
    public boolean shareReport(UUID reportId, List<UUID> shareWithUserIds, UUID userId) {
        // TODO: 实现报表分享
        return true;
    }

    @Override
    public String getReportShareLink(UUID reportId, UUID userId, int expirationHours) {
        // TODO: 实现分享链接生成
        return "https://example.com/reports/share/" + reportId;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canViewReport(UUID reportId, UUID userId) {
        // TODO: 实现查看权限检查
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canGenerateReport(UUID projectId, UUID userId) {
        // TODO: 实现生成权限检查
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canDeleteReport(UUID reportId, UUID userId) {
        // TODO: 实现删除权限检查
        return true;
    }

    @Override
    public Map<String, Object> getReportTemplate(ReportType reportType) {
        // TODO: 实现报表模板
        return new HashMap<>();
    }

    @Override
    public ProjectReport createCustomReport(UUID projectId, String title, Map<String, Object> config,
                                           LocalDateTime periodStart, LocalDateTime periodEnd, UUID generatedBy) {
        // TODO: 实现自定义报表
        return createReport(projectId, ReportType.CUSTOM, title, "自定义报表", 
                          periodStart, periodEnd, ReportFormat.JSON, generatedBy);
    }

    @Override
    public int getReportGenerationProgress(UUID reportId) {
        // TODO: 实现生成进度查询
        return 0;
    }

    @Override
    public boolean cancelReportGeneration(UUID reportId, UUID userId) {
        // TODO: 实现取消生成
        return true;
    }

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    /**
     * 生成报表数据
     */
    private Map<String, Object> generateReportData(ProjectReport report) {
        Map<String, Object> data = new HashMap<>();
        
        try {
            switch (report.getReportType()) {
                case DAILY:
                    data = generateDailyReportData(report);
                    break;
                case WEEKLY:
                    data = generateWeeklyReportData(report);
                    break;
                case MONTHLY:
                    data = generateMonthlyReportData(report);
                    break;
                case SPRINT:
                    data = generateSprintReportData(report);
                    break;
                case TEAM_PERFORMANCE:
                    data = generateTeamPerformanceReportData(report);
                    break;
                case BURNDOWN:
                    data = generateBurndownReportData(report);
                    break;
                default:
                    data = generateDefaultReportData(report);
                    break;
            }
            
            // 添加通用信息
            data.put("reportId", report.getId());
            data.put("projectId", report.getProjectId());
            data.put("reportType", report.getReportType());
            data.put("title", report.getTitle());
            data.put("periodStart", report.getPeriodStart());
            data.put("periodEnd", report.getPeriodEnd());
            data.put("generatedAt", LocalDateTime.now());
            
        } catch (Exception e) {
            logger.error("生成报表数据失败: {}", report.getId(), e);
            throw new RuntimeException("生成报表数据失败: " + e.getMessage());
        }
        
        return data;
    }

    private Map<String, Object> generateDailyReportData(ProjectReport report) {
        Map<String, Object> data = new HashMap<>();
        
        // 获取当日分析数据
        Map<String, Object> analytics = projectAnalyticsService.getProjectDashboard(report.getProjectId());
        data.put("analytics", analytics);
        
        // 添加日报特有数据
        data.put("type", "daily");
        data.put("summary", "项目日报数据");
        
        return data;
    }

    private Map<String, Object> generateWeeklyReportData(ProjectReport report) {
        Map<String, Object> data = new HashMap<>();
        
        // 获取周度趋势数据
        Map<String, Object> trends = projectAnalyticsService.getTaskCompletionTrend(report.getProjectId(), 7);
        data.put("trends", trends);
        
        data.put("type", "weekly");
        data.put("summary", "项目周报数据");
        
        return data;
    }

    private Map<String, Object> generateMonthlyReportData(ProjectReport report) {
        Map<String, Object> data = new HashMap<>();
        
        // 获取月度分析数据
        Map<String, Object> summary = projectAnalyticsService.getAnalyticsSummary(report.getProjectId(), 30);
        data.put("summary", summary);
        
        data.put("type", "monthly");
        data.put("description", "项目月报数据");
        
        return data;
    }

    private Map<String, Object> generateSprintReportData(ProjectReport report) {
        Map<String, Object> data = new HashMap<>();
        
        // TODO: 获取Sprint相关数据
        data.put("type", "sprint");
        data.put("summary", "Sprint报表数据");
        
        return data;
    }

    private Map<String, Object> generateTeamPerformanceReportData(ProjectReport report) {
        Map<String, Object> data = new HashMap<>();
        
        // 获取团队绩效数据
        Map<String, Object> performance = projectAnalyticsService.getTeamPerformanceAnalysis(
            report.getProjectId(), 30);
        data.put("performance", performance);
        
        data.put("type", "team_performance");
        data.put("summary", "团队绩效报表数据");
        
        return data;
    }

    private Map<String, Object> generateBurndownReportData(ProjectReport report) {
        Map<String, Object> data = new HashMap<>();
        
        // TODO: 生成燃尽图数据
        data.put("type", "burndown");
        data.put("summary", "燃尽图报表数据");
        
        return data;
    }

    private Map<String, Object> generateDefaultReportData(ProjectReport report) {
        Map<String, Object> data = new HashMap<>();
        
        data.put("type", "default");
        data.put("summary", "默认报表数据");
        
        return data;
    }
}
