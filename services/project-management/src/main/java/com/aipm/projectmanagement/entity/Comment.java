package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 评论实体类
 * 
 * 表示项目、任务等对象的评论
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "comments", indexes = {
    @Index(name = "idx_comment_target", columnList = "target_type, target_id"),
    @Index(name = "idx_comment_author", columnList = "author_id"),
    @Index(name = "idx_comment_parent", columnList = "parent_comment_id"),
    @Index(name = "idx_comment_created", columnList = "created_at")
})
public class Comment {

    /**
     * 评论目标类型枚举
     */
    public enum TargetType {
        PROJECT("项目", "项目相关评论"),
        TASK("任务", "任务相关评论"),
        SPRINT("Sprint", "Sprint相关评论"),
        BOARD("看板", "看板相关评论");

        private final String displayName;
        private final String description;

        TargetType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 评论唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 评论内容
     */
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    /**
     * 评论目标类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "target_type", nullable = false)
    private TargetType targetType;

    /**
     * 评论目标ID
     */
    @Column(name = "target_id", nullable = false)
    private UUID targetId;

    /**
     * 评论作者ID
     */
    @Column(name = "author_id", nullable = false)
    private UUID authorId;

    /**
     * 父评论（用于回复）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_comment_id")
    private Comment parentComment;

    /**
     * 子评论列表
     */
    @OneToMany(mappedBy = "parentComment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("createdAt ASC")
    private List<Comment> replies = new ArrayList<>();

    /**
     * 是否已删除
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;

    /**
     * 是否已编辑
     */
    @Column(name = "is_edited", nullable = false)
    private Boolean isEdited = false;

    /**
     * 点赞数
     */
    @Column(name = "like_count", nullable = false)
    private Integer likeCount = 0;

    /**
     * 评论附件列表
     */
    @OneToMany(mappedBy = "comment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Attachment> attachments = new ArrayList<>();

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public Comment() {
    }

    /**
     * 基础构造函数
     * 
     * @param content 评论内容
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param authorId 作者ID
     */
    public Comment(String content, TargetType targetType, UUID targetId, UUID authorId) {
        this.content = content;
        this.targetType = targetType;
        this.targetId = targetId;
        this.authorId = authorId;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 软删除评论
     */
    public void softDelete() {
        this.isDeleted = true;
        this.deletedAt = LocalDateTime.now();
    }

    /**
     * 恢复评论
     */
    public void restore() {
        this.isDeleted = false;
        this.deletedAt = null;
    }

    /**
     * 标记为已编辑
     */
    public void markAsEdited() {
        this.isEdited = true;
    }

    /**
     * 增加点赞数
     */
    public void incrementLikeCount() {
        this.likeCount++;
    }

    /**
     * 减少点赞数
     */
    public void decrementLikeCount() {
        if (this.likeCount > 0) {
            this.likeCount--;
        }
    }

    /**
     * 添加回复
     * 
     * @param reply 回复评论
     */
    public void addReply(Comment reply) {
        if (reply != null && !replies.contains(reply)) {
            replies.add(reply);
            reply.setParentComment(this);
        }
    }

    /**
     * 移除回复
     * 
     * @param reply 回复评论
     */
    public void removeReply(Comment reply) {
        if (reply != null && replies.contains(reply)) {
            replies.remove(reply);
            reply.setParentComment(null);
        }
    }

    /**
     * 检查是否为顶级评论
     * 
     * @return true表示是顶级评论
     */
    public boolean isTopLevel() {
        return parentComment == null;
    }

    /**
     * 检查是否为回复
     * 
     * @return true表示是回复
     */
    public boolean isReply() {
        return parentComment != null;
    }

    /**
     * 获取回复数量
     * 
     * @return 回复数量
     */
    public int getReplyCount() {
        return replies != null ? replies.size() : 0;
    }

    /**
     * 获取评论层级深度
     * 
     * @return 层级深度
     */
    public int getDepth() {
        int depth = 0;
        Comment current = this.parentComment;
        while (current != null) {
            depth++;
            current = current.getParentComment();
        }
        return depth;
    }

    /**
     * 获取根评论
     * 
     * @return 根评论
     */
    public Comment getRootComment() {
        Comment current = this;
        while (current.getParentComment() != null) {
            current = current.getParentComment();
        }
        return current;
    }

    /**
     * 检查是否有附件
     * 
     * @return true表示有附件
     */
    public boolean hasAttachments() {
        return attachments != null && !attachments.isEmpty();
    }

    /**
     * 获取附件数量
     * 
     * @return 附件数量
     */
    public int getAttachmentCount() {
        return attachments != null ? attachments.size() : 0;
    }

    /**
     * 添加附件
     * 
     * @param attachment 附件
     */
    public void addAttachment(Attachment attachment) {
        if (attachment != null && !attachments.contains(attachment)) {
            attachments.add(attachment);
            attachment.setComment(this);
        }
    }

    /**
     * 移除附件
     * 
     * @param attachment 附件
     */
    public void removeAttachment(Attachment attachment) {
        if (attachment != null && attachments.contains(attachment)) {
            attachments.remove(attachment);
            attachment.setComment(null);
        }
    }

    /**
     * 获取评论摘要（截取前100个字符）
     * 
     * @return 评论摘要
     */
    public String getSummary() {
        if (content == null || content.isEmpty()) {
            return "";
        }
        
        if (content.length() <= 100) {
            return content;
        }
        
        return content.substring(0, 100) + "...";
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public TargetType getTargetType() {
        return targetType;
    }

    public void setTargetType(TargetType targetType) {
        this.targetType = targetType;
    }

    public UUID getTargetId() {
        return targetId;
    }

    public void setTargetId(UUID targetId) {
        this.targetId = targetId;
    }

    public UUID getAuthorId() {
        return authorId;
    }

    public void setAuthorId(UUID authorId) {
        this.authorId = authorId;
    }

    public Comment getParentComment() {
        return parentComment;
    }

    public void setParentComment(Comment parentComment) {
        this.parentComment = parentComment;
    }

    public List<Comment> getReplies() {
        return replies;
    }

    public void setReplies(List<Comment> replies) {
        this.replies = replies;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Boolean getIsEdited() {
        return isEdited;
    }

    public void setIsEdited(Boolean isEdited) {
        this.isEdited = isEdited;
    }

    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Comment comment)) return false;
        return id != null && id.equals(comment.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "Comment{" +
                "id=" + id +
                ", targetType=" + targetType +
                ", targetId=" + targetId +
                ", authorId=" + authorId +
                ", isDeleted=" + isDeleted +
                ", likeCount=" + likeCount +
                ", replyCount=" + getReplyCount() +
                '}';
    }
}
