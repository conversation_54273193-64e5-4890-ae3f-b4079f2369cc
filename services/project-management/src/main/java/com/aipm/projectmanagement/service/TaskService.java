package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.Task;
import com.aipm.projectmanagement.entity.TaskPriority;
import com.aipm.projectmanagement.entity.TaskStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 任务服务接口
 * 
 * 定义任务管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface TaskService {

    /**
     * 创建新任务
     * 
     * @param task 任务信息
     * @param creatorId 创建者ID
     * @return 创建的任务
     */
    Task createTask(Task task, UUID creatorId);

    /**
     * 根据ID获取任务
     * 
     * @param taskId 任务ID
     * @return 任务信息
     */
    Optional<Task> getTaskById(UUID taskId);

    /**
     * 更新任务信息
     * 
     * @param task 任务信息
     * @param updaterId 更新者ID
     * @return 更新后的任务
     */
    Task updateTask(Task task, UUID updaterId);

    /**
     * 删除任务（软删除）
     * 
     * @param taskId 任务ID
     * @param deleterId 删除者ID
     */
    void deleteTask(UUID taskId, UUID deleterId);

    /**
     * 恢复已删除的任务
     * 
     * @param taskId 任务ID
     * @param restorerId 恢复者ID
     */
    void restoreTask(UUID taskId, UUID restorerId);

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 新状态
     * @param updaterId 更新者ID
     * @return 更新后的任务
     */
    Task updateTaskStatus(UUID taskId, TaskStatus status, UUID updaterId);

    /**
     * 分配任务
     * 
     * @param taskId 任务ID
     * @param assigneeId 分配给的用户ID
     * @param assignerId 分配者ID
     * @return 更新后的任务
     */
    Task assignTask(UUID taskId, UUID assigneeId, UUID assignerId);

    /**
     * 取消任务分配
     * 
     * @param taskId 任务ID
     * @param unassignerId 取消分配者ID
     * @return 更新后的任务
     */
    Task unassignTask(UUID taskId, UUID unassignerId);

    /**
     * 更新任务优先级
     * 
     * @param taskId 任务ID
     * @param priority 新优先级
     * @param updaterId 更新者ID
     * @return 更新后的任务
     */
    Task updateTaskPriority(UUID taskId, TaskPriority priority, UUID updaterId);

    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param progress 进度百分比 (0-100)
     * @param updaterId 更新者ID
     * @return 更新后的任务
     */
    Task updateTaskProgress(UUID taskId, Integer progress, UUID updaterId);

    /**
     * 开始任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 更新后的任务
     */
    Task startTask(UUID taskId, UUID userId);

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 更新后的任务
     */
    Task completeTask(UUID taskId, UUID userId);

    /**
     * 阻塞任务
     * 
     * @param taskId 任务ID
     * @param reason 阻塞原因
     * @param userId 用户ID
     * @return 更新后的任务
     */
    Task blockTask(UUID taskId, String reason, UUID userId);

    /**
     * 解除任务阻塞
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 更新后的任务
     */
    Task unblockTask(UUID taskId, UUID userId);

    /**
     * 根据项目ID获取任务
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getTasksByProject(UUID projectId, Pageable pageable);

    /**
     * 获取顶级任务（没有父任务的任务）
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getTopLevelTasks(UUID projectId, Pageable pageable);

    /**
     * 根据父任务获取子任务
     * 
     * @param parentTaskId 父任务ID
     * @return 子任务列表
     */
    List<Task> getSubTasks(UUID parentTaskId);

    /**
     * 根据分配人获取任务
     * 
     * @param assigneeId 分配人ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getTasksByAssignee(UUID assigneeId, Pageable pageable);

    /**
     * 搜索任务
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param status 任务状态
     * @param priority 优先级
     * @param assigneeId 分配人ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> searchTasks(UUID projectId, String keyword, TaskStatus status, 
                          TaskPriority priority, UUID assigneeId, Pageable pageable);

    /**
     * 获取用户的所有任务
     * 
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param status 任务状态
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getUserTasks(UUID userId, String keyword, TaskStatus status, Pageable pageable);

    /**
     * 获取即将到期的任务
     * 
     * @param hours 小时数
     * @return 即将到期的任务列表
     */
    List<Task> getTasksDueSoon(int hours);

    /**
     * 获取已过期的任务
     * 
     * @return 已过期的任务列表
     */
    List<Task> getOverdueTasks();

    /**
     * 获取被阻塞的任务
     * 
     * @param projectId 项目ID（可选）
     * @return 被阻塞的任务列表
     */
    List<Task> getBlockedTasks(UUID projectId);

    /**
     * 获取高优先级任务
     * 
     * @param assigneeId 分配人ID（可选）
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getHighPriorityTasks(UUID assigneeId, Pageable pageable);

    /**
     * 获取最近创建的任务
     * 
     * @param userId 用户ID（可选）
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getRecentTasks(UUID userId, Pageable pageable);

    /**
     * 创建子任务
     * 
     * @param parentTaskId 父任务ID
     * @param subTask 子任务信息
     * @param creatorId 创建者ID
     * @return 创建的子任务
     */
    Task createSubTask(UUID parentTaskId, Task subTask, UUID creatorId);

    /**
     * 移动任务到另一个父任务下
     * 
     * @param taskId 任务ID
     * @param newParentTaskId 新父任务ID（null表示移动到顶级）
     * @param moverId 移动者ID
     * @return 更新后的任务
     */
    Task moveTask(UUID taskId, UUID newParentTaskId, UUID moverId);

    /**
     * 复制任务
     * 
     * @param taskId 源任务ID
     * @param targetProjectId 目标项目ID（可选，默认为同一项目）
     * @param copierId 复制者ID
     * @return 复制的任务
     */
    Task copyTask(UUID taskId, UUID targetProjectId, UUID copierId);

    /**
     * 批量更新任务状态
     * 
     * @param taskIds 任务ID列表
     * @param status 新状态
     * @param updaterId 更新者ID
     * @return 更新的任务数量
     */
    int batchUpdateTaskStatus(List<UUID> taskIds, TaskStatus status, UUID updaterId);

    /**
     * 批量分配任务
     * 
     * @param taskIds 任务ID列表
     * @param assigneeId 分配给的用户ID
     * @param assignerId 分配者ID
     * @return 分配的任务数量
     */
    int batchAssignTasks(List<UUID> taskIds, UUID assigneeId, UUID assignerId);

    /**
     * 获取任务统计信息
     * 
     * @param projectId 项目ID
     * @return 统计信息Map
     */
    Map<String, Object> getTaskStatistics(UUID projectId);

    /**
     * 获取用户任务统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息Map
     */
    Map<String, Object> getUserTaskStatistics(UUID userId);

    /**
     * 检查用户是否有任务操作权限
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasTaskPermission(UUID taskId, UUID userId);

    /**
     * 获取任务依赖关系
     * 
     * @param taskId 任务ID
     * @return 依赖的任务列表
     */
    List<Task> getTaskDependencies(UUID taskId);

    /**
     * 添加任务依赖
     * 
     * @param taskId 任务ID
     * @param dependencyTaskId 依赖的任务ID
     * @param userId 操作者ID
     */
    void addTaskDependency(UUID taskId, UUID dependencyTaskId, UUID userId);

    /**
     * 移除任务依赖
     * 
     * @param taskId 任务ID
     * @param dependencyTaskId 依赖的任务ID
     * @param userId 操作者ID
     */
    void removeTaskDependency(UUID taskId, UUID dependencyTaskId, UUID userId);
}
