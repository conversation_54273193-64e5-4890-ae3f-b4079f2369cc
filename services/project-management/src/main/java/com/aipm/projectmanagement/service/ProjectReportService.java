package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.ProjectReport;
import com.aipm.projectmanagement.entity.ProjectReport.ReportFormat;
import com.aipm.projectmanagement.entity.ProjectReport.ReportStatus;
import com.aipm.projectmanagement.entity.ProjectReport.ReportType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 项目报表服务接口
 * 
 * 定义项目报表管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
public interface ProjectReportService {

    /**
     * 创建报表
     * 
     * @param projectId 项目ID
     * @param reportType 报表类型
     * @param title 标题
     * @param description 描述
     * @param periodStart 开始时间
     * @param periodEnd 结束时间
     * @param format 报表格式
     * @param generatedBy 生成者ID
     * @return 创建的报表
     */
    ProjectReport createReport(UUID projectId, ReportType reportType, String title, String description,
                              LocalDateTime periodStart, LocalDateTime periodEnd, 
                              ReportFormat format, UUID generatedBy);

    /**
     * 生成日报
     * 
     * @param projectId 项目ID
     * @param date 日期
     * @param generatedBy 生成者ID
     * @return 生成的日报
     */
    ProjectReport generateDailyReport(UUID projectId, LocalDateTime date, UUID generatedBy);

    /**
     * 生成周报
     * 
     * @param projectId 项目ID
     * @param weekStart 周开始日期
     * @param generatedBy 生成者ID
     * @return 生成的周报
     */
    ProjectReport generateWeeklyReport(UUID projectId, LocalDateTime weekStart, UUID generatedBy);

    /**
     * 生成月报
     * 
     * @param projectId 项目ID
     * @param monthStart 月开始日期
     * @param generatedBy 生成者ID
     * @return 生成的月报
     */
    ProjectReport generateMonthlyReport(UUID projectId, LocalDateTime monthStart, UUID generatedBy);

    /**
     * 生成Sprint报表
     * 
     * @param projectId 项目ID
     * @param sprintId Sprint ID
     * @param sprintName Sprint名称
     * @param sprintStart Sprint开始时间
     * @param sprintEnd Sprint结束时间
     * @param generatedBy 生成者ID
     * @return 生成的Sprint报表
     */
    ProjectReport generateSprintReport(UUID projectId, UUID sprintId, String sprintName,
                                      LocalDateTime sprintStart, LocalDateTime sprintEnd, UUID generatedBy);

    /**
     * 生成团队绩效报表
     * 
     * @param projectId 项目ID
     * @param periodStart 开始时间
     * @param periodEnd 结束时间
     * @param generatedBy 生成者ID
     * @return 生成的团队绩效报表
     */
    ProjectReport generateTeamPerformanceReport(UUID projectId, LocalDateTime periodStart, 
                                               LocalDateTime periodEnd, UUID generatedBy);

    /**
     * 生成燃尽图报表
     * 
     * @param projectId 项目ID
     * @param sprintId Sprint ID
     * @param generatedBy 生成者ID
     * @return 生成的燃尽图报表
     */
    ProjectReport generateBurndownReport(UUID projectId, UUID sprintId, UUID generatedBy);

    /**
     * 异步生成报表
     * 
     * @param reportId 报表ID
     */
    void generateReportAsync(UUID reportId);

    /**
     * 根据ID获取报表
     * 
     * @param reportId 报表ID
     * @return 报表对象（如果存在）
     */
    Optional<ProjectReport> getReportById(UUID reportId);

    /**
     * 下载报表
     * 
     * @param reportId 报表ID
     * @param userId 用户ID
     * @return 文件输入流
     */
    InputStream downloadReport(UUID reportId, UUID userId);

    /**
     * 预览报表
     * 
     * @param reportId 报表ID
     * @param userId 用户ID
     * @return 预览数据
     */
    Map<String, Object> previewReport(UUID reportId, UUID userId);

    /**
     * 删除报表
     * 
     * @param reportId 报表ID
     * @param userId 用户ID
     */
    void deleteReport(UUID reportId, UUID userId);

    /**
     * 获取项目报表列表
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> getProjectReports(UUID projectId, Pageable pageable);

    /**
     * 根据类型获取报表
     * 
     * @param projectId 项目ID
     * @param reportType 报表类型
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> getReportsByType(UUID projectId, ReportType reportType, Pageable pageable);

    /**
     * 根据状态获取报表
     * 
     * @param projectId 项目ID
     * @param status 报表状态
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> getReportsByStatus(UUID projectId, ReportStatus status, Pageable pageable);

    /**
     * 获取用户生成的报表
     * 
     * @param generatedBy 生成者ID
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> getUserReports(UUID generatedBy, Pageable pageable);

    /**
     * 搜索报表
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param reportType 报表类型
     * @param status 报表状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 报表分页结果
     */
    Page<ProjectReport> searchReports(UUID projectId, String keyword, ReportType reportType,
                                     ReportStatus status, LocalDateTime startDate, LocalDateTime endDate,
                                     Pageable pageable);

    /**
     * 获取最新报表
     * 
     * @param projectId 项目ID
     * @param reportType 报表类型
     * @return 最新报表
     */
    Optional<ProjectReport> getLatestReport(UUID projectId, ReportType reportType);

    /**
     * 获取热门报表
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 热门报表列表
     */
    List<ProjectReport> getPopularReports(UUID projectId, int limit);

    /**
     * 获取最近报表
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 最近报表列表
     */
    List<ProjectReport> getRecentReports(UUID projectId, int limit);

    /**
     * 批量生成报表
     * 
     * @param limit 限制数量
     * @return 生成数量
     */
    int batchGenerateReports(int limit);

    /**
     * 重试失败的报表
     * 
     * @param retryHours 重试间隔小时数
     * @return 重试数量
     */
    int retryFailedReports(int retryHours);

    /**
     * 处理过期报表
     * 
     * @return 处理数量
     */
    int processExpiredReports();

    /**
     * 清理旧报表
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupOldReports(int days);

    /**
     * 获取报表统计信息
     * 
     * @param projectId 项目ID
     * @return 统计信息
     */
    Map<String, Object> getReportStatistics(UUID projectId);

    /**
     * 获取报表生成趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 生成趋势数据
     */
    Map<String, Object> getReportGenerationTrend(UUID projectId, int days);

    /**
     * 获取用户报表统计
     * 
     * @param generatedBy 生成者ID
     * @return 用户报表统计
     */
    Map<String, Object> getUserReportStatistics(UUID generatedBy);

    /**
     * 设置自动生成
     * 
     * @param projectId 项目ID
     * @param reportType 报表类型
     * @param isAutoGenerated 是否自动生成
     * @param generationInterval 生成间隔（小时）
     * @param generatedBy 生成者ID
     * @return 是否设置成功
     */
    boolean setAutoGeneration(UUID projectId, ReportType reportType, boolean isAutoGenerated,
                             int generationInterval, UUID generatedBy);

    /**
     * 处理自动生成的报表
     * 
     * @return 处理数量
     */
    int processAutoGenerationReports();

    /**
     * 导出报表为不同格式
     * 
     * @param reportId 报表ID
     * @param format 目标格式
     * @param userId 用户ID
     * @return 导出的报表
     */
    ProjectReport exportReport(UUID reportId, ReportFormat format, UUID userId);

    /**
     * 分享报表
     * 
     * @param reportId 报表ID
     * @param shareWithUserIds 分享用户ID列表
     * @param userId 操作用户ID
     * @return 是否分享成功
     */
    boolean shareReport(UUID reportId, List<UUID> shareWithUserIds, UUID userId);

    /**
     * 获取报表分享链接
     * 
     * @param reportId 报表ID
     * @param userId 用户ID
     * @param expirationHours 过期小时数
     * @return 分享链接
     */
    String getReportShareLink(UUID reportId, UUID userId, int expirationHours);

    /**
     * 检查用户是否可以查看报表
     * 
     * @param reportId 报表ID
     * @param userId 用户ID
     * @return 是否可以查看
     */
    boolean canViewReport(UUID reportId, UUID userId);

    /**
     * 检查用户是否可以生成报表
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否可以生成
     */
    boolean canGenerateReport(UUID projectId, UUID userId);

    /**
     * 检查用户是否可以删除报表
     * 
     * @param reportId 报表ID
     * @param userId 用户ID
     * @return 是否可以删除
     */
    boolean canDeleteReport(UUID reportId, UUID userId);

    /**
     * 获取报表模板
     * 
     * @param reportType 报表类型
     * @return 报表模板
     */
    Map<String, Object> getReportTemplate(ReportType reportType);

    /**
     * 自定义报表配置
     * 
     * @param projectId 项目ID
     * @param title 标题
     * @param config 配置信息
     * @param periodStart 开始时间
     * @param periodEnd 结束时间
     * @param generatedBy 生成者ID
     * @return 自定义报表
     */
    ProjectReport createCustomReport(UUID projectId, String title, Map<String, Object> config,
                                    LocalDateTime periodStart, LocalDateTime periodEnd, UUID generatedBy);

    /**
     * 获取报表生成进度
     * 
     * @param reportId 报表ID
     * @return 生成进度（0-100）
     */
    int getReportGenerationProgress(UUID reportId);

    /**
     * 取消报表生成
     * 
     * @param reportId 报表ID
     * @param userId 用户ID
     * @return 是否取消成功
     */
    boolean cancelReportGeneration(UUID reportId, UUID userId);
}
