package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.Sprint;
import com.aipm.projectmanagement.entity.Sprint.SprintStatus;
import com.aipm.projectmanagement.entity.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Sprint服务接口
 * 
 * 定义Sprint管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface SprintService {

    /**
     * 创建Sprint
     * 
     * @param sprint Sprint对象
     * @param creatorId 创建者ID
     * @return 创建的Sprint
     */
    Sprint createSprint(Sprint sprint, UUID creatorId);

    /**
     * 根据ID获取Sprint
     * 
     * @param sprintId Sprint ID
     * @return Sprint对象（如果存在）
     */
    Optional<Sprint> getSprintById(UUID sprintId);

    /**
     * 更新Sprint信息
     * 
     * @param sprint Sprint对象
     * @param updaterId 更新者ID
     * @return 更新后的Sprint
     */
    Sprint updateSprint(Sprint sprint, UUID updaterId);

    /**
     * 删除Sprint
     * 
     * @param sprintId Sprint ID
     * @param deleterId 删除者ID
     */
    void deleteSprint(UUID sprintId, UUID deleterId);

    /**
     * 开始Sprint
     * 
     * @param sprintId Sprint ID
     * @param userId 操作用户ID
     * @return 更新后的Sprint
     */
    Sprint startSprint(UUID sprintId, UUID userId);

    /**
     * 完成Sprint
     * 
     * @param sprintId Sprint ID
     * @param userId 操作用户ID
     * @return 更新后的Sprint
     */
    Sprint completeSprint(UUID sprintId, UUID userId);

    /**
     * 取消Sprint
     * 
     * @param sprintId Sprint ID
     * @param userId 操作用户ID
     * @return 更新后的Sprint
     */
    Sprint cancelSprint(UUID sprintId, UUID userId);

    /**
     * 更新Sprint状态
     * 
     * @param sprintId Sprint ID
     * @param status 新状态
     * @param userId 操作用户ID
     * @return 更新后的Sprint
     */
    Sprint updateSprintStatus(UUID sprintId, SprintStatus status, UUID userId);

    /**
     * 添加任务到Sprint
     * 
     * @param sprintId Sprint ID
     * @param taskId 任务ID
     * @param userId 操作用户ID
     * @return 更新后的Sprint
     */
    Sprint addTaskToSprint(UUID sprintId, UUID taskId, UUID userId);

    /**
     * 从Sprint移除任务
     * 
     * @param sprintId Sprint ID
     * @param taskId 任务ID
     * @param userId 操作用户ID
     * @return 更新后的Sprint
     */
    Sprint removeTaskFromSprint(UUID sprintId, UUID taskId, UUID userId);

    /**
     * 批量添加任务到Sprint
     * 
     * @param sprintId Sprint ID
     * @param taskIds 任务ID列表
     * @param userId 操作用户ID
     * @return 成功添加的任务数量
     */
    int batchAddTasksToSprint(UUID sprintId, List<UUID> taskIds, UUID userId);

    /**
     * 批量从Sprint移除任务
     * 
     * @param sprintId Sprint ID
     * @param taskIds 任务ID列表
     * @param userId 操作用户ID
     * @return 成功移除的任务数量
     */
    int batchRemoveTasksFromSprint(UUID sprintId, List<UUID> taskIds, UUID userId);

    /**
     * 获取项目的Sprint列表
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return Sprint分页结果
     */
    Page<Sprint> getProjectSprints(UUID projectId, Pageable pageable);

    /**
     * 获取项目当前活跃的Sprint
     * 
     * @param projectId 项目ID
     * @return 当前活跃Sprint
     */
    Optional<Sprint> getCurrentActiveSprint(UUID projectId);

    /**
     * 获取项目活跃的Sprint列表
     * 
     * @param projectId 项目ID
     * @return 活跃Sprint列表
     */
    List<Sprint> getActiveSprintsByProject(UUID projectId);

    /**
     * 搜索Sprint
     * 
     * @param projectId 项目ID
     * @param keyword 关键词
     * @param status 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return Sprint分页结果
     */
    Page<Sprint> searchSprints(UUID projectId, String keyword, SprintStatus status,
                              LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * 获取用户参与的Sprint
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return Sprint分页结果
     */
    Page<Sprint> getUserSprints(UUID userId, Pageable pageable);

    /**
     * 获取最近的Sprint
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return Sprint分页结果
     */
    Page<Sprint> getRecentSprints(UUID projectId, Pageable pageable);

    /**
     * 获取过期的Sprint
     * 
     * @return 过期Sprint列表
     */
    List<Sprint> getOverdueSprints();

    /**
     * 获取即将开始的Sprint
     * 
     * @param days 天数
     * @return 即将开始的Sprint列表
     */
    List<Sprint> getUpcomingSprints(int days);

    /**
     * 获取Sprint统计信息
     * 
     * @param projectId 项目ID
     * @return 统计信息Map
     */
    Map<String, Object> getSprintStatistics(UUID projectId);

    /**
     * 获取Sprint详细统计
     * 
     * @param sprintId Sprint ID
     * @return 详细统计信息Map
     */
    Map<String, Object> getSprintDetailStatistics(UUID sprintId);

    /**
     * 获取Sprint燃尽图数据
     * 
     * @param sprintId Sprint ID
     * @return 燃尽图数据
     */
    Map<String, Object> getSprintBurndownData(UUID sprintId);

    /**
     * 获取Sprint速度图数据
     * 
     * @param projectId 项目ID
     * @param limit 限制数量
     * @return 速度图数据
     */
    Map<String, Object> getSprintVelocityData(UUID projectId, int limit);

    /**
     * 获取Sprint任务分布
     * 
     * @param sprintId Sprint ID
     * @return 任务分布统计
     */
    Map<String, Object> getSprintTaskDistribution(UUID sprintId);

    /**
     * 获取Sprint用户统计
     * 
     * @param sprintId Sprint ID
     * @return 用户统计信息
     */
    Map<String, Object> getSprintUserStatistics(UUID sprintId);

    /**
     * 计算Sprint容量
     * 
     * @param sprintId Sprint ID
     * @return 容量信息
     */
    Map<String, Object> calculateSprintCapacity(UUID sprintId);

    /**
     * 获取Sprint进度报告
     * 
     * @param sprintId Sprint ID
     * @return 进度报告
     */
    Map<String, Object> getSprintProgressReport(UUID sprintId);

    /**
     * 复制Sprint
     * 
     * @param sprintId 源Sprint ID
     * @param newName 新Sprint名称
     * @param newStartDate 新开始日期
     * @param newEndDate 新结束日期
     * @param copyTasks 是否复制任务
     * @param userId 操作用户ID
     * @return 新Sprint
     */
    Sprint copySprint(UUID sprintId, String newName, LocalDate newStartDate, 
                     LocalDate newEndDate, boolean copyTasks, UUID userId);

    /**
     * 检查用户是否有Sprint权限
     * 
     * @param sprintId Sprint ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasSprintPermission(UUID sprintId, UUID userId);

    /**
     * 检查用户是否可以管理Sprint
     * 
     * @param sprintId Sprint ID
     * @param userId 用户ID
     * @return 是否可以管理
     */
    boolean canManageSprint(UUID sprintId, UUID userId);

    /**
     * 自动完成过期的Sprint
     * 
     * @return 自动完成的Sprint数量
     */
    int autoCompleteOverdueSprints();

    /**
     * 生成Sprint报告
     * 
     * @param sprintId Sprint ID
     * @return 报告内容
     */
    Map<String, Object> generateSprintReport(UUID sprintId);

    /**
     * 获取Sprint的任务列表
     * 
     * @param sprintId Sprint ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getSprintTasks(UUID sprintId, Pageable pageable);

    /**
     * 获取Sprint待办事项
     * 
     * @param sprintId Sprint ID
     * @return 待办任务列表
     */
    List<Task> getSprintBacklog(UUID sprintId);

    /**
     * 移动任务到其他Sprint
     * 
     * @param taskId 任务ID
     * @param targetSprintId 目标Sprint ID
     * @param userId 操作用户ID
     * @return 更新后的任务
     */
    Task moveTaskToSprint(UUID taskId, UUID targetSprintId, UUID userId);
}
