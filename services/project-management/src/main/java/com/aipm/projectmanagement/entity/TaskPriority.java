package com.aipm.projectmanagement.entity;

/**
 * 任务优先级枚举
 * 
 * 定义任务的重要性和紧急程度
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public enum TaskPriority {
    
    /**
     * 低优先级 - 不紧急不重要
     */
    LOW("低", "不紧急不重要的任务", "#10B981", 1),
    
    /**
     * 普通优先级 - 重要但不紧急
     */
    NORMAL("普通", "重要但不紧急的任务", "#3B82F6", 2),
    
    /**
     * 高优先级 - 紧急且重要
     */
    HIGH("高", "紧急且重要的任务", "#F59E0B", 3),
    
    /**
     * 紧急优先级 - 非常紧急且重要
     */
    URGENT("紧急", "非常紧急且重要的任务", "#EF4444", 4),
    
    /**
     * 关键优先级 - 阻塞性任务
     */
    CRITICAL("关键", "阻塞其他任务的关键任务", "#DC2626", 5);

    private final String displayName;
    private final String description;
    private final String color; // 用于UI显示的颜色
    private final int level; // 优先级数值，越大优先级越高

    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param description 优先级描述
     * @param color 显示颜色
     * @param level 优先级数值
     */
    TaskPriority(String displayName, String description, String color, int level) {
        this.displayName = displayName;
        this.description = description;
        this.color = color;
        this.level = level;
    }

    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取优先级描述
     * 
     * @return 优先级描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取显示颜色
     * 
     * @return 颜色代码
     */
    public String getColor() {
        return color;
    }

    /**
     * 获取优先级数值
     * 
     * @return 优先级数值，越大优先级越高
     */
    public int getLevel() {
        return level;
    }

    /**
     * 检查是否为高优先级（HIGH及以上）
     * 
     * @return true表示高优先级
     */
    public boolean isHighPriority() {
        return level >= HIGH.level;
    }

    /**
     * 检查是否为紧急优先级（URGENT及以上）
     * 
     * @return true表示紧急优先级
     */
    public boolean isUrgent() {
        return level >= URGENT.level;
    }

    /**
     * 检查是否为关键优先级
     * 
     * @return true表示关键优先级
     */
    public boolean isCritical() {
        return this == CRITICAL;
    }

    /**
     * 比较优先级高低
     * 
     * @param other 另一个优先级
     * @return 正数表示当前优先级更高，负数表示更低，0表示相等
     */
    public int compareTo(TaskPriority other) {
        return Integer.compare(this.level, other.level);
    }

    /**
     * 检查是否比另一个优先级更高
     * 
     * @param other 另一个优先级
     * @return true表示当前优先级更高
     */
    public boolean isHigherThan(TaskPriority other) {
        return this.level > other.level;
    }

    /**
     * 检查是否比另一个优先级更低
     * 
     * @param other 另一个优先级
     * @return true表示当前优先级更低
     */
    public boolean isLowerThan(TaskPriority other) {
        return this.level < other.level;
    }

    /**
     * 获取建议的处理时间（小时）
     * 
     * @return 建议处理时间
     */
    public int getSuggestedResponseTimeHours() {
        return switch (this) {
            case LOW -> 72; // 3天
            case NORMAL -> 48; // 2天
            case HIGH -> 24; // 1天
            case URGENT -> 8; // 8小时
            case CRITICAL -> 2; // 2小时
        };
    }

    /**
     * 根据显示名称查找优先级
     * 
     * @param displayName 显示名称
     * @return 对应的优先级，如果未找到返回null
     */
    public static TaskPriority fromDisplayName(String displayName) {
        for (TaskPriority priority : values()) {
            if (priority.displayName.equals(displayName)) {
                return priority;
            }
        }
        return null;
    }

    /**
     * 根据优先级数值查找优先级
     * 
     * @param level 优先级数值
     * @return 对应的优先级，如果未找到返回NORMAL
     */
    public static TaskPriority fromLevel(int level) {
        for (TaskPriority priority : values()) {
            if (priority.level == level) {
                return priority;
            }
        }
        return NORMAL; // 默认返回普通优先级
    }

    /**
     * 获取所有优先级按级别排序
     * 
     * @return 按优先级从低到高排序的数组
     */
    public static TaskPriority[] getOrderedPriorities() {
        return new TaskPriority[]{LOW, NORMAL, HIGH, URGENT, CRITICAL};
    }

    @Override
    public String toString() {
        return displayName;
    }
}
