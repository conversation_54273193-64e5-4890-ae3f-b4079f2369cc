package com.aipm.projectmanagement.dto;

import com.aipm.projectmanagement.entity.TaskPriority;
import com.aipm.projectmanagement.entity.TaskStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 任务信息DTO
 * 
 * 用于API响应中返回任务信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "任务信息")
public class TaskDto {

    /**
     * 任务ID
     */
    @Schema(description = "任务唯一标识", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    /**
     * 任务标题
     */
    @Schema(description = "任务标题", example = "实现用户认证功能")
    private String title;

    /**
     * 任务描述
     */
    @Schema(description = "任务描述", example = "实现基于JWT的用户认证和授权功能")
    private String description;

    /**
     * 所属项目ID
     */
    @Schema(description = "所属项目ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID projectId;

    /**
     * 所属项目信息
     */
    @Schema(description = "所属项目信息")
    private ProjectSummaryDto project;

    /**
     * 父任务ID
     */
    @Schema(description = "父任务ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID parentTaskId;

    /**
     * 父任务信息
     */
    @Schema(description = "父任务信息")
    private TaskSummaryDto parentTask;

    /**
     * 子任务列表
     */
    @Schema(description = "子任务列表")
    private List<TaskSummaryDto> subTasks;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态", example = "IN_PROGRESS")
    private TaskStatus status;

    /**
     * 任务优先级
     */
    @Schema(description = "任务优先级", example = "HIGH")
    private TaskPriority priority;

    /**
     * 任务分配给的用户ID
     */
    @Schema(description = "分配给的用户ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID assigneeId;

    /**
     * 分配给的用户信息
     */
    @Schema(description = "分配给的用户信息")
    private UserSummaryDto assignee;

    /**
     * 任务创建者ID
     */
    @Schema(description = "任务创建者ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID reporterId;

    /**
     * 任务创建者信息
     */
    @Schema(description = "任务创建者信息")
    private UserSummaryDto reporter;

    /**
     * 预估工时（小时）
     */
    @Schema(description = "预估工时（小时）", example = "16.5")
    private BigDecimal estimatedHours;

    /**
     * 实际工时（小时）
     */
    @Schema(description = "实际工时（小时）", example = "12.0")
    private BigDecimal actualHours;

    /**
     * 任务进度百分比
     */
    @Schema(description = "任务进度百分比", example = "75")
    private Integer progress;

    /**
     * 截止日期
     */
    @Schema(description = "截止日期", example = "2025-08-20T18:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-08-15T09:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间", example = "2025-08-18T17:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedDate;

    /**
     * 任务标签
     */
    @Schema(description = "任务标签", example = "[\"认证\", \"安全\", \"后端\"]")
    private List<String> tags;

    /**
     * 任务排序序号
     */
    @Schema(description = "任务排序序号", example = "1")
    private Integer sortOrder;

    /**
     * 是否已删除
     */
    @Schema(description = "是否已删除", example = "false")
    private Boolean isDeleted;

    /**
     * 是否过期
     */
    @Schema(description = "是否过期", example = "false")
    private Boolean isOverdue;

    /**
     * 是否被阻塞
     */
    @Schema(description = "是否被阻塞", example = "false")
    private Boolean isBlocked;

    /**
     * 子任务数量
     */
    @Schema(description = "子任务数量", example = "3")
    private Integer subTaskCount;

    /**
     * 子任务完成率
     */
    @Schema(description = "子任务完成率", example = "66.7")
    private Double subTaskCompletionRate;

    /**
     * 剩余工时
     */
    @Schema(description = "剩余工时", example = "4.5")
    private BigDecimal remainingHours;

    /**
     * 工时完成率
     */
    @Schema(description = "工时完成率", example = "72.7")
    private Double hoursCompletionRate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-08-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-08-15T15:45:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID createdBy;

    /**
     * 更新者ID
     */
    @Schema(description = "更新者ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID updatedBy;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public TaskDto() {
    }

    /**
     * 基础构造函数
     * 
     * @param id 任务ID
     * @param title 任务标题
     * @param status 任务状态
     * @param priority 任务优先级
     */
    public TaskDto(UUID id, String title, TaskStatus status, TaskPriority priority) {
        this.id = id;
        this.title = title;
        this.status = status;
        this.priority = priority;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查任务是否处于活跃状态
     * 
     * @return true表示任务活跃
     */
    public boolean isActive() {
        return status != null && status.isActive() && !Boolean.TRUE.equals(isDeleted);
    }

    /**
     * 检查任务是否已完成
     * 
     * @return true表示任务已完成
     */
    public boolean isCompleted() {
        return status != null && status.isCompleted();
    }

    /**
     * 获取任务状态显示名称
     * 
     * @return 状态显示名称
     */
    public String getStatusDisplayName() {
        return status != null ? status.getDisplayName() : "未知";
    }

    /**
     * 获取优先级显示名称
     * 
     * @return 优先级显示名称
     */
    public String getPriorityDisplayName() {
        return priority != null ? priority.getDisplayName() : "普通";
    }

    /**
     * 获取优先级颜色
     * 
     * @return 优先级颜色
     */
    public String getPriorityColor() {
        return priority != null ? priority.getColor() : "#3B82F6";
    }

    /**
     * 获取状态颜色
     * 
     * @return 状态颜色
     */
    public String getStatusColor() {
        return status != null ? status.getColor() : "#6B7280";
    }

    /**
     * 检查是否为子任务
     * 
     * @return true表示是子任务
     */
    public boolean isSubTask() {
        return parentTaskId != null;
    }

    /**
     * 检查是否有子任务
     * 
     * @return true表示有子任务
     */
    public boolean hasSubTasks() {
        return subTaskCount != null && subTaskCount > 0;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UUID getProjectId() {
        return projectId;
    }

    public void setProjectId(UUID projectId) {
        this.projectId = projectId;
    }

    public ProjectSummaryDto getProject() {
        return project;
    }

    public void setProject(ProjectSummaryDto project) {
        this.project = project;
    }

    public UUID getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(UUID parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public TaskSummaryDto getParentTask() {
        return parentTask;
    }

    public void setParentTask(TaskSummaryDto parentTask) {
        this.parentTask = parentTask;
    }

    public List<TaskSummaryDto> getSubTasks() {
        return subTasks;
    }

    public void setSubTasks(List<TaskSummaryDto> subTasks) {
        this.subTasks = subTasks;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public UUID getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(UUID assigneeId) {
        this.assigneeId = assigneeId;
    }

    public UserSummaryDto getAssignee() {
        return assignee;
    }

    public void setAssignee(UserSummaryDto assignee) {
        this.assignee = assignee;
    }

    public UUID getReporterId() {
        return reporterId;
    }

    public void setReporterId(UUID reporterId) {
        this.reporterId = reporterId;
    }

    public UserSummaryDto getReporter() {
        return reporter;
    }

    public void setReporter(UserSummaryDto reporter) {
        this.reporter = reporter;
    }

    public BigDecimal getEstimatedHours() {
        return estimatedHours;
    }

    public void setEstimatedHours(BigDecimal estimatedHours) {
        this.estimatedHours = estimatedHours;
    }

    public BigDecimal getActualHours() {
        return actualHours;
    }

    public void setActualHours(BigDecimal actualHours) {
        this.actualHours = actualHours;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public LocalDateTime getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(LocalDateTime completedDate) {
        this.completedDate = completedDate;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Boolean getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(Boolean isOverdue) {
        this.isOverdue = isOverdue;
    }

    public Boolean getIsBlocked() {
        return isBlocked;
    }

    public void setIsBlocked(Boolean isBlocked) {
        this.isBlocked = isBlocked;
    }

    public Integer getSubTaskCount() {
        return subTaskCount;
    }

    public void setSubTaskCount(Integer subTaskCount) {
        this.subTaskCount = subTaskCount;
    }

    public Double getSubTaskCompletionRate() {
        return subTaskCompletionRate;
    }

    public void setSubTaskCompletionRate(Double subTaskCompletionRate) {
        this.subTaskCompletionRate = subTaskCompletionRate;
    }

    public BigDecimal getRemainingHours() {
        return remainingHours;
    }

    public void setRemainingHours(BigDecimal remainingHours) {
        this.remainingHours = remainingHours;
    }

    public Double getHoursCompletionRate() {
        return hoursCompletionRate;
    }

    public void setHoursCompletionRate(Double hoursCompletionRate) {
        this.hoursCompletionRate = hoursCompletionRate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public UUID getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TaskDto taskDto)) return false;
        return id != null && id.equals(taskDto.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "TaskDto{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", status=" + status +
                ", priority=" + priority +
                ", assigneeId=" + assigneeId +
                ", progress=" + progress +
                '}';
    }
}
