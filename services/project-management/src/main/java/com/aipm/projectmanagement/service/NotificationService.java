package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.ActivityLog;
import com.aipm.projectmanagement.entity.Notification;
import com.aipm.projectmanagement.entity.Notification.NotificationStatus;
import com.aipm.projectmanagement.entity.Notification.NotificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 通知服务接口
 * 
 * 定义通知管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface NotificationService {

    /**
     * 创建通知
     * 
     * @param notification 通知对象
     * @return 创建的通知
     */
    Notification createNotification(Notification notification);

    /**
     * 创建简单通知
     * 
     * @param notificationType 通知类型
     * @param title 标题
     * @param content 内容
     * @param recipientId 接收者ID
     * @return 创建的通知
     */
    Notification createNotification(NotificationType notificationType, String title, 
                                   String content, UUID recipientId);

    /**
     * 创建任务分配通知
     * 
     * @param recipientId 接收者ID
     * @param senderId 发送者ID
     * @param taskId 任务ID
     * @param taskTitle 任务标题
     * @param projectId 项目ID
     * @return 创建的通知
     */
    Notification createTaskAssignedNotification(UUID recipientId, UUID senderId, 
                                               UUID taskId, String taskTitle, UUID projectId);

    /**
     * 创建任务评论通知
     * 
     * @param recipientId 接收者ID
     * @param senderId 发送者ID
     * @param taskId 任务ID
     * @param taskTitle 任务标题
     * @param projectId 项目ID
     * @return 创建的通知
     */
    Notification createTaskCommentNotification(UUID recipientId, UUID senderId, 
                                              UUID taskId, String taskTitle, UUID projectId);

    /**
     * 创建项目成员添加通知
     * 
     * @param recipientId 接收者ID
     * @param senderId 发送者ID
     * @param projectId 项目ID
     * @param projectName 项目名称
     * @return 创建的通知
     */
    Notification createProjectMemberAddedNotification(UUID recipientId, UUID senderId, 
                                                     UUID projectId, String projectName);

    /**
     * 根据ID获取通知
     * 
     * @param notificationId 通知ID
     * @return 通知对象（如果存在）
     */
    Optional<Notification> getNotificationById(UUID notificationId);

    /**
     * 标记通知为已读
     * 
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 更新后的通知
     */
    Notification markAsRead(UUID notificationId, UUID userId);

    /**
     * 标记通知为未读
     * 
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 更新后的通知
     */
    Notification markAsUnread(UUID notificationId, UUID userId);

    /**
     * 批量标记通知为已读
     * 
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     * @return 更新数量
     */
    int batchMarkAsRead(List<UUID> notificationIds, UUID userId);

    /**
     * 标记用户所有通知为已读
     * 
     * @param userId 用户ID
     * @return 更新数量
     */
    int markAllAsReadForUser(UUID userId);

    /**
     * 删除通知
     * 
     * @param notificationId 通知ID
     * @param userId 用户ID
     */
    void deleteNotification(UUID notificationId, UUID userId);

    /**
     * 获取用户通知
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    Page<Notification> getUserNotifications(UUID userId, Pageable pageable);

    /**
     * 获取用户未读通知
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 未读通知分页结果
     */
    Page<Notification> getUnreadNotifications(UUID userId, Pageable pageable);

    /**
     * 获取用户指定类型的通知
     * 
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    Page<Notification> getNotificationsByType(UUID userId, NotificationType notificationType, Pageable pageable);

    /**
     * 获取项目通知
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    Page<Notification> getProjectNotifications(UUID projectId, Pageable pageable);

    /**
     * 获取用户最近通知
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近通知列表
     */
    List<Notification> getRecentNotifications(UUID userId, int limit);

    /**
     * 获取高优先级未读通知
     * 
     * @param userId 用户ID
     * @param minPriority 最小优先级
     * @param pageable 分页参数
     * @return 高优先级通知分页结果
     */
    Page<Notification> getHighPriorityUnreadNotifications(UUID userId, Integer minPriority, Pageable pageable);

    /**
     * 搜索通知
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @param notificationType 通知类型
     * @param isRead 是否已读
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 通知分页结果
     */
    Page<Notification> searchNotifications(UUID userId, String keyword, NotificationType notificationType,
                                          Boolean isRead, LocalDateTime startDate, LocalDateTime endDate,
                                          Pageable pageable);

    /**
     * 统计用户未读通知数量
     * 
     * @param userId 用户ID
     * @return 未读通知数量
     */
    long countUnreadNotifications(UUID userId);

    /**
     * 统计用户指定类型的未读通知数量
     * 
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 未读通知数量
     */
    long countUnreadNotificationsByType(UUID userId, NotificationType notificationType);

    /**
     * 获取通知类型统计
     * 
     * @param userId 用户ID
     * @return 类型统计
     */
    Map<String, Object> getNotificationTypeStatistics(UUID userId);

    /**
     * 获取用户通知活跃度
     * 
     * @param userId 用户ID
     * @param days 天数
     * @return 活跃度数据
     */
    Map<String, Object> getUserNotificationActivity(UUID userId, int days);

    /**
     * 获取通知摘要
     * 
     * @param userId 用户ID
     * @param days 天数
     * @return 通知摘要
     */
    Map<String, Object> getNotificationSummary(UUID userId, int days);

    /**
     * 发送通知
     * 
     * @param notificationId 通知ID
     * @return 是否发送成功
     */
    boolean sendNotification(UUID notificationId);

    /**
     * 批量发送通知
     * 
     * @param limit 限制数量
     * @return 发送数量
     */
    int batchSendNotifications(int limit);

    /**
     * 发送邮件通知
     * 
     * @param notificationId 通知ID
     * @return 是否发送成功
     */
    boolean sendEmailNotification(UUID notificationId);

    /**
     * 发送推送通知
     * 
     * @param notificationId 通知ID
     * @return 是否发送成功
     */
    boolean sendPushNotification(UUID notificationId);

    /**
     * 发送短信通知
     * 
     * @param notificationId 通知ID
     * @return 是否发送成功
     */
    boolean sendSmsNotification(UUID notificationId);

    /**
     * 处理过期通知
     * 
     * @return 处理数量
     */
    int processExpiredNotifications();

    /**
     * 重试失败的通知
     * 
     * @param retryHours 重试间隔小时数
     * @return 重试数量
     */
    int retryFailedNotifications(int retryHours);

    /**
     * 清理旧的已读通知
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupOldReadNotifications(int days);

    /**
     * 获取通知发送统计
     * 
     * @param days 天数
     * @return 发送统计
     */
    Map<String, Object> getNotificationSendingStatistics(int days);

    /**
     * 获取项目通知统计
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 项目通知统计
     */
    Map<String, Object> getProjectNotificationStatistics(UUID projectId, int days);

    /**
     * 检查用户是否可以接收通知
     * 
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 是否可以接收
     */
    boolean canReceiveNotification(UUID userId, NotificationType notificationType);

    /**
     * 获取用户通知设置
     * 
     * @param userId 用户ID
     * @return 通知设置
     */
    Map<String, Object> getUserNotificationSettings(UUID userId);

    /**
     * 更新用户通知设置
     * 
     * @param userId 用户ID
     * @param settings 通知设置
     * @return 是否更新成功
     */
    boolean updateUserNotificationSettings(UUID userId, Map<String, Object> settings);

    /**
     * 创建系统公告通知
     * 
     * @param title 标题
     * @param content 内容
     * @param recipientIds 接收者ID列表
     * @return 创建数量
     */
    int createSystemAnnouncementNotifications(String title, String content, List<UUID> recipientIds);

    /**
     * 创建提醒通知
     * 
     * @param recipientId 接收者ID
     * @param title 标题
     * @param content 内容
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param projectId 项目ID
     * @param reminderTime 提醒时间
     * @return 创建的通知
     */
    Notification createReminderNotification(UUID recipientId, String title, String content,
                                           ActivityLog.TargetType targetType, UUID targetId,
                                           UUID projectId, LocalDateTime reminderTime);

    /**
     * 处理定时提醒
     * 
     * @return 处理数量
     */
    int processScheduledReminders();

    /**
     * 异步发送通知
     * 
     * @param notification 通知对象
     */
    void sendNotificationAsync(Notification notification);

    /**
     * 批量创建通知
     * 
     * @param notifications 通知列表
     * @return 创建数量
     */
    int batchCreateNotifications(List<Notification> notifications);
}
