package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 通知实体类
 * 
 * 表示系统通知和消息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "notifications", indexes = {
    @Index(name = "idx_notification_recipient", columnList = "recipient_id"),
    @Index(name = "idx_notification_sender", columnList = "sender_id"),
    @Index(name = "idx_notification_type", columnList = "notification_type"),
    @Index(name = "idx_notification_read", columnList = "is_read"),
    @Index(name = "idx_notification_target", columnList = "target_type, target_id"),
    @Index(name = "idx_notification_created", columnList = "created_at")
})
public class Notification {

    /**
     * 通知类型枚举
     */
    public enum NotificationType {
        // 任务相关
        TASK_ASSIGNED("任务分配", "您被分配了新任务", "info"),
        TASK_UNASSIGNED("任务取消分配", "您的任务分配被取消", "warning"),
        TASK_STATUS_CHANGED("任务状态变更", "任务状态已更新", "info"),
        TASK_DUE_SOON("任务即将到期", "您的任务即将到期", "warning"),
        TASK_OVERDUE("任务已过期", "您的任务已过期", "danger"),
        TASK_COMPLETED("任务已完成", "任务已完成", "success"),
        TASK_COMMENTED("任务新评论", "您的任务有新评论", "info"),
        
        // 项目相关
        PROJECT_MEMBER_ADDED("项目成员添加", "您被添加到项目中", "success"),
        PROJECT_MEMBER_REMOVED("项目成员移除", "您被移出项目", "warning"),
        PROJECT_ROLE_CHANGED("项目角色变更", "您的项目角色已更改", "info"),
        PROJECT_STATUS_CHANGED("项目状态变更", "项目状态已更新", "info"),
        PROJECT_DEADLINE_APPROACHING("项目截止日期临近", "项目截止日期临近", "warning"),
        
        // Sprint相关
        SPRINT_STARTED("Sprint开始", "Sprint已开始", "success"),
        SPRINT_COMPLETED("Sprint完成", "Sprint已完成", "success"),
        SPRINT_CANCELLED("Sprint取消", "Sprint已取消", "warning"),
        SPRINT_TASK_ADDED("Sprint任务添加", "任务已添加到Sprint", "info"),
        SPRINT_TASK_REMOVED("Sprint任务移除", "任务已从Sprint移除", "info"),
        
        // 评论相关
        COMMENT_MENTIONED("评论提及", "您在评论中被提及", "info"),
        COMMENT_REPLIED("评论回复", "您的评论有新回复", "info"),
        COMMENT_LIKED("评论点赞", "您的评论被点赞", "success"),
        
        // 系统相关
        SYSTEM_ANNOUNCEMENT("系统公告", "系统公告", "info"),
        SYSTEM_MAINTENANCE("系统维护", "系统维护通知", "warning"),
        SYSTEM_UPDATE("系统更新", "系统更新通知", "info"),
        
        // 其他
        REMINDER("提醒", "提醒通知", "info"),
        INVITATION("邀请", "邀请通知", "info"),
        WELCOME("欢迎", "欢迎消息", "success");

        private final String displayName;
        private final String defaultMessage;
        private final String level; // success, info, warning, danger

        NotificationType(String displayName, String defaultMessage, String level) {
            this.displayName = displayName;
            this.defaultMessage = defaultMessage;
            this.level = level;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDefaultMessage() {
            return defaultMessage;
        }

        public String getLevel() {
            return level;
        }
    }

    /**
     * 通知状态枚举
     */
    public enum NotificationStatus {
        PENDING("待发送"),
        SENT("已发送"),
        DELIVERED("已送达"),
        READ("已读"),
        FAILED("发送失败");

        private final String displayName;

        NotificationStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 通知唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 通知类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type", nullable = false)
    private NotificationType notificationType;

    /**
     * 通知标题
     */
    @Column(name = "title", nullable = false, length = 255)
    private String title;

    /**
     * 通知内容
     */
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    /**
     * 接收者ID
     */
    @Column(name = "recipient_id", nullable = false)
    private UUID recipientId;

    /**
     * 发送者ID
     */
    @Column(name = "sender_id")
    private UUID senderId;

    /**
     * 关联目标类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "target_type")
    private ActivityLog.TargetType targetType;

    /**
     * 关联目标ID
     */
    @Column(name = "target_id")
    private UUID targetId;

    /**
     * 关联项目ID
     */
    @Column(name = "project_id")
    private UUID projectId;

    /**
     * 通知状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private NotificationStatus status = NotificationStatus.PENDING;

    /**
     * 是否已读
     */
    @Column(name = "is_read", nullable = false)
    private Boolean isRead = false;

    /**
     * 优先级（1-5，5最高）
     */
    @Column(name = "priority", nullable = false)
    private Integer priority = 3;

    /**
     * 是否需要邮件通知
     */
    @Column(name = "email_notification", nullable = false)
    private Boolean emailNotification = false;

    /**
     * 是否需要短信通知
     */
    @Column(name = "sms_notification", nullable = false)
    private Boolean smsNotification = false;

    /**
     * 是否需要推送通知
     */
    @Column(name = "push_notification", nullable = false)
    private Boolean pushNotification = true;

    /**
     * 通知数据（JSON格式）
     */
    @Column(name = "data", columnDefinition = "TEXT")
    private String data;

    /**
     * 过期时间
     */
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    /**
     * 阅读时间
     */
    @Column(name = "read_at")
    private LocalDateTime readAt;

    /**
     * 发送时间
     */
    @Column(name = "sent_at")
    private LocalDateTime sentAt;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public Notification() {
    }

    /**
     * 基础构造函数
     * 
     * @param notificationType 通知类型
     * @param title 标题
     * @param content 内容
     * @param recipientId 接收者ID
     */
    public Notification(NotificationType notificationType, String title, String content, UUID recipientId) {
        this.notificationType = notificationType;
        this.title = title;
        this.content = content;
        this.recipientId = recipientId;
    }

    /**
     * 完整构造函数
     * 
     * @param notificationType 通知类型
     * @param title 标题
     * @param content 内容
     * @param recipientId 接收者ID
     * @param senderId 发送者ID
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param projectId 项目ID
     */
    public Notification(NotificationType notificationType, String title, String content, 
                       UUID recipientId, UUID senderId, ActivityLog.TargetType targetType, 
                       UUID targetId, UUID projectId) {
        this.notificationType = notificationType;
        this.title = title;
        this.content = content;
        this.recipientId = recipientId;
        this.senderId = senderId;
        this.targetType = targetType;
        this.targetId = targetId;
        this.projectId = projectId;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 标记为已读
     */
    public void markAsRead() {
        this.isRead = true;
        this.readAt = LocalDateTime.now();
        this.status = NotificationStatus.READ;
    }

    /**
     * 标记为未读
     */
    public void markAsUnread() {
        this.isRead = false;
        this.readAt = null;
        if (this.status == NotificationStatus.READ) {
            this.status = NotificationStatus.DELIVERED;
        }
    }

    /**
     * 标记为已发送
     */
    public void markAsSent() {
        this.status = NotificationStatus.SENT;
        this.sentAt = LocalDateTime.now();
    }

    /**
     * 标记为已送达
     */
    public void markAsDelivered() {
        this.status = NotificationStatus.DELIVERED;
    }

    /**
     * 标记为发送失败
     */
    public void markAsFailed() {
        this.status = NotificationStatus.FAILED;
    }

    /**
     * 检查是否已过期
     * 
     * @return true表示已过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查是否为高优先级
     * 
     * @return true表示高优先级
     */
    public boolean isHighPriority() {
        return priority != null && priority >= 4;
    }

    /**
     * 检查是否需要任何形式的通知
     * 
     * @return true表示需要通知
     */
    public boolean needsNotification() {
        return Boolean.TRUE.equals(emailNotification) || 
               Boolean.TRUE.equals(smsNotification) || 
               Boolean.TRUE.equals(pushNotification);
    }

    /**
     * 获取通知级别CSS类
     * 
     * @return CSS类名
     */
    public String getLevelClass() {
        return "notification-" + notificationType.getLevel();
    }

    /**
     * 获取通知图标
     * 
     * @return 图标CSS类
     */
    public String getIconClass() {
        return switch (notificationType.getLevel()) {
            case "success" -> "fa-check-circle";
            case "warning" -> "fa-exclamation-triangle";
            case "danger" -> "fa-times-circle";
            default -> "fa-info-circle";
        };
    }

    /**
     * 设置过期时间（从现在开始的天数）
     * 
     * @param days 天数
     */
    public void setExpiresInDays(int days) {
        this.expiresAt = LocalDateTime.now().plusDays(days);
    }

    // ============================================================================
    // 静态工厂方法
    // ============================================================================

    /**
     * 创建任务分配通知
     */
    public static Notification createTaskAssignedNotification(UUID recipientId, UUID senderId, 
                                                            UUID taskId, String taskTitle, UUID projectId) {
        String title = "新任务分配";
        String content = String.format("您被分配了新任务：%s", taskTitle);
        
        Notification notification = new Notification(NotificationType.TASK_ASSIGNED, title, content, 
                                                    recipientId, senderId, ActivityLog.TargetType.TASK, taskId, projectId);
        notification.setEmailNotification(true);
        notification.setPushNotification(true);
        return notification;
    }

    /**
     * 创建任务评论通知
     */
    public static Notification createTaskCommentNotification(UUID recipientId, UUID senderId, 
                                                           UUID taskId, String taskTitle, UUID projectId) {
        String title = "任务新评论";
        String content = String.format("您的任务 \"%s\" 有新评论", taskTitle);
        
        return new Notification(NotificationType.TASK_COMMENTED, title, content, 
                               recipientId, senderId, ActivityLog.TargetType.TASK, taskId, projectId);
    }

    /**
     * 创建项目成员添加通知
     */
    public static Notification createProjectMemberAddedNotification(UUID recipientId, UUID senderId, 
                                                                   UUID projectId, String projectName) {
        String title = "项目邀请";
        String content = String.format("您被添加到项目 \"%s\" 中", projectName);
        
        Notification notification = new Notification(NotificationType.PROJECT_MEMBER_ADDED, title, content, 
                                                    recipientId, senderId, ActivityLog.TargetType.PROJECT, projectId, projectId);
        notification.setEmailNotification(true);
        notification.setPushNotification(true);
        return notification;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public NotificationType getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(NotificationType notificationType) {
        this.notificationType = notificationType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public UUID getRecipientId() {
        return recipientId;
    }

    public void setRecipientId(UUID recipientId) {
        this.recipientId = recipientId;
    }

    public UUID getSenderId() {
        return senderId;
    }

    public void setSenderId(UUID senderId) {
        this.senderId = senderId;
    }

    public ActivityLog.TargetType getTargetType() {
        return targetType;
    }

    public void setTargetType(ActivityLog.TargetType targetType) {
        this.targetType = targetType;
    }

    public UUID getTargetId() {
        return targetId;
    }

    public void setTargetId(UUID targetId) {
        this.targetId = targetId;
    }

    public UUID getProjectId() {
        return projectId;
    }

    public void setProjectId(UUID projectId) {
        this.projectId = projectId;
    }

    public NotificationStatus getStatus() {
        return status;
    }

    public void setStatus(NotificationStatus status) {
        this.status = status;
    }

    public Boolean getIsRead() {
        return isRead;
    }

    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Boolean getEmailNotification() {
        return emailNotification;
    }

    public void setEmailNotification(Boolean emailNotification) {
        this.emailNotification = emailNotification;
    }

    public Boolean getSmsNotification() {
        return smsNotification;
    }

    public void setSmsNotification(Boolean smsNotification) {
        this.smsNotification = smsNotification;
    }

    public Boolean getPushNotification() {
        return pushNotification;
    }

    public void setPushNotification(Boolean pushNotification) {
        this.pushNotification = pushNotification;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public LocalDateTime getReadAt() {
        return readAt;
    }

    public void setReadAt(LocalDateTime readAt) {
        this.readAt = readAt;
    }

    public LocalDateTime getSentAt() {
        return sentAt;
    }

    public void setSentAt(LocalDateTime sentAt) {
        this.sentAt = sentAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Notification that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "Notification{" +
                "id=" + id +
                ", notificationType=" + notificationType +
                ", title='" + title + '\'' +
                ", recipientId=" + recipientId +
                ", isRead=" + isRead +
                ", status=" + status +
                ", priority=" + priority +
                '}';
    }
}
