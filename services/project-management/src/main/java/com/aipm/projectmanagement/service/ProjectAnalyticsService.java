package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.ProjectAnalytics;
import com.aipm.projectmanagement.entity.ProjectAnalytics.AnalyticsType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 项目分析服务接口
 * 
 * 定义项目分析数据管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
public interface ProjectAnalyticsService {

    /**
     * 创建分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param analyticsDate 分析日期
     * @return 创建的分析数据
     */
    ProjectAnalytics createAnalytics(UUID projectId, AnalyticsType analyticsType, LocalDate analyticsDate);

    /**
     * 生成日度分析数据
     * 
     * @param projectId 项目ID
     * @param date 日期
     * @return 生成的分析数据
     */
    ProjectAnalytics generateDailyAnalytics(UUID projectId, LocalDate date);

    /**
     * 生成周度分析数据
     * 
     * @param projectId 项目ID
     * @param weekStart 周开始日期
     * @return 生成的分析数据
     */
    ProjectAnalytics generateWeeklyAnalytics(UUID projectId, LocalDate weekStart);

    /**
     * 生成月度分析数据
     * 
     * @param projectId 项目ID
     * @param monthStart 月开始日期
     * @return 生成的分析数据
     */
    ProjectAnalytics generateMonthlyAnalytics(UUID projectId, LocalDate monthStart);

    /**
     * 异步生成分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param analyticsDate 分析日期
     */
    void generateAnalyticsAsync(UUID projectId, AnalyticsType analyticsType, LocalDate analyticsDate);

    /**
     * 更新分析数据
     * 
     * @param analyticsId 分析数据ID
     * @param analytics 更新的分析数据
     * @return 更新后的分析数据
     */
    ProjectAnalytics updateAnalytics(UUID analyticsId, ProjectAnalytics analytics);

    /**
     * 根据ID获取分析数据
     * 
     * @param analyticsId 分析数据ID
     * @return 分析数据（如果存在）
     */
    Optional<ProjectAnalytics> getAnalyticsById(UUID analyticsId);

    /**
     * 获取特定日期的分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param analyticsDate 分析日期
     * @return 分析数据（如果存在）
     */
    Optional<ProjectAnalytics> getAnalyticsByDate(UUID projectId, AnalyticsType analyticsType, LocalDate analyticsDate);

    /**
     * 获取最新的分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @return 最新分析数据
     */
    Optional<ProjectAnalytics> getLatestAnalytics(UUID projectId, AnalyticsType analyticsType);

    /**
     * 获取项目分析数据列表
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 分析数据分页结果
     */
    Page<ProjectAnalytics> getProjectAnalytics(UUID projectId, Pageable pageable);

    /**
     * 根据类型获取分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param pageable 分页参数
     * @return 分析数据分页结果
     */
    Page<ProjectAnalytics> getAnalyticsByType(UUID projectId, AnalyticsType analyticsType, Pageable pageable);

    /**
     * 获取日期范围内的分析数据
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 分析数据分页结果
     */
    Page<ProjectAnalytics> getAnalyticsByDateRange(UUID projectId, LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * 获取最近N天的分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param days 天数
     * @return 分析数据列表
     */
    List<ProjectAnalytics> getRecentAnalytics(UUID projectId, AnalyticsType analyticsType, int days);

    /**
     * 获取任务完成率趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 完成率趋势数据
     */
    Map<String, Object> getTaskCompletionTrend(UUID projectId, int days);

    /**
     * 获取团队效率趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 效率趋势数据
     */
    Map<String, Object> getTeamEfficiencyTrend(UUID projectId, int days);

    /**
     * 获取项目进度趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 进度趋势数据
     */
    Map<String, Object> getProjectProgressTrend(UUID projectId, int days);

    /**
     * 获取质量分数趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 质量趋势数据
     */
    Map<String, Object> getQualityTrend(UUID projectId, int days);

    /**
     * 获取风险等级分布
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 风险分布数据
     */
    Map<String, Object> getRiskLevelDistribution(UUID projectId, int days);

    /**
     * 获取任务状态分布趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 任务状态趋势数据
     */
    Map<String, Object> getTaskStatusTrend(UUID projectId, int days);

    /**
     * 获取团队活跃度趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 活跃度趋势数据
     */
    Map<String, Object> getTeamActivityTrend(UUID projectId, int days);

    /**
     * 获取工时分析数据
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 工时分析数据
     */
    Map<String, Object> getTimeAnalysis(UUID projectId, int days);

    /**
     * 获取协作活动趋势
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 协作活动趋势数据
     */
    Map<String, Object> getCollaborationTrend(UUID projectId, int days);

    /**
     * 获取项目健康度指标
     * 
     * @param projectId 项目ID
     * @param date 指定日期（可选，默认最新）
     * @return 健康度指标
     */
    Map<String, Object> getProjectHealthMetrics(UUID projectId, LocalDate date);

    /**
     * 获取项目仪表板数据
     * 
     * @param projectId 项目ID
     * @return 仪表板数据
     */
    Map<String, Object> getProjectDashboard(UUID projectId);

    /**
     * 获取平均指标
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 平均指标数据
     */
    Map<String, Object> getAverageMetrics(UUID projectId, int days);

    /**
     * 获取高风险日期
     * 
     * @param projectId 项目ID
     * @param minRiskLevel 最小风险等级
     * @param days 天数
     * @return 高风险日期列表
     */
    List<Map<String, Object>> getHighRiskDates(UUID projectId, int minRiskLevel, int days);

    /**
     * 获取最佳表现日期
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @param limit 限制数量
     * @return 最佳表现日期列表
     */
    List<Map<String, Object>> getBestPerformanceDates(UUID projectId, int days, int limit);

    /**
     * 获取分析摘要
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 分析摘要
     */
    Map<String, Object> getAnalyticsSummary(UUID projectId, int days);

    /**
     * 预测项目完成时间
     * 
     * @param projectId 项目ID
     * @return 预测完成时间
     */
    Map<String, Object> predictProjectCompletion(UUID projectId);

    /**
     * 分析项目风险
     * 
     * @param projectId 项目ID
     * @return 风险分析结果
     */
    Map<String, Object> analyzeProjectRisks(UUID projectId);

    /**
     * 生成项目洞察
     * 
     * @param projectId 项目ID
     * @return 项目洞察
     */
    Map<String, Object> generateProjectInsights(UUID projectId);

    /**
     * 比较项目表现
     * 
     * @param projectIds 项目ID列表
     * @param days 天数
     * @return 比较结果
     */
    Map<String, Object> compareProjectPerformance(List<UUID> projectIds, int days);

    /**
     * 获取团队绩效分析
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 团队绩效分析
     */
    Map<String, Object> getTeamPerformanceAnalysis(UUID projectId, int days);

    /**
     * 获取资源利用率分析
     * 
     * @param projectId 项目ID
     * @param days 天数
     * @return 资源利用率分析
     */
    Map<String, Object> getResourceUtilizationAnalysis(UUID projectId, int days);

    /**
     * 批量生成分析数据
     * 
     * @param projectIds 项目ID列表
     * @param analyticsType 分析类型
     * @param analyticsDate 分析日期
     * @return 生成数量
     */
    int batchGenerateAnalytics(List<UUID> projectIds, AnalyticsType analyticsType, LocalDate analyticsDate);

    /**
     * 自动生成今日分析数据
     * 
     * @return 生成数量
     */
    int autoGenerateTodayAnalytics();

    /**
     * 清理旧的分析数据
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupOldAnalytics(int days);

    /**
     * 重新计算分析数据
     * 
     * @param analyticsId 分析数据ID
     * @return 重新计算的分析数据
     */
    ProjectAnalytics recalculateAnalytics(UUID analyticsId);

    /**
     * 导出分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 导出数据
     */
    String exportAnalyticsData(UUID projectId, AnalyticsType analyticsType, LocalDate startDate, LocalDate endDate);

    /**
     * 检查是否存在分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param analyticsDate 分析日期
     * @return 是否存在
     */
    boolean hasAnalyticsData(UUID projectId, AnalyticsType analyticsType, LocalDate analyticsDate);

    /**
     * 获取分析数据完整性报告
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 完整性报告
     */
    Map<String, Object> getAnalyticsCompletenessReport(UUID projectId, LocalDate startDate, LocalDate endDate);
}
