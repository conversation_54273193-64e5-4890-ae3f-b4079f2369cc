package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.Attachment;
import com.aipm.projectmanagement.entity.Attachment.AttachmentType;
import com.aipm.projectmanagement.entity.Attachment.TargetType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 附件数据访问接口
 * 
 * 提供附件相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface AttachmentRepository extends JpaRepository<Attachment, UUID> {

    /**
     * 根据目标类型和目标ID查询附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 附件分页结果
     */
    Page<Attachment> findByTargetTypeAndTargetId(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 根据目标类型和目标ID查询所有附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 附件列表
     */
    List<Attachment> findByTargetTypeAndTargetIdOrderByCreatedAtDesc(TargetType targetType, UUID targetId);

    /**
     * 根据附件类型查询附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param attachmentType 附件类型
     * @param pageable 分页参数
     * @return 附件分页结果
     */
    Page<Attachment> findByTargetTypeAndTargetIdAndAttachmentType(TargetType targetType, UUID targetId, 
                                                                 AttachmentType attachmentType, Pageable pageable);

    /**
     * 根据上传者查询附件
     * 
     * @param uploadedBy 上传者ID
     * @param pageable 分页参数
     * @return 附件分页结果
     */
    Page<Attachment> findByUploadedByOrderByCreatedAtDesc(UUID uploadedBy, Pageable pageable);

    /**
     * 根据评论ID查询附件
     * 
     * @param commentId 评论ID
     * @return 附件列表
     */
    List<Attachment> findByCommentIdOrderByCreatedAtAsc(UUID commentId);

    /**
     * 根据文件哈希查询附件
     * 
     * @param fileHash 文件哈希
     * @return 附件（如果存在）
     */
    Optional<Attachment> findByFileHash(String fileHash);

    /**
     * 统计目标的附件数量
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 附件数量
     */
    long countByTargetTypeAndTargetId(TargetType targetType, UUID targetId);

    /**
     * 统计目标的附件总大小
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 总大小（字节）
     */
    @Query("SELECT COALESCE(SUM(a.fileSize), 0) FROM Attachment a " +
           "WHERE a.targetType = :targetType AND a.targetId = :targetId")
    long getTotalSizeByTarget(@Param("targetType") TargetType targetType, @Param("targetId") UUID targetId);

    /**
     * 统计用户上传的附件总大小
     * 
     * @param uploadedBy 上传者ID
     * @return 总大小（字节）
     */
    @Query("SELECT COALESCE(SUM(a.fileSize), 0) FROM Attachment a WHERE a.uploadedBy = :uploadedBy")
    long getTotalSizeByUser(@Param("uploadedBy") UUID uploadedBy);

    /**
     * 查询最近上传的附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param limit 限制数量
     * @return 最近附件列表
     */
    @Query("SELECT a FROM Attachment a WHERE a.targetType = :targetType AND a.targetId = :targetId " +
           "ORDER BY a.createdAt DESC LIMIT :limit")
    List<Attachment> findRecentAttachments(@Param("targetType") TargetType targetType, 
                                          @Param("targetId") UUID targetId, 
                                          @Param("limit") int limit);

    /**
     * 搜索附件
     * 
     * @param keyword 关键词
     * @param attachmentType 附件类型
     * @param uploadedBy 上传者ID
     * @param pageable 分页参数
     * @return 附件分页结果
     */
    @Query("SELECT a FROM Attachment a WHERE " +
           "(:keyword IS NULL OR a.originalFilename LIKE %:keyword% OR a.description LIKE %:keyword%) " +
           "AND (:attachmentType IS NULL OR a.attachmentType = :attachmentType) " +
           "AND (:uploadedBy IS NULL OR a.uploadedBy = :uploadedBy) " +
           "ORDER BY a.createdAt DESC")
    Page<Attachment> searchAttachments(@Param("keyword") String keyword, 
                                      @Param("attachmentType") AttachmentType attachmentType,
                                      @Param("uploadedBy") UUID uploadedBy, 
                                      Pageable pageable);

    /**
     * 查询热门下载附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 热门附件分页结果
     */
    @Query("SELECT a FROM Attachment a WHERE a.targetType = :targetType AND a.targetId = :targetId " +
           "ORDER BY a.downloadCount DESC, a.createdAt DESC")
    Page<Attachment> findPopularAttachments(@Param("targetType") TargetType targetType, 
                                           @Param("targetId") UUID targetId, 
                                           Pageable pageable);

    /**
     * 查询大文件附件
     * 
     * @param minSize 最小文件大小（字节）
     * @param pageable 分页参数
     * @return 大文件附件分页结果
     */
    @Query("SELECT a FROM Attachment a WHERE a.fileSize >= :minSize ORDER BY a.fileSize DESC")
    Page<Attachment> findLargeFiles(@Param("minSize") long minSize, Pageable pageable);

    /**
     * 查询孤儿附件（没有关联目标的附件）
     * 
     * @param createdBefore 创建时间之前
     * @return 孤儿附件列表
     */
    @Query("SELECT a FROM Attachment a WHERE a.targetId IS NULL " +
           "AND a.createdAt < :createdBefore")
    List<Attachment> findOrphanAttachments(@Param("createdBefore") LocalDateTime createdBefore);

    /**
     * 统计附件类型分布
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 类型分布统计
     */
    @Query("SELECT a.attachmentType, COUNT(a), SUM(a.fileSize) " +
           "FROM Attachment a WHERE a.targetType = :targetType AND a.targetId = :targetId " +
           "GROUP BY a.attachmentType ORDER BY COUNT(a) DESC")
    List<Object[]> getAttachmentTypeDistribution(@Param("targetType") TargetType targetType, 
                                                @Param("targetId") UUID targetId);

    /**
     * 查询附件上传活跃度
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param startDate 开始日期
     * @return 上传活跃度统计
     */
    @Query("SELECT DATE(a.createdAt) as date, COUNT(a) as count, SUM(a.fileSize) as totalSize " +
           "FROM Attachment a WHERE a.targetType = :targetType AND a.targetId = :targetId " +
           "AND a.createdAt >= :startDate " +
           "GROUP BY DATE(a.createdAt) ORDER BY DATE(a.createdAt)")
    List<Object[]> getUploadActivity(@Param("targetType") TargetType targetType, 
                                    @Param("targetId") UUID targetId, 
                                    @Param("startDate") LocalDateTime startDate);

    /**
     * 查询用户附件统计
     * 
     * @param uploadedBy 上传者ID
     * @return 用户附件统计
     */
    @Query("SELECT a.attachmentType, COUNT(a), SUM(a.fileSize), SUM(a.downloadCount) " +
           "FROM Attachment a WHERE a.uploadedBy = :uploadedBy " +
           "GROUP BY a.attachmentType ORDER BY COUNT(a) DESC")
    List<Object[]> getUserAttachmentStatistics(@Param("uploadedBy") UUID uploadedBy);

    /**
     * 查询重复文件
     * 
     * @return 重复文件统计
     */
    @Query("SELECT a.fileHash, COUNT(a), SUM(a.fileSize) " +
           "FROM Attachment a WHERE a.fileHash IS NOT NULL " +
           "GROUP BY a.fileHash HAVING COUNT(a) > 1 " +
           "ORDER BY COUNT(a) DESC")
    List<Object[]> findDuplicateFiles();

    /**
     * 查询存储空间使用情况
     * 
     * @return 存储空间统计
     */
    @Query("SELECT a.attachmentType, COUNT(a), SUM(a.fileSize), AVG(a.fileSize) " +
           "FROM Attachment a GROUP BY a.attachmentType ORDER BY SUM(a.fileSize) DESC")
    List<Object[]> getStorageUsageStatistics();

    /**
     * 查询需要生成缩略图的图片附件
     * 
     * @return 图片附件列表
     */
    @Query("SELECT a FROM Attachment a WHERE a.attachmentType = 'IMAGE' " +
           "AND (a.thumbnailPath IS NULL OR a.thumbnailPath = '') " +
           "ORDER BY a.createdAt DESC")
    List<Attachment> findImagesNeedingThumbnails();

    /**
     * 更新下载次数
     * 
     * @param attachmentId 附件ID
     * @return 更新数量
     */
    @Query("UPDATE Attachment a SET a.downloadCount = a.downloadCount + 1 WHERE a.id = :attachmentId")
    int incrementDownloadCount(@Param("attachmentId") UUID attachmentId);
}
