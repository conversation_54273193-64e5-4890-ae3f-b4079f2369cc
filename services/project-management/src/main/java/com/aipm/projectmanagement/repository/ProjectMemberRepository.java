package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.ProjectMember;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 项目成员数据访问接口
 * 
 * 提供项目成员相关的数据库操作方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface ProjectMemberRepository extends JpaRepository<ProjectMember, UUID> {

    /**
     * 根据项目ID查找所有成员
     * 
     * @param projectId 项目ID
     * @return 项目成员列表
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE pm.project.id = :projectId")
    List<ProjectMember> findByProjectId(@Param("projectId") UUID projectId);

    /**
     * 根据项目ID查找活跃成员
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 项目成员分页结果
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE pm.project.id = :projectId AND pm.isActive = true")
    Page<ProjectMember> findActiveByProjectId(@Param("projectId") UUID projectId, Pageable pageable);

    /**
     * 根据用户ID查找所有项目成员记录
     * 
     * @param userId 用户ID
     * @return 项目成员列表
     */
    List<ProjectMember> findByUserId(UUID userId);

    /**
     * 根据用户ID查找活跃的项目成员记录
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 项目成员分页结果
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE pm.userId = :userId AND pm.isActive = true")
    Page<ProjectMember> findActiveByUserId(@Param("userId") UUID userId, Pageable pageable);

    /**
     * 查找特定项目中的特定用户
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 项目成员（如果存在）
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE pm.project.id = :projectId AND pm.userId = :userId")
    Optional<ProjectMember> findByProjectIdAndUserId(@Param("projectId") UUID projectId, 
                                                    @Param("userId") UUID userId);

    /**
     * 检查用户是否为项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目成员
     */
    @Query("SELECT COUNT(pm) > 0 FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.userId = :userId AND pm.isActive = true")
    boolean isProjectMember(@Param("projectId") UUID projectId, @Param("userId") UUID userId);

    /**
     * 根据角色查找项目成员
     * 
     * @param projectId 项目ID
     * @param role 角色
     * @return 项目成员列表
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.role = :role AND pm.isActive = true")
    List<ProjectMember> findByProjectIdAndRole(@Param("projectId") UUID projectId, 
                                              @Param("role") ProjectMember.Role role);

    /**
     * 查找项目经理
     * 
     * @param projectId 项目ID
     * @return 项目经理列表
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.role = 'PROJECT_MANAGER' AND pm.isActive = true")
    List<ProjectMember> findProjectManagers(@Param("projectId") UUID projectId);

    /**
     * 查找技术负责人
     * 
     * @param projectId 项目ID
     * @return 技术负责人列表
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.role = 'TECH_LEAD' AND pm.isActive = true")
    List<ProjectMember> findTechLeads(@Param("projectId") UUID projectId);

    /**
     * 查找有管理权限的成员
     * 
     * @param projectId 项目ID
     * @return 有管理权限的成员列表
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.isActive = true AND " +
           "pm.role IN ('PROJECT_MANAGER', 'TECH_LEAD')")
    List<ProjectMember> findMembersWithManagementPermission(@Param("projectId") UUID projectId);

    /**
     * 统计项目成员数量
     * 
     * @param projectId 项目ID
     * @return 活跃成员数量
     */
    @Query("SELECT COUNT(pm) FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.isActive = true")
    long countActiveByProjectId(@Param("projectId") UUID projectId);

    /**
     * 统计用户参与的项目数量
     * 
     * @param userId 用户ID
     * @return 参与的项目数量
     */
    @Query("SELECT COUNT(pm) FROM ProjectMember pm WHERE " +
           "pm.userId = :userId AND pm.isActive = true")
    long countActiveByUserId(@Param("userId") UUID userId);

    /**
     * 根据角色统计项目成员数量
     * 
     * @param projectId 项目ID
     * @return 统计信息对象数组 [role, count]
     */
    @Query("SELECT pm.role, COUNT(pm) FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.isActive = true GROUP BY pm.role")
    List<Object[]> getMemberStatisticsByProject(@Param("projectId") UUID projectId);

    /**
     * 查找用户在项目中的角色
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 用户角色（如果是活跃成员）
     */
    @Query("SELECT pm.role FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.userId = :userId AND pm.isActive = true")
    Optional<ProjectMember.Role> findUserRoleInProject(@Param("projectId") UUID projectId, 
                                                      @Param("userId") UUID userId);

    /**
     * 检查用户是否有项目管理权限
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否有管理权限
     */
    @Query("SELECT COUNT(pm) > 0 FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.userId = :userId AND pm.isActive = true AND " +
           "pm.role IN ('PROJECT_MANAGER', 'TECH_LEAD')")
    boolean hasManagementPermission(@Param("projectId") UUID projectId, @Param("userId") UUID userId);

    /**
     * 检查用户是否可以分配任务
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否可以分配任务
     */
    @Query("SELECT COUNT(pm) > 0 FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.userId = :userId AND pm.isActive = true AND " +
           "pm.role IN ('PROJECT_MANAGER', 'TECH_LEAD', 'PRODUCT_MANAGER')")
    boolean canAssignTasks(@Param("projectId") UUID projectId, @Param("userId") UUID userId);

    /**
     * 查找最近加入的项目成员
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 项目成员分页结果
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.isActive = true " +
           "ORDER BY pm.joinedAt DESC")
    Page<ProjectMember> findRecentMembers(@Param("projectId") UUID projectId, Pageable pageable);

    /**
     * 查找用户最近参与的项目
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 项目成员分页结果
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE " +
           "pm.userId = :userId AND pm.isActive = true " +
           "ORDER BY pm.joinedAt DESC")
    Page<ProjectMember> findUserRecentProjects(@Param("userId") UUID userId, Pageable pageable);

    /**
     * 删除项目成员（物理删除）
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     */
    @Query("DELETE FROM ProjectMember pm WHERE pm.project.id = :projectId AND pm.userId = :userId")
    void deleteByProjectIdAndUserId(@Param("projectId") UUID projectId, @Param("userId") UUID userId);

    /**
     * 停用项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     */
    @Query("UPDATE ProjectMember pm SET pm.isActive = false WHERE " +
           "pm.project.id = :projectId AND pm.userId = :userId")
    void deactivateByProjectIdAndUserId(@Param("projectId") UUID projectId, @Param("userId") UUID userId);

    /**
     * 激活项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     */
    @Query("UPDATE ProjectMember pm SET pm.isActive = true WHERE " +
           "pm.project.id = :projectId AND pm.userId = :userId")
    void activateByProjectIdAndUserId(@Param("projectId") UUID projectId, @Param("userId") UUID userId);

    /**
     * 查找可以访问项目的用户ID列表
     * 
     * @param projectId 项目ID
     * @return 用户ID列表
     */
    @Query("SELECT pm.userId FROM ProjectMember pm WHERE " +
           "pm.project.id = :projectId AND pm.isActive = true")
    List<UUID> findProjectAccessibleUserIds(@Param("projectId") UUID projectId);

    /**
     * 批量查找用户在多个项目中的角色
     * 
     * @param userId 用户ID
     * @param projectIds 项目ID列表
     * @return 项目成员列表
     */
    @Query("SELECT pm FROM ProjectMember pm WHERE " +
           "pm.userId = :userId AND pm.project.id IN :projectIds AND pm.isActive = true")
    List<ProjectMember> findUserRolesInProjects(@Param("userId") UUID userId, 
                                               @Param("projectIds") List<UUID> projectIds);
}
