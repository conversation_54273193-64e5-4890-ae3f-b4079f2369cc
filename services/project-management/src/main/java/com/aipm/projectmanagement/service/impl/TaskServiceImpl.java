package com.aipm.projectmanagement.service.impl;

import com.aipm.projectmanagement.entity.Project;
import com.aipm.projectmanagement.entity.Task;
import com.aipm.projectmanagement.entity.TaskPriority;
import com.aipm.projectmanagement.entity.TaskStatus;
import com.aipm.projectmanagement.repository.ProjectMemberRepository;
import com.aipm.projectmanagement.repository.ProjectRepository;
import com.aipm.projectmanagement.repository.TaskRepository;
import com.aipm.projectmanagement.service.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 任务服务实现类
 * 
 * 实现任务管理相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Service
@Transactional
public class TaskServiceImpl implements TaskService {

    private static final Logger logger = LoggerFactory.getLogger(TaskServiceImpl.class);

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectMemberRepository projectMemberRepository;

    @Override
    public Task createTask(Task task, UUID creatorId) {
        logger.info("创建新任务: {}, 创建者: {}", task.getTitle(), creatorId);

        // 验证项目存在
        if (task.getProject() == null || task.getProject().getId() == null) {
            throw new RuntimeException("任务必须关联到项目");
        }

        Project project = projectRepository.findById(task.getProject().getId())
                .orElseThrow(() -> new RuntimeException("项目不存在: " + task.getProject().getId()));

        // 验证用户有项目访问权限
        if (!hasProjectAccess(project.getId(), creatorId)) {
            throw new RuntimeException("用户无权限在此项目中创建任务");
        }

        // 设置创建者信息
        task.setCreatedBy(creatorId);
        task.setUpdatedBy(creatorId);
        task.setReporterId(creatorId);

        // 如果没有设置排序序号，设置为最大值+1
        if (task.getSortOrder() == null) {
            long maxOrder = taskRepository.countByProjectId(project.getId());
            task.setSortOrder((int) maxOrder + 1);
        }

        Task savedTask = taskRepository.save(task);
        logger.info("任务创建成功: {}", savedTask.getId());
        return savedTask;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Task> getTaskById(UUID taskId) {
        return taskRepository.findById(taskId);
    }

    @Override
    public Task updateTask(Task task, UUID updaterId) {
        logger.info("更新任务: {}, 更新者: {}", task.getId(), updaterId);

        Task existingTask = taskRepository.findById(task.getId())
                .orElseThrow(() -> new RuntimeException("任务不存在: " + task.getId()));

        // 验证权限
        if (!hasTaskPermission(task.getId(), updaterId)) {
            throw new RuntimeException("用户无权限更新此任务");
        }

        // 更新任务信息
        existingTask.setTitle(task.getTitle());
        existingTask.setDescription(task.getDescription());
        existingTask.setDueDate(task.getDueDate());
        existingTask.setEstimatedHours(task.getEstimatedHours());
        existingTask.setTags(task.getTags());
        existingTask.setUpdatedBy(updaterId);

        Task updatedTask = taskRepository.save(existingTask);
        logger.info("任务更新成功: {}", updatedTask.getId());
        return updatedTask;
    }

    @Override
    public void deleteTask(UUID taskId, UUID deleterId) {
        logger.info("删除任务: {}, 删除者: {}", taskId, deleterId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!hasTaskPermission(taskId, deleterId)) {
            throw new RuntimeException("用户无权限删除此任务");
        }

        // 软删除
        task.softDelete();
        task.setUpdatedBy(deleterId);
        taskRepository.save(task);

        logger.info("任务删除成功: {}", taskId);
    }

    @Override
    public void restoreTask(UUID taskId, UUID restorerId) {
        logger.info("恢复任务: {}, 恢复者: {}", taskId, restorerId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!hasTaskPermission(taskId, restorerId)) {
            throw new RuntimeException("用户无权限恢复此任务");
        }

        task.restore();
        task.setUpdatedBy(restorerId);
        taskRepository.save(task);

        logger.info("任务恢复成功: {}", taskId);
    }

    @Override
    public Task updateTaskStatus(UUID taskId, TaskStatus status, UUID updaterId) {
        logger.info("更新任务状态: {}, 新状态: {}, 更新者: {}", taskId, status, updaterId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!hasTaskPermission(taskId, updaterId)) {
            throw new RuntimeException("用户无权限更新此任务状态");
        }

        // 验证状态转换是否合法
        if (!task.getStatus().canTransitionTo(status)) {
            throw new RuntimeException(String.format("无法从状态 %s 转换到 %s", 
                    task.getStatus().getDisplayName(), status.getDisplayName()));
        }

        TaskStatus oldStatus = task.getStatus();
        task.setStatus(status);
        task.setUpdatedBy(updaterId);

        // 根据状态设置相关时间
        if (status == TaskStatus.IN_PROGRESS && oldStatus == TaskStatus.TODO) {
            task.setStartDate(LocalDateTime.now());
        } else if (status == TaskStatus.DONE) {
            task.setCompletedDate(LocalDateTime.now());
            task.setProgress(100);
        }

        Task updatedTask = taskRepository.save(task);
        logger.info("任务状态更新成功: {} -> {}", taskId, status);
        return updatedTask;
    }

    @Override
    public Task assignTask(UUID taskId, UUID assigneeId, UUID assignerId) {
        logger.info("分配任务: {}, 分配给: {}, 分配者: {}", taskId, assigneeId, assignerId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证分配者权限
        if (!canAssignTask(task.getProject().getId(), assignerId)) {
            throw new RuntimeException("用户无权限分配此任务");
        }

        // 验证被分配者是否为项目成员
        if (!projectMemberRepository.isProjectMember(task.getProject().getId(), assigneeId)) {
            throw new RuntimeException("被分配者不是项目成员");
        }

        task.setAssigneeId(assigneeId);
        task.setUpdatedBy(assignerId);

        Task updatedTask = taskRepository.save(task);
        logger.info("任务分配成功: {} -> {}", taskId, assigneeId);
        return updatedTask;
    }

    @Override
    public Task unassignTask(UUID taskId, UUID unassignerId) {
        logger.info("取消任务分配: {}, 操作者: {}", taskId, unassignerId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!canAssignTask(task.getProject().getId(), unassignerId)) {
            throw new RuntimeException("用户无权限取消分配此任务");
        }

        task.setAssigneeId(null);
        task.setUpdatedBy(unassignerId);

        Task updatedTask = taskRepository.save(task);
        logger.info("任务分配取消成功: {}", taskId);
        return updatedTask;
    }

    @Override
    public Task updateTaskPriority(UUID taskId, TaskPriority priority, UUID updaterId) {
        logger.info("更新任务优先级: {}, 新优先级: {}, 更新者: {}", taskId, priority, updaterId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!hasTaskPermission(taskId, updaterId)) {
            throw new RuntimeException("用户无权限更新此任务优先级");
        }

        task.setPriority(priority);
        task.setUpdatedBy(updaterId);

        Task updatedTask = taskRepository.save(task);
        logger.info("任务优先级更新成功: {}", taskId);
        return updatedTask;
    }

    @Override
    public Task updateTaskProgress(UUID taskId, Integer progress, UUID updaterId) {
        logger.info("更新任务进度: {}, 进度: {}%, 更新者: {}", taskId, progress, updaterId);

        if (progress < 0 || progress > 100) {
            throw new RuntimeException("任务进度必须在0-100之间");
        }

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!hasTaskPermission(taskId, updaterId)) {
            throw new RuntimeException("用户无权限更新此任务进度");
        }

        task.setProgress(progress);
        task.setUpdatedBy(updaterId);

        // 如果进度达到100%且状态为进行中，自动设置为已完成
        if (progress == 100 && task.getStatus() == TaskStatus.IN_PROGRESS) {
            task.setStatus(TaskStatus.DONE);
            task.setCompletedDate(LocalDateTime.now());
        }

        Task updatedTask = taskRepository.save(task);
        logger.info("任务进度更新成功: {}", taskId);
        return updatedTask;
    }

    @Override
    public Task startTask(UUID taskId, UUID userId) {
        logger.info("开始任务: {}, 用户: {}", taskId, userId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限（只有分配者或有权限的用户可以开始任务）
        if (!userId.equals(task.getAssigneeId()) && !hasTaskPermission(taskId, userId)) {
            throw new RuntimeException("用户无权限开始此任务");
        }

        task.start();
        task.setUpdatedBy(userId);

        Task updatedTask = taskRepository.save(task);
        logger.info("任务开始成功: {}", taskId);
        return updatedTask;
    }

    @Override
    public Task completeTask(UUID taskId, UUID userId) {
        logger.info("完成任务: {}, 用户: {}", taskId, userId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!userId.equals(task.getAssigneeId()) && !hasTaskPermission(taskId, userId)) {
            throw new RuntimeException("用户无权限完成此任务");
        }

        task.complete();
        task.setUpdatedBy(userId);

        Task updatedTask = taskRepository.save(task);
        logger.info("任务完成成功: {}", taskId);
        return updatedTask;
    }

    @Override
    public Task blockTask(UUID taskId, String reason, UUID userId) {
        logger.info("阻塞任务: {}, 原因: {}, 用户: {}", taskId, reason, userId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!hasTaskPermission(taskId, userId)) {
            throw new RuntimeException("用户无权限阻塞此任务");
        }

        task.block();
        task.setUpdatedBy(userId);
        // TODO: 记录阻塞原因到任务描述或单独的字段

        Task updatedTask = taskRepository.save(task);
        logger.info("任务阻塞成功: {}", taskId);
        return updatedTask;
    }

    @Override
    public Task unblockTask(UUID taskId, UUID userId) {
        logger.info("解除任务阻塞: {}, 用户: {}", taskId, userId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));

        // 验证权限
        if (!hasTaskPermission(taskId, userId)) {
            throw new RuntimeException("用户无权限解除此任务阻塞");
        }

        // 解除阻塞，恢复到待办状态
        task.setStatus(TaskStatus.TODO);
        task.setUpdatedBy(userId);

        Task updatedTask = taskRepository.save(task);
        logger.info("任务阻塞解除成功: {}", taskId);
        return updatedTask;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Task> getTasksByProject(UUID projectId, Pageable pageable) {
        return taskRepository.findByProjectId(projectId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Task> getTopLevelTasks(UUID projectId, Pageable pageable) {
        return taskRepository.findTopLevelTasks(projectId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Task> getSubTasks(UUID parentTaskId) {
        return taskRepository.findByParentTaskId(parentTaskId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Task> getTasksByAssignee(UUID assigneeId, Pageable pageable) {
        return taskRepository.findByAssigneeId(assigneeId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Task> searchTasks(UUID projectId, String keyword, TaskStatus status, 
                                 TaskPriority priority, UUID assigneeId, Pageable pageable) {
        return taskRepository.searchTasks(projectId, keyword, status, priority, assigneeId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Task> getUserTasks(UUID userId, String keyword, TaskStatus status, Pageable pageable) {
        return taskRepository.findUserTasks(userId, keyword, status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Task> getTasksDueSoon(int hours) {
        LocalDateTime dueDate = LocalDateTime.now().plusHours(hours);
        List<TaskStatus> activeStatuses = Arrays.asList(TaskStatus.TODO, TaskStatus.IN_PROGRESS, 
                                                        TaskStatus.IN_REVIEW, TaskStatus.TESTING);
        return taskRepository.findTasksDueSoon(dueDate, activeStatuses);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Task> getOverdueTasks() {
        LocalDateTime currentDate = LocalDateTime.now();
        List<TaskStatus> activeStatuses = Arrays.asList(TaskStatus.TODO, TaskStatus.IN_PROGRESS, 
                                                        TaskStatus.IN_REVIEW, TaskStatus.TESTING);
        return taskRepository.findOverdueTasks(currentDate, activeStatuses);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Task> getBlockedTasks(UUID projectId) {
        return taskRepository.findBlockedTasks(projectId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Task> getHighPriorityTasks(UUID assigneeId, Pageable pageable) {
        List<TaskPriority> highPriorities = Arrays.asList(TaskPriority.HIGH, TaskPriority.URGENT, TaskPriority.CRITICAL);
        return taskRepository.findHighPriorityTasks(highPriorities, assigneeId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Task> getRecentTasks(UUID userId, Pageable pageable) {
        return taskRepository.findRecentTasks(userId, pageable);
    }

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    /**
     * 检查用户是否有项目访问权限
     */
    private boolean hasProjectAccess(UUID projectId, UUID userId) {
        // 检查是否为项目负责人
        Optional<Project> project = projectRepository.findById(projectId);
        if (project.isPresent() && project.get().getOwnerId().equals(userId)) {
            return true;
        }

        // 检查是否为项目成员
        return projectMemberRepository.isProjectMember(projectId, userId);
    }

    /**
     * 检查用户是否可以分配任务
     */
    private boolean canAssignTask(UUID projectId, UUID userId) {
        // 检查是否为项目负责人
        Optional<Project> project = projectRepository.findById(projectId);
        if (project.isPresent() && project.get().getOwnerId().equals(userId)) {
            return true;
        }

        // 检查是否有分配任务权限
        return projectMemberRepository.canAssignTasks(projectId, userId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasTaskPermission(UUID taskId, UUID userId) {
        Task task = taskRepository.findById(taskId).orElse(null);
        if (task == null) {
            return false;
        }

        // 任务分配者有权限
        if (userId.equals(task.getAssigneeId())) {
            return true;
        }

        // 任务创建者有权限
        if (userId.equals(task.getReporterId())) {
            return true;
        }

        // 项目负责人有权限
        if (userId.equals(task.getProject().getOwnerId())) {
            return true;
        }

        // 有管理权限的项目成员有权限
        return projectMemberRepository.hasManagementPermission(task.getProject().getId(), userId);
    }

    // ============================================================================
    // 其他方法的简化实现（待完善）
    // ============================================================================

    @Override
    public Task createSubTask(UUID parentTaskId, Task subTask, UUID creatorId) {
        Task parentTask = taskRepository.findById(parentTaskId)
                .orElseThrow(() -> new RuntimeException("父任务不存在: " + parentTaskId));
        
        subTask.setParentTask(parentTask);
        subTask.setProject(parentTask.getProject());
        return createTask(subTask, creatorId);
    }

    @Override
    public Task moveTask(UUID taskId, UUID newParentTaskId, UUID moverId) {
        // TODO: 实现任务移动逻辑
        throw new UnsupportedOperationException("任务移动功能待实现");
    }

    @Override
    public Task copyTask(UUID taskId, UUID targetProjectId, UUID copierId) {
        // TODO: 实现任务复制逻辑
        throw new UnsupportedOperationException("任务复制功能待实现");
    }

    @Override
    public int batchUpdateTaskStatus(List<UUID> taskIds, TaskStatus status, UUID updaterId) {
        int updatedCount = 0;
        for (UUID taskId : taskIds) {
            try {
                updateTaskStatus(taskId, status, updaterId);
                updatedCount++;
            } catch (Exception e) {
                logger.warn("更新任务状态失败: {}, 错误: {}", taskId, e.getMessage());
            }
        }
        return updatedCount;
    }

    @Override
    public int batchAssignTasks(List<UUID> taskIds, UUID assigneeId, UUID assignerId) {
        int assignedCount = 0;
        for (UUID taskId : taskIds) {
            try {
                assignTask(taskId, assigneeId, assignerId);
                assignedCount++;
            } catch (Exception e) {
                logger.warn("分配任务失败: {}, 错误: {}", taskId, e.getMessage());
            }
        }
        return assignedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getTaskStatistics(UUID projectId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取任务状态统计
        List<Object[]> statusStats = taskRepository.getTaskStatisticsByProject(projectId);
        Map<String, Long> statusCounts = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusCounts.put(((TaskStatus) stat[0]).name(), (Long) stat[1]);
        }
        statistics.put("statusCounts", statusCounts);
        
        // 获取总任务数
        statistics.put("totalTasks", taskRepository.countByProjectId(projectId));
        
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserTaskStatistics(UUID userId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取用户各状态任务数量
        for (TaskStatus status : TaskStatus.values()) {
            long count = taskRepository.countUserTasks(userId, status);
            statistics.put(status.name().toLowerCase() + "Count", count);
        }
        
        // 获取用户总任务数
        statistics.put("totalTasks", taskRepository.countUserTasks(userId, null));
        
        return statistics;
    }

    @Override
    public List<Task> getTaskDependencies(UUID taskId) {
        // TODO: 实现任务依赖查询
        return new ArrayList<>();
    }

    @Override
    public void addTaskDependency(UUID taskId, UUID dependencyTaskId, UUID userId) {
        // TODO: 实现添加任务依赖
        throw new UnsupportedOperationException("任务依赖功能待实现");
    }

    @Override
    public void removeTaskDependency(UUID taskId, UUID dependencyTaskId, UUID userId) {
        // TODO: 实现移除任务依赖
        throw new UnsupportedOperationException("任务依赖功能待实现");
    }
}
