package com.aipm.projectmanagement.controller;

import com.aipm.projectmanagement.entity.ProjectAnalytics;
import com.aipm.projectmanagement.entity.ProjectAnalytics.AnalyticsType;
import com.aipm.projectmanagement.service.ProjectAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 项目分析数据管理控制器
 * 
 * 提供项目分析数据相关的REST API接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
@RestController
@RequestMapping("/api/v1/analytics")
@Tag(name = "项目分析数据管理", description = "项目分析数据和统计相关的API接口")
public class ProjectAnalyticsController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectAnalyticsController.class);

    @Autowired
    private ProjectAnalyticsService projectAnalyticsService;

    @Operation(summary = "生成日度分析", description = "生成指定日期的项目分析数据")
    @PostMapping("/daily")
    public ResponseEntity<ProjectAnalytics> generateDailyAnalytics(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "分析日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        logger.info("生成日度分析请求: 项目={}, 日期={}", projectId, date);

        ProjectAnalytics analytics = projectAnalyticsService.generateDailyAnalytics(projectId, date);
        return ResponseEntity.ok(analytics);
    }

    @Operation(summary = "生成周度分析", description = "生成指定周的项目分析数据")
    @PostMapping("/weekly")
    public ResponseEntity<ProjectAnalytics> generateWeeklyAnalytics(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "周开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate weekStart) {
        
        logger.info("生成周度分析请求: 项目={}, 周开始={}", projectId, weekStart);

        ProjectAnalytics analytics = projectAnalyticsService.generateWeeklyAnalytics(projectId, weekStart);
        return ResponseEntity.ok(analytics);
    }

    @Operation(summary = "生成月度分析", description = "生成指定月的项目分析数据")
    @PostMapping("/monthly")
    public ResponseEntity<ProjectAnalytics> generateMonthlyAnalytics(
            @Parameter(description = "项目ID") @RequestParam UUID projectId,
            @Parameter(description = "月开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate monthStart) {
        
        logger.info("生成月度分析请求: 项目={}, 月开始={}", projectId, monthStart);

        ProjectAnalytics analytics = projectAnalyticsService.generateMonthlyAnalytics(projectId, monthStart);
        return ResponseEntity.ok(analytics);
    }

    @Operation(summary = "获取分析数据详情", description = "根据ID获取分析数据详细信息")
    @GetMapping("/{analyticsId}")
    public ResponseEntity<ProjectAnalytics> getAnalytics(
            @Parameter(description = "分析数据ID") @PathVariable UUID analyticsId) {
        
        return projectAnalyticsService.getAnalyticsById(analyticsId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "获取特定日期分析", description = "获取指定项目、类型和日期的分析数据")
    @GetMapping("/project/{projectId}/date")
    public ResponseEntity<ProjectAnalytics> getAnalyticsByDate(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "分析类型") @RequestParam AnalyticsType analyticsType,
            @Parameter(description = "分析日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate analyticsDate) {
        
        return projectAnalyticsService.getAnalyticsByDate(projectId, analyticsType, analyticsDate)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "获取最新分析", description = "获取指定项目和类型的最新分析数据")
    @GetMapping("/project/{projectId}/latest")
    public ResponseEntity<ProjectAnalytics> getLatestAnalytics(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "分析类型") @RequestParam AnalyticsType analyticsType) {
        
        return projectAnalyticsService.getLatestAnalytics(projectId, analyticsType)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "获取项目分析列表", description = "获取指定项目的所有分析数据")
    @GetMapping("/project/{projectId}")
    public ResponseEntity<Page<ProjectAnalytics>> getProjectAnalytics(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProjectAnalytics> analytics = projectAnalyticsService.getProjectAnalytics(projectId, pageable);
        return ResponseEntity.ok(analytics);
    }

    @Operation(summary = "按类型获取分析", description = "获取指定项目和类型的分析数据")
    @GetMapping("/project/{projectId}/type/{analyticsType}")
    public ResponseEntity<Page<ProjectAnalytics>> getAnalyticsByType(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "分析类型") @PathVariable AnalyticsType analyticsType,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProjectAnalytics> analytics = projectAnalyticsService.getAnalyticsByType(projectId, analyticsType, pageable);
        return ResponseEntity.ok(analytics);
    }

    @Operation(summary = "按日期范围获取分析", description = "获取指定日期范围内的分析数据")
    @GetMapping("/project/{projectId}/range")
    public ResponseEntity<Page<ProjectAnalytics>> getAnalyticsByDateRange(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProjectAnalytics> analytics = projectAnalyticsService.getAnalyticsByDateRange(projectId, startDate, endDate, pageable);
        return ResponseEntity.ok(analytics);
    }

    @Operation(summary = "获取最近分析", description = "获取最近N天的分析数据")
    @GetMapping("/project/{projectId}/recent")
    public ResponseEntity<List<ProjectAnalytics>> getRecentAnalytics(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "分析类型") @RequestParam AnalyticsType analyticsType,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        List<ProjectAnalytics> analytics = projectAnalyticsService.getRecentAnalytics(projectId, analyticsType, days);
        return ResponseEntity.ok(analytics);
    }

    @Operation(summary = "获取项目仪表板", description = "获取项目仪表板数据")
    @GetMapping("/project/{projectId}/dashboard")
    public ResponseEntity<Map<String, Object>> getProjectDashboard(
            @Parameter(description = "项目ID") @PathVariable UUID projectId) {
        
        Map<String, Object> dashboard = projectAnalyticsService.getProjectDashboard(projectId);
        return ResponseEntity.ok(dashboard);
    }

    @Operation(summary = "获取任务完成率趋势", description = "获取任务完成率趋势数据")
    @GetMapping("/project/{projectId}/trends/task-completion")
    public ResponseEntity<Map<String, Object>> getTaskCompletionTrend(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> trend = projectAnalyticsService.getTaskCompletionTrend(projectId, days);
        return ResponseEntity.ok(trend);
    }

    @Operation(summary = "获取团队效率趋势", description = "获取团队效率趋势数据")
    @GetMapping("/project/{projectId}/trends/team-efficiency")
    public ResponseEntity<Map<String, Object>> getTeamEfficiencyTrend(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> trend = projectAnalyticsService.getTeamEfficiencyTrend(projectId, days);
        return ResponseEntity.ok(trend);
    }

    @Operation(summary = "获取项目进度趋势", description = "获取项目进度趋势数据")
    @GetMapping("/project/{projectId}/trends/progress")
    public ResponseEntity<Map<String, Object>> getProjectProgressTrend(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> trend = projectAnalyticsService.getProjectProgressTrend(projectId, days);
        return ResponseEntity.ok(trend);
    }

    @Operation(summary = "获取质量趋势", description = "获取质量分数趋势数据")
    @GetMapping("/project/{projectId}/trends/quality")
    public ResponseEntity<Map<String, Object>> getQualityTrend(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> trend = projectAnalyticsService.getQualityTrend(projectId, days);
        return ResponseEntity.ok(trend);
    }

    @Operation(summary = "获取风险等级分布", description = "获取风险等级分布数据")
    @GetMapping("/project/{projectId}/risk-distribution")
    public ResponseEntity<Map<String, Object>> getRiskLevelDistribution(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> distribution = projectAnalyticsService.getRiskLevelDistribution(projectId, days);
        return ResponseEntity.ok(distribution);
    }

    @Operation(summary = "获取任务状态趋势", description = "获取任务状态分布趋势")
    @GetMapping("/project/{projectId}/trends/task-status")
    public ResponseEntity<Map<String, Object>> getTaskStatusTrend(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> trend = projectAnalyticsService.getTaskStatusTrend(projectId, days);
        return ResponseEntity.ok(trend);
    }

    @Operation(summary = "获取团队活跃度趋势", description = "获取团队活跃度趋势数据")
    @GetMapping("/project/{projectId}/trends/team-activity")
    public ResponseEntity<Map<String, Object>> getTeamActivityTrend(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> trend = projectAnalyticsService.getTeamActivityTrend(projectId, days);
        return ResponseEntity.ok(trend);
    }

    @Operation(summary = "获取工时分析", description = "获取工时分析数据")
    @GetMapping("/project/{projectId}/time-analysis")
    public ResponseEntity<Map<String, Object>> getTimeAnalysis(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> analysis = projectAnalyticsService.getTimeAnalysis(projectId, days);
        return ResponseEntity.ok(analysis);
    }

    @Operation(summary = "获取协作趋势", description = "获取协作活动趋势数据")
    @GetMapping("/project/{projectId}/trends/collaboration")
    public ResponseEntity<Map<String, Object>> getCollaborationTrend(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> trend = projectAnalyticsService.getCollaborationTrend(projectId, days);
        return ResponseEntity.ok(trend);
    }

    @Operation(summary = "获取项目健康度", description = "获取项目健康度指标")
    @GetMapping("/project/{projectId}/health")
    public ResponseEntity<Map<String, Object>> getProjectHealthMetrics(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "指定日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        Map<String, Object> health = projectAnalyticsService.getProjectHealthMetrics(projectId, date);
        return ResponseEntity.ok(health);
    }

    @Operation(summary = "获取平均指标", description = "获取指定时间段的平均指标")
    @GetMapping("/project/{projectId}/average-metrics")
    public ResponseEntity<Map<String, Object>> getAverageMetrics(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> metrics = projectAnalyticsService.getAverageMetrics(projectId, days);
        return ResponseEntity.ok(metrics);
    }

    @Operation(summary = "获取高风险日期", description = "获取高风险日期列表")
    @GetMapping("/project/{projectId}/high-risk-dates")
    public ResponseEntity<List<Map<String, Object>>> getHighRiskDates(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "最小风险等级") @RequestParam(defaultValue = "4") int minRiskLevel,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        List<Map<String, Object>> riskDates = projectAnalyticsService.getHighRiskDates(projectId, minRiskLevel, days);
        return ResponseEntity.ok(riskDates);
    }

    @Operation(summary = "获取最佳表现日期", description = "获取最佳表现日期列表")
    @GetMapping("/project/{projectId}/best-performance-dates")
    public ResponseEntity<List<Map<String, Object>>> getBestPerformanceDates(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<Map<String, Object>> bestDates = projectAnalyticsService.getBestPerformanceDates(projectId, days, limit);
        return ResponseEntity.ok(bestDates);
    }

    @Operation(summary = "获取分析摘要", description = "获取项目分析摘要")
    @GetMapping("/project/{projectId}/summary")
    public ResponseEntity<Map<String, Object>> getAnalyticsSummary(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> summary = projectAnalyticsService.getAnalyticsSummary(projectId, days);
        return ResponseEntity.ok(summary);
    }

    @Operation(summary = "预测项目完成时间", description = "基于历史数据预测项目完成时间")
    @GetMapping("/project/{projectId}/predict-completion")
    public ResponseEntity<Map<String, Object>> predictProjectCompletion(
            @Parameter(description = "项目ID") @PathVariable UUID projectId) {
        
        Map<String, Object> prediction = projectAnalyticsService.predictProjectCompletion(projectId);
        return ResponseEntity.ok(prediction);
    }

    @Operation(summary = "分析项目风险", description = "分析项目风险因素")
    @GetMapping("/project/{projectId}/risk-analysis")
    public ResponseEntity<Map<String, Object>> analyzeProjectRisks(
            @Parameter(description = "项目ID") @PathVariable UUID projectId) {
        
        Map<String, Object> riskAnalysis = projectAnalyticsService.analyzeProjectRisks(projectId);
        return ResponseEntity.ok(riskAnalysis);
    }

    @Operation(summary = "生成项目洞察", description = "生成项目洞察和建议")
    @GetMapping("/project/{projectId}/insights")
    public ResponseEntity<Map<String, Object>> generateProjectInsights(
            @Parameter(description = "项目ID") @PathVariable UUID projectId) {
        
        Map<String, Object> insights = projectAnalyticsService.generateProjectInsights(projectId);
        return ResponseEntity.ok(insights);
    }

    @Operation(summary = "比较项目表现", description = "比较多个项目的表现")
    @PostMapping("/compare-projects")
    public ResponseEntity<Map<String, Object>> compareProjectPerformance(
            @Parameter(description = "项目ID列表") @RequestBody List<UUID> projectIds,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> comparison = projectAnalyticsService.compareProjectPerformance(projectIds, days);
        return ResponseEntity.ok(comparison);
    }

    @Operation(summary = "获取团队绩效分析", description = "获取团队绩效分析数据")
    @GetMapping("/project/{projectId}/team-performance")
    public ResponseEntity<Map<String, Object>> getTeamPerformanceAnalysis(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> analysis = projectAnalyticsService.getTeamPerformanceAnalysis(projectId, days);
        return ResponseEntity.ok(analysis);
    }

    @Operation(summary = "更新分析数据", description = "更新指定的分析数据")
    @PutMapping("/{analyticsId}")
    public ResponseEntity<ProjectAnalytics> updateAnalytics(
            @Parameter(description = "分析数据ID") @PathVariable UUID analyticsId,
            @RequestBody ProjectAnalytics analytics) {
        
        logger.info("更新分析数据请求: {}", analyticsId);

        ProjectAnalytics updated = projectAnalyticsService.updateAnalytics(analyticsId, analytics);
        return ResponseEntity.ok(updated);
    }

    @Operation(summary = "重新计算分析数据", description = "重新计算指定的分析数据")
    @PostMapping("/{analyticsId}/recalculate")
    public ResponseEntity<ProjectAnalytics> recalculateAnalytics(
            @Parameter(description = "分析数据ID") @PathVariable UUID analyticsId) {
        
        logger.info("重新计算分析数据请求: {}", analyticsId);

        ProjectAnalytics recalculated = projectAnalyticsService.recalculateAnalytics(analyticsId);
        return ResponseEntity.ok(recalculated);
    }

    @Operation(summary = "批量生成分析数据", description = "为多个项目批量生成分析数据")
    @PostMapping("/batch-generate")
    public ResponseEntity<Integer> batchGenerateAnalytics(
            @Parameter(description = "项目ID列表") @RequestBody List<UUID> projectIds,
            @Parameter(description = "分析类型") @RequestParam AnalyticsType analyticsType,
            @Parameter(description = "分析日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate analyticsDate) {
        
        logger.info("批量生成分析数据请求: 项目数量={}, 类型={}, 日期={}", projectIds.size(), analyticsType, analyticsDate);

        int generatedCount = projectAnalyticsService.batchGenerateAnalytics(projectIds, analyticsType, analyticsDate);
        return ResponseEntity.ok(generatedCount);
    }

    @Operation(summary = "清理旧分析数据", description = "清理指定天数之前的旧分析数据")
    @DeleteMapping("/cleanup")
    public ResponseEntity<Integer> cleanupOldAnalytics(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "365") int days) {
        
        logger.info("清理旧分析数据请求: 保留天数={}", days);

        int cleanedCount = projectAnalyticsService.cleanupOldAnalytics(days);
        return ResponseEntity.ok(cleanedCount);
    }

    @Operation(summary = "检查数据存在性", description = "检查指定分析数据是否存在")
    @GetMapping("/project/{projectId}/exists")
    public ResponseEntity<Boolean> hasAnalyticsData(
            @Parameter(description = "项目ID") @PathVariable UUID projectId,
            @Parameter(description = "分析类型") @RequestParam AnalyticsType analyticsType,
            @Parameter(description = "分析日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate analyticsDate) {
        
        boolean exists = projectAnalyticsService.hasAnalyticsData(projectId, analyticsType, analyticsDate);
        return ResponseEntity.ok(exists);
    }
}
