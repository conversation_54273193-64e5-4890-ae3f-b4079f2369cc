package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.ProjectMember;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 项目成员服务接口
 * 
 * 定义项目成员管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface ProjectMemberService {

    /**
     * 添加项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 角色
     * @param adderId 添加者ID
     * @return 项目成员
     */
    ProjectMember addMember(UUID projectId, UUID userId, ProjectMember.Role role, UUID adderId);

    /**
     * 移除项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param removerId 移除者ID
     */
    void removeMember(UUID projectId, UUID userId, UUID removerId);

    /**
     * 更新成员角色
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param newRole 新角色
     * @param updaterId 更新者ID
     * @return 更新后的项目成员
     */
    ProjectMember updateMemberRole(UUID projectId, UUID userId, ProjectMember.Role newRole, UUID updaterId);

    /**
     * 激活项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param activatorId 激活者ID
     * @return 更新后的项目成员
     */
    ProjectMember activateMember(UUID projectId, UUID userId, UUID activatorId);

    /**
     * 停用项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param deactivatorId 停用者ID
     * @return 更新后的项目成员
     */
    ProjectMember deactivateMember(UUID projectId, UUID userId, UUID deactivatorId);

    /**
     * 获取项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 项目成员（如果存在）
     */
    Optional<ProjectMember> getMember(UUID projectId, UUID userId);

    /**
     * 获取项目的所有成员
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 项目成员分页结果
     */
    Page<ProjectMember> getProjectMembers(UUID projectId, Pageable pageable);

    /**
     * 获取项目的活跃成员
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 活跃项目成员分页结果
     */
    Page<ProjectMember> getActiveProjectMembers(UUID projectId, Pageable pageable);

    /**
     * 获取用户参与的项目
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 项目成员分页结果
     */
    Page<ProjectMember> getUserProjects(UUID userId, Pageable pageable);

    /**
     * 根据角色获取项目成员
     * 
     * @param projectId 项目ID
     * @param role 角色
     * @return 项目成员列表
     */
    List<ProjectMember> getMembersByRole(UUID projectId, ProjectMember.Role role);

    /**
     * 获取项目经理
     * 
     * @param projectId 项目ID
     * @return 项目经理列表
     */
    List<ProjectMember> getProjectManagers(UUID projectId);

    /**
     * 获取技术负责人
     * 
     * @param projectId 项目ID
     * @return 技术负责人列表
     */
    List<ProjectMember> getTechLeads(UUID projectId);

    /**
     * 获取有管理权限的成员
     * 
     * @param projectId 项目ID
     * @return 有管理权限的成员列表
     */
    List<ProjectMember> getMembersWithManagementPermission(UUID projectId);

    /**
     * 检查用户是否为项目成员
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目成员
     */
    boolean isProjectMember(UUID projectId, UUID userId);

    /**
     * 检查用户是否有管理权限
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否有管理权限
     */
    boolean hasManagementPermission(UUID projectId, UUID userId);

    /**
     * 检查用户是否可以分配任务
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否可以分配任务
     */
    boolean canAssignTasks(UUID projectId, UUID userId);

    /**
     * 获取用户在项目中的角色
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 用户角色（如果是活跃成员）
     */
    Optional<ProjectMember.Role> getUserRoleInProject(UUID projectId, UUID userId);

    /**
     * 批量添加项目成员
     * 
     * @param projectId 项目ID
     * @param userIds 用户ID列表
     * @param role 角色
     * @param adderId 添加者ID
     * @return 成功添加的成员数量
     */
    int batchAddMembers(UUID projectId, List<UUID> userIds, ProjectMember.Role role, UUID adderId);

    /**
     * 批量移除项目成员
     * 
     * @param projectId 项目ID
     * @param userIds 用户ID列表
     * @param removerId 移除者ID
     * @return 成功移除的成员数量
     */
    int batchRemoveMembers(UUID projectId, List<UUID> userIds, UUID removerId);

    /**
     * 批量更新成员角色
     * 
     * @param projectId 项目ID
     * @param userIds 用户ID列表
     * @param newRole 新角色
     * @param updaterId 更新者ID
     * @return 成功更新的成员数量
     */
    int batchUpdateMemberRoles(UUID projectId, List<UUID> userIds, ProjectMember.Role newRole, UUID updaterId);

    /**
     * 获取项目成员统计信息
     * 
     * @param projectId 项目ID
     * @return 统计信息Map
     */
    Map<String, Object> getMemberStatistics(UUID projectId);

    /**
     * 获取用户项目参与统计
     * 
     * @param userId 用户ID
     * @return 统计信息Map
     */
    Map<String, Object> getUserProjectStatistics(UUID userId);

    /**
     * 获取最近加入的项目成员
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 项目成员分页结果
     */
    Page<ProjectMember> getRecentMembers(UUID projectId, Pageable pageable);

    /**
     * 获取用户最近参与的项目
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 项目成员分页结果
     */
    Page<ProjectMember> getUserRecentProjects(UUID userId, Pageable pageable);

    /**
     * 转移项目所有权
     * 
     * @param projectId 项目ID
     * @param newOwnerId 新负责人ID
     * @param transfererId 转移者ID
     */
    void transferProjectOwnership(UUID projectId, UUID newOwnerId, UUID transfererId);

    /**
     * 邀请用户加入项目
     * 
     * @param projectId 项目ID
     * @param userEmail 用户邮箱
     * @param role 角色
     * @param inviterId 邀请者ID
     * @return 邀请结果
     */
    boolean inviteUserToProject(UUID projectId, String userEmail, ProjectMember.Role role, UUID inviterId);

    /**
     * 接受项目邀请
     * 
     * @param invitationToken 邀请令牌
     * @param userId 用户ID
     * @return 项目成员
     */
    ProjectMember acceptProjectInvitation(String invitationToken, UUID userId);

    /**
     * 拒绝项目邀请
     * 
     * @param invitationToken 邀请令牌
     * @param userId 用户ID
     */
    void rejectProjectInvitation(String invitationToken, UUID userId);

    /**
     * 获取项目可访问的用户ID列表
     * 
     * @param projectId 项目ID
     * @return 用户ID列表
     */
    List<UUID> getProjectAccessibleUserIds(UUID projectId);

    /**
     * 检查用户在多个项目中的角色
     * 
     * @param userId 用户ID
     * @param projectIds 项目ID列表
     * @return 项目成员列表
     */
    List<ProjectMember> getUserRolesInProjects(UUID userId, List<UUID> projectIds);
}
