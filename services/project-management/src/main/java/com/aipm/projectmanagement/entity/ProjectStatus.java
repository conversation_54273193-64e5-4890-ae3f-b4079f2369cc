package com.aipm.projectmanagement.entity;

/**
 * 项目状态枚举
 * 
 * 定义项目在生命周期中的各种状态
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public enum ProjectStatus {
    
    /**
     * 规划中 - 项目正在规划阶段
     */
    PLANNING("规划中", "项目正在进行需求分析和计划制定"),
    
    /**
     * 进行中 - 项目正在执行
     */
    IN_PROGRESS("进行中", "项目正在积极开发和执行"),
    
    /**
     * 暂停 - 项目暂时停止
     */
    ON_HOLD("暂停", "项目因某种原因暂时停止"),
    
    /**
     * 已完成 - 项目成功完成
     */
    COMPLETED("已完成", "项目已成功完成所有目标"),
    
    /**
     * 已取消 - 项目被取消
     */
    CANCELLED("已取消", "项目被永久取消"),
    
    /**
     * 已归档 - 项目已归档
     */
    ARCHIVED("已归档", "项目已完成并归档保存");

    private final String displayName;
    private final String description;

    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param description 状态描述
     */
    ProjectStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 检查是否为活跃状态
     * 
     * @return true表示项目处于活跃状态
     */
    public boolean isActive() {
        return this == PLANNING || this == IN_PROGRESS;
    }

    /**
     * 检查是否为已结束状态
     * 
     * @return true表示项目已结束
     */
    public boolean isFinished() {
        return this == COMPLETED || this == CANCELLED || this == ARCHIVED;
    }

    /**
     * 检查是否可以转换到目标状态
     * 
     * @param targetStatus 目标状态
     * @return true表示可以转换
     */
    public boolean canTransitionTo(ProjectStatus targetStatus) {
        if (this == targetStatus) {
            return false; // 不能转换到相同状态
        }

        return switch (this) {
            case PLANNING -> targetStatus == IN_PROGRESS || targetStatus == ON_HOLD || targetStatus == CANCELLED;
            case IN_PROGRESS -> targetStatus == ON_HOLD || targetStatus == COMPLETED || targetStatus == CANCELLED;
            case ON_HOLD -> targetStatus == IN_PROGRESS || targetStatus == CANCELLED;
            case COMPLETED -> targetStatus == ARCHIVED;
            case CANCELLED -> targetStatus == ARCHIVED;
            case ARCHIVED -> false; // 归档状态不能转换到其他状态
        };
    }

    /**
     * 获取下一个可能的状态列表
     * 
     * @return 可转换的状态数组
     */
    public ProjectStatus[] getNextPossibleStatuses() {
        return switch (this) {
            case PLANNING -> new ProjectStatus[]{IN_PROGRESS, ON_HOLD, CANCELLED};
            case IN_PROGRESS -> new ProjectStatus[]{ON_HOLD, COMPLETED, CANCELLED};
            case ON_HOLD -> new ProjectStatus[]{IN_PROGRESS, CANCELLED};
            case COMPLETED -> new ProjectStatus[]{ARCHIVED};
            case CANCELLED -> new ProjectStatus[]{ARCHIVED};
            case ARCHIVED -> new ProjectStatus[]{};
        };
    }

    /**
     * 根据显示名称查找状态
     * 
     * @param displayName 显示名称
     * @return 对应的状态，如果未找到返回null
     */
    public static ProjectStatus fromDisplayName(String displayName) {
        for (ProjectStatus status : values()) {
            if (status.displayName.equals(displayName)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return displayName;
    }
}
