package com.aipm.projectmanagement.service.impl;

import com.aipm.projectmanagement.entity.ActivityLog;
import com.aipm.projectmanagement.entity.Notification;
import com.aipm.projectmanagement.entity.Notification.NotificationStatus;
import com.aipm.projectmanagement.entity.Notification.NotificationType;
import com.aipm.projectmanagement.repository.NotificationRepository;
import com.aipm.projectmanagement.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 通知服务实现类
 * 
 * 实现通知管理相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Service
@Transactional
public class NotificationServiceImpl implements NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);

    @Autowired
    private NotificationRepository notificationRepository;

    @Override
    public Notification createNotification(Notification notification) {
        logger.debug("创建通知: 类型={}, 接收者={}", notification.getNotificationType(), notification.getRecipientId());

        Notification savedNotification = notificationRepository.save(notification);
        
        // 异步发送通知
        sendNotificationAsync(savedNotification);
        
        logger.debug("通知创建成功: {}", savedNotification.getId());
        return savedNotification;
    }

    @Override
    public Notification createNotification(NotificationType notificationType, String title, 
                                          String content, UUID recipientId) {
        Notification notification = new Notification(notificationType, title, content, recipientId);
        return createNotification(notification);
    }

    @Override
    public Notification createTaskAssignedNotification(UUID recipientId, UUID senderId, 
                                                      UUID taskId, String taskTitle, UUID projectId) {
        Notification notification = Notification.createTaskAssignedNotification(
            recipientId, senderId, taskId, taskTitle, projectId);
        return createNotification(notification);
    }

    @Override
    public Notification createTaskCommentNotification(UUID recipientId, UUID senderId, 
                                                     UUID taskId, String taskTitle, UUID projectId) {
        Notification notification = Notification.createTaskCommentNotification(
            recipientId, senderId, taskId, taskTitle, projectId);
        return createNotification(notification);
    }

    @Override
    public Notification createProjectMemberAddedNotification(UUID recipientId, UUID senderId, 
                                                            UUID projectId, String projectName) {
        Notification notification = Notification.createProjectMemberAddedNotification(
            recipientId, senderId, projectId, projectName);
        return createNotification(notification);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Notification> getNotificationById(UUID notificationId) {
        return notificationRepository.findById(notificationId);
    }

    @Override
    public Notification markAsRead(UUID notificationId, UUID userId) {
        logger.debug("标记通知为已读: {}, 用户: {}", notificationId, userId);

        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("通知不存在: " + notificationId));

        // 验证权限
        if (!notification.getRecipientId().equals(userId)) {
            throw new RuntimeException("用户无权限操作此通知");
        }

        notification.markAsRead();
        Notification updatedNotification = notificationRepository.save(notification);

        logger.debug("通知标记为已读成功: {}", notificationId);
        return updatedNotification;
    }

    @Override
    public Notification markAsUnread(UUID notificationId, UUID userId) {
        logger.debug("标记通知为未读: {}, 用户: {}", notificationId, userId);

        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("通知不存在: " + notificationId));

        // 验证权限
        if (!notification.getRecipientId().equals(userId)) {
            throw new RuntimeException("用户无权限操作此通知");
        }

        notification.markAsUnread();
        Notification updatedNotification = notificationRepository.save(notification);

        logger.debug("通知标记为未读成功: {}", notificationId);
        return updatedNotification;
    }

    @Override
    public int batchMarkAsRead(List<UUID> notificationIds, UUID userId) {
        logger.debug("批量标记通知为已读: 数量={}, 用户={}", notificationIds.size(), userId);

        // 验证所有通知都属于该用户
        List<Notification> notifications = notificationRepository.findAllById(notificationIds);
        for (Notification notification : notifications) {
            if (!notification.getRecipientId().equals(userId)) {
                throw new RuntimeException("用户无权限操作通知: " + notification.getId());
            }
        }

        int updatedCount = notificationRepository.batchMarkAsRead(notificationIds, LocalDateTime.now());
        logger.debug("批量标记通知为已读成功: {}", updatedCount);
        return updatedCount;
    }

    @Override
    public int markAllAsReadForUser(UUID userId) {
        logger.debug("标记用户所有通知为已读: {}", userId);

        int updatedCount = notificationRepository.markAllAsReadForUser(userId, LocalDateTime.now());
        logger.debug("标记用户所有通知为已读成功: {}", updatedCount);
        return updatedCount;
    }

    @Override
    public void deleteNotification(UUID notificationId, UUID userId) {
        logger.debug("删除通知: {}, 用户: {}", notificationId, userId);

        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("通知不存在: " + notificationId));

        // 验证权限
        if (!notification.getRecipientId().equals(userId)) {
            throw new RuntimeException("用户无权限删除此通知");
        }

        notificationRepository.delete(notification);
        logger.debug("通知删除成功: {}", notificationId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Notification> getUserNotifications(UUID userId, Pageable pageable) {
        return notificationRepository.findByRecipientIdOrderByCreatedAtDesc(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Notification> getUnreadNotifications(UUID userId, Pageable pageable) {
        return notificationRepository.findByRecipientIdAndIsReadOrderByCreatedAtDesc(userId, false, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Notification> getNotificationsByType(UUID userId, NotificationType notificationType, Pageable pageable) {
        return notificationRepository.findByRecipientIdAndNotificationTypeOrderByCreatedAtDesc(
            userId, notificationType, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Notification> getProjectNotifications(UUID projectId, Pageable pageable) {
        return notificationRepository.findByProjectIdOrderByCreatedAtDesc(projectId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Notification> getRecentNotifications(UUID userId, int limit) {
        return notificationRepository.findRecentNotifications(userId, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Notification> getHighPriorityUnreadNotifications(UUID userId, Integer minPriority, Pageable pageable) {
        return notificationRepository.findHighPriorityUnreadNotifications(userId, minPriority, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Notification> searchNotifications(UUID userId, String keyword, NotificationType notificationType,
                                                  Boolean isRead, LocalDateTime startDate, LocalDateTime endDate,
                                                  Pageable pageable) {
        return notificationRepository.searchNotifications(userId, keyword, notificationType, 
                                                         isRead, startDate, endDate, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public long countUnreadNotifications(UUID userId) {
        return notificationRepository.countByRecipientIdAndIsReadFalse(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countUnreadNotificationsByType(UUID userId, NotificationType notificationType) {
        return notificationRepository.countByRecipientIdAndNotificationTypeAndIsReadFalse(userId, notificationType);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getNotificationTypeStatistics(UUID userId) {
        Map<String, Object> statistics = new HashMap<>();
        
        List<Object[]> typeStats = notificationRepository.getNotificationTypeStatistics(userId);
        Map<String, Map<String, Long>> typeData = new HashMap<>();
        
        for (Object[] stat : typeStats) {
            String typeName = ((NotificationType) stat[0]).name();
            Map<String, Long> counts = new HashMap<>();
            counts.put("total", (Long) stat[1]);
            counts.put("unread", (Long) stat[2]);
            typeData.put(typeName, counts);
        }
        
        statistics.put("typeStatistics", typeData);
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserNotificationActivity(UUID userId, int days) {
        Map<String, Object> activity = new HashMap<>();
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> activityData = notificationRepository.getUserNotificationActivity(userId, startDate);
        
        List<Map<String, Object>> dailyActivity = new ArrayList<>();
        for (Object[] data : activityData) {
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", data[0]);
            dayData.put("received", data[1]);
            dayData.put("read", data[2]);
            dailyActivity.add(dayData);
        }
        
        activity.put("dailyActivity", dailyActivity);
        activity.put("period", days + " days");
        return activity;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getNotificationSummary(UUID userId, int days) {
        Map<String, Object> summary = new HashMap<>();
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        Object[] summaryData = notificationRepository.getNotificationSummary(userId, startDate);
        
        if (summaryData != null && summaryData.length >= 4) {
            summary.put("total", summaryData[0]);
            summary.put("unread", summaryData[1]);
            summary.put("highPriority", summaryData[2]);
            summary.put("typeCount", summaryData[3]);
        }
        
        summary.put("period", days + " days");
        return summary;
    }

    @Override
    public boolean sendNotification(UUID notificationId) {
        logger.debug("发送通知: {}", notificationId);

        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("通知不存在: " + notificationId));

        try {
            boolean success = false;
            
            // 根据通知设置选择发送渠道
            if (Boolean.TRUE.equals(notification.getEmailNotification())) {
                success |= sendEmailNotification(notificationId);
            }
            
            if (Boolean.TRUE.equals(notification.getPushNotification())) {
                success |= sendPushNotification(notificationId);
            }
            
            if (Boolean.TRUE.equals(notification.getSmsNotification())) {
                success |= sendSmsNotification(notificationId);
            }

            if (success) {
                notification.markAsSent();
                notificationRepository.save(notification);
                logger.debug("通知发送成功: {}", notificationId);
                return true;
            } else {
                notification.markAsFailed();
                notificationRepository.save(notification);
                logger.warn("通知发送失败: {}", notificationId);
                return false;
            }
        } catch (Exception e) {
            logger.error("发送通知异常: {}", notificationId, e);
            notification.markAsFailed();
            notificationRepository.save(notification);
            return false;
        }
    }

    @Override
    public int batchSendNotifications(int limit) {
        logger.debug("批量发送通知: 限制数量={}", limit);

        List<Notification> pendingNotifications = notificationRepository.findPendingNotifications(
            NotificationStatus.PENDING, limit);

        int sentCount = 0;
        for (Notification notification : pendingNotifications) {
            if (sendNotification(notification.getId())) {
                sentCount++;
            }
        }

        logger.debug("批量发送通知完成: {}/{}", sentCount, pendingNotifications.size());
        return sentCount;
    }

    @Override
    public boolean sendEmailNotification(UUID notificationId) {
        // TODO: 实现邮件通知发送
        logger.debug("发送邮件通知: {}", notificationId);
        return true; // 临时实现
    }

    @Override
    public boolean sendPushNotification(UUID notificationId) {
        // TODO: 实现推送通知发送
        logger.debug("发送推送通知: {}", notificationId);
        return true; // 临时实现
    }

    @Override
    public boolean sendSmsNotification(UUID notificationId) {
        // TODO: 实现短信通知发送
        logger.debug("发送短信通知: {}", notificationId);
        return true; // 临时实现
    }

    @Override
    public int processExpiredNotifications() {
        List<Notification> expiredNotifications = notificationRepository.findExpiredNotifications(LocalDateTime.now());
        
        for (Notification notification : expiredNotifications) {
            // 标记为已过期或删除
            notificationRepository.delete(notification);
        }
        
        logger.info("处理过期通知: {}", expiredNotifications.size());
        return expiredNotifications.size();
    }

    @Override
    public int retryFailedNotifications(int retryHours) {
        LocalDateTime retryBefore = LocalDateTime.now().minusHours(retryHours);
        List<Notification> failedNotifications = notificationRepository.findFailedNotificationsForRetry(retryBefore);
        
        int retryCount = 0;
        for (Notification notification : failedNotifications) {
            notification.setStatus(NotificationStatus.PENDING);
            notificationRepository.save(notification);
            
            if (sendNotification(notification.getId())) {
                retryCount++;
            }
        }
        
        logger.info("重试失败通知: {}/{}", retryCount, failedNotifications.size());
        return retryCount;
    }

    @Override
    public int cleanupOldReadNotifications(int days) {
        LocalDateTime beforeDate = LocalDateTime.now().minusDays(days);
        int cleanedCount = notificationRepository.cleanupOldReadNotifications(beforeDate);
        logger.info("清理旧的已读通知: {}", cleanedCount);
        return cleanedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getNotificationSendingStatistics(int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> sendingStats = notificationRepository.getNotificationSendingStatistics(startDate);
        
        Map<String, Object> statistics = new HashMap<>();
        List<Map<String, Object>> dailyStats = new ArrayList<>();
        
        for (Object[] stat : sendingStats) {
            Map<String, Object> dayStats = new HashMap<>();
            dayStats.put("date", stat[0]);
            dayStats.put("total", stat[1]);
            dayStats.put("sent", stat[2]);
            dayStats.put("delivered", stat[3]);
            dayStats.put("read", stat[4]);
            dailyStats.add(dayStats);
        }
        
        statistics.put("dailyStatistics", dailyStats);
        statistics.put("period", days + " days");
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getProjectNotificationStatistics(UUID projectId, int days) {
        LocalDateTime startDate = days > 0 ? LocalDateTime.now().minusDays(days) : null;
        List<Object[]> projectStats = notificationRepository.getProjectNotificationStatistics(projectId, startDate);
        
        Map<String, Object> statistics = new HashMap<>();
        Map<String, Map<String, Long>> typeStats = new HashMap<>();
        
        for (Object[] stat : projectStats) {
            String typeName = ((NotificationType) stat[0]).name();
            Map<String, Long> counts = new HashMap<>();
            counts.put("total", (Long) stat[1]);
            counts.put("recipients", (Long) stat[2]);
            typeStats.put(typeName, counts);
        }
        
        statistics.put("typeStatistics", typeStats);
        if (days > 0) {
            statistics.put("period", days + " days");
        }
        return statistics;
    }

    // ============================================================================
    // 其他方法的简化实现
    // ============================================================================

    @Override
    @Transactional(readOnly = true)
    public boolean canReceiveNotification(UUID userId, NotificationType notificationType) {
        // TODO: 实现用户通知接收权限检查
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserNotificationSettings(UUID userId) {
        // TODO: 实现用户通知设置查询
        return new HashMap<>();
    }

    @Override
    public boolean updateUserNotificationSettings(UUID userId, Map<String, Object> settings) {
        // TODO: 实现用户通知设置更新
        return true;
    }

    @Override
    public int createSystemAnnouncementNotifications(String title, String content, List<UUID> recipientIds) {
        List<Notification> notifications = new ArrayList<>();
        
        for (UUID recipientId : recipientIds) {
            Notification notification = new Notification(NotificationType.SYSTEM_ANNOUNCEMENT, 
                                                        title, content, recipientId);
            notification.setPriority(4); // 高优先级
            notifications.add(notification);
        }
        
        List<Notification> savedNotifications = notificationRepository.saveAll(notifications);
        return savedNotifications.size();
    }

    @Override
    public Notification createReminderNotification(UUID recipientId, String title, String content,
                                                  ActivityLog.TargetType targetType, UUID targetId,
                                                  UUID projectId, LocalDateTime reminderTime) {
        Notification notification = new Notification(NotificationType.REMINDER, title, content, recipientId);
        notification.setTargetType(targetType);
        notification.setTargetId(targetId);
        notification.setProjectId(projectId);
        notification.setExpiresAt(reminderTime.plusDays(1)); // 提醒后1天过期
        
        return createNotification(notification);
    }

    @Override
    public int processScheduledReminders() {
        // TODO: 实现定时提醒处理
        return 0;
    }

    @Override
    @Async
    public void sendNotificationAsync(Notification notification) {
        try {
            sendNotification(notification.getId());
        } catch (Exception e) {
            logger.error("异步发送通知失败: {}", notification.getId(), e);
        }
    }

    @Override
    public int batchCreateNotifications(List<Notification> notifications) {
        List<Notification> savedNotifications = notificationRepository.saveAll(notifications);
        
        // 异步发送所有通知
        for (Notification notification : savedNotifications) {
            sendNotificationAsync(notification);
        }
        
        return savedNotifications.size();
    }
}
