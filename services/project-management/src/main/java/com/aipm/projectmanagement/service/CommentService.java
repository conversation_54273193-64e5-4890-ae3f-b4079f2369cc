package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.Comment;
import com.aipm.projectmanagement.entity.Comment.TargetType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 评论服务接口
 * 
 * 定义评论管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface CommentService {

    /**
     * 创建评论
     * 
     * @param comment 评论对象
     * @param authorId 作者ID
     * @return 创建的评论
     */
    Comment createComment(Comment comment, UUID authorId);

    /**
     * 创建回复
     * 
     * @param parentCommentId 父评论ID
     * @param content 回复内容
     * @param authorId 作者ID
     * @return 创建的回复
     */
    Comment createReply(UUID parentCommentId, String content, UUID authorId);

    /**
     * 根据ID获取评论
     * 
     * @param commentId 评论ID
     * @return 评论对象（如果存在）
     */
    Optional<Comment> getCommentById(UUID commentId);

    /**
     * 更新评论内容
     * 
     * @param commentId 评论ID
     * @param content 新内容
     * @param userId 操作用户ID
     * @return 更新后的评论
     */
    Comment updateComment(UUID commentId, String content, UUID userId);

    /**
     * 删除评论
     * 
     * @param commentId 评论ID
     * @param userId 操作用户ID
     */
    void deleteComment(UUID commentId, UUID userId);

    /**
     * 恢复评论
     * 
     * @param commentId 评论ID
     * @param userId 操作用户ID
     * @return 恢复的评论
     */
    Comment restoreComment(UUID commentId, UUID userId);

    /**
     * 点赞评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 更新后的评论
     */
    Comment likeComment(UUID commentId, UUID userId);

    /**
     * 取消点赞评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 更新后的评论
     */
    Comment unlikeComment(UUID commentId, UUID userId);

    /**
     * 获取目标的评论列表
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 评论分页结果
     */
    Page<Comment> getCommentsByTarget(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 获取目标的顶级评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 顶级评论分页结果
     */
    Page<Comment> getTopLevelComments(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 获取评论的回复
     * 
     * @param parentCommentId 父评论ID
     * @param pageable 分页参数
     * @return 回复分页结果
     */
    Page<Comment> getCommentReplies(UUID parentCommentId, Pageable pageable);

    /**
     * 获取用户的评论
     * 
     * @param authorId 作者ID
     * @param pageable 分页参数
     * @return 评论分页结果
     */
    Page<Comment> getUserComments(UUID authorId, Pageable pageable);

    /**
     * 搜索评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 评论分页结果
     */
    Page<Comment> searchComments(TargetType targetType, UUID targetId, String keyword, Pageable pageable);

    /**
     * 获取热门评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 热门评论分页结果
     */
    Page<Comment> getPopularComments(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 获取最近评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param limit 限制数量
     * @return 最近评论列表
     */
    List<Comment> getRecentComments(TargetType targetType, UUID targetId, int limit);

    /**
     * 获取评论完整线程
     * 
     * @param rootCommentId 根评论ID
     * @return 评论线程列表
     */
    List<Comment> getCommentThread(UUID rootCommentId);

    /**
     * 统计目标的评论数量
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 评论数量
     */
    long countCommentsByTarget(TargetType targetType, UUID targetId);

    /**
     * 统计目标的顶级评论数量
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 顶级评论数量
     */
    long countTopLevelComments(TargetType targetType, UUID targetId);

    /**
     * 获取评论统计信息
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 统计信息Map
     */
    Map<String, Object> getCommentStatistics(TargetType targetType, UUID targetId);

    /**
     * 获取用户评论统计
     * 
     * @param authorId 作者ID
     * @return 用户评论统计
     */
    Map<String, Object> getUserCommentStatistics(UUID authorId);

    /**
     * 获取评论活跃度数据
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param days 天数
     * @return 活跃度数据
     */
    Map<String, Object> getCommentActivity(TargetType targetType, UUID targetId, int days);

    /**
     * 获取评论作者排行
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param limit 限制数量
     * @return 作者排行列表
     */
    List<Map<String, Object>> getTopCommentAuthors(TargetType targetType, UUID targetId, int limit);

    /**
     * 批量删除评论
     * 
     * @param commentIds 评论ID列表
     * @param userId 操作用户ID
     * @return 删除数量
     */
    int batchDeleteComments(List<UUID> commentIds, UUID userId);

    /**
     * 检查用户是否可以编辑评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否可以编辑
     */
    boolean canEditComment(UUID commentId, UUID userId);

    /**
     * 检查用户是否可以删除评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否可以删除
     */
    boolean canDeleteComment(UUID commentId, UUID userId);

    /**
     * 检查用户是否可以查看评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否可以查看
     */
    boolean canViewComment(UUID commentId, UUID userId);

    /**
     * 提及用户
     * 
     * @param commentId 评论ID
     * @param mentionedUserIds 被提及用户ID列表
     * @param userId 操作用户ID
     */
    void mentionUsers(UUID commentId, List<UUID> mentionedUserIds, UUID userId);

    /**
     * 获取用户被提及的评论
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 提及评论分页结果
     */
    Page<Comment> getMentionedComments(UUID userId, Pageable pageable);

    /**
     * 清理已删除的评论
     * 
     * @param days 删除天数之前
     * @return 清理数量
     */
    int cleanupDeletedComments(int days);

    /**
     * 导出评论数据
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 导出数据
     */
    String exportComments(TargetType targetType, UUID targetId);

    /**
     * 生成评论摘要
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 评论摘要
     */
    Map<String, Object> generateCommentSummary(TargetType targetType, UUID targetId);
}
