/**
 * 缓存服务
 * 
 * 提供统一的缓存管理功能，包括数据缓存、查询结果缓存、会话缓存等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-28
 */

package com.aipm.projectmanagement.cache;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    // 缓存键前缀
    private static final String PROJECT_PREFIX = "project:";
    private static final String TASK_PREFIX = "task:";
    private static final String USER_PREFIX = "user:";
    private static final String ANALYTICS_PREFIX = "analytics:";
    private static final String SEARCH_PREFIX = "search:";
    private static final String SESSION_PREFIX = "session:";

    // 默认缓存时间
    private static final Duration DEFAULT_TTL = Duration.ofHours(1);
    private static final Duration SHORT_TTL = Duration.ofMinutes(15);
    private static final Duration LONG_TTL = Duration.ofHours(24);

    /**
     * 缓存项目数据
     */
    public void cacheProject(String projectId, Object projectData) {
        cacheProject(projectId, projectData, DEFAULT_TTL);
    }

    public void cacheProject(String projectId, Object projectData, Duration ttl) {
        String key = PROJECT_PREFIX + projectId;
        setCache(key, projectData, ttl);
        log.debug("缓存项目数据: {}", projectId);
    }

    /**
     * 获取缓存的项目数据
     */
    public <T> T getCachedProject(String projectId, Class<T> clazz) {
        String key = PROJECT_PREFIX + projectId;
        return getCache(key, clazz);
    }

    /**
     * 删除项目缓存
     */
    public void evictProject(String projectId) {
        String key = PROJECT_PREFIX + projectId;
        deleteCache(key);
        
        // 删除相关的分析缓存
        evictProjectAnalytics(projectId);
        
        log.debug("清除项目缓存: {}", projectId);
    }

    /**
     * 缓存任务数据
     */
    public void cacheTask(String taskId, Object taskData) {
        cacheTask(taskId, taskData, DEFAULT_TTL);
    }

    public void cacheTask(String taskId, Object taskData, Duration ttl) {
        String key = TASK_PREFIX + taskId;
        setCache(key, taskData, ttl);
        log.debug("缓存任务数据: {}", taskId);
    }

    /**
     * 获取缓存的任务数据
     */
    public <T> T getCachedTask(String taskId, Class<T> clazz) {
        String key = TASK_PREFIX + taskId;
        return getCache(key, clazz);
    }

    /**
     * 删除任务缓存
     */
    public void evictTask(String taskId) {
        String key = TASK_PREFIX + taskId;
        deleteCache(key);
        log.debug("清除任务缓存: {}", taskId);
    }

    /**
     * 缓存用户数据
     */
    public void cacheUser(String userId, Object userData) {
        cacheUser(userId, userData, LONG_TTL);
    }

    public void cacheUser(String userId, Object userData, Duration ttl) {
        String key = USER_PREFIX + userId;
        setCache(key, userData, ttl);
        log.debug("缓存用户数据: {}", userId);
    }

    /**
     * 获取缓存的用户数据
     */
    public <T> T getCachedUser(String userId, Class<T> clazz) {
        String key = USER_PREFIX + userId;
        return getCache(key, clazz);
    }

    /**
     * 删除用户缓存
     */
    public void evictUser(String userId) {
        String key = USER_PREFIX + userId;
        deleteCache(key);
        log.debug("清除用户缓存: {}", userId);
    }

    /**
     * 缓存分析数据
     */
    public void cacheAnalytics(String analyticsKey, Object analyticsData) {
        cacheAnalytics(analyticsKey, analyticsData, SHORT_TTL);
    }

    public void cacheAnalytics(String analyticsKey, Object analyticsData, Duration ttl) {
        String key = ANALYTICS_PREFIX + analyticsKey;
        setCache(key, analyticsData, ttl);
        log.debug("缓存分析数据: {}", analyticsKey);
    }

    /**
     * 获取缓存的分析数据
     */
    public <T> T getCachedAnalytics(String analyticsKey, Class<T> clazz) {
        String key = ANALYTICS_PREFIX + analyticsKey;
        return getCache(key, clazz);
    }

    /**
     * 删除项目分析缓存
     */
    public void evictProjectAnalytics(String projectId) {
        String pattern = ANALYTICS_PREFIX + "*" + projectId + "*";
        deleteByPattern(pattern);
        log.debug("清除项目分析缓存: {}", projectId);
    }

    /**
     * 缓存搜索结果
     */
    public void cacheSearchResult(String searchKey, Object searchResult) {
        cacheSearchResult(searchKey, searchResult, SHORT_TTL);
    }

    public void cacheSearchResult(String searchKey, Object searchResult, Duration ttl) {
        String key = SEARCH_PREFIX + searchKey;
        setCache(key, searchResult, ttl);
        log.debug("缓存搜索结果: {}", searchKey);
    }

    /**
     * 获取缓存的搜索结果
     */
    public <T> T getCachedSearchResult(String searchKey, Class<T> clazz) {
        String key = SEARCH_PREFIX + searchKey;
        return getCache(key, clazz);
    }

    /**
     * 缓存会话数据
     */
    public void cacheSession(String sessionId, Object sessionData, Duration ttl) {
        String key = SESSION_PREFIX + sessionId;
        setCache(key, sessionData, ttl);
        log.debug("缓存会话数据: {}", sessionId);
    }

    /**
     * 获取缓存的会话数据
     */
    public <T> T getCachedSession(String sessionId, Class<T> clazz) {
        String key = SESSION_PREFIX + sessionId;
        return getCache(key, clazz);
    }

    /**
     * 删除会话缓存
     */
    public void evictSession(String sessionId) {
        String key = SESSION_PREFIX + sessionId;
        deleteCache(key);
        log.debug("清除会话缓存: {}", sessionId);
    }

    /**
     * 通用缓存设置方法
     */
    private void setCache(String key, Object value, Duration ttl) {
        try {
            String jsonValue = objectMapper.writeValueAsString(value);
            redisTemplate.opsForValue().set(key, jsonValue, ttl.toSeconds(), TimeUnit.SECONDS);
        } catch (JsonProcessingException e) {
            log.error("缓存序列化失败: key={}, error={}", key, e.getMessage());
        } catch (Exception e) {
            log.error("设置缓存失败: key={}, error={}", key, e.getMessage());
        }
    }

    /**
     * 通用缓存获取方法
     */
    private <T> T getCache(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            
            String jsonValue = value.toString();
            return objectMapper.readValue(jsonValue, clazz);
        } catch (JsonProcessingException e) {
            log.error("缓存反序列化失败: key={}, error={}", key, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("获取缓存失败: key={}, error={}", key, e.getMessage());
            return null;
        }
    }

    /**
     * 删除单个缓存
     */
    private void deleteCache(String key) {
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("删除缓存失败: key={}, error={}", key, e.getMessage());
        }
    }

    /**
     * 按模式删除缓存
     */
    private void deleteByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.debug("按模式删除缓存: pattern={}, count={}", pattern, keys.size());
            }
        } catch (Exception e) {
            log.error("按模式删除缓存失败: pattern={}, error={}", pattern, e.getMessage());
        }
    }

    /**
     * 检查缓存是否存在
     */
    public boolean hasCache(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查缓存存在性失败: key={}, error={}", key, e.getMessage());
            return false;
        }
    }

    /**
     * 设置缓存过期时间
     */
    public void expireCache(String key, Duration ttl) {
        try {
            redisTemplate.expire(key, ttl.toSeconds(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("设置缓存过期时间失败: key={}, error={}", key, e.getMessage());
        }
    }

    /**
     * 获取缓存剩余时间
     */
    public Duration getCacheTTL(String key) {
        try {
            Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return ttl != null && ttl > 0 ? Duration.ofSeconds(ttl) : Duration.ZERO;
        } catch (Exception e) {
            log.error("获取缓存TTL失败: key={}, error={}", key, e.getMessage());
            return Duration.ZERO;
        }
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        try {
            Set<String> keys = redisTemplate.keys("*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清除所有缓存: count={}", keys.size());
            }
        } catch (Exception e) {
            log.error("清除所有缓存失败: error={}", e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        try {
            Set<String> allKeys = redisTemplate.keys("*");
            int totalKeys = allKeys != null ? allKeys.size() : 0;
            
            int projectKeys = countKeysByPrefix(PROJECT_PREFIX);
            int taskKeys = countKeysByPrefix(TASK_PREFIX);
            int userKeys = countKeysByPrefix(USER_PREFIX);
            int analyticsKeys = countKeysByPrefix(ANALYTICS_PREFIX);
            int searchKeys = countKeysByPrefix(SEARCH_PREFIX);
            int sessionKeys = countKeysByPrefix(SESSION_PREFIX);
            
            return new CacheStats(totalKeys, projectKeys, taskKeys, userKeys, 
                                analyticsKeys, searchKeys, sessionKeys);
        } catch (Exception e) {
            log.error("获取缓存统计失败: error={}", e.getMessage());
            return new CacheStats(0, 0, 0, 0, 0, 0, 0);
        }
    }

    /**
     * 按前缀统计键数量
     */
    private int countKeysByPrefix(String prefix) {
        try {
            Set<String> keys = redisTemplate.keys(prefix + "*");
            return keys != null ? keys.size() : 0;
        } catch (Exception e) {
            log.error("统计键数量失败: prefix={}, error={}", prefix, e.getMessage());
            return 0;
        }
    }

    /**
     * 缓存统计信息类
     */
    public static class CacheStats {
        private final int totalKeys;
        private final int projectKeys;
        private final int taskKeys;
        private final int userKeys;
        private final int analyticsKeys;
        private final int searchKeys;
        private final int sessionKeys;

        public CacheStats(int totalKeys, int projectKeys, int taskKeys, int userKeys,
                         int analyticsKeys, int searchKeys, int sessionKeys) {
            this.totalKeys = totalKeys;
            this.projectKeys = projectKeys;
            this.taskKeys = taskKeys;
            this.userKeys = userKeys;
            this.analyticsKeys = analyticsKeys;
            this.searchKeys = searchKeys;
            this.sessionKeys = sessionKeys;
        }

        // Getters
        public int getTotalKeys() { return totalKeys; }
        public int getProjectKeys() { return projectKeys; }
        public int getTaskKeys() { return taskKeys; }
        public int getUserKeys() { return userKeys; }
        public int getAnalyticsKeys() { return analyticsKeys; }
        public int getSearchKeys() { return searchKeys; }
        public int getSessionKeys() { return sessionKeys; }
    }
}
