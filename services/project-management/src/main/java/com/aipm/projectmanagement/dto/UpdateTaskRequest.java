package com.aipm.projectmanagement.dto;

import com.aipm.projectmanagement.entity.TaskPriority;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 更新任务请求DTO
 * 
 * 用于接收更新任务的请求参数
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "更新任务请求")
public class UpdateTaskRequest {

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    @Size(min = 2, max = 200, message = "任务标题长度必须在2-200个字符之间")
    @Schema(description = "任务标题", example = "实现用户认证功能", required = true)
    private String title;

    /**
     * 任务描述
     */
    @Size(max = 5000, message = "任务描述长度不能超过5000个字符")
    @Schema(description = "任务描述", example = "实现基于JWT的用户认证和授权功能，包括登录、注册、令牌刷新等")
    private String description;

    /**
     * 任务优先级
     */
    @Schema(description = "任务优先级", example = "HIGH")
    private TaskPriority priority;

    /**
     * 预估工时（小时）
     */
    @Schema(description = "预估工时（小时）", example = "16.5")
    private BigDecimal estimatedHours;

    /**
     * 截止日期
     */
    @Schema(description = "截止日期", example = "2025-08-20T18:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;

    /**
     * 任务标签
     */
    @Schema(description = "任务标签", example = "[\"认证\", \"安全\", \"后端\"]")
    private List<String> tags;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public UpdateTaskRequest() {
    }

    /**
     * 基础构造函数
     * 
     * @param title 任务标题
     * @param description 任务描述
     */
    public UpdateTaskRequest(String title, String description) {
        this.title = title;
        this.description = description;
    }

    // ============================================================================
    // 验证方法
    // ============================================================================

    /**
     * 验证截止日期
     * 
     * @return 验证结果
     */
    public boolean isDueDateValid() {
        return dueDate == null || dueDate.isAfter(LocalDateTime.now());
    }

    /**
     * 验证预估工时
     * 
     * @return 验证结果
     */
    public boolean isEstimatedHoursValid() {
        return estimatedHours == null || estimatedHours.compareTo(BigDecimal.ZERO) >= 0;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public BigDecimal getEstimatedHours() {
        return estimatedHours;
    }

    public void setEstimatedHours(BigDecimal estimatedHours) {
        this.estimatedHours = estimatedHours;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public String toString() {
        return "UpdateTaskRequest{" +
                "title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", priority=" + priority +
                ", estimatedHours=" + estimatedHours +
                ", dueDate=" + dueDate +
                '}';
    }
}
