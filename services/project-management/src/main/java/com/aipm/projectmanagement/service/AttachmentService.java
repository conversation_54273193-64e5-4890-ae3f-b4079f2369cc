package com.aipm.projectmanagement.service;

import com.aipm.projectmanagement.entity.Attachment;
import com.aipm.projectmanagement.entity.Attachment.AttachmentType;
import com.aipm.projectmanagement.entity.Attachment.TargetType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 附件服务接口
 * 
 * 定义附件管理相关的业务操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public interface AttachmentService {

    /**
     * 上传附件
     * 
     * @param file 上传文件
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param description 描述
     * @param uploaderId 上传者ID
     * @return 上传的附件
     */
    Attachment uploadAttachment(MultipartFile file, TargetType targetType, UUID targetId, 
                               String description, UUID uploaderId);

    /**
     * 上传附件到评论
     * 
     * @param file 上传文件
     * @param commentId 评论ID
     * @param description 描述
     * @param uploaderId 上传者ID
     * @return 上传的附件
     */
    Attachment uploadAttachmentToComment(MultipartFile file, UUID commentId, 
                                        String description, UUID uploaderId);

    /**
     * 根据ID获取附件
     * 
     * @param attachmentId 附件ID
     * @return 附件对象（如果存在）
     */
    Optional<Attachment> getAttachmentById(UUID attachmentId);

    /**
     * 下载附件
     * 
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 文件输入流
     */
    InputStream downloadAttachment(UUID attachmentId, UUID userId);

    /**
     * 获取附件预览
     * 
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 预览输入流
     */
    InputStream previewAttachment(UUID attachmentId, UUID userId);

    /**
     * 获取附件缩略图
     * 
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 缩略图输入流
     */
    InputStream getThumbnail(UUID attachmentId, UUID userId);

    /**
     * 更新附件信息
     * 
     * @param attachmentId 附件ID
     * @param description 新描述
     * @param isPublic 是否公开
     * @param userId 操作用户ID
     * @return 更新后的附件
     */
    Attachment updateAttachment(UUID attachmentId, String description, Boolean isPublic, UUID userId);

    /**
     * 删除附件
     * 
     * @param attachmentId 附件ID
     * @param userId 操作用户ID
     */
    void deleteAttachment(UUID attachmentId, UUID userId);

    /**
     * 获取目标的附件列表
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 附件分页结果
     */
    Page<Attachment> getAttachmentsByTarget(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 根据类型获取附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param attachmentType 附件类型
     * @param pageable 分页参数
     * @return 附件分页结果
     */
    Page<Attachment> getAttachmentsByType(TargetType targetType, UUID targetId, 
                                         AttachmentType attachmentType, Pageable pageable);

    /**
     * 获取用户上传的附件
     * 
     * @param uploaderId 上传者ID
     * @param pageable 分页参数
     * @return 附件分页结果
     */
    Page<Attachment> getUserAttachments(UUID uploaderId, Pageable pageable);

    /**
     * 获取评论的附件
     * 
     * @param commentId 评论ID
     * @return 附件列表
     */
    List<Attachment> getCommentAttachments(UUID commentId);

    /**
     * 搜索附件
     * 
     * @param keyword 关键词
     * @param attachmentType 附件类型
     * @param uploaderId 上传者ID
     * @param pageable 分页参数
     * @return 附件分页结果
     */
    Page<Attachment> searchAttachments(String keyword, AttachmentType attachmentType, 
                                      UUID uploaderId, Pageable pageable);

    /**
     * 获取热门下载附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 热门附件分页结果
     */
    Page<Attachment> getPopularAttachments(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 获取最近上传的附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param limit 限制数量
     * @return 最近附件列表
     */
    List<Attachment> getRecentAttachments(TargetType targetType, UUID targetId, int limit);

    /**
     * 统计目标的附件数量
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 附件数量
     */
    long countAttachmentsByTarget(TargetType targetType, UUID targetId);

    /**
     * 统计目标的附件总大小
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 总大小（字节）
     */
    long getTotalSizeByTarget(TargetType targetType, UUID targetId);

    /**
     * 统计用户上传的附件总大小
     * 
     * @param uploaderId 上传者ID
     * @return 总大小（字节）
     */
    long getUserTotalSize(UUID uploaderId);

    /**
     * 获取附件统计信息
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 统计信息Map
     */
    Map<String, Object> getAttachmentStatistics(TargetType targetType, UUID targetId);

    /**
     * 获取用户附件统计
     * 
     * @param uploaderId 上传者ID
     * @return 用户附件统计
     */
    Map<String, Object> getUserAttachmentStatistics(UUID uploaderId);

    /**
     * 获取附件类型分布
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 类型分布统计
     */
    Map<String, Object> getAttachmentTypeDistribution(TargetType targetType, UUID targetId);

    /**
     * 获取上传活跃度数据
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param days 天数
     * @return 上传活跃度数据
     */
    Map<String, Object> getUploadActivity(TargetType targetType, UUID targetId, int days);

    /**
     * 批量删除附件
     * 
     * @param attachmentIds 附件ID列表
     * @param userId 操作用户ID
     * @return 删除数量
     */
    int batchDeleteAttachments(List<UUID> attachmentIds, UUID userId);

    /**
     * 生成缩略图
     * 
     * @param attachmentId 附件ID
     * @return 是否成功生成
     */
    boolean generateThumbnail(UUID attachmentId);

    /**
     * 批量生成缩略图
     * 
     * @return 生成数量
     */
    int batchGenerateThumbnails();

    /**
     * 检查文件是否重复
     * 
     * @param fileHash 文件哈希
     * @return 重复的附件（如果存在）
     */
    Optional<Attachment> checkDuplicateFile(String fileHash);

    /**
     * 获取重复文件列表
     * 
     * @return 重复文件统计
     */
    List<Map<String, Object>> getDuplicateFiles();

    /**
     * 清理孤儿附件
     * 
     * @param days 创建天数之前
     * @return 清理数量
     */
    int cleanupOrphanAttachments(int days);

    /**
     * 获取存储空间使用情况
     * 
     * @return 存储空间统计
     */
    Map<String, Object> getStorageUsageStatistics();

    /**
     * 检查用户是否可以上传附件
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param userId 用户ID
     * @return 是否可以上传
     */
    boolean canUploadAttachment(TargetType targetType, UUID targetId, UUID userId);

    /**
     * 检查用户是否可以下载附件
     * 
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 是否可以下载
     */
    boolean canDownloadAttachment(UUID attachmentId, UUID userId);

    /**
     * 检查用户是否可以删除附件
     * 
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 是否可以删除
     */
    boolean canDeleteAttachment(UUID attachmentId, UUID userId);

    /**
     * 验证文件类型
     * 
     * @param file 文件
     * @return 是否允许的文件类型
     */
    boolean isAllowedFileType(MultipartFile file);

    /**
     * 验证文件大小
     * 
     * @param file 文件
     * @return 是否在允许的大小范围内
     */
    boolean isAllowedFileSize(MultipartFile file);

    /**
     * 计算文件哈希
     * 
     * @param file 文件
     * @return 文件哈希值
     */
    String calculateFileHash(MultipartFile file);

    /**
     * 获取文件MIME类型
     * 
     * @param file 文件
     * @return MIME类型
     */
    String getFileMimeType(MultipartFile file);

    /**
     * 压缩图片
     * 
     * @param attachmentId 附件ID
     * @param quality 压缩质量（0-100）
     * @return 是否成功压缩
     */
    boolean compressImage(UUID attachmentId, int quality);

    /**
     * 转换文件格式
     * 
     * @param attachmentId 附件ID
     * @param targetFormat 目标格式
     * @param userId 操作用户ID
     * @return 转换后的附件
     */
    Attachment convertFileFormat(UUID attachmentId, String targetFormat, UUID userId);
}
