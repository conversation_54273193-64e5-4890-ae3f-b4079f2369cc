package com.aipm.projectmanagement.mapper;

import com.aipm.projectmanagement.dto.CreateProjectRequest;
import com.aipm.projectmanagement.dto.ProjectDto;
import com.aipm.projectmanagement.dto.UpdateProjectRequest;
import com.aipm.projectmanagement.entity.Project;
import org.mapstruct.*;

import java.util.List;

/**
 * 项目映射器
 * 
 * 负责项目实体和DTO之间的转换
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProjectMapper {

    /**
     * 实体转换为DTO
     * 
     * @param project 项目实体
     * @return 项目DTO
     */
    @Mapping(target = "owner", ignore = true) // 需要单独处理用户信息
    @Mapping(target = "memberCount", ignore = true) // 需要单独计算
    @Mapping(target = "taskCount", ignore = true) // 需要单独计算
    @Mapping(target = "completedTaskCount", ignore = true) // 需要单独计算
    @Mapping(target = "taskCompletionRate", ignore = true) // 需要单独计算
    @Mapping(target = "healthScore", ignore = true) // 需要单独计算
    @Mapping(target = "isOverdue", expression = "java(project.isOverdue())")
    @Mapping(target = "remainingDays", expression = "java(project.getRemainingDays())")
    @Mapping(target = "tags", expression = "java(parseTagsFromJson(project.getTags()))")
    ProjectDto toDto(Project project);

    /**
     * 实体转换为详细DTO（包含统计信息）
     * 
     * @param project 项目实体
     * @return 详细项目DTO
     */
    @Mapping(target = "owner", ignore = true) // 需要单独处理用户信息
    @Mapping(target = "memberCount", expression = "java(project.getMembers() != null ? (long) project.getMembers().size() : 0L)")
    @Mapping(target = "taskCount", expression = "java(project.getTasks() != null ? (long) project.getTasks().size() : 0L)")
    @Mapping(target = "completedTaskCount", expression = "java(project.getCompletedTaskCount())")
    @Mapping(target = "taskCompletionRate", expression = "java(project.getTaskCompletionRate())")
    @Mapping(target = "healthScore", ignore = true) // 需要通过服务计算
    @Mapping(target = "isOverdue", expression = "java(project.isOverdue())")
    @Mapping(target = "remainingDays", expression = "java(project.getRemainingDays())")
    @Mapping(target = "tags", expression = "java(parseTagsFromJson(project.getTags()))")
    ProjectDto toDetailDto(Project project);

    /**
     * 实体列表转换为DTO列表
     * 
     * @param projects 项目实体列表
     * @return 项目DTO列表
     */
    List<ProjectDto> toDtoList(List<Project> projects);

    /**
     * 创建请求转换为实体
     * 
     * @param request 创建项目请求
     * @return 项目实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", constant = "PLANNING")
    @Mapping(target = "progress", constant = "0")
    @Mapping(target = "isTemplate", defaultValue = "false")
    @Mapping(target = "isArchived", constant = "false")
    @Mapping(target = "members", ignore = true)
    @Mapping(target = "tasks", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "tags", expression = "java(convertTagsToJson(request.getTags()))")
    @Mapping(target = "settings", ignore = true)
    Project fromCreateRequest(CreateProjectRequest request);

    /**
     * 更新请求转换为实体
     * 
     * @param request 更新项目请求
     * @return 项目实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", ignore = true) // 状态通过专门的接口更新
    @Mapping(target = "progress", ignore = true) // 进度通过专门的接口更新
    @Mapping(target = "isTemplate", ignore = true)
    @Mapping(target = "isArchived", ignore = true)
    @Mapping(target = "members", ignore = true)
    @Mapping(target = "tasks", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "tags", expression = "java(convertTagsToJson(request.getTags()))")
    @Mapping(target = "settings", ignore = true)
    Project fromUpdateRequest(UpdateProjectRequest request);

    /**
     * 更新实体（从DTO）
     * 
     * @param dto 项目DTO
     * @param project 目标项目实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "isTemplate", ignore = true)
    @Mapping(target = "isArchived", ignore = true)
    @Mapping(target = "members", ignore = true)
    @Mapping(target = "tasks", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "tags", expression = "java(convertTagsToJson(dto.getTags()))")
    @Mapping(target = "settings", ignore = true)
    void updateProjectFromDto(ProjectDto dto, @MappingTarget Project project);

    // ============================================================================
    // 默认方法实现
    // ============================================================================

    /**
     * 解析JSON格式的标签字符串为列表
     * 
     * @param tagsJson JSON格式的标签字符串
     * @return 标签列表
     */
    default List<String> parseTagsFromJson(String tagsJson) {
        if (tagsJson == null || tagsJson.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 简单的JSON数组解析（实际项目中应该使用Jackson等库）
            String cleaned = tagsJson.trim();
            if (cleaned.startsWith("[") && cleaned.endsWith("]")) {
                cleaned = cleaned.substring(1, cleaned.length() - 1);
                if (cleaned.trim().isEmpty()) {
                    return List.of();
                }
                
                return List.of(cleaned.split(","))
                    .stream()
                    .map(tag -> tag.trim().replaceAll("\"", ""))
                    .filter(tag -> !tag.isEmpty())
                    .toList();
            }
            
            return List.of(cleaned);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将标签列表转换为JSON格式字符串
     * 
     * @param tags 标签列表
     * @return JSON格式的标签字符串
     */
    default String convertTagsToJson(List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return null;
        }
        
        // 简单的JSON数组生成（实际项目中应该使用Jackson等库）
        StringBuilder json = new StringBuilder("[");
        for (int i = 0; i < tags.size(); i++) {
            if (i > 0) {
                json.append(",");
            }
            json.append("\"").append(tags.get(i).replace("\"", "\\\"")).append("\"");
        }
        json.append("]");
        
        return json.toString();
    }
}
