package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.Comment;
import com.aipm.projectmanagement.entity.Comment.TargetType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 评论数据访问接口
 * 
 * 提供评论相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface CommentRepository extends JpaRepository<Comment, UUID> {

    /**
     * 根据目标类型和目标ID查询评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 评论分页结果
     */
    Page<Comment> findByTargetTypeAndTargetIdAndIsDeletedFalse(TargetType targetType, UUID targetId, Pageable pageable);

    /**
     * 根据目标类型和目标ID查询顶级评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 顶级评论分页结果
     */
    @Query("SELECT c FROM Comment c WHERE c.targetType = :targetType AND c.targetId = :targetId " +
           "AND c.parentComment IS NULL AND c.isDeleted = false ORDER BY c.createdAt ASC")
    Page<Comment> findTopLevelComments(@Param("targetType") TargetType targetType, 
                                      @Param("targetId") UUID targetId, 
                                      Pageable pageable);

    /**
     * 根据父评论ID查询回复
     * 
     * @param parentCommentId 父评论ID
     * @param pageable 分页参数
     * @return 回复分页结果
     */
    Page<Comment> findByParentCommentIdAndIsDeletedFalseOrderByCreatedAtAsc(UUID parentCommentId, Pageable pageable);

    /**
     * 根据作者ID查询评论
     * 
     * @param authorId 作者ID
     * @param pageable 分页参数
     * @return 评论分页结果
     */
    Page<Comment> findByAuthorIdAndIsDeletedFalseOrderByCreatedAtDesc(UUID authorId, Pageable pageable);

    /**
     * 统计目标的评论数量
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 评论数量
     */
    long countByTargetTypeAndTargetIdAndIsDeletedFalse(TargetType targetType, UUID targetId);

    /**
     * 统计目标的顶级评论数量
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 顶级评论数量
     */
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.targetType = :targetType AND c.targetId = :targetId " +
           "AND c.parentComment IS NULL AND c.isDeleted = false")
    long countTopLevelComments(@Param("targetType") TargetType targetType, @Param("targetId") UUID targetId);

    /**
     * 统计评论的回复数量
     * 
     * @param parentCommentId 父评论ID
     * @return 回复数量
     */
    long countByParentCommentIdAndIsDeletedFalse(UUID parentCommentId);

    /**
     * 查询最近的评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param limit 限制数量
     * @return 最近评论列表
     */
    @Query("SELECT c FROM Comment c WHERE c.targetType = :targetType AND c.targetId = :targetId " +
           "AND c.isDeleted = false ORDER BY c.createdAt DESC LIMIT :limit")
    List<Comment> findRecentComments(@Param("targetType") TargetType targetType, 
                                    @Param("targetId") UUID targetId, 
                                    @Param("limit") int limit);

    /**
     * 搜索评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 评论分页结果
     */
    @Query("SELECT c FROM Comment c WHERE c.targetType = :targetType AND c.targetId = :targetId " +
           "AND c.isDeleted = false AND (:keyword IS NULL OR c.content LIKE %:keyword%) " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> searchComments(@Param("targetType") TargetType targetType, 
                                @Param("targetId") UUID targetId, 
                                @Param("keyword") String keyword, 
                                Pageable pageable);

    /**
     * 查询用户的评论统计
     * 
     * @param authorId 作者ID
     * @return 统计结果
     */
    @Query("SELECT c.targetType, COUNT(c) FROM Comment c WHERE c.authorId = :authorId " +
           "AND c.isDeleted = false GROUP BY c.targetType")
    List<Object[]> getUserCommentStatistics(@Param("authorId") UUID authorId);

    /**
     * 查询指定时间范围内的评论
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 评论列表
     */
    @Query("SELECT c FROM Comment c WHERE c.targetType = :targetType AND c.targetId = :targetId " +
           "AND c.isDeleted = false AND c.createdAt BETWEEN :startTime AND :endTime " +
           "ORDER BY c.createdAt ASC")
    List<Comment> findCommentsByTimeRange(@Param("targetType") TargetType targetType, 
                                         @Param("targetId") UUID targetId,
                                         @Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询热门评论（按点赞数排序）
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param pageable 分页参数
     * @return 热门评论分页结果
     */
    @Query("SELECT c FROM Comment c WHERE c.targetType = :targetType AND c.targetId = :targetId " +
           "AND c.isDeleted = false ORDER BY c.likeCount DESC, c.createdAt DESC")
    Page<Comment> findPopularComments(@Param("targetType") TargetType targetType, 
                                     @Param("targetId") UUID targetId, 
                                     Pageable pageable);

    /**
     * 查询评论的完整线程（包含所有回复）
     * 
     * @param rootCommentId 根评论ID
     * @return 评论线程列表
     */
    @Query("WITH RECURSIVE comment_tree AS (" +
           "  SELECT c.* FROM Comment c WHERE c.id = :rootCommentId " +
           "  UNION ALL " +
           "  SELECT c.* FROM Comment c " +
           "  INNER JOIN comment_tree ct ON c.parent_comment_id = ct.id " +
           "  WHERE c.is_deleted = false" +
           ") SELECT * FROM comment_tree ORDER BY created_at ASC")
    List<Comment> findCommentThread(@Param("rootCommentId") UUID rootCommentId);

    /**
     * 查询用户提及的评论
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 提及评论分页结果
     */
    @Query("SELECT c FROM Comment c WHERE c.isDeleted = false " +
           "AND c.content LIKE CONCAT('%@', :userId, '%') " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> findMentionedComments(@Param("userId") String userId, Pageable pageable);

    /**
     * 批量软删除评论
     * 
     * @param commentIds 评论ID列表
     * @return 删除数量
     */
    @Query("UPDATE Comment c SET c.isDeleted = true, c.deletedAt = CURRENT_TIMESTAMP " +
           "WHERE c.id IN :commentIds")
    int batchSoftDelete(@Param("commentIds") List<UUID> commentIds);

    /**
     * 查询需要清理的已删除评论
     * 
     * @param deletedBefore 删除时间之前
     * @return 已删除评论列表
     */
    @Query("SELECT c FROM Comment c WHERE c.isDeleted = true " +
           "AND c.deletedAt IS NOT NULL AND c.deletedAt < :deletedBefore")
    List<Comment> findDeletedCommentsForCleanup(@Param("deletedBefore") LocalDateTime deletedBefore);

    /**
     * 统计评论活跃度
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param days 天数
     * @return 活跃度统计
     */
    @Query("SELECT DATE(c.createdAt) as date, COUNT(c) as count " +
           "FROM Comment c WHERE c.targetType = :targetType AND c.targetId = :targetId " +
           "AND c.isDeleted = false AND c.createdAt >= :startDate " +
           "GROUP BY DATE(c.createdAt) ORDER BY DATE(c.createdAt)")
    List<Object[]> getCommentActivity(@Param("targetType") TargetType targetType, 
                                     @Param("targetId") UUID targetId, 
                                     @Param("startDate") LocalDateTime startDate);

    /**
     * 查询评论作者排行
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param limit 限制数量
     * @return 作者排行列表
     */
    @Query("SELECT c.authorId, COUNT(c) as commentCount, SUM(c.likeCount) as totalLikes " +
           "FROM Comment c WHERE c.targetType = :targetType AND c.targetId = :targetId " +
           "AND c.isDeleted = false GROUP BY c.authorId " +
           "ORDER BY commentCount DESC, totalLikes DESC LIMIT :limit")
    List<Object[]> getTopCommentAuthors(@Param("targetType") TargetType targetType, 
                                       @Param("targetId") UUID targetId, 
                                       @Param("limit") int limit);
}
