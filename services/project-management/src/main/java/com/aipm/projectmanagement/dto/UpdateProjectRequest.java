package com.aipm.projectmanagement.dto;

import com.aipm.projectmanagement.entity.TaskPriority;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * 更新项目请求DTO
 * 
 * 用于接收更新项目的请求参数
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "更新项目请求")
public class UpdateProjectRequest {

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @Size(min = 2, max = 100, message = "项目名称长度必须在2-100个字符之间")
    @Schema(description = "项目名称", example = "AI项目管理平台", required = true)
    private String name;

    /**
     * 项目描述
     */
    @Size(max = 2000, message = "项目描述长度不能超过2000个字符")
    @Schema(description = "项目描述", example = "基于AI的智能项目管理平台，提供项目规划、任务管理、团队协作等功能")
    private String description;

    /**
     * 项目负责人ID
     */
    @Schema(description = "项目负责人ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID ownerId;

    /**
     * 项目开始日期
     */
    @Schema(description = "项目开始日期", example = "2025-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    @Schema(description = "项目结束日期", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 项目预算
     */
    @Schema(description = "项目预算", example = "1000000.00")
    private BigDecimal budget;

    /**
     * 项目优先级
     */
    @Schema(description = "项目优先级", example = "HIGH")
    private TaskPriority priority;

    /**
     * 项目标签
     */
    @Schema(description = "项目标签", example = "[\"AI\", \"管理平台\", \"微服务\"]")
    private List<String> tags;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public UpdateProjectRequest() {
    }

    /**
     * 基础构造函数
     * 
     * @param name 项目名称
     * @param description 项目描述
     */
    public UpdateProjectRequest(String name, String description) {
        this.name = name;
        this.description = description;
    }

    // ============================================================================
    // 验证方法
    // ============================================================================

    /**
     * 验证日期范围
     * 
     * @return 验证结果
     */
    public boolean isDateRangeValid() {
        if (startDate == null || endDate == null) {
            return true; // 允许为空
        }
        return !endDate.isBefore(startDate);
    }

    /**
     * 验证预算
     * 
     * @return 验证结果
     */
    public boolean isBudgetValid() {
        return budget == null || budget.compareTo(BigDecimal.ZERO) >= 0;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UUID getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(UUID ownerId) {
        this.ownerId = ownerId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public String toString() {
        return "UpdateProjectRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", ownerId=" + ownerId +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", priority=" + priority +
                '}';
    }
}
