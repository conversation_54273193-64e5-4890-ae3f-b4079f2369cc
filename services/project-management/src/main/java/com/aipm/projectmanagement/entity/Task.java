package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 任务实体类
 * 
 * 表示项目中的一个任务，包括基本信息、状态、分配等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "tasks", indexes = {
    @Index(name = "idx_tasks_project_id", columnList = "project_id"),
    @Index(name = "idx_tasks_assignee_id", columnList = "assignee_id"),
    @Index(name = "idx_tasks_status", columnList = "status"),
    @Index(name = "idx_tasks_priority", columnList = "priority"),
    @Index(name = "idx_tasks_due_date", columnList = "due_date"),
    @Index(name = "idx_tasks_created_at", columnList = "created_at"),
    @Index(name = "idx_tasks_parent_id", columnList = "parent_task_id")
})
@EntityListeners(AuditingEntityListener.class)
public class Task {

    /**
     * 任务唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    @Size(min = 2, max = 200, message = "任务标题长度必须在2-200个字符之间")
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    /**
     * 任务描述
     */
    @Size(max = 5000, message = "任务描述长度不能超过5000个字符")
    @Column(name = "description", length = 5000)
    private String description;

    /**
     * 所属项目
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    @NotNull(message = "所属项目不能为空")
    private Project project;

    /**
     * 父任务（用于子任务）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_task_id")
    private Task parentTask;

    /**
     * 所属Sprint
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sprint_id")
    private Sprint sprint;

    /**
     * 子任务列表
     */
    @OneToMany(mappedBy = "parentTask", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Task> subTasks = new ArrayList<>();

    /**
     * 任务状态
     */
    @NotNull(message = "任务状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private TaskStatus status = TaskStatus.TODO;

    /**
     * 任务优先级
     */
    @NotNull(message = "任务优先级不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "priority", nullable = false, length = 20)
    private TaskPriority priority = TaskPriority.NORMAL;

    /**
     * 任务分配给的用户ID
     */
    @Column(name = "assignee_id")
    private UUID assigneeId;

    /**
     * 任务创建者ID
     */
    @Column(name = "reporter_id")
    private UUID reporterId;

    /**
     * 预估工时（小时）
     */
    @Column(name = "estimated_hours", precision = 8, scale = 2)
    private BigDecimal estimatedHours;

    /**
     * 故事点（敏捷开发中的工作量估算）
     */
    @Column(name = "story_points")
    private Integer storyPoints;

    /**
     * 实际工时（小时）
     */
    @Column(name = "actual_hours", precision = 8, scale = 2)
    private BigDecimal actualHours;

    /**
     * 任务进度百分比 (0-100)
     */
    @Column(name = "progress", columnDefinition = "INTEGER DEFAULT 0")
    private Integer progress = 0;

    /**
     * 截止日期
     */
    @Column(name = "due_date")
    private LocalDateTime dueDate;

    /**
     * 开始时间
     */
    @Column(name = "start_date")
    private LocalDateTime startDate;

    /**
     * 完成时间
     */
    @Column(name = "completed_date")
    private LocalDateTime completedDate;

    /**
     * 任务标签（JSON格式存储）
     */
    @Column(name = "tags", length = 500)
    private String tags;

    /**
     * 任务附件信息（JSON格式存储）
     */
    @Column(name = "attachments", columnDefinition = "TEXT")
    private String attachments;

    /**
     * 任务排序序号
     */
    @Column(name = "sort_order", columnDefinition = "INTEGER DEFAULT 0")
    private Integer sortOrder = 0;

    /**
     * 是否已删除（软删除）
     */
    @Column(name = "is_deleted", columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isDeleted = false;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private UUID createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private UUID updatedBy;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public Task() {
    }

    /**
     * 基础构造函数
     * 
     * @param title 任务标题
     * @param description 任务描述
     * @param project 所属项目
     */
    public Task(String title, String description, Project project) {
        this.title = title;
        this.description = description;
        this.project = project;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查任务是否处于活跃状态
     * 
     * @return true表示任务活跃
     */
    public boolean isActive() {
        return status != null && status.isActive() && !Boolean.TRUE.equals(isDeleted);
    }

    /**
     * 检查任务是否已完成
     * 
     * @return true表示任务已完成
     */
    public boolean isCompleted() {
        return status != null && status.isCompleted();
    }

    /**
     * 检查任务是否已过期
     * 
     * @return true表示任务已过期
     */
    public boolean isOverdue() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate) && !isCompleted();
    }

    /**
     * 检查任务是否被阻塞
     * 
     * @return true表示任务被阻塞
     */
    public boolean isBlocked() {
        return status == TaskStatus.BLOCKED;
    }

    /**
     * 检查是否为子任务
     * 
     * @return true表示是子任务
     */
    public boolean isSubTask() {
        return parentTask != null;
    }

    /**
     * 检查是否有子任务
     * 
     * @return true表示有子任务
     */
    public boolean hasSubTasks() {
        return subTasks != null && !subTasks.isEmpty();
    }

    /**
     * 添加子任务
     * 
     * @param subTask 子任务
     */
    public void addSubTask(Task subTask) {
        if (subTasks == null) {
            subTasks = new ArrayList<>();
        }
        subTasks.add(subTask);
        subTask.setParentTask(this);
    }

    /**
     * 移除子任务
     * 
     * @param subTask 子任务
     */
    public void removeSubTask(Task subTask) {
        if (subTasks != null) {
            subTasks.remove(subTask);
            subTask.setParentTask(null);
        }
    }

    /**
     * 计算子任务完成率
     * 
     * @return 完成率百分比 (0-100)
     */
    public double getSubTaskCompletionRate() {
        if (subTasks == null || subTasks.isEmpty()) {
            return 0.0;
        }
        long completedCount = subTasks.stream()
            .filter(task -> !Boolean.TRUE.equals(task.isDeleted))
            .filter(Task::isCompleted)
            .count();
        long totalCount = subTasks.stream()
            .filter(task -> !Boolean.TRUE.equals(task.isDeleted))
            .count();
        
        return totalCount > 0 ? (double) completedCount / totalCount * 100 : 0.0;
    }

    /**
     * 计算剩余工时
     * 
     * @return 剩余工时
     */
    public BigDecimal getRemainingHours() {
        if (estimatedHours == null) {
            return null;
        }
        if (actualHours == null) {
            return estimatedHours;
        }
        return estimatedHours.subtract(actualHours);
    }

    /**
     * 计算工时完成率
     * 
     * @return 工时完成率百分比
     */
    public double getHoursCompletionRate() {
        if (estimatedHours == null || estimatedHours.compareTo(BigDecimal.ZERO) == 0) {
            return 0.0;
        }
        if (actualHours == null) {
            return 0.0;
        }
        return actualHours.divide(estimatedHours, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue();
    }

    /**
     * 开始任务
     */
    public void start() {
        if (status == TaskStatus.TODO || status == TaskStatus.BLOCKED) {
            this.status = TaskStatus.IN_PROGRESS;
            this.startDate = LocalDateTime.now();
        }
    }

    /**
     * 完成任务
     */
    public void complete() {
        if (status.canTransitionTo(TaskStatus.DONE)) {
            this.status = TaskStatus.DONE;
            this.completedDate = LocalDateTime.now();
            this.progress = 100;
        }
    }

    /**
     * 阻塞任务
     */
    public void block() {
        if (status.canTransitionTo(TaskStatus.BLOCKED)) {
            this.status = TaskStatus.BLOCKED;
        }
    }

    /**
     * 软删除任务
     */
    public void softDelete() {
        this.isDeleted = true;
    }

    /**
     * 恢复已删除的任务
     */
    public void restore() {
        this.isDeleted = false;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public Task getParentTask() {
        return parentTask;
    }

    public void setParentTask(Task parentTask) {
        this.parentTask = parentTask;
    }

    public Sprint getSprint() {
        return sprint;
    }

    public void setSprint(Sprint sprint) {
        this.sprint = sprint;
    }

    public Integer getStoryPoints() {
        return storyPoints;
    }

    public void setStoryPoints(Integer storyPoints) {
        this.storyPoints = storyPoints;
    }

    public List<Task> getSubTasks() {
        return subTasks;
    }

    public void setSubTasks(List<Task> subTasks) {
        this.subTasks = subTasks;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public UUID getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(UUID assigneeId) {
        this.assigneeId = assigneeId;
    }

    public UUID getReporterId() {
        return reporterId;
    }

    public void setReporterId(UUID reporterId) {
        this.reporterId = reporterId;
    }

    public BigDecimal getEstimatedHours() {
        return estimatedHours;
    }

    public void setEstimatedHours(BigDecimal estimatedHours) {
        this.estimatedHours = estimatedHours;
    }

    public BigDecimal getActualHours() {
        return actualHours;
    }

    public void setActualHours(BigDecimal actualHours) {
        this.actualHours = actualHours;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public LocalDateTime getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(LocalDateTime completedDate) {
        this.completedDate = completedDate;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getAttachments() {
        return attachments;
    }

    public void setAttachments(String attachments) {
        this.attachments = attachments;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public UUID getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Task task)) return false;
        return id != null && id.equals(task.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "Task{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", status=" + status +
                ", priority=" + priority +
                ", assigneeId=" + assigneeId +
                ", progress=" + progress +
                ", createdAt=" + createdAt +
                '}';
    }
}
