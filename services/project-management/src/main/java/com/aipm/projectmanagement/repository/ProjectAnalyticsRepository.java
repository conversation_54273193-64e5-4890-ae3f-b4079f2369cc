package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.ProjectAnalytics;
import com.aipm.projectmanagement.entity.ProjectAnalytics.AnalyticsType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 项目分析数据访问接口
 * 
 * 提供项目分析数据相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
@Repository
public interface ProjectAnalyticsRepository extends JpaRepository<ProjectAnalytics, UUID> {

    /**
     * 根据项目ID查询分析数据
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 分析数据分页结果
     */
    Page<ProjectAnalytics> findByProjectIdOrderByAnalyticsDateDesc(UUID projectId, Pageable pageable);

    /**
     * 根据项目ID和分析类型查询数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param pageable 分页参数
     * @return 分析数据分页结果
     */
    Page<ProjectAnalytics> findByProjectIdAndAnalyticsTypeOrderByAnalyticsDateDesc(UUID projectId, 
                                                                                   AnalyticsType analyticsType, 
                                                                                   Pageable pageable);

    /**
     * 查询指定日期范围的分析数据
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 分析数据分页结果
     */
    @Query("SELECT a FROM ProjectAnalytics a WHERE a.projectId = :projectId " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate DESC")
    Page<ProjectAnalytics> findByProjectIdAndDateRange(@Param("projectId") UUID projectId,
                                                       @Param("startDate") LocalDate startDate,
                                                       @Param("endDate") LocalDate endDate,
                                                       Pageable pageable);

    /**
     * 查询特定日期的分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param analyticsDate 分析日期
     * @return 分析数据
     */
    Optional<ProjectAnalytics> findByProjectIdAndAnalyticsTypeAndAnalyticsDate(UUID projectId, 
                                                                               AnalyticsType analyticsType, 
                                                                               LocalDate analyticsDate);

    /**
     * 查询最新的分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @return 最新分析数据
     */
    @Query("SELECT a FROM ProjectAnalytics a WHERE a.projectId = :projectId " +
           "AND a.analyticsType = :analyticsType ORDER BY a.analyticsDate DESC LIMIT 1")
    Optional<ProjectAnalytics> findLatestAnalytics(@Param("projectId") UUID projectId, 
                                                   @Param("analyticsType") AnalyticsType analyticsType);

    /**
     * 查询最近N天的分析数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param days 天数
     * @return 分析数据列表
     */
    @Query("SELECT a FROM ProjectAnalytics a WHERE a.projectId = :projectId " +
           "AND a.analyticsType = :analyticsType " +
           "AND a.analyticsDate >= :startDate " +
           "ORDER BY a.analyticsDate DESC")
    List<ProjectAnalytics> findRecentAnalytics(@Param("projectId") UUID projectId,
                                              @Param("analyticsType") AnalyticsType analyticsType,
                                              @Param("startDate") LocalDate startDate);

    /**
     * 查询任务完成率趋势
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 完成率趋势数据
     */
    @Query("SELECT a.analyticsDate, a.taskCompletionRate FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate ASC")
    List<Object[]> getTaskCompletionTrend(@Param("projectId") UUID projectId,
                                         @Param("startDate") LocalDate startDate,
                                         @Param("endDate") LocalDate endDate);

    /**
     * 查询团队效率趋势
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 效率趋势数据
     */
    @Query("SELECT a.analyticsDate, a.teamEfficiencyScore FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate ASC")
    List<Object[]> getTeamEfficiencyTrend(@Param("projectId") UUID projectId,
                                         @Param("startDate") LocalDate startDate,
                                         @Param("endDate") LocalDate endDate);

    /**
     * 查询项目进度趋势
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 进度趋势数据
     */
    @Query("SELECT a.analyticsDate, a.projectProgress, a.plannedProgress FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate ASC")
    List<Object[]> getProjectProgressTrend(@Param("projectId") UUID projectId,
                                          @Param("startDate") LocalDate startDate,
                                          @Param("endDate") LocalDate endDate);

    /**
     * 查询质量分数趋势
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 质量趋势数据
     */
    @Query("SELECT a.analyticsDate, a.qualityScore, a.bugCount FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate ASC")
    List<Object[]> getQualityTrend(@Param("projectId") UUID projectId,
                                  @Param("startDate") LocalDate startDate,
                                  @Param("endDate") LocalDate endDate);

    /**
     * 查询风险等级分布
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 风险分布数据
     */
    @Query("SELECT a.riskLevel, COUNT(a) FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "GROUP BY a.riskLevel ORDER BY a.riskLevel")
    List<Object[]> getRiskLevelDistribution(@Param("projectId") UUID projectId,
                                           @Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate);

    /**
     * 计算平均指标
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 平均指标数据
     */
    @Query("SELECT " +
           "AVG(a.taskCompletionRate) as avgCompletionRate, " +
           "AVG(a.teamEfficiencyScore) as avgEfficiencyScore, " +
           "AVG(a.qualityScore) as avgQualityScore, " +
           "AVG(a.projectProgress) as avgProgress " +
           "FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate")
    Object[] getAverageMetrics(@Param("projectId") UUID projectId,
                              @Param("startDate") LocalDate startDate,
                              @Param("endDate") LocalDate endDate);

    /**
     * 查询高风险日期
     * 
     * @param projectId 项目ID
     * @param minRiskLevel 最小风险等级
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 高风险日期列表
     */
    @Query("SELECT a.analyticsDate, a.riskLevel FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.riskLevel >= :minRiskLevel " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.riskLevel DESC, a.analyticsDate DESC")
    List<Object[]> getHighRiskDates(@Param("projectId") UUID projectId,
                                   @Param("minRiskLevel") Integer minRiskLevel,
                                   @Param("startDate") LocalDate startDate,
                                   @Param("endDate") LocalDate endDate);

    /**
     * 查询任务状态分布趋势
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 任务状态趋势数据
     */
    @Query("SELECT a.analyticsDate, a.completedTasks, a.inProgressTasks, a.todoTasks, a.blockedTasks " +
           "FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate ASC")
    List<Object[]> getTaskStatusTrend(@Param("projectId") UUID projectId,
                                     @Param("startDate") LocalDate startDate,
                                     @Param("endDate") LocalDate endDate);

    /**
     * 查询团队活跃度趋势
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 活跃度趋势数据
     */
    @Query("SELECT a.analyticsDate, a.activeMembers, a.totalMembers FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate ASC")
    List<Object[]> getTeamActivityTrend(@Param("projectId") UUID projectId,
                                       @Param("startDate") LocalDate startDate,
                                       @Param("endDate") LocalDate endDate);

    /**
     * 查询工时分析数据
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 工时分析数据
     */
    @Query("SELECT a.analyticsDate, a.plannedHours, a.actualHours, a.timeVarianceRate " +
           "FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate ASC")
    List<Object[]> getTimeAnalysis(@Param("projectId") UUID projectId,
                                  @Param("startDate") LocalDate startDate,
                                  @Param("endDate") LocalDate endDate);

    /**
     * 查询协作活动趋势
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 协作活动趋势数据
     */
    @Query("SELECT a.analyticsDate, a.commentCount, a.attachmentCount, a.activityCount " +
           "FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.analyticsDate ASC")
    List<Object[]> getCollaborationTrend(@Param("projectId") UUID projectId,
                                        @Param("startDate") LocalDate startDate,
                                        @Param("endDate") LocalDate endDate);

    /**
     * 查询项目健康度指标
     * 
     * @param projectId 项目ID
     * @param analyticsDate 分析日期
     * @return 健康度指标
     */
    @Query("SELECT " +
           "a.taskCompletionRate, " +
           "a.teamEfficiencyScore, " +
           "a.qualityScore, " +
           "a.progressVariance, " +
           "a.riskLevel " +
           "FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate = :analyticsDate")
    Object[] getProjectHealthMetrics(@Param("projectId") UUID projectId, @Param("analyticsDate") LocalDate analyticsDate);

    /**
     * 查询最佳表现日期
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 最佳表现日期列表
     */
    @Query("SELECT a.analyticsDate, a.teamEfficiencyScore FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate " +
           "ORDER BY a.teamEfficiencyScore DESC LIMIT :limit")
    List<Object[]> getBestPerformanceDates(@Param("projectId") UUID projectId,
                                          @Param("startDate") LocalDate startDate,
                                          @Param("endDate") LocalDate endDate,
                                          @Param("limit") int limit);

    /**
     * 查询项目分析摘要
     * 
     * @param projectId 项目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分析摘要
     */
    @Query("SELECT " +
           "COUNT(a) as totalDays, " +
           "AVG(a.taskCompletionRate) as avgCompletionRate, " +
           "AVG(a.teamEfficiencyScore) as avgEfficiencyScore, " +
           "AVG(a.qualityScore) as avgQualityScore, " +
           "AVG(a.riskLevel) as avgRiskLevel, " +
           "MAX(a.projectProgress) as maxProgress " +
           "FROM ProjectAnalytics a " +
           "WHERE a.projectId = :projectId " +
           "AND a.analyticsType = 'DAILY' " +
           "AND a.analyticsDate BETWEEN :startDate AND :endDate")
    Object[] getAnalyticsSummary(@Param("projectId") UUID projectId,
                                @Param("startDate") LocalDate startDate,
                                @Param("endDate") LocalDate endDate);

    /**
     * 删除旧的分析数据
     * 
     * @param beforeDate 删除日期之前的数据
     * @return 删除数量
     */
    @Query("DELETE FROM ProjectAnalytics a WHERE a.analyticsDate < :beforeDate")
    int deleteOldAnalytics(@Param("beforeDate") LocalDate beforeDate);

    /**
     * 检查是否存在指定日期的数据
     * 
     * @param projectId 项目ID
     * @param analyticsType 分析类型
     * @param analyticsDate 分析日期
     * @return 是否存在
     */
    boolean existsByProjectIdAndAnalyticsTypeAndAnalyticsDate(UUID projectId, 
                                                             AnalyticsType analyticsType, 
                                                             LocalDate analyticsDate);
}
