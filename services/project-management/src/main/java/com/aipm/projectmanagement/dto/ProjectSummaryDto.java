package com.aipm.projectmanagement.dto;

import com.aipm.projectmanagement.entity.ProjectStatus;
import com.aipm.projectmanagement.entity.TaskPriority;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.UUID;

/**
 * 项目摘要信息DTO
 * 
 * 用于在任务等其他对象中显示项目基本信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "项目摘要信息")
public class ProjectSummaryDto {

    /**
     * 项目ID
     */
    @Schema(description = "项目唯一标识", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称", example = "AI项目管理平台")
    private String name;

    /**
     * 项目状态
     */
    @Schema(description = "项目状态", example = "IN_PROGRESS")
    private ProjectStatus status;

    /**
     * 项目优先级
     */
    @Schema(description = "项目优先级", example = "HIGH")
    private TaskPriority priority;

    /**
     * 项目进度百分比
     */
    @Schema(description = "项目进度百分比", example = "75")
    private Integer progress;

    /**
     * 是否已归档
     */
    @Schema(description = "是否已归档", example = "false")
    private Boolean isArchived;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public ProjectSummaryDto() {
    }

    /**
     * 基础构造函数
     * 
     * @param id 项目ID
     * @param name 项目名称
     * @param status 项目状态
     */
    public ProjectSummaryDto(UUID id, String name, ProjectStatus status) {
        this.id = id;
        this.name = name;
        this.status = status;
    }

    /**
     * 完整构造函数
     * 
     * @param id 项目ID
     * @param name 项目名称
     * @param status 项目状态
     * @param priority 项目优先级
     * @param progress 项目进度
     */
    public ProjectSummaryDto(UUID id, String name, ProjectStatus status, 
                           TaskPriority priority, Integer progress) {
        this.id = id;
        this.name = name;
        this.status = status;
        this.priority = priority;
        this.progress = progress;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查项目是否处于活跃状态
     * 
     * @return true表示项目活跃
     */
    public boolean isActive() {
        return status != null && status.isActive() && !Boolean.TRUE.equals(isArchived);
    }

    /**
     * 检查项目是否已完成
     * 
     * @return true表示项目已完成
     */
    public boolean isCompleted() {
        return status == ProjectStatus.COMPLETED;
    }

    /**
     * 获取项目状态显示名称
     * 
     * @return 状态显示名称
     */
    public String getStatusDisplayName() {
        return status != null ? status.getDisplayName() : "未知";
    }

    /**
     * 获取优先级显示名称
     * 
     * @return 优先级显示名称
     */
    public String getPriorityDisplayName() {
        return priority != null ? priority.getDisplayName() : "普通";
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ProjectStatus getStatus() {
        return status;
    }

    public void setStatus(ProjectStatus status) {
        this.status = status;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public Boolean getIsArchived() {
        return isArchived;
    }

    public void setIsArchived(Boolean isArchived) {
        this.isArchived = isArchived;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ProjectSummaryDto that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "ProjectSummaryDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status=" + status +
                ", priority=" + priority +
                ", progress=" + progress +
                '}';
    }
}
