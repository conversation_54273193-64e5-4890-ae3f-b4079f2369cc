package com.aipm.projectmanagement.dto;

import com.aipm.projectmanagement.entity.TaskPriority;
import com.aipm.projectmanagement.entity.TaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.UUID;

/**
 * 任务摘要信息DTO
 * 
 * 用于在项目等其他对象中显示任务基本信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "任务摘要信息")
public class TaskSummaryDto {

    /**
     * 任务ID
     */
    @Schema(description = "任务唯一标识", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    /**
     * 任务标题
     */
    @Schema(description = "任务标题", example = "实现用户认证功能")
    private String title;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态", example = "IN_PROGRESS")
    private TaskStatus status;

    /**
     * 任务优先级
     */
    @Schema(description = "任务优先级", example = "HIGH")
    private TaskPriority priority;

    /**
     * 任务进度百分比
     */
    @Schema(description = "任务进度百分比", example = "75")
    private Integer progress;

    /**
     * 任务分配给的用户ID
     */
    @Schema(description = "分配给的用户ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID assigneeId;

    /**
     * 分配给的用户信息
     */
    @Schema(description = "分配给的用户信息")
    private UserSummaryDto assignee;

    /**
     * 是否已删除
     */
    @Schema(description = "是否已删除", example = "false")
    private Boolean isDeleted;

    /**
     * 任务排序序号
     */
    @Schema(description = "任务排序序号", example = "1")
    private Integer sortOrder;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public TaskSummaryDto() {
    }

    /**
     * 基础构造函数
     * 
     * @param id 任务ID
     * @param title 任务标题
     * @param status 任务状态
     */
    public TaskSummaryDto(UUID id, String title, TaskStatus status) {
        this.id = id;
        this.title = title;
        this.status = status;
    }

    /**
     * 完整构造函数
     * 
     * @param id 任务ID
     * @param title 任务标题
     * @param status 任务状态
     * @param priority 任务优先级
     * @param progress 任务进度
     */
    public TaskSummaryDto(UUID id, String title, TaskStatus status, 
                         TaskPriority priority, Integer progress) {
        this.id = id;
        this.title = title;
        this.status = status;
        this.priority = priority;
        this.progress = progress;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 检查任务是否处于活跃状态
     * 
     * @return true表示任务活跃
     */
    public boolean isActive() {
        return status != null && status.isActive() && !Boolean.TRUE.equals(isDeleted);
    }

    /**
     * 检查任务是否已完成
     * 
     * @return true表示任务已完成
     */
    public boolean isCompleted() {
        return status != null && status.isCompleted();
    }

    /**
     * 获取任务状态显示名称
     * 
     * @return 状态显示名称
     */
    public String getStatusDisplayName() {
        return status != null ? status.getDisplayName() : "未知";
    }

    /**
     * 获取优先级显示名称
     * 
     * @return 优先级显示名称
     */
    public String getPriorityDisplayName() {
        return priority != null ? priority.getDisplayName() : "普通";
    }

    /**
     * 获取优先级颜色
     * 
     * @return 优先级颜色
     */
    public String getPriorityColor() {
        return priority != null ? priority.getColor() : "#3B82F6";
    }

    /**
     * 获取状态颜色
     * 
     * @return 状态颜色
     */
    public String getStatusColor() {
        return status != null ? status.getColor() : "#6B7280";
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public UUID getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(UUID assigneeId) {
        this.assigneeId = assigneeId;
    }

    public UserSummaryDto getAssignee() {
        return assignee;
    }

    public void setAssignee(UserSummaryDto assignee) {
        this.assignee = assignee;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TaskSummaryDto that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "TaskSummaryDto{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", status=" + status +
                ", priority=" + priority +
                ", progress=" + progress +
                '}';
    }
}
