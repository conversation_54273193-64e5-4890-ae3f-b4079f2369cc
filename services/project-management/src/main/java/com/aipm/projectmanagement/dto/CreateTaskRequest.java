package com.aipm.projectmanagement.dto;

import com.aipm.projectmanagement.entity.TaskPriority;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 创建任务请求DTO
 * 
 * 用于接收创建任务的请求参数
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "创建任务请求")
public class CreateTaskRequest {

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    @Size(min = 2, max = 200, message = "任务标题长度必须在2-200个字符之间")
    @Schema(description = "任务标题", example = "实现用户认证功能", required = true)
    private String title;

    /**
     * 任务描述
     */
    @Size(max = 5000, message = "任务描述长度不能超过5000个字符")
    @Schema(description = "任务描述", example = "实现基于JWT的用户认证和授权功能，包括登录、注册、令牌刷新等")
    private String description;

    /**
     * 所属项目ID
     */
    @NotNull(message = "所属项目不能为空")
    @Schema(description = "所属项目ID", example = "123e4567-e89b-12d3-a456-************", required = true)
    private UUID projectId;

    /**
     * 父任务ID（创建子任务时使用）
     */
    @Schema(description = "父任务ID（创建子任务时使用）", example = "123e4567-e89b-12d3-a456-************")
    private UUID parentTaskId;

    /**
     * 任务优先级
     */
    @Schema(description = "任务优先级", example = "HIGH")
    private TaskPriority priority = TaskPriority.NORMAL;

    /**
     * 任务分配给的用户ID
     */
    @Schema(description = "分配给的用户ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID assigneeId;

    /**
     * 预估工时（小时）
     */
    @Schema(description = "预估工时（小时）", example = "16.5")
    private BigDecimal estimatedHours;

    /**
     * 截止日期
     */
    @Schema(description = "截止日期", example = "2025-08-20T18:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;

    /**
     * 任务标签
     */
    @Schema(description = "任务标签", example = "[\"认证\", \"安全\", \"后端\"]")
    private List<String> tags;

    /**
     * 任务排序序号
     */
    @Schema(description = "任务排序序号", example = "1")
    private Integer sortOrder;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public CreateTaskRequest() {
    }

    /**
     * 基础构造函数
     * 
     * @param title 任务标题
     * @param description 任务描述
     * @param projectId 所属项目ID
     */
    public CreateTaskRequest(String title, String description, UUID projectId) {
        this.title = title;
        this.description = description;
        this.projectId = projectId;
    }

    // ============================================================================
    // 验证方法
    // ============================================================================

    /**
     * 验证截止日期
     * 
     * @return 验证结果
     */
    public boolean isDueDateValid() {
        return dueDate == null || dueDate.isAfter(LocalDateTime.now());
    }

    /**
     * 验证预估工时
     * 
     * @return 验证结果
     */
    public boolean isEstimatedHoursValid() {
        return estimatedHours == null || estimatedHours.compareTo(BigDecimal.ZERO) >= 0;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UUID getProjectId() {
        return projectId;
    }

    public void setProjectId(UUID projectId) {
        this.projectId = projectId;
    }

    public UUID getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(UUID parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public TaskPriority getPriority() {
        return priority;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public UUID getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(UUID assigneeId) {
        this.assigneeId = assigneeId;
    }

    public BigDecimal getEstimatedHours() {
        return estimatedHours;
    }

    public void setEstimatedHours(BigDecimal estimatedHours) {
        this.estimatedHours = estimatedHours;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public String toString() {
        return "CreateTaskRequest{" +
                "title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", projectId=" + projectId +
                ", parentTaskId=" + parentTaskId +
                ", priority=" + priority +
                ", assigneeId=" + assigneeId +
                ", dueDate=" + dueDate +
                '}';
    }
}
