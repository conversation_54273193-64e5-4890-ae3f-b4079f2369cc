package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 项目报表实体类
 * 
 * 存储项目的各种报表和分析数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */
@Entity
@Table(name = "project_reports", indexes = {
    @Index(name = "idx_report_project", columnList = "project_id"),
    @Index(name = "idx_report_type", columnList = "report_type"),
    @Index(name = "idx_report_period", columnList = "period_start, period_end"),
    @Index(name = "idx_report_created", columnList = "created_at"),
    @Index(name = "idx_report_status", columnList = "status")
})
public class ProjectReport {

    /**
     * 报表类型枚举
     */
    public enum ReportType {
        DAILY("日报", "每日项目进度报表"),
        WEEKLY("周报", "每周项目总结报表"),
        MONTHLY("月报", "每月项目分析报表"),
        SPRINT("Sprint报表", "Sprint周期报表"),
        MILESTONE("里程碑报表", "项目里程碑报表"),
        TEAM_PERFORMANCE("团队绩效", "团队绩效分析报表"),
        TASK_ANALYSIS("任务分析", "任务完成情况分析"),
        TIME_TRACKING("时间跟踪", "时间使用情况报表"),
        BURNDOWN("燃尽图", "Sprint燃尽图报表"),
        VELOCITY("团队速度", "团队开发速度报表"),
        QUALITY("质量分析", "代码质量和缺陷分析"),
        RESOURCE("资源利用", "资源使用情况报表"),
        RISK("风险分析", "项目风险评估报表"),
        CUSTOM("自定义", "用户自定义报表");

        private final String displayName;
        private final String description;

        ReportType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 报表状态枚举
     */
    public enum ReportStatus {
        PENDING("待生成"),
        GENERATING("生成中"),
        COMPLETED("已完成"),
        FAILED("生成失败"),
        EXPIRED("已过期");

        private final String displayName;

        ReportStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 报表格式枚举
     */
    public enum ReportFormat {
        JSON("JSON格式"),
        PDF("PDF文档"),
        EXCEL("Excel表格"),
        CSV("CSV文件"),
        HTML("HTML网页");

        private final String displayName;

        ReportFormat(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 报表唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 所属项目ID
     */
    @Column(name = "project_id", nullable = false)
    private UUID projectId;

    /**
     * 报表类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "report_type", nullable = false)
    private ReportType reportType;

    /**
     * 报表标题
     */
    @Column(name = "title", nullable = false, length = 255)
    private String title;

    /**
     * 报表描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 统计周期开始时间
     */
    @Column(name = "period_start", nullable = false)
    private LocalDateTime periodStart;

    /**
     * 统计周期结束时间
     */
    @Column(name = "period_end", nullable = false)
    private LocalDateTime periodEnd;

    /**
     * 报表状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ReportStatus status = ReportStatus.PENDING;

    /**
     * 报表格式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "format", nullable = false)
    private ReportFormat format = ReportFormat.JSON;

    /**
     * 报表数据（JSON格式）
     */
    @Column(name = "data", columnDefinition = "LONGTEXT")
    private String data;

    /**
     * 报表文件路径
     */
    @Column(name = "file_path", length = 500)
    private String filePath;

    /**
     * 报表文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 生成者ID
     */
    @Column(name = "generated_by", nullable = false)
    private UUID generatedBy;

    /**
     * 生成开始时间
     */
    @Column(name = "generation_started_at")
    private LocalDateTime generationStartedAt;

    /**
     * 生成完成时间
     */
    @Column(name = "generation_completed_at")
    private LocalDateTime generationCompletedAt;

    /**
     * 生成耗时（毫秒）
     */
    @Column(name = "generation_duration")
    private Long generationDuration;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 报表配置（JSON格式）
     */
    @Column(name = "config", columnDefinition = "TEXT")
    private String config;

    /**
     * 是否自动生成
     */
    @Column(name = "is_auto_generated", nullable = false)
    private Boolean isAutoGenerated = false;

    /**
     * 下次生成时间（用于定期报表）
     */
    @Column(name = "next_generation_time")
    private LocalDateTime nextGenerationTime;

    /**
     * 过期时间
     */
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    /**
     * 下载次数
     */
    @Column(name = "download_count", nullable = false)
    private Integer downloadCount = 0;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public ProjectReport() {
    }

    /**
     * 基础构造函数
     * 
     * @param projectId 项目ID
     * @param reportType 报表类型
     * @param title 标题
     * @param periodStart 开始时间
     * @param periodEnd 结束时间
     * @param generatedBy 生成者ID
     */
    public ProjectReport(UUID projectId, ReportType reportType, String title, 
                        LocalDateTime periodStart, LocalDateTime periodEnd, UUID generatedBy) {
        this.projectId = projectId;
        this.reportType = reportType;
        this.title = title;
        this.periodStart = periodStart;
        this.periodEnd = periodEnd;
        this.generatedBy = generatedBy;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 开始生成报表
     */
    public void startGeneration() {
        this.status = ReportStatus.GENERATING;
        this.generationStartedAt = LocalDateTime.now();
    }

    /**
     * 完成报表生成
     * 
     * @param data 报表数据
     * @param filePath 文件路径
     * @param fileSize 文件大小
     */
    public void completeGeneration(String data, String filePath, Long fileSize) {
        this.status = ReportStatus.COMPLETED;
        this.data = data;
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.generationCompletedAt = LocalDateTime.now();
        
        if (this.generationStartedAt != null) {
            this.generationDuration = java.time.Duration.between(
                this.generationStartedAt, this.generationCompletedAt).toMillis();
        }
    }

    /**
     * 标记生成失败
     * 
     * @param errorMessage 错误信息
     */
    public void markAsFailed(String errorMessage) {
        this.status = ReportStatus.FAILED;
        this.errorMessage = errorMessage;
        this.generationCompletedAt = LocalDateTime.now();
        
        if (this.generationStartedAt != null) {
            this.generationDuration = java.time.Duration.between(
                this.generationStartedAt, this.generationCompletedAt).toMillis();
        }
    }

    /**
     * 增加下载次数
     */
    public void incrementDownloadCount() {
        this.downloadCount = (this.downloadCount == null ? 0 : this.downloadCount) + 1;
    }

    /**
     * 检查是否已过期
     * 
     * @return true表示已过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查是否生成完成
     * 
     * @return true表示生成完成
     */
    public boolean isCompleted() {
        return status == ReportStatus.COMPLETED;
    }

    /**
     * 检查是否生成失败
     * 
     * @return true表示生成失败
     */
    public boolean isFailed() {
        return status == ReportStatus.FAILED;
    }

    /**
     * 检查是否正在生成
     * 
     * @return true表示正在生成
     */
    public boolean isGenerating() {
        return status == ReportStatus.GENERATING;
    }

    /**
     * 设置过期时间（从现在开始的天数）
     * 
     * @param days 天数
     */
    public void setExpiresInDays(int days) {
        this.expiresAt = LocalDateTime.now().plusDays(days);
    }

    /**
     * 获取生成耗时描述
     * 
     * @return 耗时描述
     */
    public String getGenerationDurationDescription() {
        if (generationDuration == null) {
            return "未知";
        }
        
        long seconds = generationDuration / 1000;
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        } else {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            return hours + "小时" + minutes + "分";
        }
    }

    // ============================================================================
    // 静态工厂方法
    // ============================================================================

    /**
     * 创建日报
     */
    public static ProjectReport createDailyReport(UUID projectId, LocalDateTime date, UUID generatedBy) {
        LocalDateTime start = date.toLocalDate().atStartOfDay();
        LocalDateTime end = start.plusDays(1).minusNanos(1);
        
        return new ProjectReport(projectId, ReportType.DAILY, 
                               "项目日报 - " + date.toLocalDate(), start, end, generatedBy);
    }

    /**
     * 创建周报
     */
    public static ProjectReport createWeeklyReport(UUID projectId, LocalDateTime weekStart, UUID generatedBy) {
        LocalDateTime end = weekStart.plusWeeks(1).minusNanos(1);
        
        return new ProjectReport(projectId, ReportType.WEEKLY, 
                               "项目周报 - " + weekStart.toLocalDate(), weekStart, end, generatedBy);
    }

    /**
     * 创建Sprint报表
     */
    public static ProjectReport createSprintReport(UUID projectId, String sprintName, 
                                                  LocalDateTime sprintStart, LocalDateTime sprintEnd, UUID generatedBy) {
        ProjectReport report = new ProjectReport(projectId, ReportType.SPRINT, 
                                               "Sprint报表 - " + sprintName, sprintStart, sprintEnd, generatedBy);
        return report;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getProjectId() {
        return projectId;
    }

    public void setProjectId(UUID projectId) {
        this.projectId = projectId;
    }

    public ReportType getReportType() {
        return reportType;
    }

    public void setReportType(ReportType reportType) {
        this.reportType = reportType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getPeriodStart() {
        return periodStart;
    }

    public void setPeriodStart(LocalDateTime periodStart) {
        this.periodStart = periodStart;
    }

    public LocalDateTime getPeriodEnd() {
        return periodEnd;
    }

    public void setPeriodEnd(LocalDateTime periodEnd) {
        this.periodEnd = periodEnd;
    }

    public ReportStatus getStatus() {
        return status;
    }

    public void setStatus(ReportStatus status) {
        this.status = status;
    }

    public ReportFormat getFormat() {
        return format;
    }

    public void setFormat(ReportFormat format) {
        this.format = format;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public UUID getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(UUID generatedBy) {
        this.generatedBy = generatedBy;
    }

    public LocalDateTime getGenerationStartedAt() {
        return generationStartedAt;
    }

    public void setGenerationStartedAt(LocalDateTime generationStartedAt) {
        this.generationStartedAt = generationStartedAt;
    }

    public LocalDateTime getGenerationCompletedAt() {
        return generationCompletedAt;
    }

    public void setGenerationCompletedAt(LocalDateTime generationCompletedAt) {
        this.generationCompletedAt = generationCompletedAt;
    }

    public Long getGenerationDuration() {
        return generationDuration;
    }

    public void setGenerationDuration(Long generationDuration) {
        this.generationDuration = generationDuration;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public Boolean getIsAutoGenerated() {
        return isAutoGenerated;
    }

    public void setIsAutoGenerated(Boolean isAutoGenerated) {
        this.isAutoGenerated = isAutoGenerated;
    }

    public LocalDateTime getNextGenerationTime() {
        return nextGenerationTime;
    }

    public void setNextGenerationTime(LocalDateTime nextGenerationTime) {
        this.nextGenerationTime = nextGenerationTime;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public Integer getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ProjectReport that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "ProjectReport{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", reportType=" + reportType +
                ", title='" + title + '\'' +
                ", status=" + status +
                ", periodStart=" + periodStart +
                ", periodEnd=" + periodEnd +
                '}';
    }
}
