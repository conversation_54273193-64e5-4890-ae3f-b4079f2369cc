package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 看板列实体类
 * 
 * 表示看板中的列
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "board_columns", indexes = {
    @Index(name = "idx_board_column_board", columnList = "board_id"),
    @Index(name = "idx_board_column_sort", columnList = "board_id, sort_order"),
    @Index(name = "idx_board_column_status", columnList = "task_status")
})
public class BoardColumn {

    /**
     * 看板列唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 列名称
     */
    @Column(name = "name", nullable = false, length = 50)
    private String name;

    /**
     * 列描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 所属看板
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "board_id", nullable = false)
    private Board board;

    /**
     * 对应的任务状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "task_status")
    private TaskStatus taskStatus;

    /**
     * 排序序号
     */
    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder;

    /**
     * 列颜色（十六进制）
     */
    @Column(name = "color", length = 7)
    private String color = "#6B7280";

    /**
     * 工作进行中限制（WIP限制）
     */
    @Column(name = "wip_limit")
    private Integer wipLimit;

    /**
     * 是否可见
     */
    @Column(name = "is_visible", nullable = false)
    private Boolean isVisible = true;

    /**
     * 列配置（JSON格式）
     */
    @Column(name = "settings", columnDefinition = "TEXT")
    private String settings;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by", nullable = false)
    private UUID createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by", nullable = false)
    private UUID updatedBy;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public BoardColumn() {
    }

    /**
     * 基础构造函数
     * 
     * @param name 列名称
     * @param taskStatus 对应的任务状态
     * @param sortOrder 排序序号
     */
    public BoardColumn(String name, TaskStatus taskStatus, Integer sortOrder) {
        this.name = name;
        this.taskStatus = taskStatus;
        this.sortOrder = sortOrder;
        this.color = taskStatus != null ? taskStatus.getColor() : "#6B7280";
    }

    /**
     * 完整构造函数
     * 
     * @param name 列名称
     * @param taskStatus 对应的任务状态
     * @param sortOrder 排序序号
     * @param color 列颜色
     */
    public BoardColumn(String name, TaskStatus taskStatus, Integer sortOrder, String color) {
        this.name = name;
        this.taskStatus = taskStatus;
        this.sortOrder = sortOrder;
        this.color = color;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 获取当前列的任务数量
     * 
     * @return 任务数量
     */
    public int getTaskCount() {
        if (board == null || board.getProject() == null) {
            return 0;
        }
        
        // 这里需要通过服务层查询，暂时返回0
        // 实际实现中应该通过TaskService查询
        return 0;
    }

    /**
     * 检查是否超过WIP限制
     * 
     * @return true表示超过限制
     */
    public boolean isWipLimitExceeded() {
        if (wipLimit == null || wipLimit <= 0) {
            return false;
        }
        
        return getTaskCount() > wipLimit;
    }

    /**
     * 获取WIP限制使用率
     * 
     * @return 使用率百分比
     */
    public double getWipUtilization() {
        if (wipLimit == null || wipLimit <= 0) {
            return 0.0;
        }
        
        return (double) getTaskCount() / wipLimit * 100;
    }

    /**
     * 检查列是否可以接受新任务
     * 
     * @return true表示可以接受
     */
    public boolean canAcceptTask() {
        return isVisible && !isWipLimitExceeded();
    }

    /**
     * 获取列的显示名称（包含任务数量）
     * 
     * @return 显示名称
     */
    public String getDisplayNameWithCount() {
        int taskCount = getTaskCount();
        StringBuilder displayName = new StringBuilder(name);
        
        if (taskCount > 0) {
            displayName.append(" (").append(taskCount).append(")");
        }
        
        if (wipLimit != null && wipLimit > 0) {
            displayName.append(" [").append(taskCount).append("/").append(wipLimit).append("]");
        }
        
        return displayName.toString();
    }

    /**
     * 获取状态显示名称
     * 
     * @return 状态显示名称
     */
    public String getStatusDisplayName() {
        return taskStatus != null ? taskStatus.getDisplayName() : "无状态";
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Board getBoard() {
        return board;
    }

    public void setBoard(Board board) {
        this.board = board;
    }

    public TaskStatus getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(TaskStatus taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public Integer getWipLimit() {
        return wipLimit;
    }

    public void setWipLimit(Integer wipLimit) {
        this.wipLimit = wipLimit;
    }

    public Boolean getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    public String getSettings() {
        return settings;
    }

    public void setSettings(String settings) {
        this.settings = settings;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public UUID getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BoardColumn that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "BoardColumn{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", taskStatus=" + taskStatus +
                ", sortOrder=" + sortOrder +
                ", color='" + color + '\'' +
                ", wipLimit=" + wipLimit +
                ", isVisible=" + isVisible +
                '}';
    }
}
