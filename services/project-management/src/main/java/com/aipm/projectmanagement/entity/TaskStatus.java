package com.aipm.projectmanagement.entity;

/**
 * 任务状态枚举
 * 
 * 定义任务在生命周期中的各种状态
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
public enum TaskStatus {
    
    /**
     * 待办 - 任务已创建但尚未开始
     */
    TODO("待办", "任务已创建，等待开始执行", "#6B7280"),
    
    /**
     * 进行中 - 任务正在执行
     */
    IN_PROGRESS("进行中", "任务正在积极执行", "#3B82F6"),
    
    /**
     * 待审核 - 任务已完成，等待审核
     */
    IN_REVIEW("待审核", "任务已完成，等待代码审核或质量检查", "#F59E0B"),
    
    /**
     * 测试中 - 任务在测试阶段
     */
    TESTING("测试中", "任务正在进行测试验证", "#8B5CF6"),
    
    /**
     * 已完成 - 任务成功完成
     */
    DONE("已完成", "任务已成功完成所有要求", "#10B981"),
    
    /**
     * 已阻塞 - 任务被阻塞无法继续
     */
    BLOCKED("已阻塞", "任务因依赖或其他问题被阻塞", "#EF4444"),
    
    /**
     * 已取消 - 任务被取消
     */
    CANCELLED("已取消", "任务被永久取消", "#6B7280");

    private final String displayName;
    private final String description;
    private final String color; // 用于UI显示的颜色

    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param description 状态描述
     * @param color 显示颜色
     */
    TaskStatus(String displayName, String description, String color) {
        this.displayName = displayName;
        this.description = description;
        this.color = color;
    }

    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取显示颜色
     * 
     * @return 颜色代码
     */
    public String getColor() {
        return color;
    }

    /**
     * 检查是否为活跃状态
     * 
     * @return true表示任务处于活跃状态
     */
    public boolean isActive() {
        return this == TODO || this == IN_PROGRESS || this == IN_REVIEW || this == TESTING || this == BLOCKED;
    }

    /**
     * 检查是否为已完成状态
     * 
     * @return true表示任务已完成
     */
    public boolean isCompleted() {
        return this == DONE;
    }

    /**
     * 检查是否为已结束状态（完成或取消）
     * 
     * @return true表示任务已结束
     */
    public boolean isFinished() {
        return this == DONE || this == CANCELLED;
    }

    /**
     * 检查是否为阻塞状态
     * 
     * @return true表示任务被阻塞
     */
    public boolean isBlocked() {
        return this == BLOCKED;
    }

    /**
     * 检查是否可以转换到目标状态
     * 
     * @param targetStatus 目标状态
     * @return true表示可以转换
     */
    public boolean canTransitionTo(TaskStatus targetStatus) {
        if (this == targetStatus) {
            return false; // 不能转换到相同状态
        }

        return switch (this) {
            case TODO -> targetStatus == IN_PROGRESS || targetStatus == BLOCKED || targetStatus == CANCELLED;
            case IN_PROGRESS -> targetStatus == IN_REVIEW || targetStatus == TESTING || targetStatus == DONE || 
                               targetStatus == BLOCKED || targetStatus == CANCELLED;
            case IN_REVIEW -> targetStatus == IN_PROGRESS || targetStatus == TESTING || targetStatus == DONE || 
                             targetStatus == BLOCKED;
            case TESTING -> targetStatus == IN_PROGRESS || targetStatus == IN_REVIEW || targetStatus == DONE || 
                           targetStatus == BLOCKED;
            case BLOCKED -> targetStatus == TODO || targetStatus == IN_PROGRESS || targetStatus == CANCELLED;
            case DONE -> false; // 已完成的任务不能转换到其他状态
            case CANCELLED -> false; // 已取消的任务不能转换到其他状态
        };
    }

    /**
     * 获取下一个可能的状态列表
     * 
     * @return 可转换的状态数组
     */
    public TaskStatus[] getNextPossibleStatuses() {
        return switch (this) {
            case TODO -> new TaskStatus[]{IN_PROGRESS, BLOCKED, CANCELLED};
            case IN_PROGRESS -> new TaskStatus[]{IN_REVIEW, TESTING, DONE, BLOCKED, CANCELLED};
            case IN_REVIEW -> new TaskStatus[]{IN_PROGRESS, TESTING, DONE, BLOCKED};
            case TESTING -> new TaskStatus[]{IN_PROGRESS, IN_REVIEW, DONE, BLOCKED};
            case BLOCKED -> new TaskStatus[]{TODO, IN_PROGRESS, CANCELLED};
            case DONE, CANCELLED -> new TaskStatus[]{};
        };
    }

    /**
     * 获取任务状态的优先级（用于排序）
     * 
     * @return 优先级数值，越小优先级越高
     */
    public int getPriority() {
        return switch (this) {
            case BLOCKED -> 1; // 最高优先级，需要立即处理
            case IN_PROGRESS -> 2;
            case IN_REVIEW -> 3;
            case TESTING -> 4;
            case TODO -> 5;
            case DONE -> 6;
            case CANCELLED -> 7; // 最低优先级
        };
    }

    /**
     * 根据显示名称查找状态
     * 
     * @param displayName 显示名称
     * @return 对应的状态，如果未找到返回null
     */
    public static TaskStatus fromDisplayName(String displayName) {
        for (TaskStatus status : values()) {
            if (status.displayName.equals(displayName)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return displayName;
    }
}
