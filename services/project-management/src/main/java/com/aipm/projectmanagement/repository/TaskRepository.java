package com.aipm.projectmanagement.repository;

import com.aipm.projectmanagement.entity.Task;
import com.aipm.projectmanagement.entity.TaskPriority;
import com.aipm.projectmanagement.entity.TaskStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 任务数据访问接口
 * 
 * 提供任务相关的数据库操作方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Repository
public interface TaskRepository extends JpaRepository<Task, UUID> {

    /**
     * 根据项目ID查找任务
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE t.project.id = :projectId AND t.isDeleted = false")
    Page<Task> findByProjectId(@Param("projectId") UUID projectId, Pageable pageable);

    /**
     * 根据分配人查找任务
     * 
     * @param assigneeId 分配人ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE t.assigneeId = :assigneeId AND t.isDeleted = false")
    Page<Task> findByAssigneeId(@Param("assigneeId") UUID assigneeId, Pageable pageable);

    /**
     * 根据任务状态查找任务
     * 
     * @param status 任务状态
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE t.status = :status AND t.isDeleted = false")
    Page<Task> findByStatus(@Param("status") TaskStatus status, Pageable pageable);

    /**
     * 根据父任务查找子任务
     * 
     * @param parentTaskId 父任务ID
     * @return 子任务列表
     */
    @Query("SELECT t FROM Task t WHERE t.parentTask.id = :parentTaskId AND t.isDeleted = false ORDER BY t.sortOrder")
    List<Task> findByParentTaskId(@Param("parentTaskId") UUID parentTaskId);

    /**
     * 查找顶级任务（没有父任务的任务）
     * 
     * @param projectId 项目ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE t.project.id = :projectId AND t.parentTask IS NULL AND t.isDeleted = false")
    Page<Task> findTopLevelTasks(@Param("projectId") UUID projectId, Pageable pageable);

    /**
     * 根据多个条件搜索任务
     * 
     * @param projectId 项目ID
     * @param keyword 关键词（搜索标题和描述）
     * @param status 任务状态
     * @param priority 优先级
     * @param assigneeId 分配人ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.project.id = :projectId AND t.isDeleted = false AND " +
           "(:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           " LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:priority IS NULL OR t.priority = :priority) AND " +
           "(:assigneeId IS NULL OR t.assigneeId = :assigneeId)")
    Page<Task> searchTasks(@Param("projectId") UUID projectId,
                          @Param("keyword") String keyword,
                          @Param("status") TaskStatus status,
                          @Param("priority") TaskPriority priority,
                          @Param("assigneeId") UUID assigneeId,
                          Pageable pageable);

    /**
     * 查找用户的所有任务
     * 
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param status 任务状态
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE " +
           "(t.assigneeId = :userId OR t.reporterId = :userId) AND t.isDeleted = false AND " +
           "(:keyword IS NULL OR LOWER(t.title) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:status IS NULL OR t.status = :status)")
    Page<Task> findUserTasks(@Param("userId") UUID userId,
                            @Param("keyword") String keyword,
                            @Param("status") TaskStatus status,
                            Pageable pageable);

    /**
     * 查找即将到期的任务
     * 
     * @param dueDate 截止日期
     * @param statuses 任务状态列表
     * @return 任务列表
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.dueDate <= :dueDate AND t.status IN :statuses AND t.isDeleted = false")
    List<Task> findTasksDueSoon(@Param("dueDate") LocalDateTime dueDate,
                               @Param("statuses") List<TaskStatus> statuses);

    /**
     * 查找已过期的任务
     * 
     * @param currentDate 当前日期时间
     * @param statuses 任务状态列表
     * @return 任务列表
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.dueDate < :currentDate AND t.status IN :statuses AND t.isDeleted = false")
    List<Task> findOverdueTasks(@Param("currentDate") LocalDateTime currentDate,
                               @Param("statuses") List<TaskStatus> statuses);

    /**
     * 查找被阻塞的任务
     * 
     * @param projectId 项目ID（可选）
     * @return 被阻塞的任务列表
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.status = 'BLOCKED' AND t.isDeleted = false AND " +
           "(:projectId IS NULL OR t.project.id = :projectId)")
    List<Task> findBlockedTasks(@Param("projectId") UUID projectId);

    /**
     * 查找高优先级任务
     * 
     * @param priorities 优先级列表
     * @param assigneeId 分配人ID（可选）
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.priority IN :priorities AND t.isDeleted = false AND " +
           "(:assigneeId IS NULL OR t.assigneeId = :assigneeId) AND " +
           "t.status NOT IN ('DONE', 'CANCELLED')")
    Page<Task> findHighPriorityTasks(@Param("priorities") List<TaskPriority> priorities,
                                    @Param("assigneeId") UUID assigneeId,
                                    Pageable pageable);

    /**
     * 统计项目任务数量
     * 
     * @param projectId 项目ID
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE t.project.id = :projectId AND t.isDeleted = false")
    long countByProjectId(@Param("projectId") UUID projectId);

    /**
     * 统计项目中各状态的任务数量
     * 
     * @param projectId 项目ID
     * @return 统计信息对象数组 [status, count]
     */
    @Query("SELECT t.status, COUNT(t) FROM Task t WHERE " +
           "t.project.id = :projectId AND t.isDeleted = false GROUP BY t.status")
    List<Object[]> getTaskStatisticsByProject(@Param("projectId") UUID projectId);

    /**
     * 统计用户任务数量
     * 
     * @param userId 用户ID
     * @param status 任务状态（可选）
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE " +
           "t.assigneeId = :userId AND t.isDeleted = false AND " +
           "(:status IS NULL OR t.status = :status)")
    long countUserTasks(@Param("userId") UUID userId, @Param("status") TaskStatus status);

    /**
     * 查找最近创建的任务
     * 
     * @param userId 用户ID（可选，查找用户相关的任务）
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.isDeleted = false AND " +
           "(:userId IS NULL OR t.assigneeId = :userId OR t.reporterId = :userId) " +
           "ORDER BY t.createdAt DESC")
    Page<Task> findRecentTasks(@Param("userId") UUID userId, Pageable pageable);

    /**
     * 查找最近更新的任务
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE " +
           "(t.assigneeId = :userId OR t.reporterId = :userId) AND t.isDeleted = false " +
           "ORDER BY t.updatedAt DESC")
    Page<Task> findRecentlyUpdatedTasks(@Param("userId") UUID userId, Pageable pageable);

    /**
     * 根据标签查找任务
     * 
     * @param tag 标签
     * @param projectId 项目ID（可选）
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.tags IS NOT NULL AND t.tags LIKE CONCAT('%', :tag, '%') AND t.isDeleted = false AND " +
           "(:projectId IS NULL OR t.project.id = :projectId)")
    Page<Task> findByTag(@Param("tag") String tag,
                         @Param("projectId") UUID projectId,
                         Pageable pageable);

    /**
     * 查找指定日期范围内创建的任务
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param projectId 项目ID（可选）
     * @return 任务列表
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.createdAt >= :startDate AND t.createdAt <= :endDate AND t.isDeleted = false AND " +
           "(:projectId IS NULL OR t.project.id = :projectId) " +
           "ORDER BY t.createdAt DESC")
    List<Task> findTasksCreatedBetween(@Param("startDate") LocalDateTime startDate,
                                      @Param("endDate") LocalDateTime endDate,
                                      @Param("projectId") UUID projectId);

    /**
     * 查找指定日期范围内完成的任务
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param projectId 项目ID（可选）
     * @return 任务列表
     */
    @Query("SELECT t FROM Task t WHERE " +
           "t.completedDate >= :startDate AND t.completedDate <= :endDate AND " +
           "t.status = 'DONE' AND t.isDeleted = false AND " +
           "(:projectId IS NULL OR t.project.id = :projectId) " +
           "ORDER BY t.completedDate DESC")
    List<Task> findTasksCompletedBetween(@Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate,
                                        @Param("projectId") UUID projectId);

    /**
     * 获取任务层级深度
     * 
     * @param taskId 任务ID
     * @return 层级深度
     */
    @Query(value = "WITH RECURSIVE task_hierarchy AS (" +
                   "  SELECT id, parent_task_id, 0 as depth FROM tasks WHERE id = :taskId " +
                   "  UNION ALL " +
                   "  SELECT t.id, t.parent_task_id, th.depth + 1 " +
                   "  FROM tasks t JOIN task_hierarchy th ON t.id = th.parent_task_id" +
                   ") SELECT MAX(depth) FROM task_hierarchy", nativeQuery = true)
    Integer getTaskDepth(@Param("taskId") UUID taskId);

    /**
     * 查找任务的所有子任务（递归）
     * 
     * @param parentTaskId 父任务ID
     * @return 所有子任务ID列表
     */
    @Query(value = "WITH RECURSIVE task_tree AS (" +
                   "  SELECT id FROM tasks WHERE parent_task_id = :parentTaskId AND is_deleted = false " +
                   "  UNION ALL " +
                   "  SELECT t.id FROM tasks t JOIN task_tree tt ON t.parent_task_id = tt.id " +
                   "  WHERE t.is_deleted = false" +
                   ") SELECT id FROM task_tree", nativeQuery = true)
    List<UUID> findAllSubTaskIds(@Param("parentTaskId") UUID parentTaskId);
}
