package com.aipm.projectmanagement.controller;

import com.aipm.projectmanagement.dto.*;
import com.aipm.projectmanagement.entity.Project;
import com.aipm.projectmanagement.entity.ProjectStatus;
import com.aipm.projectmanagement.entity.TaskPriority;
import com.aipm.projectmanagement.mapper.ProjectMapper;
import com.aipm.projectmanagement.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 项目管理控制器
 * 
 * 提供项目管理相关的REST API接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@RestController
@RequestMapping("/projects")
@Tag(name = "项目管理", description = "项目管理相关API")
@SecurityRequirement(name = "bearerAuth")
public class ProjectController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectController.class);

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectMapper projectMapper;

    /**
     * 创建新项目
     */
    @PostMapping
    @Operation(summary = "创建新项目", description = "创建一个新的项目")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "201", 
            description = "项目创建成功",
            content = @Content(schema = @Schema(implementation = ProjectDto.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "请求参数无效"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "409", 
            description = "项目名称已存在"
        )
    })
    public ResponseEntity<ApiResponse<ProjectDto>> createProject(
            @Valid @RequestBody CreateProjectRequest request,
            Authentication authentication) {
        
        logger.info("创建项目请求: {}", request.getName());

        try {
            UUID creatorId = getCurrentUserId(authentication);
            
            // 验证日期范围
            if (!request.isDateRangeValid()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("项目结束日期不能早于开始日期", "INVALID_DATE_RANGE"));
            }

            // 验证预算
            if (!request.isBudgetValid()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("项目预算不能为负数", "INVALID_BUDGET"));
            }

            Project project;
            
            // 检查是否从模板创建
            if (request.getTemplateId() != null) {
                project = projectService.createProjectFromTemplate(
                    request.getTemplateId(), 
                    request.getName(), 
                    request.getOwnerId() != null ? request.getOwnerId() : creatorId,
                    creatorId
                );
            } else {
                project = projectMapper.fromCreateRequest(request);
                project = projectService.createProject(project, creatorId);
            }

            ProjectDto projectDto = projectMapper.toDto(project);
            
            logger.info("项目创建成功: {}", project.getId());
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("项目创建成功", projectDto));

        } catch (RuntimeException e) {
            logger.error("创建项目失败: {}", e.getMessage());
            
            if (e.getMessage().contains("已存在")) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error(e.getMessage(), "PROJECT_NAME_EXISTS"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "CREATE_PROJECT_FAILED"));
        } catch (Exception e) {
            logger.error("创建项目异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("创建项目失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 根据ID获取项目详情
     */
    @GetMapping("/{projectId}")
    @Operation(summary = "获取项目详情", description = "根据项目ID获取项目详细信息")
    public ResponseEntity<ApiResponse<ProjectDto>> getProject(
            @Parameter(description = "项目ID", required = true) @PathVariable UUID projectId,
            Authentication authentication) {
        
        logger.debug("获取项目详情: {}", projectId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 检查访问权限
            if (!projectService.hasProjectAccess(projectId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限访问此项目", "ACCESS_DENIED"));
            }

            Optional<Project> projectOpt = projectService.getProjectById(projectId);
            if (projectOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("项目不存在", "PROJECT_NOT_FOUND"));
            }

            Project project = projectOpt.get();
            ProjectDto projectDto = projectMapper.toDetailDto(project);
            
            return ResponseEntity.ok(ApiResponse.success("获取项目详情成功", projectDto));

        } catch (Exception e) {
            logger.error("获取项目详情异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取项目详情失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 更新项目信息
     */
    @PutMapping("/{projectId}")
    @Operation(summary = "更新项目信息", description = "更新指定项目的基本信息")
    public ResponseEntity<ApiResponse<ProjectDto>> updateProject(
            @Parameter(description = "项目ID", required = true) @PathVariable UUID projectId,
            @Valid @RequestBody UpdateProjectRequest request,
            Authentication authentication) {
        
        logger.info("更新项目信息: {}", projectId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 检查管理权限
            if (!projectService.hasProjectManagementPermission(projectId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限更新此项目", "ACCESS_DENIED"));
            }

            // 验证日期范围
            if (!request.isDateRangeValid()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("项目结束日期不能早于开始日期", "INVALID_DATE_RANGE"));
            }

            Project project = projectMapper.fromUpdateRequest(request);
            project.setId(projectId);
            
            Project updatedProject = projectService.updateProject(project, userId);
            ProjectDto projectDto = projectMapper.toDto(updatedProject);
            
            logger.info("项目信息更新成功: {}", projectId);
            return ResponseEntity.ok(ApiResponse.success("项目信息更新成功", projectDto));

        } catch (RuntimeException e) {
            logger.error("更新项目信息失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "PROJECT_NOT_FOUND"));
            } else if (e.getMessage().contains("已存在")) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error(e.getMessage(), "PROJECT_NAME_EXISTS"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "UPDATE_PROJECT_FAILED"));
        } catch (Exception e) {
            logger.error("更新项目信息异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新项目信息失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 删除项目
     */
    @DeleteMapping("/{projectId}")
    @Operation(summary = "删除项目", description = "删除指定项目（归档）")
    public ResponseEntity<ApiResponse<Void>> deleteProject(
            @Parameter(description = "项目ID", required = true) @PathVariable UUID projectId,
            Authentication authentication) {
        
        logger.info("删除项目: {}", projectId);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 检查管理权限
            if (!projectService.hasProjectManagementPermission(projectId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限删除此项目", "ACCESS_DENIED"));
            }

            projectService.deleteProject(projectId, userId);
            
            logger.info("项目删除成功: {}", projectId);
            return ResponseEntity.ok(ApiResponse.success("项目删除成功"));

        } catch (RuntimeException e) {
            logger.error("删除项目失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "PROJECT_NOT_FOUND"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "DELETE_PROJECT_FAILED"));
        } catch (Exception e) {
            logger.error("删除项目异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("删除项目失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 分页查询项目列表
     */
    @GetMapping
    @Operation(summary = "分页查询项目列表", description = "分页查询用户可访问的项目列表，支持搜索和过滤")
    public ResponseEntity<ApiResponse<PagedResponse<ProjectDto>>> getProjects(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "updatedAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "项目状态") @RequestParam(required = false) ProjectStatus status,
            @Parameter(description = "项目负责人ID") @RequestParam(required = false) UUID ownerId,
            @Parameter(description = "项目优先级") @RequestParam(required = false) TaskPriority priority,
            @Parameter(description = "是否已归档") @RequestParam(required = false) Boolean isArchived,
            Authentication authentication) {
        
        logger.debug("查询项目列表: page={}, size={}, keyword={}", page, size, keyword);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 创建分页和排序参数
            Sort.Direction direction = "desc".equalsIgnoreCase(sortDir) ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Page<Project> projectPage;
            
            // 如果有搜索条件，使用搜索方法
            if (keyword != null || status != null || ownerId != null || priority != null || isArchived != null) {
                projectPage = projectService.searchProjects(keyword, status, ownerId, priority, isArchived, pageable);
            } else {
                // 否则获取用户可访问的项目
                projectPage = projectService.getUserAccessibleProjects(userId, null, pageable);
            }
            
            // 转换为DTO
            List<ProjectDto> projectDtos = projectMapper.toDtoList(projectPage.getContent());
            PagedResponse<ProjectDto> pagedResponse = PagedResponse.from(projectPage, projectDtos);
            
            return ResponseEntity.ok(ApiResponse.success("获取项目列表成功", pagedResponse));

        } catch (Exception e) {
            logger.error("查询项目列表异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询项目列表失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 更新项目状态
     */
    @PatchMapping("/{projectId}/status")
    @Operation(summary = "更新项目状态", description = "更新指定项目的状态")
    public ResponseEntity<ApiResponse<ProjectDto>> updateProjectStatus(
            @Parameter(description = "项目ID", required = true) @PathVariable UUID projectId,
            @Parameter(description = "新状态", required = true) @RequestParam ProjectStatus status,
            Authentication authentication) {
        
        logger.info("更新项目状态: {} -> {}", projectId, status);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 检查管理权限
            if (!projectService.hasProjectManagementPermission(projectId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限更新此项目状态", "ACCESS_DENIED"));
            }

            Project updatedProject = projectService.updateProjectStatus(projectId, status, userId);
            ProjectDto projectDto = projectMapper.toDto(updatedProject);
            
            logger.info("项目状态更新成功: {} -> {}", projectId, status);
            return ResponseEntity.ok(ApiResponse.success("项目状态更新成功", projectDto));

        } catch (RuntimeException e) {
            logger.error("更新项目状态失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "PROJECT_NOT_FOUND"));
            } else if (e.getMessage().contains("无法从状态")) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), "INVALID_STATUS_TRANSITION"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "UPDATE_STATUS_FAILED"));
        } catch (Exception e) {
            logger.error("更新项目状态异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新项目状态失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 更新项目进度
     */
    @PatchMapping("/{projectId}/progress")
    @Operation(summary = "更新项目进度", description = "更新指定项目的进度百分比")
    public ResponseEntity<ApiResponse<ProjectDto>> updateProjectProgress(
            @Parameter(description = "项目ID", required = true) @PathVariable UUID projectId,
            @Parameter(description = "进度百分比 (0-100)", required = true) @RequestParam Integer progress,
            Authentication authentication) {
        
        logger.info("更新项目进度: {} -> {}%", projectId, progress);

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 检查管理权限
            if (!projectService.hasProjectManagementPermission(projectId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("无权限更新此项目进度", "ACCESS_DENIED"));
            }

            Project updatedProject = projectService.updateProjectProgress(projectId, progress, userId);
            ProjectDto projectDto = projectMapper.toDto(updatedProject);
            
            logger.info("项目进度更新成功: {} -> {}%", projectId, progress);
            return ResponseEntity.ok(ApiResponse.success("项目进度更新成功", projectDto));

        } catch (RuntimeException e) {
            logger.error("更新项目进度失败: {}", e.getMessage());
            
            if (e.getMessage().contains("不存在")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage(), "PROJECT_NOT_FOUND"));
            } else if (e.getMessage().contains("必须在0-100之间")) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage(), "INVALID_PROGRESS"));
            }
            
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage(), "UPDATE_PROGRESS_FAILED"));
        } catch (Exception e) {
            logger.error("更新项目进度异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新项目进度失败", "INTERNAL_ERROR"));
        }
    }

    /**
     * 获取项目统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取项目统计信息", description = "获取项目相关的统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getProjectStatistics(
            Authentication authentication) {
        
        logger.debug("获取项目统计信息");

        try {
            UUID userId = getCurrentUserId(authentication);
            
            // 获取用户项目统计
            Map<String, Object> statistics = projectService.getUserProjectStatistics(userId);
            
            return ResponseEntity.ok(ApiResponse.success("获取项目统计信息成功", statistics));

        } catch (Exception e) {
            logger.error("获取项目统计信息异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取项目统计信息失败", "INTERNAL_ERROR"));
        }
    }

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    /**
     * 获取当前用户ID
     */
    private UUID getCurrentUserId(Authentication authentication) {
        // TODO: 从认证信息中获取用户ID
        // 这里需要根据实际的认证实现来获取用户ID
        return UUID.randomUUID(); // 临时实现
    }
}
