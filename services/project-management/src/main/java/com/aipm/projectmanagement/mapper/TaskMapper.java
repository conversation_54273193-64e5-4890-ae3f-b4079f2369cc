package com.aipm.projectmanagement.mapper;

import com.aipm.projectmanagement.dto.CreateTaskRequest;
import com.aipm.projectmanagement.dto.TaskDto;
import com.aipm.projectmanagement.dto.UpdateTaskRequest;
import com.aipm.projectmanagement.entity.Task;
import org.mapstruct.*;

import java.util.List;

/**
 * 任务映射器
 * 
 * 负责任务实体和DTO之间的转换
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface TaskMapper {

    /**
     * 实体转换为DTO
     * 
     * @param task 任务实体
     * @return 任务DTO
     */
    @Mapping(target = "projectId", source = "project.id")
    @Mapping(target = "project", ignore = true) // 需要单独处理项目信息
    @Mapping(target = "parentTaskId", source = "parentTask.id")
    @Mapping(target = "parentTask", ignore = true) // 需要单独处理父任务信息
    @Mapping(target = "subTasks", ignore = true) // 需要单独处理子任务列表
    @Mapping(target = "assignee", ignore = true) // 需要单独处理用户信息
    @Mapping(target = "reporter", ignore = true) // 需要单独处理用户信息
    @Mapping(target = "isOverdue", expression = "java(task.isOverdue())")
    @Mapping(target = "isBlocked", expression = "java(task.isBlocked())")
    @Mapping(target = "subTaskCount", expression = "java(task.getSubTasks() != null ? task.getSubTasks().size() : 0)")
    @Mapping(target = "subTaskCompletionRate", expression = "java(task.getSubTaskCompletionRate())")
    @Mapping(target = "remainingHours", expression = "java(task.getRemainingHours())")
    @Mapping(target = "hoursCompletionRate", expression = "java(task.getHoursCompletionRate())")
    @Mapping(target = "tags", expression = "java(parseTagsFromJson(task.getTags()))")
    TaskDto toDto(Task task);

    /**
     * 实体转换为详细DTO（包含关联信息）
     * 
     * @param task 任务实体
     * @return 详细任务DTO
     */
    @Mapping(target = "projectId", source = "project.id")
    @Mapping(target = "project", ignore = true) // 需要单独处理项目信息
    @Mapping(target = "parentTaskId", source = "parentTask.id")
    @Mapping(target = "parentTask", ignore = true) // 需要单独处理父任务信息
    @Mapping(target = "subTasks", ignore = true) // 需要单独处理子任务列表
    @Mapping(target = "assignee", ignore = true) // 需要单独处理用户信息
    @Mapping(target = "reporter", ignore = true) // 需要单独处理用户信息
    @Mapping(target = "isOverdue", expression = "java(task.isOverdue())")
    @Mapping(target = "isBlocked", expression = "java(task.isBlocked())")
    @Mapping(target = "subTaskCount", expression = "java(task.getSubTasks() != null ? task.getSubTasks().size() : 0)")
    @Mapping(target = "subTaskCompletionRate", expression = "java(task.getSubTaskCompletionRate())")
    @Mapping(target = "remainingHours", expression = "java(task.getRemainingHours())")
    @Mapping(target = "hoursCompletionRate", expression = "java(task.getHoursCompletionRate())")
    @Mapping(target = "tags", expression = "java(parseTagsFromJson(task.getTags()))")
    TaskDto toDetailDto(Task task);

    /**
     * 实体列表转换为DTO列表
     * 
     * @param tasks 任务实体列表
     * @return 任务DTO列表
     */
    List<TaskDto> toDtoList(List<Task> tasks);

    /**
     * 创建请求转换为实体
     * 
     * @param request 创建任务请求
     * @return 任务实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "project", ignore = true) // 需要通过projectId设置
    @Mapping(target = "parentTask", ignore = true) // 需要通过parentTaskId设置
    @Mapping(target = "subTasks", ignore = true)
    @Mapping(target = "status", constant = "TODO")
    @Mapping(target = "reporterId", ignore = true) // 由服务层设置
    @Mapping(target = "actualHours", ignore = true)
    @Mapping(target = "progress", constant = "0")
    @Mapping(target = "startDate", ignore = true)
    @Mapping(target = "completedDate", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "tags", expression = "java(convertTagsToJson(request.getTags()))")
    Task fromCreateRequest(CreateTaskRequest request);

    /**
     * 更新请求转换为实体
     * 
     * @param request 更新任务请求
     * @return 任务实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "project", ignore = true)
    @Mapping(target = "parentTask", ignore = true)
    @Mapping(target = "subTasks", ignore = true)
    @Mapping(target = "status", ignore = true) // 状态通过专门的接口更新
    @Mapping(target = "assigneeId", ignore = true) // 分配通过专门的接口更新
    @Mapping(target = "reporterId", ignore = true)
    @Mapping(target = "actualHours", ignore = true)
    @Mapping(target = "progress", ignore = true) // 进度通过专门的接口更新
    @Mapping(target = "startDate", ignore = true)
    @Mapping(target = "completedDate", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "sortOrder", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "tags", expression = "java(convertTagsToJson(request.getTags()))")
    Task fromUpdateRequest(UpdateTaskRequest request);

    /**
     * 更新实体（从DTO）
     * 
     * @param dto 任务DTO
     * @param task 目标任务实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "project", ignore = true)
    @Mapping(target = "parentTask", ignore = true)
    @Mapping(target = "subTasks", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "assigneeId", ignore = true)
    @Mapping(target = "reporterId", ignore = true)
    @Mapping(target = "actualHours", ignore = true)
    @Mapping(target = "startDate", ignore = true)
    @Mapping(target = "completedDate", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "sortOrder", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "tags", expression = "java(convertTagsToJson(dto.getTags()))")
    void updateTaskFromDto(TaskDto dto, @MappingTarget Task task);

    // ============================================================================
    // 默认方法实现
    // ============================================================================

    /**
     * 解析JSON格式的标签字符串为列表
     * 
     * @param tagsJson JSON格式的标签字符串
     * @return 标签列表
     */
    default List<String> parseTagsFromJson(String tagsJson) {
        if (tagsJson == null || tagsJson.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 简单的JSON数组解析（实际项目中应该使用Jackson等库）
            String cleaned = tagsJson.trim();
            if (cleaned.startsWith("[") && cleaned.endsWith("]")) {
                cleaned = cleaned.substring(1, cleaned.length() - 1);
                if (cleaned.trim().isEmpty()) {
                    return List.of();
                }
                
                return List.of(cleaned.split(","))
                    .stream()
                    .map(tag -> tag.trim().replaceAll("\"", ""))
                    .filter(tag -> !tag.isEmpty())
                    .toList();
            }
            
            return List.of(cleaned);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将标签列表转换为JSON格式字符串
     * 
     * @param tags 标签列表
     * @return JSON格式的标签字符串
     */
    default String convertTagsToJson(List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return null;
        }
        
        // 简单的JSON数组生成（实际项目中应该使用Jackson等库）
        StringBuilder json = new StringBuilder("[");
        for (int i = 0; i < tags.size(); i++) {
            if (i > 0) {
                json.append(",");
            }
            json.append("\"").append(tags.get(i).replace("\"", "\\\"")).append("\"");
        }
        json.append("]");
        
        return json.toString();
    }
}
