package com.aipm.projectmanagement.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.UUID;

/**
 * 用户摘要信息DTO
 * 
 * 用于在项目和任务中显示用户基本信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "用户摘要信息")
public class UserSummaryDto {

    /**
     * 用户ID
     */
    @Schema(description = "用户唯一标识", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    /**
     * 用户名
     */
    @Schema(description = "用户名", example = "johndoe")
    private String username;

    /**
     * 用户全名
     */
    @Schema(description = "用户全名", example = "John Doe")
    private String fullName;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    /**
     * 头像URL
     */
    @Schema(description = "头像图片URL", example = "https://example.com/avatars/johndoe.jpg")
    private String avatarUrl;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public UserSummaryDto() {
    }

    /**
     * 基础构造函数
     * 
     * @param id 用户ID
     * @param username 用户名
     * @param fullName 用户全名
     * @param email 邮箱地址
     */
    public UserSummaryDto(UUID id, String username, String fullName, String email) {
        this.id = id;
        this.username = username;
        this.fullName = fullName;
        this.email = email;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 获取用户显示名称（优先使用全名，否则使用用户名）
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return (fullName != null && !fullName.trim().isEmpty()) ? fullName : username;
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserSummaryDto that)) return false;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "UserSummaryDto{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
