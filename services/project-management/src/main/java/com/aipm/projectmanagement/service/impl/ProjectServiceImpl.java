package com.aipm.projectmanagement.service.impl;

import com.aipm.projectmanagement.entity.Project;
import com.aipm.projectmanagement.entity.ProjectMember;
import com.aipm.projectmanagement.entity.ProjectStatus;
import com.aipm.projectmanagement.entity.TaskPriority;
import com.aipm.projectmanagement.repository.ProjectMemberRepository;
import com.aipm.projectmanagement.repository.ProjectRepository;
import com.aipm.projectmanagement.repository.TaskRepository;
import com.aipm.projectmanagement.service.ProjectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

/**
 * 项目服务实现类
 * 
 * 实现项目管理相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Service
@Transactional
public class ProjectServiceImpl implements ProjectService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectServiceImpl.class);

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectMemberRepository projectMemberRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Override
    public Project createProject(Project project, UUID creatorId) {
        logger.info("创建新项目: {}, 创建者: {}", project.getName(), creatorId);

        // 验证项目名称唯一性
        if (!isProjectNameAvailable(project.getName(), null)) {
            throw new RuntimeException("项目名称已存在: " + project.getName());
        }

        // 设置创建者信息
        project.setCreatedBy(creatorId);
        project.setUpdatedBy(creatorId);

        // 如果没有设置负责人，默认为创建者
        if (project.getOwnerId() == null) {
            project.setOwnerId(creatorId);
        }

        // 保存项目
        Project savedProject = projectRepository.save(project);

        // 自动添加项目负责人为项目经理
        ProjectMember ownerMember = new ProjectMember(savedProject, savedProject.getOwnerId(), 
                                                     ProjectMember.Role.PROJECT_MANAGER);
        ownerMember.setAddedBy(creatorId);
        projectMemberRepository.save(ownerMember);

        logger.info("项目创建成功: {}", savedProject.getId());
        return savedProject;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Project> getProjectById(UUID projectId) {
        return projectRepository.findById(projectId);
    }

    @Override
    public Project updateProject(Project project, UUID updaterId) {
        logger.info("更新项目: {}, 更新者: {}", project.getId(), updaterId);

        // 验证项目存在
        Project existingProject = projectRepository.findById(project.getId())
                .orElseThrow(() -> new RuntimeException("项目不存在: " + project.getId()));

        // 验证项目名称唯一性（排除当前项目）
        if (!isProjectNameAvailable(project.getName(), project.getId())) {
            throw new RuntimeException("项目名称已存在: " + project.getName());
        }

        // 更新项目信息
        existingProject.setName(project.getName());
        existingProject.setDescription(project.getDescription());
        existingProject.setStartDate(project.getStartDate());
        existingProject.setEndDate(project.getEndDate());
        existingProject.setBudget(project.getBudget());
        existingProject.setPriority(project.getPriority());
        existingProject.setTags(project.getTags());
        existingProject.setSettings(project.getSettings());
        existingProject.setUpdatedBy(updaterId);

        Project updatedProject = projectRepository.save(existingProject);
        logger.info("项目更新成功: {}", updatedProject.getId());
        return updatedProject;
    }

    @Override
    public void deleteProject(UUID projectId, UUID deleterId) {
        logger.info("删除项目: {}, 删除者: {}", projectId, deleterId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));

        // 软删除：设置为归档状态
        project.setIsArchived(true);
        project.setUpdatedBy(deleterId);
        projectRepository.save(project);

        logger.info("项目删除成功: {}", projectId);
    }

    @Override
    public void archiveProject(UUID projectId, UUID archiverId) {
        logger.info("归档项目: {}, 归档者: {}", projectId, archiverId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));

        project.setIsArchived(true);
        project.setStatus(ProjectStatus.ARCHIVED);
        project.setUpdatedBy(archiverId);
        projectRepository.save(project);

        logger.info("项目归档成功: {}", projectId);
    }

    @Override
    public void restoreProject(UUID projectId, UUID restorerId) {
        logger.info("恢复项目: {}, 恢复者: {}", projectId, restorerId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));

        project.setIsArchived(false);
        // 恢复到进行中状态
        project.setStatus(ProjectStatus.IN_PROGRESS);
        project.setUpdatedBy(restorerId);
        projectRepository.save(project);

        logger.info("项目恢复成功: {}", projectId);
    }

    @Override
    public Project updateProjectStatus(UUID projectId, ProjectStatus status, UUID updaterId) {
        logger.info("更新项目状态: {}, 新状态: {}, 更新者: {}", projectId, status, updaterId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));

        // 验证状态转换是否合法
        if (!project.getStatus().canTransitionTo(status)) {
            throw new RuntimeException(String.format("无法从状态 %s 转换到 %s", 
                    project.getStatus().getDisplayName(), status.getDisplayName()));
        }

        project.setStatus(status);
        project.setUpdatedBy(updaterId);

        // 如果项目完成，自动设置进度为100%
        if (status == ProjectStatus.COMPLETED) {
            project.setProgress(100);
        }

        Project updatedProject = projectRepository.save(project);
        logger.info("项目状态更新成功: {} -> {}", projectId, status);
        return updatedProject;
    }

    @Override
    public Project updateProjectProgress(UUID projectId, Integer progress, UUID updaterId) {
        logger.info("更新项目进度: {}, 进度: {}%, 更新者: {}", projectId, progress, updaterId);

        if (progress < 0 || progress > 100) {
            throw new RuntimeException("项目进度必须在0-100之间");
        }

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));

        project.setProgress(progress);
        project.setUpdatedBy(updaterId);

        // 如果进度达到100%且状态为进行中，自动设置为已完成
        if (progress == 100 && project.getStatus() == ProjectStatus.IN_PROGRESS) {
            project.setStatus(ProjectStatus.COMPLETED);
        }

        Project updatedProject = projectRepository.save(project);
        logger.info("项目进度更新成功: {}", projectId);
        return updatedProject;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Project> getUserAccessibleProjects(UUID userId, String keyword, Pageable pageable) {
        return projectRepository.findAccessibleProjects(userId, keyword, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Project> searchProjects(String keyword, ProjectStatus status, UUID ownerId, 
                                       TaskPriority priority, Boolean isArchived, Pageable pageable) {
        return projectRepository.searchProjects(keyword, status, ownerId, priority, isArchived, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Project> getProjectsByOwner(UUID ownerId, Pageable pageable) {
        return projectRepository.findByOwnerId(ownerId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Project> getProjectsByUser(UUID userId, Pageable pageable) {
        return projectRepository.findProjectsByUserId(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Project> getRecentlyUpdatedProjects(UUID userId, Pageable pageable) {
        return projectRepository.findRecentlyUpdatedProjects(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> getProjectsDueSoon(int days) {
        LocalDate dueDate = LocalDate.now().plusDays(days);
        List<ProjectStatus> activeStatuses = Arrays.asList(ProjectStatus.PLANNING, ProjectStatus.IN_PROGRESS);
        return projectRepository.findProjectsDueSoon(dueDate, activeStatuses);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Project> getOverdueProjects() {
        LocalDate currentDate = LocalDate.now();
        List<ProjectStatus> activeStatuses = Arrays.asList(ProjectStatus.PLANNING, ProjectStatus.IN_PROGRESS);
        return projectRepository.findOverdueProjects(currentDate, activeStatuses);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Project> getProjectTemplates(Pageable pageable) {
        return projectRepository.findByIsTemplateTrue(pageable);
    }

    @Override
    public Project createProjectFromTemplate(UUID templateId, String projectName, UUID ownerId, UUID creatorId) {
        logger.info("从模板创建项目: 模板ID={}, 项目名称={}, 负责人={}", templateId, projectName, ownerId);

        Project template = projectRepository.findById(templateId)
                .orElseThrow(() -> new RuntimeException("项目模板不存在: " + templateId));

        if (!Boolean.TRUE.equals(template.getIsTemplate())) {
            throw new RuntimeException("指定的项目不是模板: " + templateId);
        }

        // 创建新项目（复制模板信息）
        Project newProject = new Project();
        newProject.setName(projectName);
        newProject.setDescription(template.getDescription());
        newProject.setOwnerId(ownerId);
        newProject.setPriority(template.getPriority());
        newProject.setTags(template.getTags());
        newProject.setSettings(template.getSettings());
        newProject.setStatus(ProjectStatus.PLANNING);

        return createProject(newProject, creatorId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isProjectNameAvailable(String name, UUID excludeId) {
        return !projectRepository.existsByNameIgnoreCase(name, excludeId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasProjectAccess(UUID projectId, UUID userId) {
        // 检查是否为项目负责人
        Optional<Project> project = projectRepository.findById(projectId);
        if (project.isPresent() && project.get().getOwnerId().equals(userId)) {
            return true;
        }

        // 检查是否为项目成员
        return projectMemberRepository.isProjectMember(projectId, userId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasProjectManagementPermission(UUID projectId, UUID userId) {
        // 检查是否为项目负责人
        Optional<Project> project = projectRepository.findById(projectId);
        if (project.isPresent() && project.get().getOwnerId().equals(userId)) {
            return true;
        }

        // 检查是否有管理权限
        return projectMemberRepository.hasManagementPermission(projectId, userId);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getProjectStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取项目状态统计
        List<Object[]> statusStats = projectRepository.getProjectStatistics();
        Map<String, Long> statusCounts = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusCounts.put(((ProjectStatus) stat[0]).name(), (Long) stat[1]);
        }
        statistics.put("statusCounts", statusCounts);
        
        // 获取总体统计
        statistics.put("totalProjects", projectRepository.count());
        statistics.put("activeProjects", projectRepository.countActiveProjects());
        
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserProjectStatistics(UUID userId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取用户项目状态统计
        List<Object[]> statusStats = projectRepository.getUserProjectStatistics(userId);
        Map<String, Long> statusCounts = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusCounts.put(((ProjectStatus) stat[0]).name(), (Long) stat[1]);
        }
        statistics.put("statusCounts", statusCounts);
        
        // 获取用户项目总数
        statistics.put("totalProjects", projectRepository.countProjectsByUserId(userId));
        
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public double calculateProjectCompletionRate(UUID projectId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));
        
        return project.getTaskCompletionRate();
    }

    @Override
    public Project autoUpdateProjectProgress(UUID projectId) {
        logger.info("自动更新项目进度: {}", projectId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));

        // 计算基于任务的完成率
        double completionRate = calculateProjectCompletionRate(projectId);
        int newProgress = (int) Math.round(completionRate);

        // 更新项目进度
        project.setProgress(newProgress);

        // 如果进度达到100%且状态为进行中，自动设置为已完成
        if (newProgress == 100 && project.getStatus() == ProjectStatus.IN_PROGRESS) {
            project.setStatus(ProjectStatus.COMPLETED);
        }

        Project updatedProject = projectRepository.save(project);
        logger.info("项目进度自动更新完成: {} -> {}%", projectId, newProgress);
        return updatedProject;
    }

    @Override
    public byte[] exportProjectData(UUID projectId, String format) {
        // TODO: 实现项目数据导出功能
        throw new UnsupportedOperationException("项目数据导出功能待实现");
    }

    @Override
    public int batchUpdateProjectStatus(List<UUID> projectIds, ProjectStatus status, UUID updaterId) {
        logger.info("批量更新项目状态: 项目数量={}, 新状态={}", projectIds.size(), status);

        int updatedCount = 0;
        for (UUID projectId : projectIds) {
            try {
                updateProjectStatus(projectId, status, updaterId);
                updatedCount++;
            } catch (Exception e) {
                logger.warn("更新项目状态失败: {}, 错误: {}", projectId, e.getMessage());
            }
        }

        logger.info("批量更新完成: 成功更新 {} 个项目", updatedCount);
        return updatedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public int getProjectHealthScore(UUID projectId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));

        int score = 100;

        // 根据项目状态扣分
        if (project.getStatus() == ProjectStatus.ON_HOLD) {
            score -= 20;
        } else if (project.getStatus() == ProjectStatus.CANCELLED) {
            score = 0;
        }

        // 根据是否过期扣分
        if (project.isOverdue()) {
            score -= 30;
        }

        // 根据进度扣分
        if (project.getProgress() < 50) {
            score -= 10;
        }

        return Math.max(0, score);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getProjectRiskAssessment(UUID projectId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在: " + projectId));

        Map<String, Object> assessment = new HashMap<>();
        List<String> risks = new ArrayList<>();

        // 检查时间风险
        if (project.isOverdue()) {
            risks.add("项目已过期");
        } else if (project.getRemainingDays() != null && project.getRemainingDays() < 7) {
            risks.add("项目即将到期");
        }

        // 检查进度风险
        if (project.getProgress() < 30 && project.getStatus() == ProjectStatus.IN_PROGRESS) {
            risks.add("项目进度缓慢");
        }

        // 检查状态风险
        if (project.getStatus() == ProjectStatus.ON_HOLD) {
            risks.add("项目处于暂停状态");
        }

        assessment.put("risks", risks);
        assessment.put("riskLevel", risks.isEmpty() ? "LOW" : risks.size() > 2 ? "HIGH" : "MEDIUM");
        assessment.put("healthScore", getProjectHealthScore(projectId));

        return assessment;
    }
}
