package com.aipm.projectmanagement.service.impl;

import com.aipm.projectmanagement.entity.ActivityLog;
import com.aipm.projectmanagement.entity.ActivityLog.ActionType;
import com.aipm.projectmanagement.entity.ActivityLog.TargetType;
import com.aipm.projectmanagement.repository.ActivityLogRepository;
import com.aipm.projectmanagement.service.ActivityLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 活动日志服务实现类
 * 
 * 实现活动日志管理相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Service
@Transactional
public class ActivityLogServiceImpl implements ActivityLogService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityLogServiceImpl.class);

    @Autowired
    private ActivityLogRepository activityLogRepository;

    // 线程本地变量存储请求上下文信息
    private static final ThreadLocal<String> REQUEST_IP = new ThreadLocal<>();
    private static final ThreadLocal<String> REQUEST_USER_AGENT = new ThreadLocal<>();

    @Override
    public ActivityLog logActivity(UUID projectId, UUID userId, ActionType actionType, String description) {
        logger.debug("记录活动日志: 项目={}, 用户={}, 类型={}", projectId, userId, actionType);

        ActivityLog activityLog = new ActivityLog(projectId, userId, actionType, description);
        setRequestInfo(activityLog);

        ActivityLog savedLog = activityLogRepository.save(activityLog);
        logger.debug("活动日志记录成功: {}", savedLog.getId());
        return savedLog;
    }

    @Override
    public ActivityLog logActivity(UUID projectId, UUID userId, ActionType actionType, 
                                  TargetType targetType, UUID targetId, String targetName, String description) {
        logger.debug("记录活动日志: 项目={}, 用户={}, 类型={}, 目标={}:{}", 
                    projectId, userId, actionType, targetType, targetId);

        ActivityLog activityLog = new ActivityLog(projectId, userId, actionType, 
                                                 targetType, targetId, targetName, description);
        setRequestInfo(activityLog);

        ActivityLog savedLog = activityLogRepository.save(activityLog);
        logger.debug("活动日志记录成功: {}", savedLog.getId());
        return savedLog;
    }

    @Override
    public ActivityLog logActivityWithChanges(UUID projectId, UUID userId, ActionType actionType, 
                                             TargetType targetType, UUID targetId, String targetName, 
                                             String description, String oldValue, String newValue) {
        logger.debug("记录变更活动日志: 项目={}, 用户={}, 类型={}, 目标={}:{}", 
                    projectId, userId, actionType, targetType, targetId);

        ActivityLog activityLog = new ActivityLog(projectId, userId, actionType, 
                                                 targetType, targetId, targetName, description);
        activityLog.setChangeValues(oldValue, newValue);
        setRequestInfo(activityLog);

        ActivityLog savedLog = activityLogRepository.save(activityLog);
        logger.debug("变更活动日志记录成功: {}", savedLog.getId());
        return savedLog;
    }

    @Override
    public ActivityLog logProjectActivity(UUID projectId, UUID userId, ActionType actionType, 
                                         String projectName, String description) {
        return ActivityLog.createProjectLog(projectId, userId, actionType, projectName, description);
    }

    @Override
    public ActivityLog logTaskActivity(UUID projectId, UUID userId, ActionType actionType, 
                                      UUID taskId, String taskTitle, String description) {
        return logActivity(projectId, userId, actionType, TargetType.TASK, taskId, taskTitle, description);
    }

    @Override
    public ActivityLog logSprintActivity(UUID projectId, UUID userId, ActionType actionType, 
                                        UUID sprintId, String sprintName, String description) {
        return logActivity(projectId, userId, actionType, TargetType.SPRINT, sprintId, sprintName, description);
    }

    @Override
    public ActivityLog logCommentActivity(UUID projectId, UUID userId, ActionType actionType, 
                                         UUID commentId, String description) {
        return logActivity(projectId, userId, actionType, TargetType.COMMENT, commentId, null, description);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ActivityLog> getProjectActivities(UUID projectId, Pageable pageable) {
        return activityLogRepository.findByProjectIdOrderByCreatedAtDesc(projectId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ActivityLog> getUserActivities(UUID userId, Pageable pageable) {
        return activityLogRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ActivityLog> getTargetActivities(TargetType targetType, UUID targetId, Pageable pageable) {
        return activityLogRepository.findByTargetTypeAndTargetIdOrderByCreatedAtDesc(targetType, targetId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ActivityLog> getRecentActivities(UUID projectId, int limit) {
        return activityLogRepository.findRecentActivities(projectId, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ActivityLog> getUserRecentActivities(UUID userId, int limit) {
        return activityLogRepository.findUserRecentActivities(userId, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ActivityLog> searchActivities(UUID projectId, String keyword, ActionType actionType, 
                                             UUID userId, LocalDateTime startDate, LocalDateTime endDate, 
                                             Pageable pageable) {
        return activityLogRepository.searchActivities(projectId, keyword, actionType, userId, 
                                                     startDate, endDate, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ActivityLog> getTargetActivityHistory(TargetType targetType, UUID targetId) {
        return activityLogRepository.getTargetActivityHistory(targetType, targetId);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getActivityTypeStatistics(UUID projectId) {
        Map<String, Object> statistics = new HashMap<>();
        
        List<Object[]> typeStats = activityLogRepository.getActivityTypeStatistics(projectId);
        Map<String, Long> typeCounts = new HashMap<>();
        
        for (Object[] stat : typeStats) {
            typeCounts.put(((ActionType) stat[0]).name(), (Long) stat[1]);
        }
        
        statistics.put("actionTypeCounts", typeCounts);
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserActivityStatistics(UUID projectId, int days) {
        Map<String, Object> statistics = new HashMap<>();
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> userStats = activityLogRepository.getUserActivityStatistics(projectId, startDate);
        
        Map<String, Long> userCounts = new HashMap<>();
        for (Object[] stat : userStats) {
            userCounts.put(stat[0].toString(), (Long) stat[1]);
        }
        
        statistics.put("userActivityCounts", userCounts);
        statistics.put("period", days + " days");
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getActivityTimeline(UUID projectId, int days) {
        Map<String, Object> timeline = new HashMap<>();
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> timelineData = activityLogRepository.getActivityTimeline(projectId, startDate);
        
        List<Map<String, Object>> dailyActivities = new ArrayList<>();
        for (Object[] data : timelineData) {
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", data[0]);
            dayData.put("count", data[1]);
            dailyActivities.add(dayData);
        }
        
        timeline.put("dailyActivities", dailyActivities);
        timeline.put("period", days + " days");
        return timeline;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getMostActiveUsers(UUID projectId, int days, int limit) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> activeUsers = activityLogRepository.getMostActiveUsers(projectId, startDate, limit);
        
        List<Map<String, Object>> result = new ArrayList<>();
        for (Object[] user : activeUsers) {
            Map<String, Object> userData = new HashMap<>();
            userData.put("userId", user[0]);
            userData.put("activityCount", user[1]);
            result.add(userData);
        }
        
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getActivityHeatmapData(UUID projectId, int days) {
        Map<String, Object> heatmap = new HashMap<>();
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> heatmapData = activityLogRepository.getActivityHeatmapData(projectId, startDate);
        
        List<Map<String, Object>> heatmapPoints = new ArrayList<>();
        for (Object[] data : heatmapData) {
            Map<String, Object> point = new HashMap<>();
            point.put("hour", data[0]);
            point.put("dayOfWeek", data[1]);
            point.put("count", data[2]);
            heatmapPoints.add(point);
        }
        
        heatmap.put("heatmapData", heatmapPoints);
        heatmap.put("period", days + " days");
        return heatmap;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getActivitySummary(UUID projectId, int days) {
        Map<String, Object> summary = new HashMap<>();
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        Object[] summaryData = activityLogRepository.getActivitySummary(projectId, startDate);
        
        if (summaryData != null && summaryData.length >= 3) {
            summary.put("totalActivities", summaryData[0]);
            summary.put("activeUsers", summaryData[1]);
            summary.put("affectedTargets", summaryData[2]);
        }
        
        summary.put("period", days + " days");
        return summary;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserActivityInProject(UUID projectId, UUID userId, int days) {
        Map<String, Object> userActivity = new HashMap<>();
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> activityData = activityLogRepository.getUserActivityInProject(projectId, userId, startDate);
        
        Map<String, Long> actionCounts = new HashMap<>();
        for (Object[] data : activityData) {
            actionCounts.put(((ActionType) data[0]).name(), (Long) data[1]);
        }
        
        userActivity.put("actionCounts", actionCounts);
        userActivity.put("period", days + " days");
        return userActivity;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getActivityTrend(UUID projectId, int days) {
        Map<String, Object> trend = new HashMap<>();
        
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> trendData = activityLogRepository.getActivityTrend(projectId, startDate);
        
        List<Map<String, Object>> dailyTrend = new ArrayList<>();
        for (Object[] data : trendData) {
            Map<String, Object> dayTrend = new HashMap<>();
            dayTrend.put("date", data[0]);
            dayTrend.put("totalCount", data[1]);
            dayTrend.put("userCount", data[2]);
            dayTrend.put("taskCount", data[3]);
            dailyTrend.add(dayTrend);
        }
        
        trend.put("dailyTrend", dailyTrend);
        trend.put("period", days + " days");
        return trend;
    }

    @Override
    @Transactional(readOnly = true)
    public long countActivitiesByDateRange(UUID projectId, LocalDateTime startDate, LocalDateTime endDate) {
        return activityLogRepository.countActivitiesByDateRange(projectId, startDate, endDate);
    }

    @Override
    public int cleanupOldActivities(int days) {
        LocalDateTime beforeDate = LocalDateTime.now().minusDays(days);
        int cleanedCount = activityLogRepository.cleanupOldActivities(beforeDate);
        logger.info("清理了 {} 条旧活动日志", cleanedCount);
        return cleanedCount;
    }

    @Override
    public String exportActivities(UUID projectId, LocalDateTime startDate, LocalDateTime endDate) {
        // TODO: 实现活动日志导出
        return "";
    }

    @Override
    public Map<String, Object> generateActivityReport(UUID projectId, LocalDateTime startDate, LocalDateTime endDate) {
        // TODO: 实现活动报告生成
        return new HashMap<>();
    }

    @Override
    public void setRequestContext(String ipAddress, String userAgent) {
        REQUEST_IP.set(ipAddress);
        REQUEST_USER_AGENT.set(userAgent);
    }

    @Override
    public int batchLogActivities(List<ActivityLog> activityLogs) {
        List<ActivityLog> savedLogs = activityLogRepository.saveAll(activityLogs);
        return savedLogs.size();
    }

    @Override
    @Async
    public void logActivityAsync(UUID projectId, UUID userId, ActionType actionType, String description) {
        try {
            logActivity(projectId, userId, actionType, description);
        } catch (Exception e) {
            logger.error("异步记录活动日志失败: {}", e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canViewActivities(UUID projectId, UUID userId) {
        // TODO: 实现活动日志查看权限检查
        return true;
    }

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    /**
     * 设置请求上下文信息
     */
    private void setRequestInfo(ActivityLog activityLog) {
        String ipAddress = REQUEST_IP.get();
        String userAgent = REQUEST_USER_AGENT.get();
        
        if (ipAddress != null || userAgent != null) {
            activityLog.setRequestInfo(ipAddress, userAgent);
        }
    }

    /**
     * 清理线程本地变量
     */
    public static void clearRequestContext() {
        REQUEST_IP.remove();
        REQUEST_USER_AGENT.remove();
    }
}
