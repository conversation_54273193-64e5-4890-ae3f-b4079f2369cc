package com.aipm.projectmanagement.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 看板实体类
 * 
 * 表示敏捷开发中的看板
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Entity
@Table(name = "boards", indexes = {
    @Index(name = "idx_board_project", columnList = "project_id"),
    @Index(name = "idx_board_type", columnList = "board_type"),
    @Index(name = "idx_board_created", columnList = "created_at")
})
public class Board {

    /**
     * 看板类型枚举
     */
    public enum BoardType {
        KANBAN("看板", "持续流动的看板"),
        SCRUM("Scrum看板", "Sprint驱动的Scrum看板"),
        CUSTOM("自定义看板", "用户自定义的看板");

        private final String displayName;
        private final String description;

        BoardType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 看板唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    /**
     * 看板名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * 看板描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 所属项目
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    /**
     * 看板类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "board_type", nullable = false)
    private BoardType boardType = BoardType.KANBAN;

    /**
     * 是否为默认看板
     */
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    /**
     * 看板列列表
     */
    @OneToMany(mappedBy = "board", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("sortOrder ASC")
    private List<BoardColumn> columns = new ArrayList<>();

    /**
     * 看板配置（JSON格式）
     */
    @Column(name = "settings", columnDefinition = "TEXT")
    private String settings;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by", nullable = false)
    private UUID createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by", nullable = false)
    private UUID updatedBy;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public Board() {
    }

    /**
     * 基础构造函数
     * 
     * @param name 看板名称
     * @param project 所属项目
     * @param boardType 看板类型
     */
    public Board(String name, Project project, BoardType boardType) {
        this.name = name;
        this.project = project;
        this.boardType = boardType;
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 添加看板列
     */
    public void addColumn(BoardColumn column) {
        if (column != null && !columns.contains(column)) {
            columns.add(column);
            column.setBoard(this);
            
            // 设置排序序号
            if (column.getSortOrder() == null) {
                column.setSortOrder(columns.size());
            }
        }
    }

    /**
     * 移除看板列
     */
    public void removeColumn(BoardColumn column) {
        if (column != null && columns.contains(column)) {
            columns.remove(column);
            column.setBoard(null);
            
            // 重新排序
            reorderColumns();
        }
    }

    /**
     * 重新排序看板列
     */
    private void reorderColumns() {
        for (int i = 0; i < columns.size(); i++) {
            columns.get(i).setSortOrder(i + 1);
        }
    }

    /**
     * 获取指定状态的看板列
     */
    public BoardColumn getColumnByStatus(TaskStatus status) {
        return columns.stream()
            .filter(column -> status.equals(column.getTaskStatus()))
            .findFirst()
            .orElse(null);
    }

    /**
     * 初始化默认看板列
     */
    public void initializeDefaultColumns() {
        if (columns.isEmpty()) {
            switch (boardType) {
                case KANBAN:
                    initializeKanbanColumns();
                    break;
                case SCRUM:
                    initializeScrumColumns();
                    break;
                case CUSTOM:
                    initializeCustomColumns();
                    break;
            }
        }
    }

    /**
     * 初始化看板列
     */
    private void initializeKanbanColumns() {
        addColumn(new BoardColumn("待办", TaskStatus.TODO, 1));
        addColumn(new BoardColumn("进行中", TaskStatus.IN_PROGRESS, 2));
        addColumn(new BoardColumn("测试中", TaskStatus.TESTING, 3));
        addColumn(new BoardColumn("已完成", TaskStatus.DONE, 4));
    }

    /**
     * 初始化Scrum看板列
     */
    private void initializeScrumColumns() {
        addColumn(new BoardColumn("产品待办", TaskStatus.TODO, 1));
        addColumn(new BoardColumn("Sprint待办", TaskStatus.TODO, 2));
        addColumn(new BoardColumn("进行中", TaskStatus.IN_PROGRESS, 3));
        addColumn(new BoardColumn("代码审查", TaskStatus.IN_REVIEW, 4));
        addColumn(new BoardColumn("测试中", TaskStatus.TESTING, 5));
        addColumn(new BoardColumn("已完成", TaskStatus.DONE, 6));
    }

    /**
     * 初始化自定义看板列
     */
    private void initializeCustomColumns() {
        addColumn(new BoardColumn("新建", TaskStatus.TODO, 1));
        addColumn(new BoardColumn("进行中", TaskStatus.IN_PROGRESS, 2));
        addColumn(new BoardColumn("已完成", TaskStatus.DONE, 3));
    }

    /**
     * 获取看板统计信息
     */
    public BoardStatistics getStatistics() {
        int totalTasks = 0;
        int completedTasks = 0;
        
        for (BoardColumn column : columns) {
            int columnTaskCount = column.getTaskCount();
            totalTasks += columnTaskCount;
            
            if (column.getTaskStatus() != null && column.getTaskStatus().isCompleted()) {
                completedTasks += columnTaskCount;
            }
        }
        
        double completionRate = totalTasks > 0 ? (double) completedTasks / totalTasks * 100 : 0.0;
        
        return new BoardStatistics(totalTasks, completedTasks, completionRate);
    }

    /**
     * 看板统计信息内部类
     */
    public static class BoardStatistics {
        private final int totalTasks;
        private final int completedTasks;
        private final double completionRate;

        public BoardStatistics(int totalTasks, int completedTasks, double completionRate) {
            this.totalTasks = totalTasks;
            this.completedTasks = completedTasks;
            this.completionRate = completionRate;
        }

        public int getTotalTasks() {
            return totalTasks;
        }

        public int getCompletedTasks() {
            return completedTasks;
        }

        public double getCompletionRate() {
            return completionRate;
        }
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public BoardType getBoardType() {
        return boardType;
    }

    public void setBoardType(BoardType boardType) {
        this.boardType = boardType;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public List<BoardColumn> getColumns() {
        return columns;
    }

    public void setColumns(List<BoardColumn> columns) {
        this.columns = columns;
    }

    public String getSettings() {
        return settings;
    }

    public void setSettings(String settings) {
        this.settings = settings;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public UUID getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Board board)) return false;
        return id != null && id.equals(board.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "Board{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", boardType=" + boardType +
                ", isDefault=" + isDefault +
                ", columnsCount=" + (columns != null ? columns.size() : 0) +
                '}';
    }
}
