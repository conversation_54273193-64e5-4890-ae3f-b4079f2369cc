package com.aipm.projectmanagement.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页响应DTO
 * 
 * 用于包装分页查询结果
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-15
 */
@Schema(description = "分页响应")
public class PagedResponse<T> {

    /**
     * 数据列表
     */
    @Schema(description = "数据列表")
    private List<T> content;

    /**
     * 当前页码（从0开始）
     */
    @Schema(description = "当前页码（从0开始）", example = "0")
    private int page;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "20")
    private int size;

    /**
     * 总记录数
     */
    @Schema(description = "总记录数", example = "150")
    private long totalElements;

    /**
     * 总页数
     */
    @Schema(description = "总页数", example = "8")
    private int totalPages;

    /**
     * 是否为第一页
     */
    @Schema(description = "是否为第一页", example = "true")
    private boolean first;

    /**
     * 是否为最后一页
     */
    @Schema(description = "是否为最后一页", example = "false")
    private boolean last;

    /**
     * 是否有下一页
     */
    @Schema(description = "是否有下一页", example = "true")
    private boolean hasNext;

    /**
     * 是否有上一页
     */
    @Schema(description = "是否有上一页", example = "false")
    private boolean hasPrevious;

    /**
     * 当前页记录数
     */
    @Schema(description = "当前页记录数", example = "20")
    private int numberOfElements;

    /**
     * 是否为空页
     */
    @Schema(description = "是否为空页", example = "false")
    private boolean empty;

    // ============================================================================
    // 构造函数
    // ============================================================================

    /**
     * 默认构造函数
     */
    public PagedResponse() {
    }

    /**
     * 基础构造函数
     * 
     * @param content 数据列表
     * @param page 当前页码
     * @param size 每页大小
     * @param totalElements 总记录数
     * @param totalPages 总页数
     * @param first 是否为第一页
     * @param last 是否为最后一页
     */
    public PagedResponse(List<T> content, int page, int size, long totalElements, 
                        int totalPages, boolean first, boolean last) {
        this.content = content;
        this.page = page;
        this.size = size;
        this.totalElements = totalElements;
        this.totalPages = totalPages;
        this.first = first;
        this.last = last;
        this.hasNext = !last;
        this.hasPrevious = !first;
        this.numberOfElements = content != null ? content.size() : 0;
        this.empty = numberOfElements == 0;
    }

    /**
     * 从Spring Data Page对象创建
     * 
     * @param page Spring Data Page对象
     * @param <T> 数据类型
     * @return 分页响应对象
     */
    public static <T> PagedResponse<T> from(Page<T> page) {
        return new PagedResponse<>(
            page.getContent(),
            page.getNumber(),
            page.getSize(),
            page.getTotalElements(),
            page.getTotalPages(),
            page.isFirst(),
            page.isLast()
        );
    }

    /**
     * 从Spring Data Page对象创建（转换数据类型）
     * 
     * @param page Spring Data Page对象
     * @param content 转换后的数据列表
     * @param <T> 目标数据类型
     * @param <S> 源数据类型
     * @return 分页响应对象
     */
    public static <T, S> PagedResponse<T> from(Page<S> page, List<T> content) {
        return new PagedResponse<>(
            content,
            page.getNumber(),
            page.getSize(),
            page.getTotalElements(),
            page.getTotalPages(),
            page.isFirst(),
            page.isLast()
        );
    }

    // ============================================================================
    // 业务方法
    // ============================================================================

    /**
     * 获取下一页页码
     * 
     * @return 下一页页码，如果没有下一页返回null
     */
    public Integer getNextPage() {
        return hasNext ? page + 1 : null;
    }

    /**
     * 获取上一页页码
     * 
     * @return 上一页页码，如果没有上一页返回null
     */
    public Integer getPreviousPage() {
        return hasPrevious ? page - 1 : null;
    }

    /**
     * 获取分页信息摘要
     * 
     * @return 分页信息字符串
     */
    public String getPageInfo() {
        long start = (long) page * size + 1;
        long end = Math.min(start + numberOfElements - 1, totalElements);
        return String.format("第 %d-%d 条，共 %d 条记录", start, end, totalElements);
    }

    // ============================================================================
    // Getter 和 Setter 方法
    // ============================================================================

    public List<T> getContent() {
        return content;
    }

    public void setContent(List<T> content) {
        this.content = content;
        this.numberOfElements = content != null ? content.size() : 0;
        this.empty = numberOfElements == 0;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public long getTotalElements() {
        return totalElements;
    }

    public void setTotalElements(long totalElements) {
        this.totalElements = totalElements;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public boolean isFirst() {
        return first;
    }

    public void setFirst(boolean first) {
        this.first = first;
        this.hasPrevious = !first;
    }

    public boolean isLast() {
        return last;
    }

    public void setLast(boolean last) {
        this.last = last;
        this.hasNext = !last;
    }

    public boolean isHasNext() {
        return hasNext;
    }

    public void setHasNext(boolean hasNext) {
        this.hasNext = hasNext;
    }

    public boolean isHasPrevious() {
        return hasPrevious;
    }

    public void setHasPrevious(boolean hasPrevious) {
        this.hasPrevious = hasPrevious;
    }

    public int getNumberOfElements() {
        return numberOfElements;
    }

    public void setNumberOfElements(int numberOfElements) {
        this.numberOfElements = numberOfElements;
    }

    public boolean isEmpty() {
        return empty;
    }

    public void setEmpty(boolean empty) {
        this.empty = empty;
    }

    // ============================================================================
    // Object 方法重写
    // ============================================================================

    @Override
    public String toString() {
        return "PagedResponse{" +
                "page=" + page +
                ", size=" + size +
                ", totalElements=" + totalElements +
                ", totalPages=" + totalPages +
                ", numberOfElements=" + numberOfElements +
                ", first=" + first +
                ", last=" + last +
                '}';
    }
}
