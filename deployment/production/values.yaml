# AI项目管理平台生产环境Helm Values配置
# 
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-28

# ==================== 全局配置 ====================

global:
  environment: production
  imageRegistry: ghcr.io/your-org/ai-pm
  imageTag: "v1.0.0"
  imagePullPolicy: IfNotPresent
  
  # 域名配置
  domain: ai-pm.your-domain.com
  
  # 存储类
  storageClass: fast-ssd
  
  # 资源配额
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 512Mi

# ==================== 前端服务配置 ====================

frontend:
  enabled: true
  name: frontend
  
  image:
    repository: frontend
    tag: ""  # 使用global.imageTag
    pullPolicy: ""  # 使用global.imagePullPolicy
  
  replicaCount: 3
  
  service:
    type: ClusterIP
    port: 80
    targetPort: 80
  
  resources:
    requests:
      cpu: 50m
      memory: 64Mi
    limits:
      cpu: 200m
      memory: 256Mi
  
  # 健康检查
  livenessProbe:
    httpGet:
      path: /
      port: 80
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /
      port: 80
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
  
  # 自动扩缩容
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# ==================== 后端服务配置 ====================

backend:
  enabled: true
  name: backend
  
  image:
    repository: backend
    tag: ""
    pullPolicy: ""
  
  replicaCount: 3
  
  service:
    type: ClusterIP
    port: 8000
    targetPort: 8000
  
  resources:
    requests:
      cpu: 200m
      memory: 512Mi
    limits:
      cpu: 1000m
      memory: 2Gi
  
  # 环境变量
  env:
    - name: ENVIRONMENT
      value: "production"
    - name: DEBUG
      value: "false"
    - name: LOG_LEVEL
      value: "INFO"
    - name: DATABASE_URL
      valueFrom:
        secretKeyRef:
          name: database-secret
          key: url
    - name: REDIS_URL
      valueFrom:
        secretKeyRef:
          name: redis-secret
          key: url
    - name: JWT_SECRET_KEY
      valueFrom:
        secretKeyRef:
          name: app-secret
          key: jwt-secret
  
  # 健康检查
  livenessProbe:
    httpGet:
      path: /health
      port: 8000
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /ready
      port: 8000
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  # 自动扩缩容
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# ==================== AI服务配置 ====================

aiService:
  enabled: true
  name: ai-service
  
  image:
    repository: ai-service
    tag: ""
    pullPolicy: ""
  
  replicaCount: 2
  
  service:
    type: ClusterIP
    port: 8001
    targetPort: 8001
  
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 2000m
      memory: 4Gi
  
  # GPU支持（如果需要）
  nodeSelector:
    accelerator: nvidia-tesla-k80
  
  tolerations:
    - key: nvidia.com/gpu
      operator: Exists
      effect: NoSchedule
  
  # 持久化存储（模型文件）
  persistence:
    enabled: true
    storageClass: fast-ssd
    size: 50Gi
    mountPath: /app/models
  
  # 自动扩缩容
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 85

# ==================== 通知服务配置 ====================

notificationService:
  enabled: true
  name: notification-service
  
  image:
    repository: notification-service
    tag: ""
    pullPolicy: ""
  
  replicaCount: 2
  
  service:
    type: ClusterIP
    port: 8002
    targetPort: 8002
  
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 1Gi
  
  # 自动扩缩容
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70

# ==================== 集成服务配置 ====================

integrationService:
  enabled: true
  name: integration-service
  
  image:
    repository: integration-service
    tag: ""
    pullPolicy: ""
  
  replicaCount: 2
  
  service:
    type: ClusterIP
    port: 8003
    targetPort: 8003
  
  resources:
    requests:
      cpu: 200m
      memory: 512Mi
    limits:
      cpu: 1000m
      memory: 2Gi

# ==================== Ingress配置 ====================

ingress:
  enabled: true
  className: nginx
  
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  
  hosts:
    - host: ai-pm.your-domain.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: frontend
            port: 80
        - path: /api
          pathType: Prefix
          service:
            name: backend
            port: 8000
        - path: /ai
          pathType: Prefix
          service:
            name: ai-service
            port: 8001
  
  tls:
    - secretName: ai-pm-tls
      hosts:
        - ai-pm.your-domain.com

# ==================== 数据库配置 ====================

postgresql:
  enabled: false  # 使用外部PostgreSQL
  
  # 外部数据库配置
  external:
    host: postgres-primary.ai-pm-prod.svc.cluster.local
    port: 5432
    database: ai_pm_prod
    username: ai_pm_user
    existingSecret: database-secret
    existingSecretPasswordKey: password

redis:
  enabled: false  # 使用外部Redis
  
  # 外部Redis配置
  external:
    host: redis-master.ai-pm-prod.svc.cluster.local
    port: 6379
    existingSecret: redis-secret
    existingSecretPasswordKey: password

# ==================== 监控配置 ====================

monitoring:
  enabled: true
  
  # Prometheus监控
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
      interval: 30s
      scrapeTimeout: 10s
  
  # Grafana仪表板
  grafana:
    enabled: true
    dashboards:
      enabled: true
  
  # 告警规则
  alerting:
    enabled: true
    rules:
      - name: high-cpu-usage
        threshold: 80
      - name: high-memory-usage
        threshold: 85
      - name: high-error-rate
        threshold: 5

# ==================== 安全配置 ====================

security:
  # Pod安全策略
  podSecurityPolicy:
    enabled: true
  
  # 网络策略
  networkPolicy:
    enabled: true
    ingress:
      enabled: true
    egress:
      enabled: true
  
  # 服务网格
  serviceMesh:
    enabled: false  # 可选启用Istio

# ==================== 备份配置 ====================

backup:
  enabled: true
  
  # 数据库备份
  database:
    enabled: true
    schedule: "0 2 * * *"  # 每天凌晨2点
    retention: "30d"
    storage:
      type: s3
      bucket: ai-pm-backups
      region: us-west-2
  
  # 文件备份
  files:
    enabled: true
    schedule: "0 3 * * *"  # 每天凌晨3点
    retention: "7d"

# ==================== 日志配置 ====================

logging:
  enabled: true
  
  # 日志级别
  level: INFO
  
  # 日志格式
  format: json
  
  # 日志聚合
  aggregation:
    enabled: true
    type: elasticsearch
    endpoint: https://elasticsearch.logging.svc.cluster.local:9200

# ==================== 配置映射 ====================

configMaps:
  app-config:
    data:
      CORS_ORIGINS: "https://ai-pm.your-domain.com"
      MAX_UPLOAD_SIZE: "50MB"
      SESSION_TIMEOUT: "3600"
      RATE_LIMIT_REQUESTS: "1000"
      RATE_LIMIT_WINDOW: "3600"
