# AI项目管理平台Prometheus告警规则
# 
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-28

apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: ai-pm-alerts
  namespace: ai-pm-prod
  labels:
    app: ai-pm
    component: monitoring
spec:
  groups:
  # ==================== 应用性能告警 ====================
  - name: ai-pm.application.performance
    interval: 30s
    rules:
    - alert: HighAPIResponseTime
      expr: histogram_quantile(0.95, rate(http_server_requests_seconds_bucket{job="ai-pm-backend"}[5m])) > 0.2
      for: 2m
      labels:
        severity: warning
        service: "{{ $labels.service }}"
        team: platform
      annotations:
        summary: "API响应时间过高"
        description: "服务 {{ $labels.service }} 的95%分位响应时间超过200ms，当前值: {{ $value | humanizeDuration }}"
        runbook_url: "https://docs.ai-pm.com/runbooks/high-response-time"

    - alert: HighAPIErrorRate
      expr: rate(http_server_requests_seconds_count{status=~"5..",job="ai-pm-backend"}[5m]) / rate(http_server_requests_seconds_count{job="ai-pm-backend"}[5m]) > 0.05
      for: 1m
      labels:
        severity: critical
        service: "{{ $labels.service }}"
        team: platform
      annotations:
        summary: "API错误率过高"
        description: "服务 {{ $labels.service }} 的错误率超过5%，当前值: {{ $value | humanizePercentage }}"
        runbook_url: "https://docs.ai-pm.com/runbooks/high-error-rate"

    - alert: LowAPIThroughput
      expr: rate(http_server_requests_seconds_count{job="ai-pm-backend"}[5m]) < 1
      for: 5m
      labels:
        severity: warning
        service: "{{ $labels.service }}"
        team: platform
      annotations:
        summary: "API吞吐量过低"
        description: "服务 {{ $labels.service }} 的请求量异常低，当前值: {{ $value }} req/s"

  # ==================== 基础设施告警 ====================
  - name: ai-pm.infrastructure
    interval: 30s
    rules:
    - alert: PodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total{namespace="ai-pm-prod"}[15m]) > 0
      for: 5m
      labels:
        severity: critical
        pod: "{{ $labels.pod }}"
        container: "{{ $labels.container }}"
        team: platform
      annotations:
        summary: "Pod频繁重启"
        description: "Pod {{ $labels.pod }} 中的容器 {{ $labels.container }} 在过去15分钟内重启了 {{ $value }} 次"

    - alert: PodNotReady
      expr: kube_pod_status_ready{condition="false",namespace="ai-pm-prod"} == 1
      for: 5m
      labels:
        severity: warning
        pod: "{{ $labels.pod }}"
        team: platform
      annotations:
        summary: "Pod未就绪"
        description: "Pod {{ $labels.pod }} 已经未就绪超过5分钟"

    - alert: DeploymentReplicasMismatch
      expr: kube_deployment_spec_replicas{namespace="ai-pm-prod"} != kube_deployment_status_replicas_available{namespace="ai-pm-prod"}
      for: 10m
      labels:
        severity: warning
        deployment: "{{ $labels.deployment }}"
        team: platform
      annotations:
        summary: "部署副本数不匹配"
        description: "部署 {{ $labels.deployment }} 的期望副本数与可用副本数不匹配"

  # ==================== 资源使用告警 ====================
  - name: ai-pm.resources
    interval: 30s
    rules:
    - alert: HighCPUUsage
      expr: rate(container_cpu_usage_seconds_total{namespace="ai-pm-prod",container!="POD"}[5m]) > 0.8
      for: 5m
      labels:
        severity: warning
        pod: "{{ $labels.pod }}"
        container: "{{ $labels.container }}"
        team: platform
      annotations:
        summary: "CPU使用率过高"
        description: "容器 {{ $labels.container }} 在Pod {{ $labels.pod }} 中的CPU使用率超过80%"

    - alert: HighMemoryUsage
      expr: container_memory_usage_bytes{namespace="ai-pm-prod",container!="POD"} / container_spec_memory_limit_bytes > 0.9
      for: 5m
      labels:
        severity: warning
        pod: "{{ $labels.pod }}"
        container: "{{ $labels.container }}"
        team: platform
      annotations:
        summary: "内存使用率过高"
        description: "容器 {{ $labels.container }} 在Pod {{ $labels.pod }} 中的内存使用率超过90%"

    - alert: PVCStorageRunningFull
      expr: kubelet_volume_stats_available_bytes{namespace="ai-pm-prod"} / kubelet_volume_stats_capacity_bytes < 0.1
      for: 2m
      labels:
        severity: critical
        persistentvolumeclaim: "{{ $labels.persistentvolumeclaim }}"
        team: platform
      annotations:
        summary: "存储空间不足"
        description: "PVC {{ $labels.persistentvolumeclaim }} 的可用空间少于10%"

  # ==================== 数据库告警 ====================
  - name: ai-pm.database
    interval: 30s
    rules:
    - alert: PostgreSQLDown
      expr: pg_up{namespace="ai-pm-prod"} == 0
      for: 1m
      labels:
        severity: critical
        instance: "{{ $labels.instance }}"
        team: platform
      annotations:
        summary: "PostgreSQL数据库宕机"
        description: "PostgreSQL实例 {{ $labels.instance }} 无法连接"

    - alert: PostgreSQLHighConnections
      expr: pg_stat_database_numbackends{namespace="ai-pm-prod"} > 80
      for: 2m
      labels:
        severity: warning
        datname: "{{ $labels.datname }}"
        team: platform
      annotations:
        summary: "PostgreSQL连接数过高"
        description: "数据库 {{ $labels.datname }} 的连接数超过80，当前值: {{ $value }}"

    - alert: PostgreSQLSlowQueries
      expr: rate(pg_stat_database_tup_returned{namespace="ai-pm-prod"}[5m]) / rate(pg_stat_database_tup_fetched{namespace="ai-pm-prod"}[5m]) < 0.1
      for: 3m
      labels:
        severity: warning
        datname: "{{ $labels.datname }}"
        team: platform
      annotations:
        summary: "PostgreSQL查询效率低"
        description: "数据库 {{ $labels.datname }} 的查询效率过低，可能存在慢查询"

  # ==================== Redis告警 ====================
  - name: ai-pm.redis
    interval: 30s
    rules:
    - alert: RedisDown
      expr: redis_up{namespace="ai-pm-prod"} == 0
      for: 1m
      labels:
        severity: critical
        instance: "{{ $labels.instance }}"
        team: platform
      annotations:
        summary: "Redis服务宕机"
        description: "Redis实例 {{ $labels.instance }} 无法连接"

    - alert: RedisHighMemoryUsage
      expr: redis_memory_used_bytes{namespace="ai-pm-prod"} / redis_memory_max_bytes > 0.8
      for: 2m
      labels:
        severity: warning
        instance: "{{ $labels.instance }}"
        team: platform
      annotations:
        summary: "Redis内存使用率过高"
        description: "Redis实例 {{ $labels.instance }} 的内存使用率超过80%"

    - alert: RedisHighConnections
      expr: redis_connected_clients{namespace="ai-pm-prod"} > 100
      for: 2m
      labels:
        severity: warning
        instance: "{{ $labels.instance }}"
        team: platform
      annotations:
        summary: "Redis连接数过高"
        description: "Redis实例 {{ $labels.instance }} 的连接数超过100，当前值: {{ $value }}"

  # ==================== 业务指标告警 ====================
  - name: ai-pm.business
    interval: 60s
    rules:
    - alert: LowUserActivity
      expr: rate(ai_pm_user_actions_total[1h]) < 10
      for: 30m
      labels:
        severity: warning
        team: product
      annotations:
        summary: "用户活跃度低"
        description: "过去1小时用户操作次数低于10次，当前值: {{ $value }}"

    - alert: HighTaskFailureRate
      expr: rate(ai_pm_task_failures_total[5m]) / rate(ai_pm_task_total[5m]) > 0.1
      for: 5m
      labels:
        severity: warning
        team: product
      annotations:
        summary: "任务失败率过高"
        description: "任务失败率超过10%，当前值: {{ $value | humanizePercentage }}"

    - alert: AIModelPredictionLatency
      expr: histogram_quantile(0.95, rate(ai_pm_model_prediction_duration_seconds_bucket[5m])) > 5
      for: 3m
      labels:
        severity: warning
        model: "{{ $labels.model }}"
        team: ai
      annotations:
        summary: "AI模型预测延迟过高"
        description: "AI模型 {{ $labels.model }} 的95%分位预测延迟超过5秒"

  # ==================== 安全告警 ====================
  - name: ai-pm.security
    interval: 30s
    rules:
    - alert: HighFailedLoginAttempts
      expr: rate(ai_pm_failed_login_attempts_total[5m]) > 10
      for: 2m
      labels:
        severity: warning
        team: security
      annotations:
        summary: "登录失败次数过多"
        description: "过去5分钟登录失败次数超过10次，可能存在暴力破解攻击"

    - alert: UnauthorizedAPIAccess
      expr: rate(http_server_requests_seconds_count{status="401",job="ai-pm-backend"}[5m]) > 5
      for: 2m
      labels:
        severity: warning
        team: security
      annotations:
        summary: "未授权API访问过多"
        description: "过去5分钟401错误次数超过5次，可能存在未授权访问尝试"

    - alert: SuspiciousUserBehavior
      expr: rate(ai_pm_suspicious_activities_total[10m]) > 0
      for: 1m
      labels:
        severity: critical
        team: security
      annotations:
        summary: "检测到可疑用户行为"
        description: "系统检测到可疑用户行为，需要立即调查"
