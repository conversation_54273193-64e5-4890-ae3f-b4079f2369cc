#!/bin/bash

# AI项目管理平台生产环境部署脚本
# 
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-28

set -euo pipefail

# ==================== 配置变量 ====================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
NAMESPACE="ai-pm-prod"
RELEASE_NAME="ai-pm"
CHART_PATH="${PROJECT_ROOT}/helm/ai-pm"
VALUES_FILE="${SCRIPT_DIR}/values.yaml"
BACKUP_DIR="${PROJECT_ROOT}/backups"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ==================== 工具函数 ====================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "检查部署前置条件..."
    
    # 检查必要的工具
    local tools=("kubectl" "helm" "docker")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查Helm版本
    local helm_version=$(helm version --short | grep -oE 'v[0-9]+\.[0-9]+\.[0-9]+')
    log_info "Helm版本: $helm_version"
    
    log_success "前置条件检查通过"
}

create_namespace() {
    log_info "创建命名空间: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "命名空间 $NAMESPACE 已存在"
    else
        kubectl create namespace "$NAMESPACE"
        log_success "命名空间 $NAMESPACE 创建成功"
    fi
    
    # 设置默认命名空间
    kubectl config set-context --current --namespace="$NAMESPACE"
}

create_secrets() {
    log_info "创建密钥配置..."
    
    # 检查是否存在环境变量文件
    local env_file="${SCRIPT_DIR}/.env.prod"
    if [[ ! -f "$env_file" ]]; then
        log_error "生产环境配置文件不存在: $env_file"
        log_info "请创建 $env_file 文件并配置必要的环境变量"
        exit 1
    fi
    
    # 加载环境变量
    source "$env_file"
    
    # 创建数据库密钥
    kubectl create secret generic database-secret \
        --from-literal=url="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}" \
        --from-literal=username="${DB_USER}" \
        --from-literal=password="${DB_PASSWORD}" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建Redis密钥
    kubectl create secret generic redis-secret \
        --from-literal=url="redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/0" \
        --from-literal=password="${REDIS_PASSWORD}" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建应用密钥
    kubectl create secret generic app-secret \
        --from-literal=jwt-secret="${JWT_SECRET}" \
        --from-literal=secret-key="${SECRET_KEY}" \
        --from-literal=encryption-key="${ENCRYPTION_KEY}" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建第三方服务密钥
    kubectl create secret generic external-secret \
        --from-literal=smtp-password="${SMTP_PASSWORD}" \
        --from-literal=github-token="${GITHUB_TOKEN}" \
        --from-literal=openai-api-key="${OPENAI_API_KEY}" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "密钥配置创建完成"
}

backup_current_deployment() {
    log_info "备份当前部署..."
    
    # 创建备份目录
    local backup_timestamp=$(date +"%Y%m%d_%H%M%S")
    local current_backup_dir="${BACKUP_DIR}/deployment_${backup_timestamp}"
    mkdir -p "$current_backup_dir"
    
    # 备份Helm release
    if helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME"; then
        helm get all "$RELEASE_NAME" -n "$NAMESPACE" > "${current_backup_dir}/helm_release.yaml"
        log_info "Helm release备份完成"
    fi
    
    # 备份数据库
    if kubectl get pods -n "$NAMESPACE" -l app=postgres | grep -q Running; then
        log_info "开始数据库备份..."
        kubectl exec -n "$NAMESPACE" deployment/postgres -- pg_dump -U "$DB_USER" "$DB_NAME" > "${current_backup_dir}/database_backup.sql"
        log_success "数据库备份完成"
    fi
    
    # 备份配置文件
    kubectl get configmaps -n "$NAMESPACE" -o yaml > "${current_backup_dir}/configmaps.yaml"
    kubectl get secrets -n "$NAMESPACE" -o yaml > "${current_backup_dir}/secrets.yaml"
    
    log_success "备份完成: $current_backup_dir"
}

deploy_infrastructure() {
    log_info "部署基础设施组件..."
    
    # 部署PostgreSQL
    helm upgrade --install postgres \
        oci://registry-1.docker.io/bitnamicharts/postgresql \
        --namespace="$NAMESPACE" \
        --set auth.postgresPassword="$DB_PASSWORD" \
        --set auth.database="$DB_NAME" \
        --set primary.persistence.size=50Gi \
        --set primary.resources.requests.memory=1Gi \
        --set primary.resources.requests.cpu=500m \
        --wait --timeout=10m
    
    # 部署Redis
    helm upgrade --install redis \
        oci://registry-1.docker.io/bitnamicharts/redis \
        --namespace="$NAMESPACE" \
        --set auth.password="$REDIS_PASSWORD" \
        --set master.persistence.size=10Gi \
        --set master.resources.requests.memory=512Mi \
        --set master.resources.requests.cpu=250m \
        --wait --timeout=5m
    
    log_success "基础设施部署完成"
}

deploy_application() {
    log_info "部署应用服务..."
    
    # 检查Helm chart是否存在
    if [[ ! -d "$CHART_PATH" ]]; then
        log_error "Helm chart不存在: $CHART_PATH"
        exit 1
    fi
    
    # 检查values文件是否存在
    if [[ ! -f "$VALUES_FILE" ]]; then
        log_error "Values文件不存在: $VALUES_FILE"
        exit 1
    fi
    
    # 部署应用
    helm upgrade --install "$RELEASE_NAME" "$CHART_PATH" \
        --namespace="$NAMESPACE" \
        --values="$VALUES_FILE" \
        --set image.tag="${IMAGE_TAG:-latest}" \
        --set global.environment="production" \
        --wait --timeout=15m
    
    log_success "应用部署完成"
}

run_database_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待数据库就绪
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgresql -n "$NAMESPACE" --timeout=300s
    
    # 运行迁移任务
    kubectl create job --from=cronjob/database-migration migration-$(date +%s) -n "$NAMESPACE"
    
    # 等待迁移完成
    kubectl wait --for=condition=complete job -l job-name=migration -n "$NAMESPACE" --timeout=300s
    
    log_success "数据库迁移完成"
}

verify_deployment() {
    log_info "验证部署状态..."
    
    # 检查所有Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n "$NAMESPACE" -o wide
    
    # 等待所有Pod就绪
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance="$RELEASE_NAME" -n "$NAMESPACE" --timeout=300s
    
    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get services -n "$NAMESPACE"
    
    # 检查Ingress状态
    log_info "检查Ingress状态..."
    kubectl get ingress -n "$NAMESPACE"
    
    # 运行健康检查
    log_info "运行健康检查..."
    local health_check_pod=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=backend -o jsonpath='{.items[0].metadata.name}')
    
    if kubectl exec -n "$NAMESPACE" "$health_check_pod" -- curl -f http://localhost:8000/health; then
        log_success "健康检查通过"
    else
        log_error "健康检查失败"
        return 1
    fi
    
    log_success "部署验证完成"
}

setup_monitoring() {
    log_info "设置监控和告警..."
    
    # 部署Prometheus
    helm upgrade --install prometheus \
        prometheus-community/kube-prometheus-stack \
        --namespace="$NAMESPACE" \
        --set prometheus.prometheusSpec.retention=30d \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi \
        --wait --timeout=10m
    
    # 部署Grafana仪表板
    kubectl apply -f "${SCRIPT_DIR}/monitoring/" -n "$NAMESPACE"
    
    log_success "监控设置完成"
}

cleanup_old_resources() {
    log_info "清理旧资源..."
    
    # 清理旧的ReplicaSets
    kubectl delete replicaset -n "$NAMESPACE" --field-selector='status.replicas=0'
    
    # 清理已完成的Jobs
    kubectl delete job -n "$NAMESPACE" --field-selector='status.conditions[0].type=Complete'
    
    # 清理旧的镜像
    docker system prune -f
    
    log_success "资源清理完成"
}

# ==================== 主要部署流程 ====================

main() {
    log_info "开始AI项目管理平台生产环境部署"
    log_info "部署时间: $(date)"
    log_info "命名空间: $NAMESPACE"
    log_info "Release名称: $RELEASE_NAME"
    
    # 执行部署步骤
    check_prerequisites
    create_namespace
    create_secrets
    backup_current_deployment
    deploy_infrastructure
    deploy_application
    run_database_migrations
    verify_deployment
    setup_monitoring
    cleanup_old_resources
    
    log_success "🎉 AI项目管理平台部署完成!"
    log_info "访问地址: https://ai-pm.your-domain.com"
    log_info "监控地址: https://monitoring.your-domain.com"
    
    # 显示部署信息
    echo ""
    echo "部署信息:"
    echo "=========================================="
    kubectl get all -n "$NAMESPACE"
}

# ==================== 错误处理 ====================

trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# ==================== 脚本入口 ====================

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
