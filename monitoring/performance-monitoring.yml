# AI项目管理平台性能监控配置
# 包含Prometheus告警规则、Grafana仪表板配置和性能指标定义
#
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-28

# ============================================================================
# Prometheus告警规则
# ============================================================================

groups:
  # 应用性能告警
  - name: application_performance
    rules:
      # API响应时间告警
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m])) > 0.2
        for: 2m
        labels:
          severity: warning
          service: "{{ $labels.service }}"
        annotations:
          summary: "API响应时间过高"
          description: "服务 {{ $labels.service }} 的95%分位响应时间超过200ms，当前值: {{ $value }}s"

      # API错误率告警
      - alert: HighAPIErrorRate
        expr: rate(http_server_requests_seconds_count{status=~"5.."}[5m]) / rate(http_server_requests_seconds_count[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.service }}"
        annotations:
          summary: "API错误率过高"
          description: "服务 {{ $labels.service }} 的错误率超过5%，当前值: {{ $value | humanizePercentage }}"

      # 数据库连接池告警
      - alert: DatabaseConnectionPoolHigh
        expr: hikaricp_connections_active / hikaricp_connections_max > 0.8
        for: 2m
        labels:
          severity: warning
          service: "{{ $labels.service }}"
        annotations:
          summary: "数据库连接池使用率过高"
          description: "服务 {{ $labels.service }} 的数据库连接池使用率超过80%，当前值: {{ $value | humanizePercentage }}"

      # JVM内存使用告警
      - alert: HighJVMMemoryUsage
        expr: jvm_memory_used_bytes{area="heap"} / jvm_memory_max_bytes{area="heap"} > 0.85
        for: 3m
        labels:
          severity: warning
          service: "{{ $labels.service }}"
        annotations:
          summary: "JVM堆内存使用率过高"
          description: "服务 {{ $labels.service }} 的JVM堆内存使用率超过85%，当前值: {{ $value | humanizePercentage }}"

      # GC时间告警
      - alert: HighGCTime
        expr: rate(jvm_gc_collection_seconds_sum[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: "{{ $labels.service }}"
        annotations:
          summary: "GC时间过长"
          description: "服务 {{ $labels.service }} 的GC时间占比超过10%，当前值: {{ $value | humanizePercentage }}"

  # 数据库性能告警
  - name: database_performance
    rules:
      # 数据库连接数告警
      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends > 80
        for: 2m
        labels:
          severity: warning
          database: "{{ $labels.datname }}"
        annotations:
          summary: "数据库连接数过高"
          description: "数据库 {{ $labels.datname }} 的连接数超过80，当前值: {{ $value }}"

      # 慢查询告警
      - alert: SlowDatabaseQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 3m
        labels:
          severity: warning
          database: "{{ $labels.datname }}"
        annotations:
          summary: "数据库查询效率低"
          description: "数据库 {{ $labels.datname }} 的查询效率过低，可能存在慢查询"

      # 数据库锁等待告警
      - alert: DatabaseLockWaits
        expr: pg_locks_count{mode="ExclusiveLock"} > 10
        for: 1m
        labels:
          severity: critical
          database: "{{ $labels.datname }}"
        annotations:
          summary: "数据库锁等待过多"
          description: "数据库 {{ $labels.datname }} 存在过多的排他锁，当前值: {{ $value }}"

  # Redis性能告警
  - name: redis_performance
    rules:
      # Redis内存使用告警
      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.8
        for: 2m
        labels:
          severity: warning
          instance: "{{ $labels.instance }}"
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis实例 {{ $labels.instance }} 的内存使用率超过80%，当前值: {{ $value | humanizePercentage }}"

      # Redis连接数告警
      - alert: HighRedisConnections
        expr: redis_connected_clients > 100
        for: 2m
        labels:
          severity: warning
          instance: "{{ $labels.instance }}"
        annotations:
          summary: "Redis连接数过高"
          description: "Redis实例 {{ $labels.instance }} 的连接数超过100，当前值: {{ $value }}"

      # Redis命令执行时间告警
      - alert: SlowRedisCommands
        expr: redis_command_duration_seconds_total{command!="info"} > 0.01
        for: 1m
        labels:
          severity: warning
          instance: "{{ $labels.instance }}"
          command: "{{ $labels.command }}"
        annotations:
          summary: "Redis命令执行缓慢"
          description: "Redis命令 {{ $labels.command }} 执行时间超过10ms，当前值: {{ $value }}s"

  # 系统资源告警
  - name: system_resources
    rules:
      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 3m
        labels:
          severity: warning
          instance: "{{ $labels.instance }}"
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} 的CPU使用率超过80%，当前值: {{ $value }}%"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.85
        for: 3m
        labels:
          severity: warning
          instance: "{{ $labels.instance }}"
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 的内存使用率超过85%，当前值: {{ $value | humanizePercentage }}"

      # 磁盘使用率告警
      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) > 0.85
        for: 2m
        labels:
          severity: warning
          instance: "{{ $labels.instance }}"
          mountpoint: "{{ $labels.mountpoint }}"
        annotations:
          summary: "磁盘使用率过高"
          description: "实例 {{ $labels.instance }} 挂载点 {{ $labels.mountpoint }} 的磁盘使用率超过85%，当前值: {{ $value | humanizePercentage }}"

      # 磁盘I/O告警
      - alert: HighDiskIO
        expr: rate(node_disk_io_time_seconds_total[5m]) > 0.8
        for: 2m
        labels:
          severity: warning
          instance: "{{ $labels.instance }}"
          device: "{{ $labels.device }}"
        annotations:
          summary: "磁盘I/O使用率过高"
          description: "实例 {{ $labels.instance }} 设备 {{ $labels.device }} 的I/O使用率超过80%，当前值: {{ $value | humanizePercentage }}"

# ============================================================================
# 性能指标定义
# ============================================================================

# 自定义性能指标
custom_metrics:
  # 业务指标
  business_metrics:
    - name: active_users_total
      description: "当前活跃用户数"
      type: gauge
      
    - name: project_creation_rate
      description: "项目创建速率"
      type: counter
      
    - name: task_completion_rate
      description: "任务完成速率"
      type: counter
      
    - name: api_request_duration_seconds
      description: "API请求持续时间"
      type: histogram
      buckets: [0.01, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0]

  # 技术指标
  technical_metrics:
    - name: cache_hit_ratio
      description: "缓存命中率"
      type: gauge
      
    - name: database_query_duration_seconds
      description: "数据库查询持续时间"
      type: histogram
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0]
      
    - name: ai_model_prediction_duration_seconds
      description: "AI模型预测持续时间"
      type: histogram
      buckets: [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]

# ============================================================================
# Grafana仪表板配置
# ============================================================================

grafana_dashboards:
  # 应用性能仪表板
  - name: "AI项目管理平台 - 应用性能"
    panels:
      - title: "API响应时间"
        type: "graph"
        targets:
          - expr: 'histogram_quantile(0.95, rate(http_server_requests_seconds_bucket[5m]))'
            legend: "95th percentile"
          - expr: 'histogram_quantile(0.50, rate(http_server_requests_seconds_bucket[5m]))'
            legend: "50th percentile"
            
      - title: "API请求量"
        type: "graph"
        targets:
          - expr: 'rate(http_server_requests_seconds_count[5m])'
            legend: "{{ $labels.service }}"
            
      - title: "错误率"
        type: "singlestat"
        targets:
          - expr: 'rate(http_server_requests_seconds_count{status=~"5.."}[5m]) / rate(http_server_requests_seconds_count[5m])'
            
      - title: "JVM内存使用"
        type: "graph"
        targets:
          - expr: 'jvm_memory_used_bytes{area="heap"}'
            legend: "Heap Used"
          - expr: 'jvm_memory_max_bytes{area="heap"}'
            legend: "Heap Max"

  # 数据库性能仪表板
  - name: "AI项目管理平台 - 数据库性能"
    panels:
      - title: "数据库连接数"
        type: "graph"
        targets:
          - expr: 'pg_stat_database_numbackends'
            legend: "{{ $labels.datname }}"
            
      - title: "查询执行时间"
        type: "graph"
        targets:
          - expr: 'rate(pg_stat_database_tup_returned[5m])'
            legend: "Tuples Returned"
          - expr: 'rate(pg_stat_database_tup_fetched[5m])'
            legend: "Tuples Fetched"
            
      - title: "缓存命中率"
        type: "singlestat"
        targets:
          - expr: 'pg_stat_database_blks_hit / (pg_stat_database_blks_hit + pg_stat_database_blks_read)'

# ============================================================================
# 性能优化建议
# ============================================================================

performance_optimization_tips:
  database:
    - "定期执行VACUUM ANALYZE维护表统计信息"
    - "监控慢查询日志，优化查询语句"
    - "合理设置数据库连接池大小"
    - "使用适当的索引策略"
    
  application:
    - "实现合理的缓存策略"
    - "优化JVM参数设置"
    - "使用异步处理提高并发性能"
    - "实现API限流防止过载"
    
  infrastructure:
    - "监控系统资源使用情况"
    - "配置适当的负载均衡"
    - "使用CDN加速静态资源"
    - "定期进行性能测试"
