# Prometheus配置文件 - OAuth2监控

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'aipm-prod'
    environment: 'production'

# 规则文件
rule_files:
  - "rules/*.yml"

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # 用户管理服务监控
  - job_name: 'user-management'
    static_configs:
      - targets: ['user-management:8080']
    scrape_interval: 15s
    metrics_path: /actuator/prometheus
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: user-management:8080

  # OAuth2特定指标
  - job_name: 'oauth2-metrics'
    static_configs:
      - targets: ['user-management:8080']
    scrape_interval: 30s
    metrics_path: /actuator/prometheus
    params:
      match[]: 
        - '{__name__=~"oauth2_.*"}'
        - '{__name__=~"spring_security_.*"}'
        - '{__name__=~"http_server_requests_seconds.*",uri=~"/oauth2/.*"}'
        - '{__name__=~"http_server_requests_seconds.*",uri=~"/api/v1/oauth2/.*"}'

  # 数据库监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # 节点监控
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 远程写入配置（可选）
# remote_write:
#   - url: "https://your-remote-storage/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# 远程读取配置（可选）
# remote_read:
#   - url: "https://your-remote-storage/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"
