# OAuth2监控告警规则

groups:
  - name: oauth2.rules
    interval: 30s
    rules:
      # OAuth2认证失败率告警
      - alert: OAuth2HighFailureRate
        expr: |
          (
            rate(http_server_requests_seconds_count{uri=~"/oauth2/.*", status=~"4..|5.."}[5m]) /
            rate(http_server_requests_seconds_count{uri=~"/oauth2/.*"}[5m])
          ) * 100 > 10
        for: 2m
        labels:
          severity: warning
          service: oauth2
          component: authentication
        annotations:
          summary: "OAuth2认证失败率过高"
          description: "OAuth2认证失败率在过去5分钟内超过10%，当前值: {{ $value }}%"
          runbook_url: "https://docs.your-domain.com/runbooks/oauth2-high-failure-rate"

      # OAuth2认证响应时间告警
      - alert: OAuth2HighLatency
        expr: |
          histogram_quantile(0.95, 
            rate(http_server_requests_seconds_bucket{uri=~"/oauth2/.*"}[5m])
          ) > 5
        for: 3m
        labels:
          severity: warning
          service: oauth2
          component: performance
        annotations:
          summary: "OAuth2认证响应时间过长"
          description: "OAuth2认证95%分位响应时间超过5秒，当前值: {{ $value }}秒"
          runbook_url: "https://docs.your-domain.com/runbooks/oauth2-high-latency"

      # OAuth2服务不可用告警
      - alert: OAuth2ServiceDown
        expr: up{job="user-management"} == 0
        for: 1m
        labels:
          severity: critical
          service: oauth2
          component: availability
        annotations:
          summary: "OAuth2服务不可用"
          description: "OAuth2服务已下线超过1分钟"
          runbook_url: "https://docs.your-domain.com/runbooks/oauth2-service-down"

      # JWT令牌生成失败告警
      - alert: JWTTokenGenerationFailure
        expr: |
          rate(jwt_token_generation_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: oauth2
          component: jwt
        annotations:
          summary: "JWT令牌生成失败"
          description: "JWT令牌生成失败率过高，每分钟失败次数: {{ $value }}"
          runbook_url: "https://docs.your-domain.com/runbooks/jwt-generation-failure"

      # Redis连接失败告警
      - alert: RedisConnectionFailure
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
          service: oauth2
          component: cache
        annotations:
          summary: "Redis连接失败"
          description: "Redis服务不可用，影响OAuth2令牌缓存"
          runbook_url: "https://docs.your-domain.com/runbooks/redis-connection-failure"

      # 数据库连接失败告警
      - alert: DatabaseConnectionFailure
        expr: |
          hikaricp_connections_active{pool="HikariPool-1"} == 0 and
          hikaricp_connections_max{pool="HikariPool-1"} > 0
        for: 1m
        labels:
          severity: critical
          service: oauth2
          component: database
        annotations:
          summary: "数据库连接失败"
          description: "数据库连接池无可用连接"
          runbook_url: "https://docs.your-domain.com/runbooks/database-connection-failure"

      # OAuth2用户注册异常告警
      - alert: OAuth2UserRegistrationAnomaly
        expr: |
          rate(oauth2_user_registrations_total[1h]) > 
          (avg_over_time(rate(oauth2_user_registrations_total[1h])[7d:1h]) * 3)
        for: 5m
        labels:
          severity: warning
          service: oauth2
          component: security
        annotations:
          summary: "OAuth2用户注册异常"
          description: "OAuth2用户注册速率异常，可能存在安全风险"
          runbook_url: "https://docs.your-domain.com/runbooks/oauth2-registration-anomaly"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: |
          (
            jvm_memory_used_bytes{area="heap"} / 
            jvm_memory_max_bytes{area="heap"}
          ) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: oauth2
          component: resources
        annotations:
          summary: "内存使用率过高"
          description: "JVM堆内存使用率超过85%，当前值: {{ $value }}%"
          runbook_url: "https://docs.your-domain.com/runbooks/high-memory-usage"

      # CPU使用率告警
      - alert: HighCPUUsage
        expr: |
          rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: oauth2
          component: resources
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value }}%"
          runbook_url: "https://docs.your-domain.com/runbooks/high-cpu-usage"

      # 磁盘空间告警
      - alert: LowDiskSpace
        expr: |
          (
            node_filesystem_avail_bytes{mountpoint="/"} / 
            node_filesystem_size_bytes{mountpoint="/"}
          ) * 100 < 20
        for: 5m
        labels:
          severity: warning
          service: oauth2
          component: resources
        annotations:
          summary: "磁盘空间不足"
          description: "根分区可用空间少于20%，当前值: {{ $value }}%"
          runbook_url: "https://docs.your-domain.com/runbooks/low-disk-space"

  - name: oauth2.performance
    interval: 15s
    rules:
      # OAuth2认证成功率
      - record: oauth2:authentication_success_rate
        expr: |
          (
            rate(http_server_requests_seconds_count{uri=~"/oauth2/.*", status=~"2..|3.."}[5m]) /
            rate(http_server_requests_seconds_count{uri=~"/oauth2/.*"}[5m])
          ) * 100

      # OAuth2平均响应时间
      - record: oauth2:average_response_time
        expr: |
          rate(http_server_requests_seconds_sum{uri=~"/oauth2/.*"}[5m]) /
          rate(http_server_requests_seconds_count{uri=~"/oauth2/.*"}[5m])

      # OAuth2请求速率
      - record: oauth2:request_rate
        expr: |
          rate(http_server_requests_seconds_count{uri=~"/oauth2/.*"}[5m])

      # JWT令牌生成速率
      - record: oauth2:jwt_generation_rate
        expr: |
          rate(jwt_tokens_generated_total[5m])

      # OAuth2用户注册速率
      - record: oauth2:user_registration_rate
        expr: |
          rate(oauth2_user_registrations_total[5m])
