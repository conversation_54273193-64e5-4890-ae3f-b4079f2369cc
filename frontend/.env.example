# 前端环境配置示例

# 应用配置
VITE_APP_TITLE=AI项目管理平台
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=智能项目管理平台前端应用

# API配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=30000

# 认证配置
VITE_AUTH_TOKEN_KEY=access_token
VITE_AUTH_REFRESH_TOKEN_KEY=refresh_token

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_ANALYTICS=false

# 第三方服务
VITE_SENTRY_DSN=
VITE_GOOGLE_ANALYTICS_ID=

# 开发配置
VITE_DEV_PORT=3000
VITE_DEV_HOST=localhost
