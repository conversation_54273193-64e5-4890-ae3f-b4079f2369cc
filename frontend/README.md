# AI项目管理平台 - 前端应用

基于React + TypeScript + Ant Design构建的现代化项目管理平台前端应用。

## 功能特性

### 🎨 用户界面
- **现代化设计**: 基于Ant Design设计语言，提供一致的用户体验
- **响应式布局**: 支持桌面端、平板和移动端设备
- **深色主题**: 支持明暗主题切换
- **国际化**: 支持中英文多语言切换

### 🔐 用户认证
- **安全登录**: JWT令牌认证，支持记住登录状态
- **权限控制**: 基于角色的访问控制(RBAC)
- **路由守卫**: 保护需要认证的页面
- **自动刷新**: 令牌自动刷新机制

### 📊 仪表板
- **数据概览**: 项目、任务、团队等关键指标展示
- **可视化图表**: 项目进度、任务状态等数据可视化
- **实时更新**: 数据实时刷新和状态同步
- **个性化**: 可定制的仪表板布局

### 📋 项目管理
- **项目列表**: 项目筛选、搜索、排序功能
- **项目详情**: 完整的项目信息展示和编辑
- **进度跟踪**: 项目进度可视化和里程碑管理
- **团队协作**: 项目成员管理和权限分配

### ✅ 任务管理
- **任务看板**: 拖拽式任务状态管理
- **任务详情**: 任务信息、附件、评论管理
- **时间跟踪**: 任务工时记录和统计
- **优先级管理**: 任务优先级设置和排序

### 👥 团队管理
- **成员管理**: 团队成员添加、编辑、权限设置
- **角色管理**: 自定义角色和权限配置
- **活动跟踪**: 团队成员活动记录
- **绩效分析**: 团队和个人绩效统计

### 📈 智能分析
- **AI预测**: 项目进度和完成时间预测
- **风险识别**: 项目风险自动识别和预警
- **数据洞察**: 基于数据的项目洞察和建议
- **趋势分析**: 历史数据趋势分析

### 📊 报表中心
- **多类型报表**: 项目、任务、团队等各类报表
- **自定义报表**: 可配置的报表模板
- **导出功能**: 支持PDF、Excel等格式导出
- **定时报表**: 自动生成和发送报表

## 技术栈

### 核心框架
- **React 18**: 现代化React框架，支持并发特性
- **TypeScript**: 类型安全的JavaScript超集
- **Vite**: 快速的构建工具和开发服务器

### UI组件库
- **Ant Design 5**: 企业级UI设计语言和组件库
- **Ant Design Icons**: 丰富的图标库
- **Tailwind CSS**: 实用优先的CSS框架

### 状态管理
- **Redux Toolkit**: 现代化的Redux状态管理
- **Redux Persist**: 状态持久化
- **React Query**: 服务端状态管理和缓存

### 路由和导航
- **React Router 6**: 声明式路由管理
- **路由守卫**: 基于认证状态的路由保护

### 数据可视化
- **ECharts**: 强大的数据可视化图表库
- **ECharts for React**: React集成组件

### 表单处理
- **React Hook Form**: 高性能表单库
- **Yup**: 表单验证schema

### 工具库
- **Axios**: HTTP客户端
- **Day.js**: 轻量级日期处理库
- **Lodash**: 实用工具函数库
- **React Beautiful DnD**: 拖拽功能

### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Vitest**: 单元测试框架
- **TypeScript**: 类型检查

## 快速开始

### 环境要求
- Node.js 18+
- npm 9+ 或 yarn 1.22+

### 安装依赖
```bash
cd frontend
npm install
```

### 开发环境
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
npm run lint:fix
```

### 运行测试
```bash
npm run test
npm run test:ui
npm run test:coverage
```

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 可复用组件
│   │   ├── common/         # 通用组件
│   │   ├── layout/         # 布局组件
│   │   ├── auth/           # 认证组件
│   │   └── charts/         # 图表组件
│   ├── pages/              # 页面组件
│   │   ├── auth/           # 认证页面
│   │   ├── dashboard/      # 仪表板
│   │   ├── projects/       # 项目管理
│   │   ├── tasks/          # 任务管理
│   │   ├── team/           # 团队管理
│   │   ├── reports/        # 报表中心
│   │   ├── analytics/      # 智能分析
│   │   └── settings/       # 系统设置
│   ├── store/              # Redux状态管理
│   │   └── slices/         # Redux切片
│   ├── services/           # API服务
│   ├── hooks/              # 自定义Hooks
│   ├── utils/              # 工具函数
│   ├── types/              # TypeScript类型定义
│   ├── assets/             # 静态资源
│   ├── styles/             # 样式文件
│   ├── i18n/               # 国际化
│   └── test/               # 测试文件
├── package.json
├── vite.config.ts
├── tsconfig.json
├── tailwind.config.js
└── README.md
```

## 开发指南

### 组件开发
- 使用函数式组件和Hooks
- 遵循单一职责原则
- 提供完整的TypeScript类型定义
- 编写组件文档和使用示例

### 状态管理
- 使用Redux Toolkit管理全局状态
- 本地状态优先使用useState
- 服务端状态使用React Query
- 避免过度使用全局状态

### 样式规范
- 优先使用Ant Design组件
- 使用Tailwind CSS处理自定义样式
- 遵循BEM命名规范
- 支持主题切换

### API集成
- 统一使用axios进行HTTP请求
- 实现请求和响应拦截器
- 处理错误和加载状态
- 支持请求取消和重试

### 测试策略
- 单元测试覆盖核心业务逻辑
- 集成测试验证组件交互
- E2E测试覆盖关键用户流程
- 保持测试覆盖率在80%以上

## 部署说明

### Docker部署
```bash
docker build -t ai-pm-frontend .
docker run -p 80:80 ai-pm-frontend
```

### Nginx配置
- 配置SPA路由支持
- 启用Gzip压缩
- 设置静态资源缓存
- 配置API代理

### 环境变量
- 复制`.env.example`为`.env`
- 配置API地址和其他环境变量
- 生产环境使用环境变量注入

## 性能优化

### 代码分割
- 路由级别的代码分割
- 组件懒加载
- 第三方库分离打包

### 资源优化
- 图片压缩和格式优化
- 字体文件优化
- CSS和JS压缩

### 缓存策略
- 浏览器缓存配置
- Service Worker缓存
- API响应缓存

### 监控和分析
- 性能监控集成
- 错误追踪
- 用户行为分析

## 浏览器支持

- Chrome >= 88
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License
