/**
 * API工具函数
 * 提供统一的API调用、错误处理和数据验证功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-28
 */

import { message } from 'antd';
import { ApiResponse, PageResponse } from '@/services/api';

/**
 * API错误类型
 */
export enum ApiErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * API错误信息
 */
export interface ApiError {
  type: ApiErrorType;
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
}

/**
 * 请求配置
 */
export interface RequestConfig {
  showLoading?: boolean;
  showSuccessMessage?: boolean;
  showErrorMessage?: boolean;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
}

/**
 * 默认请求配置
 */
const DEFAULT_CONFIG: RequestConfig = {
  showLoading: false,
  showSuccessMessage: false,
  showErrorMessage: true,
  timeout: 30000,
  retryCount: 0,
  retryDelay: 1000
};

/**
 * 创建API错误对象
 */
export function createApiError(
  type: ApiErrorType,
  message: string,
  code?: string,
  details?: any
): ApiError {
  return {
    type,
    message,
    code,
    details,
    timestamp: new Date().toISOString()
  };
}

/**
 * 解析API错误
 */
export function parseApiError(error: any): ApiError {
  // 网络错误
  if (error.code === 'ECONNABORTED' || error.message === 'timeout') {
    return createApiError(
      ApiErrorType.TIMEOUT_ERROR,
      '请求超时，请检查网络连接',
      'TIMEOUT'
    );
  }

  if (error.message === 'Network Error') {
    return createApiError(
      ApiErrorType.NETWORK_ERROR,
      '网络连接失败，请检查网络设置',
      'NETWORK_ERROR'
    );
  }

  // HTTP错误
  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        return createApiError(
          ApiErrorType.VALIDATION_ERROR,
          data?.message || '请求参数错误',
          'BAD_REQUEST',
          data
        );
      
      case 401:
        return createApiError(
          ApiErrorType.AUTHENTICATION_ERROR,
          '身份验证失败，请重新登录',
          'UNAUTHORIZED'
        );
      
      case 403:
        return createApiError(
          ApiErrorType.AUTHORIZATION_ERROR,
          '权限不足，无法访问该资源',
          'FORBIDDEN'
        );
      
      case 404:
        return createApiError(
          ApiErrorType.NOT_FOUND_ERROR,
          '请求的资源不存在',
          'NOT_FOUND'
        );
      
      case 422:
        return createApiError(
          ApiErrorType.VALIDATION_ERROR,
          data?.message || '数据验证失败',
          'VALIDATION_ERROR',
          data?.errors
        );
      
      case 429:
        return createApiError(
          ApiErrorType.SERVER_ERROR,
          '请求过于频繁，请稍后再试',
          'TOO_MANY_REQUESTS'
        );
      
      case 500:
      case 502:
      case 503:
      case 504:
        return createApiError(
          ApiErrorType.SERVER_ERROR,
          '服务器错误，请稍后再试',
          'SERVER_ERROR'
        );
      
      default:
        return createApiError(
          ApiErrorType.UNKNOWN_ERROR,
          data?.message || `请求失败 (${status})`,
          'HTTP_ERROR'
        );
    }
  }

  // 未知错误
  return createApiError(
    ApiErrorType.UNKNOWN_ERROR,
    error.message || '未知错误',
    'UNKNOWN'
  );
}

/**
 * 显示错误消息
 */
export function showErrorMessage(error: ApiError): void {
  switch (error.type) {
    case ApiErrorType.VALIDATION_ERROR:
      if (error.details && Array.isArray(error.details)) {
        error.details.forEach((detail: string) => {
          message.error(detail);
        });
      } else {
        message.error(error.message);
      }
      break;
    
    case ApiErrorType.AUTHENTICATION_ERROR:
      message.error(error.message);
      // 可以在这里触发登录页面跳转
      break;
    
    default:
      message.error(error.message);
  }
}

/**
 * 验证API响应格式
 */
export function validateApiResponse<T>(response: any): ApiResponse<T> {
  if (!response || typeof response !== 'object') {
    throw createApiError(
      ApiErrorType.SERVER_ERROR,
      '服务器响应格式错误',
      'INVALID_RESPONSE'
    );
  }

  // 检查必需字段
  if (typeof response.success !== 'boolean') {
    throw createApiError(
      ApiErrorType.SERVER_ERROR,
      '服务器响应格式错误：缺少success字段',
      'INVALID_RESPONSE'
    );
  }

  return response as ApiResponse<T>;
}

/**
 * 验证分页响应格式
 */
export function validatePageResponse<T>(response: any): PageResponse<T> {
  const validated = validateApiResponse(response);
  
  if (!validated.data || typeof validated.data !== 'object') {
    throw createApiError(
      ApiErrorType.SERVER_ERROR,
      '分页响应格式错误',
      'INVALID_PAGE_RESPONSE'
    );
  }

  const pageData = validated.data as any;
  
  // 检查分页字段
  const requiredFields = ['content', 'totalElements', 'totalPages', 'size', 'number'];
  for (const field of requiredFields) {
    if (!(field in pageData)) {
      throw createApiError(
        ApiErrorType.SERVER_ERROR,
        `分页响应缺少${field}字段`,
        'INVALID_PAGE_RESPONSE'
      );
    }
  }

  return pageData as PageResponse<T>;
}

/**
 * 安全的API调用包装器
 */
export async function safeApiCall<T>(
  apiCall: () => Promise<T>,
  config: RequestConfig = {}
): Promise<T> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  try {
    // 显示加载状态
    if (finalConfig.showLoading) {
      message.loading('加载中...', 0);
    }

    const result = await apiCall();

    // 隐藏加载状态
    if (finalConfig.showLoading) {
      message.destroy();
    }

    // 显示成功消息
    if (finalConfig.showSuccessMessage) {
      message.success('操作成功');
    }

    return result;
  } catch (error) {
    // 隐藏加载状态
    if (finalConfig.showLoading) {
      message.destroy();
    }

    const apiError = parseApiError(error);

    // 显示错误消息
    if (finalConfig.showErrorMessage) {
      showErrorMessage(apiError);
    }

    throw apiError;
  }
}

/**
 * 重试机制包装器
 */
export async function withRetry<T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break;
      }

      // 对于某些错误类型，不进行重试
      const apiError = parseApiError(error);
      if ([
        ApiErrorType.AUTHENTICATION_ERROR,
        ApiErrorType.AUTHORIZATION_ERROR,
        ApiErrorType.VALIDATION_ERROR
      ].includes(apiError.type)) {
        break;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * (attempt + 1)));
    }
  }

  throw lastError;
}

/**
 * 数据转换工具
 */
export class DataTransformer {
  /**
   * 转换日期格式
   */
  static transformDates(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.transformDates(item));
    }

    const result = { ...data };
    
    for (const [key, value] of Object.entries(result)) {
      if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
        result[key] = new Date(value);
      } else if (typeof value === 'object' && value !== null) {
        result[key] = this.transformDates(value);
      }
    }

    return result;
  }

  /**
   * 清理空值
   */
  static cleanEmptyValues(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.cleanEmptyValues(item)).filter(item => item != null);
    }

    const result: any = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (value != null && value !== '' && value !== undefined) {
        if (typeof value === 'object') {
          const cleaned = this.cleanEmptyValues(value);
          if (Object.keys(cleaned).length > 0) {
            result[key] = cleaned;
          }
        } else {
          result[key] = value;
        }
      }
    }

    return result;
  }
}

/**
 * 导出常用工具
 */
export {
  safeApiCall as apiCall,
  withRetry,
  validateApiResponse,
  validatePageResponse,
  parseApiError,
  showErrorMessage,
  DataTransformer
};
