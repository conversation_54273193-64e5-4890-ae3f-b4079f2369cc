/**
 * 全局样式文件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 自定义工具类 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 布局相关 */
.dashboard-page {
  animation: fadeIn 0.3s ease-in-out;
}

.auth-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    z-index: 1000;
  }

  .ant-layout-content {
    margin-left: 0 !important;
    padding: 8px !important;
  }

  /* 移动端优化 */
  .mobile-layout {
    padding: 0;
  }

  .mobile-header {
    position: sticky;
    top: 0;
    z-index: 100;
  }

  /* 移动端表格优化 */
  .ant-table {
    font-size: 12px;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px;
  }

  /* 移动端表单优化 */
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-btn {
    height: 40px;
    font-size: 14px;
  }

  /* 移动端卡片优化 */
  .ant-card {
    margin-bottom: 8px;
    border-radius: 4px;
  }

  .ant-card-body {
    padding: 12px;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .tablet-layout {
    padding: 12px;
  }

  .ant-layout-content {
    padding: 16px;
  }

  .ant-card {
    margin-bottom: 12px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .desktop-layout {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ant-layout-content {
    padding: 24px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .ant-layout-sider {
    display: none !important;
  }
  
  .ant-layout-header {
    display: none !important;
  }
  
  .ant-layout-content {
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* 深色主题支持 */
[data-theme='dark'] {
  background-color: #141414;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .ant-layout {
  background-color: #141414;
}

[data-theme='dark'] .ant-layout-content {
  background-color: #1f1f1f;
}

/* 自定义组件样式 */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.error-boundary {
  padding: 20px;
  text-align: center;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #999;
}

/* 表格样式增强 */
.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

[data-theme='dark'] .ant-table-tbody > tr:hover > td {
  background-color: #262626 !important;
}

/* 卡片样式增强 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 用户体验增强 */
.ux-enhanced {
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

/* 加载状态优化 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-skeleton {
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    background-color: #f2f2f2;
  }
  50% {
    background-color: #e6e6e6;
  }
  100% {
    background-color: #f2f2f2;
  }
}

/* 交互反馈优化 */
.interactive-element {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive-element:hover {
  transform: scale(1.02);
}

.interactive-element:active {
  transform: scale(0.98);
}

/* 错误状态样式 */
.error-state {
  color: #ff4d4f;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 8px 12px;
}

/* 成功状态样式 */
.success-state {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 8px 12px;
}

/* 警告状态样式 */
.warning-state {
  color: #faad14;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 4px;
  padding: 8px 12px;
}

/* 信息状态样式 */
.info-state {
  color: #1890ff;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  padding: 8px 12px;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transition: box-shadow 0.3s ease;
}

/* 按钮样式增强 */
.ant-btn {
  border-radius: 6px;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

/* 输入框样式增强 */
.ant-input,
.ant-input-password {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-password:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 标签样式增强 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 进度条样式增强 */
.ant-progress-line {
  border-radius: 4px;
}

/* 面包屑样式 */
.ant-breadcrumb {
  font-size: 14px;
}

.ant-breadcrumb-link {
  color: #666;
}

.ant-breadcrumb-link:hover {
  color: #1890ff;
}
