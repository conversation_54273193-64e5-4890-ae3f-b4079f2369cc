/**
 * WebSocket通知服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
}

interface NotificationData {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  content: string
  data?: any
}

type MessageHandler = (message: WebSocketMessage) => void
type ConnectionHandler = () => void
type ErrorHandler = (error: Event) => void

class WebSocketService {
  private ws: WebSocket | null = null
  private url: string
  private userId: string | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000
  private heartbeatInterval: NodeJS.Timeout | null = null
  private isConnecting = false

  // 事件处理器
  private messageHandlers: Map<string, MessageHandler[]> = new Map()
  private connectionHandlers: ConnectionHandler[] = []
  private disconnectionHandlers: ConnectionHandler[] = []
  private errorHandlers: ErrorHandler[] = []

  constructor(baseUrl: string = 'ws://localhost:8084') {
    this.url = `${baseUrl}/api/v1/ws`
  }

  /**
   * 连接WebSocket
   */
  connect(userId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      if (this.isConnecting) {
        reject(new Error('正在连接中'))
        return
      }

      this.isConnecting = true
      this.userId = userId
      const wsUrl = `${this.url}?user_id=${userId}`

      try {
        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log('WebSocket连接已建立')
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.startHeartbeat()
          this.connectionHandlers.forEach(handler => handler())
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            this.handleMessage(message)
          } catch (error) {
            console.error('解析WebSocket消息失败:', error)
          }
        }

        this.ws.onclose = (event) => {
          console.log('WebSocket连接已关闭:', event.code, event.reason)
          this.isConnecting = false
          this.stopHeartbeat()
          this.disconnectionHandlers.forEach(handler => handler())
          
          // 自动重连
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error)
          this.isConnecting = false
          this.errorHandlers.forEach(handler => handler(error))
          reject(error)
        }
      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close(1000, '主动断开连接')
      this.ws = null
    }
  }

  /**
   * 发送消息
   */
  send(type: string, data: any): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，无法发送消息')
      return
    }

    const message = {
      type,
      data,
      timestamp: new Date().toISOString(),
    }

    this.ws.send(JSON.stringify(message))
  }

  /**
   * 标记通知为已读
   */
  markNotificationAsRead(notificationId: string): void {
    this.send('mark_read', { notification_id: notificationId })
  }

  /**
   * 订阅频道
   */
  subscribe(channels: string[]): void {
    this.send('subscribe', { channels })
  }

  /**
   * 取消订阅频道
   */
  unsubscribe(channels: string[]): void {
    this.send('unsubscribe', { channels })
  }

  /**
   * 添加消息处理器
   */
  onMessage(type: string, handler: MessageHandler): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, [])
    }
    this.messageHandlers.get(type)!.push(handler)
  }

  /**
   * 移除消息处理器
   */
  offMessage(type: string, handler: MessageHandler): void {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 添加连接处理器
   */
  onConnect(handler: ConnectionHandler): void {
    this.connectionHandlers.push(handler)
  }

  /**
   * 添加断开连接处理器
   */
  onDisconnect(handler: ConnectionHandler): void {
    this.disconnectionHandlers.push(handler)
  }

  /**
   * 添加错误处理器
   */
  onError(handler: ErrorHandler): void {
    this.errorHandlers.push(handler)
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): number {
    return this.ws ? this.ws.readyState : WebSocket.CLOSED
  }

  /**
   * 是否已连接
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    console.log('收到WebSocket消息:', message)

    // 调用对应类型的处理器
    const handlers = this.messageHandlers.get(message.type)
    if (handlers) {
      handlers.forEach(handler => handler(message))
    }

    // 调用通用处理器
    const allHandlers = this.messageHandlers.get('*')
    if (allHandlers) {
      allHandlers.forEach(handler => handler(message))
    }
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.send('ping', { timestamp: Date.now() })
    }, 30000) // 30秒心跳
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++
    console.log(`${this.reconnectInterval / 1000}秒后尝试第${this.reconnectAttempts}次重连`)

    setTimeout(() => {
      if (this.userId) {
        this.connect(this.userId).catch(error => {
          console.error('重连失败:', error)
        })
      }
    }, this.reconnectInterval)

    // 递增重连间隔，最大30秒
    this.reconnectInterval = Math.min(this.reconnectInterval * 1.5, 30000)
  }
}

// 创建全局实例
const websocketService = new WebSocketService()

// 设置通知处理器
websocketService.onMessage('notification', (message) => {
  const notification: NotificationData = message.data
  
  // 显示浏览器通知
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(notification.title, {
      body: notification.content,
      icon: '/favicon.ico',
      tag: notification.id,
    })
  }

  // 触发自定义事件
  window.dispatchEvent(new CustomEvent('websocket-notification', {
    detail: notification
  }))
})

websocketService.onMessage('welcome', (message) => {
  console.log('WebSocket欢迎消息:', message.data)
})

websocketService.onMessage('pong', (message) => {
  console.log('收到心跳响应:', message.data)
})

websocketService.onConnect(() => {
  console.log('WebSocket连接成功')
  // 可以在这里订阅频道
  // websocketService.subscribe(['notifications', 'updates'])
})

websocketService.onDisconnect(() => {
  console.log('WebSocket连接断开')
})

websocketService.onError((error) => {
  console.error('WebSocket连接错误:', error)
})

export default websocketService
