/**
 * API客户端配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'
const API_TIMEOUT = 30000

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求ID用于追踪
    const requestId = Math.random().toString(36).substring(7)
    if (config.headers) {
      config.headers['X-Request-ID'] = requestId
    }

    // 记录请求日志
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      requestId,
      data: config.data,
      params: config.params,
    })

    return config
  },
  (error) => {
    console.error('[API Request Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 记录响应日志
    console.log(`[API Response] ${response.status} ${response.config.url}`, {
      requestId: response.config.headers?.['X-Request-ID'],
      data: response.data,
    })

    return response
  },
  async (error) => {
    const { response, config } = error

    // 记录错误日志
    console.error(`[API Error] ${response?.status} ${config?.url}`, {
      requestId: config?.headers?.['X-Request-ID'],
      error: response?.data,
    })

    // 处理不同的错误状态
    if (response) {
      switch (response.status) {
        case 401:
          // 未授权，尝试刷新token
          try {
            const refreshToken = localStorage.getItem('refresh_token')
            if (refreshToken) {
              const refreshResponse = await axios.post(`${API_BASE_URL}/api/v1/auth/refresh`, {
                refreshToken,
              })
              
              const { token, refreshToken: newRefreshToken } = refreshResponse.data
              localStorage.setItem('access_token', token)
              localStorage.setItem('refresh_token', newRefreshToken)
              
              // 重试原请求
              if (config.headers) {
                config.headers.Authorization = `Bearer ${token}`
              }
              return api.request(config)
            }
          } catch (refreshError) {
            // 刷新失败，清除token并跳转到登录页
            localStorage.removeItem('access_token')
            localStorage.removeItem('refresh_token')
            window.location.href = '/login'
            return Promise.reject(refreshError)
          }
          break

        case 403:
          message.error('权限不足，无法访问该资源')
          break

        case 404:
          message.error('请求的资源不存在')
          break

        case 422:
          // 表单验证错误
          const validationErrors = response.data?.errors || response.data?.message
          if (validationErrors) {
            if (typeof validationErrors === 'string') {
              message.error(validationErrors)
            } else if (Array.isArray(validationErrors)) {
              validationErrors.forEach((err: string) => message.error(err))
            }
          }
          break

        case 429:
          message.error('请求过于频繁，请稍后再试')
          break

        case 500:
          message.error('服务器内部错误，请稍后再试')
          break

        case 502:
        case 503:
        case 504:
          message.error('服务暂时不可用，请稍后再试')
          break

        default:
          message.error(response.data?.message || '请求失败，请稍后再试')
      }
    } else if (error.code === 'ECONNABORTED') {
      message.error('请求超时，请检查网络连接')
    } else if (error.message === 'Network Error') {
      message.error('网络连接失败，请检查网络设置')
    } else {
      message.error('未知错误，请稍后再试')
    }

    return Promise.reject(error)
  }
)

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: string
  timestamp?: string
}

// 分页响应类型
export interface PageResponse<T = any> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  numberOfElements: number
  empty: boolean
}

// 通用API方法
export const apiClient = {
  // GET请求
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.get(url, config).then((response) => response.data),

  // POST请求
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.post(url, data, config).then((response) => response.data),

  // PUT请求
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.put(url, data, config).then((response) => response.data),

  // PATCH请求
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.patch(url, data, config).then((response) => response.data),

  // DELETE请求
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.delete(url, config).then((response) => response.data),

  // 上传文件
  upload: <T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> =>
    api.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    }).then((response) => response.data),

  // 下载文件
  download: (url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> =>
    api.get(url, {
      ...config,
      responseType: 'blob',
    }).then((response) => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    }),
}

export default api
