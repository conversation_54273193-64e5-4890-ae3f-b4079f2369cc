/**
 * 仪表板API服务
 * 处理仪表板相关的API请求
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import { apiClient } from './client';
import { 
  DashboardData, 
  DashboardStats, 
  ProjectProgress, 
  TeamPerformance, 
  RiskAnalysis 
} from '../../store/slices/dashboardSlice';

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

class DashboardApi {
  /**
   * 获取完整的仪表板数据
   */
  async getDashboardData(): Promise<ApiResponse<DashboardData>> {
    try {
      const response = await apiClient.get('/dashboard');
      return response.data;
    } catch (error) {
      // 如果API失败，返回模拟数据
      console.warn('API调用失败，使用模拟数据:', error);
      return {
        data: this.getMockDashboardData(),
        success: true,
        message: '数据加载成功（模拟数据）'
      };
    }
  }

  /**
   * 获取统计数据
   */
  async getStats(): Promise<ApiResponse<DashboardStats>> {
    try {
      const response = await apiClient.get('/dashboard/stats');
      return response.data;
    } catch (error) {
      console.warn('获取统计数据失败，使用模拟数据:', error);
      return {
        data: {
          totalProjects: 12,
          activeProjects: 8,
          completedTasks: 156,
          totalTasks: 203,
          teamMembers: 15,
          overdueTasks: 7,
        },
        success: true
      };
    }
  }

  /**
   * 获取项目进度
   */
  async getProjectProgress(): Promise<ApiResponse<ProjectProgress[]>> {
    try {
      const response = await apiClient.get('/dashboard/projects/progress');
      return response.data;
    } catch (error) {
      console.warn('获取项目进度失败，使用模拟数据:', error);
      return {
        data: [
          {
            id: '1',
            name: 'AI推荐系统',
            progress: 85,
            status: 'active',
            startDate: '2025-07-01',
            endDate: '2025-08-30'
          },
          {
            id: '2',
            name: '用户管理模块',
            progress: 100,
            status: 'completed',
            startDate: '2025-06-15',
            endDate: '2025-07-15'
          },
          {
            id: '3',
            name: '数据分析平台',
            progress: 65,
            status: 'active',
            startDate: '2025-07-15',
            endDate: '2025-09-15'
          }
        ],
        success: true
      };
    }
  }

  /**
   * 获取团队绩效
   */
  async getTeamPerformance(timeRange: string = '30d'): Promise<ApiResponse<TeamPerformance[]>> {
    try {
      const response = await apiClient.get(`/dashboard/team/performance?range=${timeRange}`);
      return response.data;
    } catch (error) {
      console.warn('获取团队绩效失败，使用模拟数据:', error);
      return {
        data: [
          {
            memberId: '1',
            memberName: '张三',
            metrics: {
              taskCompletion: 85,
              codeQuality: 90,
              collaboration: 88,
              innovation: 75,
              learning: 82,
              communication: 85
            }
          },
          {
            memberId: '2',
            memberName: '李四',
            metrics: {
              taskCompletion: 78,
              codeQuality: 85,
              collaboration: 92,
              innovation: 88,
              learning: 85,
              communication: 90
            }
          }
        ],
        success: true
      };
    }
  }

  /**
   * 获取风险分析
   */
  async getRiskAnalysis(days: number = 7): Promise<ApiResponse<RiskAnalysis[]>> {
    try {
      const response = await apiClient.get(`/dashboard/risk/analysis?days=${days}`);
      return response.data;
    } catch (error) {
      console.warn('获取风险分析失败，使用模拟数据:', error);
      return {
        data: [
          { date: '08-10', highRisk: 2, mediumRisk: 5, lowRisk: 8 },
          { date: '08-11', highRisk: 3, mediumRisk: 4, lowRisk: 9 },
          { date: '08-12', highRisk: 1, mediumRisk: 6, lowRisk: 7 },
          { date: '08-13', highRisk: 2, mediumRisk: 5, lowRisk: 10 },
          { date: '08-14', highRisk: 4, mediumRisk: 3, lowRisk: 8 },
          { date: '08-15', highRisk: 2, mediumRisk: 7, lowRisk: 6 },
          { date: '08-16', highRisk: 1, mediumRisk: 5, lowRisk: 9 }
        ],
        success: true
      };
    }
  }

  /**
   * 获取最近活动
   */
  async getRecentActivities(limit: number = 10): Promise<ApiResponse<any[]>> {
    try {
      const response = await apiClient.get(`/dashboard/activities?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.warn('获取最近活动失败，使用模拟数据:', error);
      return {
        data: [
          {
            id: '1',
            type: 'project',
            title: '创建了新项目',
            description: 'AI推荐系统开发',
            timestamp: '2小时前',
            user: { 
              id: '1',
              name: '张三', 
              avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1' 
            }
          },
          {
            id: '2',
            type: 'task',
            title: '完成了任务',
            description: '用户界面设计',
            timestamp: '4小时前',
            user: { 
              id: '2',
              name: '李四', 
              avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=2' 
            }
          }
        ],
        success: true
      };
    }
  }

  /**
   * 获取即将到期的任务
   */
  async getUpcomingDeadlines(days: number = 7): Promise<ApiResponse<any[]>> {
    try {
      const response = await apiClient.get(`/dashboard/deadlines?days=${days}`);
      return response.data;
    } catch (error) {
      console.warn('获取即将到期任务失败，使用模拟数据:', error);
      return {
        data: [
          {
            id: '1',
            title: 'API接口开发',
            type: 'task',
            deadline: '2025-08-20',
            priority: 'high',
            progress: 75,
            projectId: '1'
          },
          {
            id: '2',
            title: '用户测试',
            type: 'project',
            deadline: '2025-08-25',
            priority: 'medium',
            progress: 45,
            projectId: '2'
          }
        ],
        success: true
      };
    }
  }

  /**
   * 获取模拟的完整仪表板数据
   */
  private getMockDashboardData(): DashboardData {
    return {
      stats: {
        totalProjects: 12,
        activeProjects: 8,
        completedTasks: 156,
        totalTasks: 203,
        teamMembers: 15,
        overdueTasks: 7,
      },
      projectProgress: [
        {
          id: '1',
          name: 'AI推荐系统',
          progress: 85,
          status: 'active',
          startDate: '2025-07-01',
          endDate: '2025-08-30'
        },
        {
          id: '2',
          name: '用户管理模块',
          progress: 100,
          status: 'completed',
          startDate: '2025-06-15',
          endDate: '2025-07-15'
        }
      ],
      taskDistribution: [
        { status: '待开始', count: 25, percentage: 12.3 },
        { status: '进行中', count: 45, percentage: 22.2 },
        { status: '已完成', count: 120, percentage: 59.1 },
        { status: '已逾期', count: 8, percentage: 3.9 },
        { status: '已暂停', count: 5, percentage: 2.5 }
      ],
      teamPerformance: [
        {
          memberId: '1',
          memberName: '张三',
          metrics: {
            taskCompletion: 85,
            codeQuality: 90,
            collaboration: 88,
            innovation: 75,
            learning: 82,
            communication: 85
          }
        }
      ],
      riskAnalysis: [
        { date: '08-10', highRisk: 2, mediumRisk: 5, lowRisk: 8 },
        { date: '08-11', highRisk: 3, mediumRisk: 4, lowRisk: 9 },
        { date: '08-12', highRisk: 1, mediumRisk: 6, lowRisk: 7 }
      ],
      recentActivities: [
        {
          id: '1',
          type: 'project',
          title: '创建了新项目',
          description: 'AI推荐系统开发',
          timestamp: '2小时前',
          user: { 
            id: '1',
            name: '张三', 
            avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1' 
          }
        }
      ],
      upcomingDeadlines: [
        {
          id: '1',
          title: 'API接口开发',
          type: 'task',
          deadline: '2025-08-20',
          priority: 'high',
          progress: 75,
          projectId: '1'
        }
      ]
    };
  }
}

export const dashboardApi = new DashboardApi();
