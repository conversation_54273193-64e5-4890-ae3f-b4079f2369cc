/**
 * OAuth2认证服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */

import { apiClient, ApiResponse } from './api'

// OAuth2提供商信息接口
export interface OAuth2Provider {
  clientName: string
  authorizationUri: string
}

// OAuth2提供商列表响应接口
export interface OAuth2ProvidersResponse {
  providers: Record<string, OAuth2Provider>
  redirectUris: string[]
}

// OAuth2认证状态接口
export interface OAuth2Status {
  authenticated: boolean
  principal?: string
}

// OAuth2授权URL响应接口
export interface OAuth2AuthorizationUrlResponse {
  authorizationUrl: string
  provider: string
}

/**
 * OAuth2认证服务类
 */
export class OAuth2Service {
  
  /**
   * 获取可用的OAuth2提供商列表
   * 
   * @returns OAuth2提供商列表
   */
  static async getProviders(): Promise<OAuth2ProvidersResponse> {
    const response = await apiClient.get<ApiResponse<OAuth2ProvidersResponse>>('/api/v1/oauth2/providers')
    return response.data.data
  }

  /**
   * 检查OAuth2认证状态
   * 
   * @returns 认证状态
   */
  static async checkStatus(): Promise<OAuth2Status> {
    const response = await apiClient.get<ApiResponse<OAuth2Status>>('/api/v1/oauth2/status')
    return response.data.data
  }

  /**
   * 获取OAuth2授权URL
   * 
   * @param provider OAuth2提供商ID
   * @param redirectUri 重定向URI
   * @returns 授权URL信息
   */
  static async getAuthorizationUrl(provider: string, redirectUri?: string): Promise<OAuth2AuthorizationUrlResponse> {
    const params = redirectUri ? { redirect_uri: redirectUri } : {}
    const response = await apiClient.get<ApiResponse<OAuth2AuthorizationUrlResponse>>(
      `/api/v1/oauth2/authorization-url/${provider}`,
      { params }
    )
    return response.data.data
  }

  /**
   * 启动OAuth2认证流程
   * 
   * @param provider OAuth2提供商ID
   * @param redirectUri 认证成功后的重定向URI
   */
  static async startAuthentication(provider: string, redirectUri?: string): Promise<void> {
    try {
      // 保存重定向URI到localStorage，认证成功后使用
      if (redirectUri) {
        localStorage.setItem('oauth2_redirect_uri', redirectUri)
      }

      // 构建授权URL
      const baseUrl = window.location.origin
      const authUrl = `${baseUrl}/oauth2/authorization/${provider}`
      
      // 添加重定向参数
      const params = new URLSearchParams()
      if (redirectUri) {
        params.append('redirect_uri', redirectUri)
      }
      
      const finalUrl = params.toString() ? `${authUrl}?${params.toString()}` : authUrl
      
      // 重定向到OAuth2授权页面
      window.location.href = finalUrl
      
    } catch (error) {
      console.error('启动OAuth2认证失败:', error)
      throw error
    }
  }

  /**
   * 处理OAuth2认证回调
   * 
   * @param urlParams URL参数
   * @returns 处理结果
   */
  static handleAuthenticationCallback(urlParams: URLSearchParams): {
    success: boolean
    token?: string
    refreshToken?: string
    error?: string
    message?: string
  } {
    try {
      // 检查是否有错误
      const error = urlParams.get('error')
      if (error) {
        const message = urlParams.get('message') || '认证失败'
        return {
          success: false,
          error,
          message: decodeURIComponent(message)
        }
      }

      // 获取令牌
      const token = urlParams.get('token')
      const refreshToken = urlParams.get('refreshToken')

      if (token && refreshToken) {
        // 保存令牌到localStorage
        localStorage.setItem('access_token', token)
        localStorage.setItem('refresh_token', refreshToken)

        return {
          success: true,
          token,
          refreshToken
        }
      } else {
        return {
          success: false,
          error: 'missing_tokens',
          message: '未收到认证令牌'
        }
      }

    } catch (error) {
      console.error('处理OAuth2回调失败:', error)
      return {
        success: false,
        error: 'callback_error',
        message: '处理认证回调时发生错误'
      }
    }
  }

  /**
   * 获取保存的重定向URI
   * 
   * @returns 重定向URI
   */
  static getSavedRedirectUri(): string | null {
    return localStorage.getItem('oauth2_redirect_uri')
  }

  /**
   * 清除保存的重定向URI
   */
  static clearSavedRedirectUri(): void {
    localStorage.removeItem('oauth2_redirect_uri')
  }
}

export default OAuth2Service
