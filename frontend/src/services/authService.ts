/**
 * 认证服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import { apiClient, ApiResponse } from './api'
import { User } from '@/types/user'

// 登录请求接口
export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

// 登录响应接口
export interface LoginResponse {
  user: User
  token: string
  refreshToken: string
  expiresIn: number
}

// 注册请求接口
export interface RegisterRequest {
  username: string
  email: string
  password: string
  firstName?: string
  lastName?: string
}

// 注册响应接口
export interface RegisterResponse {
  user: User
  token: string
  refreshToken: string
  expiresIn: number
}

// 刷新token响应接口
export interface RefreshTokenResponse {
  token: string
  refreshToken: string
  expiresIn: number
}

// 认证服务类
export class AuthService {
  private readonly baseUrl = '/api/v1/auth'

  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await apiClient.post<ApiResponse<LoginResponse>>(
      `${this.baseUrl}/login`,
      credentials
    )
    
    if (response.success) {
      // 保存token到localStorage
      localStorage.setItem('access_token', response.data.token)
      localStorage.setItem('refresh_token', response.data.refreshToken)
      localStorage.setItem('user', JSON.stringify(response.data.user))
      
      return response.data
    }
    
    throw new Error(response.message || '登录失败')
  }

  /**
   * 用户注册
   */
  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    const response = await apiClient.post<ApiResponse<RegisterResponse>>(
      `${this.baseUrl}/register`,
      userData
    )
    
    if (response.success) {
      // 保存token到localStorage
      localStorage.setItem('access_token', response.data.token)
      localStorage.setItem('refresh_token', response.data.refreshToken)
      localStorage.setItem('user', JSON.stringify(response.data.user))
      
      return response.data
    }
    
    throw new Error(response.message || '注册失败')
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/logout`)
    } catch (error) {
      console.warn('登出请求失败，但仍会清除本地数据', error)
    } finally {
      // 清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user')
    }
  }

  /**
   * 刷新访问token
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    const response = await apiClient.post<ApiResponse<RefreshTokenResponse>>(
      `${this.baseUrl}/refresh`,
      { refreshToken }
    )
    
    if (response.success) {
      // 更新token
      localStorage.setItem('access_token', response.data.token)
      localStorage.setItem('refresh_token', response.data.refreshToken)
      
      return response.data
    }
    
    throw new Error(response.message || '刷新token失败')
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<ApiResponse<User>>(`${this.baseUrl}/me`)
    
    if (response.success) {
      // 更新本地用户信息
      localStorage.setItem('user', JSON.stringify(response.data))
      return response.data
    }
    
    throw new Error(response.message || '获取用户信息失败')
  }

  /**
   * 修改密码
   */
  async changePassword(data: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<void> {
    const response = await apiClient.post<ApiResponse<void>>(
      `${this.baseUrl}/change-password`,
      data
    )
    
    if (!response.success) {
      throw new Error(response.message || '修改密码失败')
    }
  }

  /**
   * 忘记密码
   */
  async forgotPassword(email: string): Promise<void> {
    const response = await apiClient.post<ApiResponse<void>>(
      `${this.baseUrl}/forgot-password`,
      { email }
    )
    
    if (!response.success) {
      throw new Error(response.message || '发送重置密码邮件失败')
    }
  }

  /**
   * 重置密码
   */
  async resetPassword(data: {
    token: string
    newPassword: string
    confirmPassword: string
  }): Promise<void> {
    const response = await apiClient.post<ApiResponse<void>>(
      `${this.baseUrl}/reset-password`,
      data
    )
    
    if (!response.success) {
      throw new Error(response.message || '重置密码失败')
    }
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(token: string): Promise<void> {
    const response = await apiClient.post<ApiResponse<void>>(
      `${this.baseUrl}/verify-email`,
      { token }
    )
    
    if (!response.success) {
      throw new Error(response.message || '邮箱验证失败')
    }
  }

  /**
   * 重新发送验证邮件
   */
  async resendVerificationEmail(): Promise<void> {
    const response = await apiClient.post<ApiResponse<void>>(
      `${this.baseUrl}/resend-verification`
    )
    
    if (!response.success) {
      throw new Error(response.message || '发送验证邮件失败')
    }
  }

  /**
   * 检查token是否有效
   */
  async validateToken(): Promise<boolean> {
    try {
      await this.getCurrentUser()
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 获取本地存储的token
   */
  getStoredToken(): string | null {
    return localStorage.getItem('access_token')
  }

  /**
   * 获取本地存储的用户信息
   */
  getStoredUser(): User | null {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch (error) {
        console.error('解析本地用户信息失败', error)
        return null
      }
    }
    return null
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    const token = this.getStoredToken()
    const user = this.getStoredUser()
    return !!(token && user)
  }

  /**
   * 检查用户是否有指定权限
   */
  hasPermission(permission: string): boolean {
    const user = this.getStoredUser()
    if (!user) return false
    
    return user.permissions.some(p => p.name === permission)
  }

  /**
   * 检查用户是否有指定角色
   */
  hasRole(roleName: string): boolean {
    const user = this.getStoredUser()
    if (!user) return false
    
    return user.roles.some(role => role.name === roleName)
  }
}

// 导出单例实例
export const authService = new AuthService()
