/**
 * OAuth2服务测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import OAuth2Service from '../oauth2Service'
import { apiClient } from '../api'

// Mock API client
vi.mock('../api', () => ({
  apiClient: {
    get: vi.fn()
  }
}))

// Mock window.location
const mockLocation = {
  origin: 'http://localhost:3000',
  href: ''
}

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true
})

describe('OAuth2Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocation.href = ''
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getProviders', () => {
    it('应该成功获取OAuth2提供商列表', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            providers: {
              google: {
                clientName: 'Google',
                authorizationUri: '/oauth2/authorization/google'
              },
              github: {
                clientName: 'GitHub',
                authorizationUri: '/oauth2/authorization/github'
              }
            },
            redirectUris: ['http://localhost:3000/oauth2/redirect']
          }
        }
      }

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse)

      const result = await OAuth2Service.getProviders()

      expect(apiClient.get).toHaveBeenCalledWith('/api/v1/oauth2/providers')
      expect(result).toEqual(mockResponse.data.data)
      expect(result.providers).toHaveProperty('google')
      expect(result.providers).toHaveProperty('github')
    })

    it('应该处理API错误', async () => {
      const mockError = new Error('网络错误')
      vi.mocked(apiClient.get).mockRejectedValue(mockError)

      await expect(OAuth2Service.getProviders()).rejects.toThrow('网络错误')
    })
  })

  describe('checkStatus', () => {
    it('应该成功检查OAuth2认证状态', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            authenticated: true,
            principal: '<EMAIL>'
          }
        }
      }

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse)

      const result = await OAuth2Service.checkStatus()

      expect(apiClient.get).toHaveBeenCalledWith('/api/v1/oauth2/status')
      expect(result).toEqual(mockResponse.data.data)
      expect(result.authenticated).toBe(true)
      expect(result.principal).toBe('<EMAIL>')
    })
  })

  describe('getAuthorizationUrl', () => {
    it('应该成功获取授权URL', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            authorizationUrl: '/oauth2/authorization/google',
            provider: 'google'
          }
        }
      }

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse)

      const result = await OAuth2Service.getAuthorizationUrl('google')

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/v1/oauth2/authorization-url/google',
        { params: {} }
      )
      expect(result).toEqual(mockResponse.data.data)
    })

    it('应该支持重定向URI参数', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            authorizationUrl: '/oauth2/authorization/google',
            provider: 'google'
          }
        }
      }

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse)

      const redirectUri = 'http://localhost:3000/custom/redirect'
      await OAuth2Service.getAuthorizationUrl('google', redirectUri)

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/v1/oauth2/authorization-url/google',
        { params: { redirect_uri: redirectUri } }
      )
    })
  })

  describe('startAuthentication', () => {
    it('应该启动OAuth2认证流程', async () => {
      const provider = 'google'
      const redirectUri = 'http://localhost:3000/oauth2/redirect'

      await OAuth2Service.startAuthentication(provider, redirectUri)

      expect(localStorageMock.setItem).toHaveBeenCalledWith('oauth2_redirect_uri', redirectUri)
      expect(mockLocation.href).toBe('http://localhost:3000/oauth2/authorization/google?redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Foauth2%2Fredirect')
    })

    it('应该支持不带重定向URI的认证', async () => {
      const provider = 'github'

      await OAuth2Service.startAuthentication(provider)

      expect(localStorageMock.setItem).not.toHaveBeenCalled()
      expect(mockLocation.href).toBe('http://localhost:3000/oauth2/authorization/github')
    })

    it('应该处理认证启动错误', async () => {
      // 模拟URL构建错误
      const originalURLSearchParams = global.URLSearchParams
      global.URLSearchParams = vi.fn().mockImplementation(() => {
        throw new Error('URL构建失败')
      })

      await expect(OAuth2Service.startAuthentication('google')).rejects.toThrow('URL构建失败')

      global.URLSearchParams = originalURLSearchParams
    })
  })

  describe('handleAuthenticationCallback', () => {
    it('应该成功处理认证回调', () => {
      const urlParams = new URLSearchParams('token=access_token_123&refreshToken=refresh_token_456')

      const result = OAuth2Service.handleAuthenticationCallback(urlParams)

      expect(result.success).toBe(true)
      expect(result.token).toBe('access_token_123')
      expect(result.refreshToken).toBe('refresh_token_456')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('access_token', 'access_token_123')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('refresh_token', 'refresh_token_456')
    })

    it('应该处理认证错误', () => {
      const urlParams = new URLSearchParams('error=access_denied&message=%E7%94%A8%E6%88%B7%E6%8B%92%E7%BB%9D%E6%8E%88%E6%9D%83')

      const result = OAuth2Service.handleAuthenticationCallback(urlParams)

      expect(result.success).toBe(false)
      expect(result.error).toBe('access_denied')
      expect(result.message).toBe('用户拒绝授权')
      expect(localStorageMock.setItem).not.toHaveBeenCalled()
    })

    it('应该处理缺少令牌的情况', () => {
      const urlParams = new URLSearchParams('token=access_token_123')

      const result = OAuth2Service.handleAuthenticationCallback(urlParams)

      expect(result.success).toBe(false)
      expect(result.error).toBe('missing_tokens')
      expect(result.message).toBe('未收到认证令牌')
    })

    it('应该处理回调处理异常', () => {
      // 模拟URLSearchParams.get方法抛出异常
      const urlParams = {
        get: vi.fn().mockImplementation(() => {
          throw new Error('参数解析失败')
        })
      } as any

      const result = OAuth2Service.handleAuthenticationCallback(urlParams)

      expect(result.success).toBe(false)
      expect(result.error).toBe('callback_error')
      expect(result.message).toBe('处理认证回调时发生错误')
    })
  })

  describe('getSavedRedirectUri', () => {
    it('应该获取保存的重定向URI', () => {
      const savedUri = 'http://localhost:3000/dashboard'
      localStorageMock.getItem.mockReturnValue(savedUri)

      const result = OAuth2Service.getSavedRedirectUri()

      expect(localStorageMock.getItem).toHaveBeenCalledWith('oauth2_redirect_uri')
      expect(result).toBe(savedUri)
    })

    it('应该处理没有保存URI的情况', () => {
      localStorageMock.getItem.mockReturnValue(null)

      const result = OAuth2Service.getSavedRedirectUri()

      expect(result).toBeNull()
    })
  })

  describe('clearSavedRedirectUri', () => {
    it('应该清除保存的重定向URI', () => {
      OAuth2Service.clearSavedRedirectUri()

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('oauth2_redirect_uri')
    })
  })
})
