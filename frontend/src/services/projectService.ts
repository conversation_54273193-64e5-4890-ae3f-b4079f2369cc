/**
 * 项目服务
 */

import { apiClient } from './api'
import { Project, ProjectCreateRequest, ProjectUpdateRequest } from '@/types/project'

export class ProjectService {
  private readonly baseUrl = '/api/v1/projects'

  async getProjects(params: any) {
    return apiClient.get(`${this.baseUrl}`, { params })
  }

  async getProjectById(id: string): Promise<Project> {
    return apiClient.get(`${this.baseUrl}/${id}`)
  }

  async createProject(data: ProjectCreateRequest): Promise<Project> {
    return apiClient.post(this.baseUrl, data)
  }

  async updateProject(id: string, data: ProjectUpdateRequest): Promise<Project> {
    return apiClient.put(`${this.baseUrl}/${id}`, data)
  }

  async deleteProject(id: string): Promise<void> {
    return apiClient.delete(`${this.baseUrl}/${id}`)
  }
}

export const projectService = new ProjectService()
