/**
 * OAuth2登录组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */

import React, { useState, useEffect } from 'react'
import { Button, Space, Divider, message, Spin } from 'antd'
import { GoogleOutlined, Gith<PERSON>Outlined, UserOutlined } from '@ant-design/icons'
import OAuth2Service, { OAuth2Provider } from '@/services/oauth2Service'

interface OAuth2LoginProps {
  onSuccess?: (token: string, refreshToken: string) => void
  onError?: (error: string, message: string) => void
  redirectUri?: string
  className?: string
}

/**
 * OAuth2登录组件
 */
const OAuth2Login: React.FC<OAuth2LoginProps> = ({
  onSuccess,
  onError,
  redirectUri = `${window.location.origin}/oauth2/redirect`,
  className
}) => {
  const [providers, setProviders] = useState<Record<string, OAuth2Provider>>({})
  const [loading, setLoading] = useState(false)
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null)

  // 获取OAuth2提供商列表
  useEffect(() => {
    const fetchProviders = async () => {
      try {
        setLoading(true)
        const data = await OAuth2Service.getProviders()
        setProviders(data.providers)
      } catch (error) {
        console.error('获取OAuth2提供商失败:', error)
        message.error('获取登录选项失败')
      } finally {
        setLoading(false)
      }
    }

    fetchProviders()
  }, [])

  /**
   * 处理OAuth2登录
   * 
   * @param provider 提供商ID
   */
  const handleOAuth2Login = async (provider: string) => {
    try {
      setLoadingProvider(provider)
      await OAuth2Service.startAuthentication(provider, redirectUri)
    } catch (error) {
      console.error(`${provider} OAuth2登录失败:`, error)
      message.error(`${provider} 登录失败`)
      setLoadingProvider(null)
    }
  }

  /**
   * 获取提供商图标
   * 
   * @param provider 提供商ID
   * @returns 图标组件
   */
  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'google':
        return <GoogleOutlined />
      case 'github':
        return <GithubOutlined />
      default:
        return <UserOutlined />
    }
  }

  /**
   * 获取提供商按钮样式
   * 
   * @param provider 提供商ID
   * @returns 按钮样式
   */
  const getProviderButtonStyle = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'google':
        return {
          backgroundColor: '#db4437',
          borderColor: '#db4437',
          color: 'white'
        }
      case 'github':
        return {
          backgroundColor: '#333',
          borderColor: '#333',
          color: 'white'
        }
      default:
        return {
          backgroundColor: '#1890ff',
          borderColor: '#1890ff',
          color: 'white'
        }
    }
  }

  if (loading) {
    return (
      <div className={`text-center ${className || ''}`}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载登录选项...</div>
      </div>
    )
  }

  if (Object.keys(providers).length === 0) {
    return null
  }

  return (
    <div className={className}>
      <Divider>
        <span style={{ color: '#999', fontSize: '14px' }}>或使用第三方账号登录</span>
      </Divider>
      
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {Object.entries(providers).map(([providerId, provider]) => (
          <Button
            key={providerId}
            type="primary"
            size="large"
            block
            icon={getProviderIcon(providerId)}
            loading={loadingProvider === providerId}
            onClick={() => handleOAuth2Login(providerId)}
            style={getProviderButtonStyle(providerId)}
          >
            使用 {provider.clientName} 登录
          </Button>
        ))}
      </Space>
    </div>
  )
}

export default OAuth2Login
