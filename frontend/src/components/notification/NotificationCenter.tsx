/**
 * 通知中心组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState, useEffect } from 'react'
import {
  Drawer,
  List,
  Badge,
  Button,
  Typography,
  Empty,
  Spin,
  Tag,
  Avatar,
  Dropdown,
  Space,
  Tabs,
  Switch,
  message,
} from 'antd'
import {
  BellOutlined,
  CheckOutlined,
  DeleteOutlined,
  SettingOutlined,
  MailOutlined,
  MessageOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  MoreOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { TabPane } = Tabs

// 通知接口
interface Notification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  content: string
  status: 'pending' | 'sent' | 'read'
  createdAt: string
  readAt?: string
  data?: any
}

// 通知偏好设置接口
interface NotificationPreferences {
  emailEnabled: boolean
  smsEnabled: boolean
  inAppEnabled: boolean
  websocketEnabled: boolean
  quietHoursStart?: string
  quietHoursEnd?: string
}

interface NotificationCenterProps {
  visible: boolean
  onClose: () => void
  userId: string
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  visible,
  onClose,
  userId,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('all')
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    emailEnabled: true,
    smsEnabled: false,
    inAppEnabled: true,
    websocketEnabled: true,
  })

  // 模拟通知数据
  const mockNotifications: Notification[] = [
    {
      id: '1',
      type: 'info',
      title: '项目进度更新',
      content: 'AI客服系统项目进度已更新至75%',
      status: 'sent',
      createdAt: '2025-08-16T10:30:00Z',
      data: { projectId: '1', progress: 75 },
    },
    {
      id: '2',
      type: 'warning',
      title: '任务即将到期',
      content: '您有3个任务将在24小时内到期，请及时处理',
      status: 'sent',
      createdAt: '2025-08-16T09:15:00Z',
      data: { taskCount: 3 },
    },
    {
      id: '3',
      type: 'success',
      title: '代码审查通过',
      content: '您提交的代码已通过审查，可以合并到主分支',
      status: 'read',
      createdAt: '2025-08-16T08:45:00Z',
      readAt: '2025-08-16T09:00:00Z',
      data: { prId: '123' },
    },
    {
      id: '4',
      type: 'error',
      title: '构建失败',
      content: '前端项目构建失败，请检查代码错误',
      status: 'sent',
      createdAt: '2025-08-16T08:00:00Z',
      data: { buildId: '456' },
    },
  ]

  useEffect(() => {
    if (visible) {
      loadNotifications()
      loadPreferences()
    }
  }, [visible, userId])

  // 加载通知列表
  const loadNotifications = async () => {
    setLoading(true)
    try {
      // TODO: 调用API获取通知
      setTimeout(() => {
        setNotifications(mockNotifications)
        setLoading(false)
      }, 1000)
    } catch (error) {
      message.error('加载通知失败')
      setLoading(false)
    }
  }

  // 加载偏好设置
  const loadPreferences = async () => {
    try {
      // TODO: 调用API获取偏好设置
      console.log('加载偏好设置')
    } catch (error) {
      message.error('加载偏好设置失败')
    }
  }

  // 获取通知图标
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      default:
        return <InfoCircleOutlined />
    }
  }

  // 标记为已读
  const markAsRead = async (notificationId: string) => {
    try {
      // TODO: 调用API标记已读
      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId
            ? { ...n, status: 'read' as const, readAt: new Date().toISOString() }
            : n
        )
      )
      message.success('已标记为已读')
    } catch (error) {
      message.error('标记已读失败')
    }
  }

  // 删除通知
  const deleteNotification = async (notificationId: string) => {
    try {
      // TODO: 调用API删除通知
      setNotifications(prev => prev.filter(n => n.id !== notificationId))
      message.success('通知已删除')
    } catch (error) {
      message.error('删除通知失败')
    }
  }

  // 全部标记为已读
  const markAllAsRead = async () => {
    try {
      // TODO: 调用API全部标记已读
      setNotifications(prev =>
        prev.map(n => ({
          ...n,
          status: 'read' as const,
          readAt: n.readAt || new Date().toISOString(),
        }))
      )
      message.success('全部已标记为已读')
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 更新偏好设置
  const updatePreferences = async (key: string, value: boolean) => {
    try {
      // TODO: 调用API更新偏好设置
      setPreferences(prev => ({ ...prev, [key]: value }))
      message.success('设置已更新')
    } catch (error) {
      message.error('更新设置失败')
    }
  }

  // 过滤通知
  const filteredNotifications = notifications.filter(notification => {
    switch (activeTab) {
      case 'unread':
        return notification.status !== 'read'
      case 'read':
        return notification.status === 'read'
      default:
        return true
    }
  })

  const unreadCount = notifications.filter(n => n.status !== 'read').length

  return (
    <Drawer
      title={
        <div className="flex justify-between items-center">
          <Space>
            <BellOutlined />
            <span>通知中心</span>
            {unreadCount > 0 && (
              <Badge count={unreadCount} size="small" />
            )}
          </Space>
          <Space>
            <Button
              type="text"
              size="small"
              onClick={markAllAsRead}
              disabled={unreadCount === 0}
            >
              全部已读
            </Button>
          </Space>
        </div>
      }
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={`全部 (${notifications.length})`} key="all">
          <NotificationList
            notifications={filteredNotifications}
            loading={loading}
            onMarkAsRead={markAsRead}
            onDelete={deleteNotification}
            getIcon={getNotificationIcon}
          />
        </TabPane>
        
        <TabPane tab={`未读 (${unreadCount})`} key="unread">
          <NotificationList
            notifications={filteredNotifications}
            loading={loading}
            onMarkAsRead={markAsRead}
            onDelete={deleteNotification}
            getIcon={getNotificationIcon}
          />
        </TabPane>
        
        <TabPane tab="设置" key="settings">
          <div className="space-y-4">
            <Title level={5}>通知偏好</Title>
            
            <div className="flex justify-between items-center">
              <Space>
                <MailOutlined />
                <span>邮件通知</span>
              </Space>
              <Switch
                checked={preferences.emailEnabled}
                onChange={(checked) => updatePreferences('emailEnabled', checked)}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <Space>
                <MessageOutlined />
                <span>短信通知</span>
              </Space>
              <Switch
                checked={preferences.smsEnabled}
                onChange={(checked) => updatePreferences('smsEnabled', checked)}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <Space>
                <BellOutlined />
                <span>应用内通知</span>
              </Space>
              <Switch
                checked={preferences.inAppEnabled}
                onChange={(checked) => updatePreferences('inAppEnabled', checked)}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <Space>
                <SettingOutlined />
                <span>实时通知</span>
              </Space>
              <Switch
                checked={preferences.websocketEnabled}
                onChange={(checked) => updatePreferences('websocketEnabled', checked)}
              />
            </div>
          </div>
        </TabPane>
      </Tabs>
    </Drawer>
  )
}

// 通知列表组件
interface NotificationListProps {
  notifications: Notification[]
  loading: boolean
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
  getIcon: (type: string) => React.ReactNode
}

const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  loading,
  onMarkAsRead,
  onDelete,
  getIcon,
}) => {
  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Spin size="large" />
      </div>
    )
  }

  if (notifications.length === 0) {
    return (
      <Empty
        description="暂无通知"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    )
  }

  return (
    <List
      dataSource={notifications}
      renderItem={(notification) => (
        <List.Item
          className={`${notification.status === 'read' ? 'opacity-60' : ''}`}
          actions={[
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'read',
                    icon: <CheckOutlined />,
                    label: '标记已读',
                    disabled: notification.status === 'read',
                    onClick: () => onMarkAsRead(notification.id),
                  },
                  {
                    key: 'delete',
                    icon: <DeleteOutlined />,
                    label: '删除',
                    danger: true,
                    onClick: () => onDelete(notification.id),
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button type="text" icon={<MoreOutlined />} size="small" />
            </Dropdown>,
          ]}
        >
          <List.Item.Meta
            avatar={<Avatar icon={getIcon(notification.type)} />}
            title={
              <div className="flex justify-between items-start">
                <span className={notification.status === 'read' ? 'line-through' : ''}>
                  {notification.title}
                </span>
                {notification.status !== 'read' && (
                  <Badge status="processing" />
                )}
              </div>
            }
            description={
              <div>
                <div className="mb-2">{notification.content}</div>
                <div className="text-xs text-gray-400">
                  {new Date(notification.createdAt).toLocaleString('zh-CN')}
                  {notification.readAt && (
                    <span className="ml-2">
                      · 已读于 {new Date(notification.readAt).toLocaleString('zh-CN')}
                    </span>
                  )}
                </div>
              </div>
            }
          />
        </List.Item>
      )}
    />
  )
}

export default NotificationCenter
