/**
 * 主布局组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState } from 'react'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Button,
  Badge,
  Space,
  Breadcrumb,
  theme,
} from 'antd'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ProjectOutlined,
  CheckSquareOutlined,
  TeamOutlined,
  BarChartOutlined,
  FileTextOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  QuestionCircleOutlined,
  BranchesOutlined,
  CodeOutlined,
  NotificationOutlined,
  ApiOutlined,
} from '@ant-design/icons'

import { RootState, AppDispatch } from '@/store'
import { logout } from '@/store/slices/authSlice'
import { toggleSidebar } from '@/store/slices/uiSlice'

const { Head<PERSON>, Sider, Content } = Layout

// 菜单项配置
const menuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: '/projects',
    icon: <ProjectOutlined />,
    label: '项目管理',
  },
  {
    key: '/tasks',
    icon: <CheckSquareOutlined />,
    label: '任务管理',
  },
  {
    key: '/team',
    icon: <TeamOutlined />,
    label: '团队管理',
  },
  {
    key: '/git',
    icon: <BranchesOutlined />,
    label: 'Git集成',
    children: [
      {
        key: '/git',
        icon: <BranchesOutlined />,
        label: '仓库管理',
      },
      {
        key: '/git/analytics',
        icon: <CodeOutlined />,
        label: '代码分析',
      },
    ],
  },
  {
    key: '/notifications',
    icon: <NotificationOutlined />,
    label: '通知中心',
  },
  {
    key: '/integrations',
    icon: <ApiOutlined />,
    label: '第三方集成',
  },
  {
    key: '/analytics',
    icon: <BarChartOutlined />,
    label: 'AI分析',
  },
  {
    key: '/reports',
    icon: <FileTextOutlined />,
    label: '报表中心',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '系统设置',
  },
]

const MainLayout: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const dispatch = useDispatch<AppDispatch>()
  
  const { user } = useSelector((state: RootState) => state.auth)
  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui)
  
  const {
    token: { colorBgContainer },
  } = theme.useToken()

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate('/settings/profile')
        break
      case 'settings':
        navigate('/settings')
        break
      case 'help':
        window.open('/help', '_blank')
        break
      case 'logout':
        dispatch(logout())
        break
    }
  }

  // 切换侧边栏
  const toggleSidebarCollapse = () => {
    dispatch(toggleSidebar())
  }

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ]

  // 生成面包屑
  const generateBreadcrumb = () => {
    const pathSnippets = location.pathname.split('/').filter(i => i)
    const breadcrumbItems = [
      {
        title: '首页',
        href: '/',
      },
    ]

    pathSnippets.forEach((snippet, index) => {
      const url = `/${pathSnippets.slice(0, index + 1).join('/')}`
      const menuItem = menuItems.find(item => item.key === url)
      
      breadcrumbItems.push({
        title: menuItem?.label || snippet,
        href: url,
      })
    })

    return breadcrumbItems
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={sidebarCollapsed}
        width={240}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        {/* Logo */}
        <div className="flex items-center justify-center h-16 bg-blue-600">
          <div className="text-white font-bold text-lg">
            {sidebarCollapsed ? 'AI' : 'AI项目管理'}
          </div>
        </div>

        {/* 菜单 */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>

      {/* 主内容区 */}
      <Layout style={{ marginLeft: sidebarCollapsed ? 80 : 240 }}>
        {/* 顶部导航 */}
        <Header
          style={{
            padding: '0 24px',
            background: colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <div className="flex items-center">
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={toggleSidebarCollapse}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
              }}
            />
            
            <Breadcrumb
              items={generateBreadcrumb()}
              style={{ marginLeft: 16 }}
            />
          </div>

          <Space size="middle">
            {/* 通知 */}
            <Badge count={5} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                size="large"
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
              arrow
            >
              <Space className="cursor-pointer">
                <Avatar
                  src={user?.avatar}
                  icon={<UserOutlined />}
                  size="default"
                />
                <span className="text-gray-700">
                  {user?.firstName || user?.username}
                </span>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content
          style={{
            margin: '24px',
            padding: '24px',
            background: colorBgContainer,
            borderRadius: '8px',
            minHeight: 'calc(100vh - 112px)',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  )
}

export default MainLayout
