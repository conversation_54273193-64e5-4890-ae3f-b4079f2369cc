/**
 * 移动端适配组件
 * 提供移动端优化的用户界面和交互体验
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-28
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Drawer,
  Button,
  Menu,
  Layout,
  Space,
  Typography,
  Card,
  List,
  Avatar,
  Badge,
  FloatButton,
  Affix,
  Tabs,
  Collapse,
  Grid,
  Dropdown,
  MenuProps
} from 'antd';
import {
  MenuOutlined,
  BellOutlined,
  UserOutlined,
  SearchOutlined,
  PlusOutlined,
  MoreOutlined,
  ArrowLeftOutlined,
  FilterOutlined,
  SortAscendingOutlined
} from '@ant-design/icons';

const { Header, Content } = Layout;
const { Title, Text } = Typography;
const { useBreakpoint } = Grid;

// ==================== 移动端导航栏 ====================

interface MobileHeaderProps {
  title: string;
  showBack?: boolean;
  onBack?: () => void;
  rightActions?: React.ReactNode;
  onMenuClick?: () => void;
  notificationCount?: number;
  onNotificationClick?: () => void;
}

export const MobileHeader: React.FC<MobileHeaderProps> = ({
  title,
  showBack = false,
  onBack,
  rightActions,
  onMenuClick,
  notificationCount = 0,
  onNotificationClick
}) => {
  return (
    <Header className="mobile-header" style={{ 
      padding: '0 16px', 
      background: '#fff', 
      borderBottom: '1px solid #f0f0f0',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: '56px'
    }}>
      <div className="header-left" style={{ display: 'flex', alignItems: 'center' }}>
        {showBack ? (
          <Button 
            type="text" 
            icon={<ArrowLeftOutlined />} 
            onClick={onBack}
            style={{ marginRight: '8px' }}
          />
        ) : (
          <Button 
            type="text" 
            icon={<MenuOutlined />} 
            onClick={onMenuClick}
            style={{ marginRight: '8px' }}
          />
        )}
        <Title level={5} style={{ margin: 0, fontSize: '16px' }}>
          {title}
        </Title>
      </div>
      
      <div className="header-right" style={{ display: 'flex', alignItems: 'center' }}>
        {rightActions}
        <Badge count={notificationCount} size="small">
          <Button 
            type="text" 
            icon={<BellOutlined />} 
            onClick={onNotificationClick}
          />
        </Badge>
      </div>
    </Header>
  );
};

// ==================== 移动端侧边栏 ====================

interface MobileSidebarProps {
  visible: boolean;
  onClose: () => void;
  menuItems: MenuProps['items'];
  userInfo?: {
    name: string;
    avatar?: string;
    role?: string;
  };
  onMenuSelect?: (key: string) => void;
}

export const MobileSidebar: React.FC<MobileSidebarProps> = ({
  visible,
  onClose,
  menuItems,
  userInfo,
  onMenuSelect
}) => {
  return (
    <Drawer
      title={null}
      placement="left"
      onClose={onClose}
      open={visible}
      width={280}
      bodyStyle={{ padding: 0 }}
      headerStyle={{ display: 'none' }}
    >
      {/* 用户信息区域 */}
      {userInfo && (
        <div style={{ 
          padding: '24px 16px', 
          background: '#001529', 
          color: '#fff' 
        }}>
          <Space>
            <Avatar 
              size={48} 
              src={userInfo.avatar} 
              icon={<UserOutlined />} 
            />
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                {userInfo.name}
              </div>
              {userInfo.role && (
                <div style={{ fontSize: '12px', opacity: 0.8 }}>
                  {userInfo.role}
                </div>
              )}
            </div>
          </Space>
        </div>
      )}
      
      {/* 菜单区域 */}
      <Menu
        mode="inline"
        items={menuItems}
        style={{ border: 'none' }}
        onSelect={({ key }) => {
          onMenuSelect?.(key);
          onClose();
        }}
      />
    </Drawer>
  );
};

// ==================== 移动端卡片列表 ====================

interface MobileCardListProps<T> {
  data: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  emptyText?: string;
  searchable?: boolean;
  onSearch?: (value: string) => void;
  filterable?: boolean;
  onFilter?: () => void;
  sortable?: boolean;
  onSort?: () => void;
}

export function MobileCardList<T>({
  data,
  renderItem,
  loading = false,
  onLoadMore,
  hasMore = false,
  emptyText = '暂无数据',
  searchable = false,
  onSearch,
  filterable = false,
  onFilter,
  sortable = false,
  onSort
}: MobileCardListProps<T>) {
  const [searchValue, setSearchValue] = useState('');

  const handleSearch = useCallback((value: string) => {
    setSearchValue(value);
    onSearch?.(value);
  }, [onSearch]);

  return (
    <div className="mobile-card-list">
      {/* 搜索和筛选栏 */}
      {(searchable || filterable || sortable) && (
        <div style={{ 
          padding: '12px 16px', 
          background: '#fff', 
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          gap: '8px'
        }}>
          {searchable && (
            <Button 
              icon={<SearchOutlined />} 
              onClick={() => {
                // 触发搜索模态框或跳转到搜索页面
                const value = prompt('请输入搜索关键词');
                if (value !== null) {
                  handleSearch(value);
                }
              }}
              style={{ flex: 1 }}
            >
              搜索
            </Button>
          )}
          
          {filterable && (
            <Button icon={<FilterOutlined />} onClick={onFilter}>
              筛选
            </Button>
          )}
          
          {sortable && (
            <Button icon={<SortAscendingOutlined />} onClick={onSort}>
              排序
            </Button>
          )}
        </div>
      )}
      
      {/* 列表内容 */}
      <List
        dataSource={data}
        renderItem={(item, index) => (
          <List.Item style={{ padding: '12px 16px', border: 'none' }}>
            {renderItem(item, index)}
          </List.Item>
        )}
        loading={loading}
        locale={{ emptyText }}
        loadMore={
          hasMore && onLoadMore ? (
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <Button onClick={onLoadMore} loading={loading}>
                加载更多
              </Button>
            </div>
          ) : null
        }
      />
    </div>
  );
}

// ==================== 移动端标签页 ====================

interface MobileTabsProps {
  items: Array<{
    key: string;
    label: string;
    children: React.ReactNode;
    badge?: number;
  }>;
  activeKey?: string;
  onChange?: (key: string) => void;
  sticky?: boolean;
}

export const MobileTabs: React.FC<MobileTabsProps> = ({
  items,
  activeKey,
  onChange,
  sticky = true
}) => {
  const tabItems = items.map(item => ({
    key: item.key,
    label: item.badge ? (
      <Badge count={item.badge} size="small">
        {item.label}
      </Badge>
    ) : item.label,
    children: item.children
  }));

  const TabsComponent = (
    <Tabs
      activeKey={activeKey}
      onChange={onChange}
      items={tabItems}
      size="small"
      style={{ background: '#fff' }}
      tabBarStyle={{ 
        margin: 0, 
        padding: '0 16px',
        borderBottom: '1px solid #f0f0f0'
      }}
    />
  );

  return sticky ? (
    <Affix offsetTop={56}> {/* Header高度 */}
      {TabsComponent}
    </Affix>
  ) : TabsComponent;
};

// ==================== 移动端折叠面板 ====================

interface MobileCollapseProps {
  items: Array<{
    key: string;
    label: string;
    children: React.ReactNode;
    extra?: React.ReactNode;
  }>;
  accordion?: boolean;
  defaultActiveKey?: string | string[];
}

export const MobileCollapse: React.FC<MobileCollapseProps> = ({
  items,
  accordion = false,
  defaultActiveKey
}) => {
  const collapseItems = items.map(item => ({
    key: item.key,
    label: (
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <span>{item.label}</span>
        {item.extra}
      </div>
    ),
    children: item.children
  }));

  return (
    <Collapse
      items={collapseItems}
      accordion={accordion}
      defaultActiveKey={defaultActiveKey}
      ghost
      style={{ background: '#fff' }}
    />
  );
};

// ==================== 移动端浮动操作按钮 ====================

interface MobileFloatingActionProps {
  primaryAction?: {
    icon: React.ReactNode;
    onClick: () => void;
    tooltip?: string;
  };
  secondaryActions?: Array<{
    icon: React.ReactNode;
    label: string;
    onClick: () => void;
  }>;
}

export const MobileFloatingAction: React.FC<MobileFloatingActionProps> = ({
  primaryAction,
  secondaryActions = []
}) => {
  if (!primaryAction && secondaryActions.length === 0) {
    return null;
  }

  if (secondaryActions.length === 0) {
    return (
      <FloatButton
        icon={primaryAction?.icon}
        onClick={primaryAction?.onClick}
        tooltip={primaryAction?.tooltip}
        type="primary"
        style={{ right: 16, bottom: 16 }}
      />
    );
  }

  return (
    <FloatButton.Group
      trigger="click"
      type="primary"
      style={{ right: 16, bottom: 16 }}
      icon={primaryAction?.icon || <PlusOutlined />}
      onClick={primaryAction?.onClick}
    >
      {secondaryActions.map((action, index) => (
        <FloatButton
          key={index}
          icon={action.icon}
          tooltip={action.label}
          onClick={action.onClick}
        />
      ))}
    </FloatButton.Group>
  );
};

// ==================== 移动端底部导航 ====================

interface MobileBottomNavProps {
  items: Array<{
    key: string;
    icon: React.ReactNode;
    label: string;
    badge?: number;
  }>;
  activeKey?: string;
  onChange?: (key: string) => void;
}

export const MobileBottomNav: React.FC<MobileBottomNavProps> = ({
  items,
  activeKey,
  onChange
}) => {
  return (
    <div style={{
      position: 'fixed',
      bottom: 0,
      left: 0,
      right: 0,
      background: '#fff',
      borderTop: '1px solid #f0f0f0',
      display: 'flex',
      zIndex: 1000,
      paddingBottom: 'env(safe-area-inset-bottom)' // iOS安全区域适配
    }}>
      {items.map(item => (
        <div
          key={item.key}
          style={{
            flex: 1,
            textAlign: 'center',
            padding: '8px 4px',
            cursor: 'pointer',
            color: activeKey === item.key ? '#1890ff' : '#666'
          }}
          onClick={() => onChange?.(item.key)}
        >
          <div style={{ fontSize: '20px', marginBottom: '2px' }}>
            {item.badge ? (
              <Badge count={item.badge} size="small">
                {item.icon}
              </Badge>
            ) : item.icon}
          </div>
          <div style={{ fontSize: '10px' }}>
            {item.label}
          </div>
        </div>
      ))}
    </div>
  );
};

// ==================== 移动端适配Hook ====================

export const useMobileAdaptation = () => {
  const screens = useBreakpoint();
  const isMobile = !screens.md; // 小于md断点认为是移动端

  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    handleOrientationChange();
    window.addEventListener('resize', handleOrientationChange);
    
    return () => window.removeEventListener('resize', handleOrientationChange);
  }, []);

  return {
    isMobile,
    isTablet: screens.md && !screens.lg,
    isDesktop: screens.lg,
    orientation,
    screens
  };
};

// ==================== 移动端手势支持 ====================

interface TouchGestureProps {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  children: React.ReactNode;
  threshold?: number;
}

export const TouchGesture: React.FC<TouchGestureProps> = ({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  children,
  threshold = 50
}) => {
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);

  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    setTouchStart({ x: touch.clientX, y: touch.clientY });
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!touchStart) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // 水平滑动
      if (Math.abs(deltaX) > threshold) {
        if (deltaX > 0) {
          onSwipeRight?.();
        } else {
          onSwipeLeft?.();
        }
      }
    } else {
      // 垂直滑动
      if (Math.abs(deltaY) > threshold) {
        if (deltaY > 0) {
          onSwipeDown?.();
        } else {
          onSwipeUp?.();
        }
      }
    }

    setTouchStart(null);
  };

  return (
    <div
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      style={{ touchAction: 'pan-y' }} // 允许垂直滚动
    >
      {children}
    </div>
  );
};

// ==================== 导出所有组件 ====================

export const MobileComponents = {
  MobileHeader,
  MobileSidebar,
  MobileCardList,
  MobileTabs,
  MobileCollapse,
  MobileFloatingAction,
  MobileBottomNav,
  TouchGesture,
  useMobileAdaptation
};

export default MobileComponents;
