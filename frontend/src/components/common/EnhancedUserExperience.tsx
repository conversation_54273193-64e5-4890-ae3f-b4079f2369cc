/**
 * 增强用户体验组件库
 * 提供统一的用户交互体验组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-28
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Button,
  Modal,
  message,
  notification,
  Progress,
  Skeleton,
  Empty,
  Result,
  Tooltip,
  Popconfirm,
  Drawer,
  FloatButton,
  Tour,
  TourProps,
  Space,
  Typography,
  Alert,
  Spin
} from 'antd';
import {
  QuestionCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  LoadingOutlined,
  ArrowUpOutlined,
  BulbOutlined,
  CustomerServiceOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

// ==================== 智能加载组件 ====================

interface SmartLoadingProps {
  loading: boolean;
  error?: string | null;
  empty?: boolean;
  children: React.ReactNode;
  skeleton?: boolean;
  emptyDescription?: string;
  retryAction?: () => void;
}

export const SmartLoading: React.FC<SmartLoadingProps> = ({
  loading,
  error,
  empty,
  children,
  skeleton = true,
  emptyDescription = '暂无数据',
  retryAction
}) => {
  if (loading) {
    return skeleton ? (
      <div className="p-4">
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    ) : (
      <div className="flex items-center justify-center min-h-[200px]">
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (error) {
    return (
      <Result
        status="error"
        title="加载失败"
        subTitle={error}
        extra={
          retryAction && (
            <Button type="primary" onClick={retryAction}>
              重试
            </Button>
          )
        }
      />
    );
  }

  if (empty) {
    return (
      <Empty
        description={emptyDescription}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  return <>{children}</>;
};

// ==================== 操作确认组件 ====================

interface ActionConfirmProps {
  title: string;
  description?: string;
  onConfirm: () => Promise<void> | void;
  children: React.ReactNode;
  type?: 'delete' | 'warning' | 'info';
  okText?: string;
  cancelText?: string;
}

export const ActionConfirm: React.FC<ActionConfirmProps> = ({
  title,
  description,
  onConfirm,
  children,
  type = 'warning',
  okText = '确定',
  cancelText = '取消'
}) => {
  const [loading, setLoading] = useState(false);

  const handleConfirm = async () => {
    setLoading(true);
    try {
      await onConfirm();
      message.success('操作成功');
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'delete':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <QuestionCircleOutlined />;
    }
  };

  return (
    <Popconfirm
      title={title}
      description={description}
      icon={getIcon()}
      okText={okText}
      cancelText={cancelText}
      onConfirm={handleConfirm}
      okButtonProps={{ loading }}
    >
      {children}
    </Popconfirm>
  );
};

// ==================== 进度指示器 ====================

interface ProgressIndicatorProps {
  steps: Array<{
    title: string;
    description?: string;
    status: 'wait' | 'process' | 'finish' | 'error';
  }>;
  current: number;
  showProgress?: boolean;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  steps,
  current,
  showProgress = true
}) => {
  const percent = Math.round((current / (steps.length - 1)) * 100);

  return (
    <div className="mb-6">
      {showProgress && (
        <Progress
          percent={percent}
          status={steps[current]?.status === 'error' ? 'exception' : 'active'}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
      )}
      <div className="mt-4">
        <Title level={4}>{steps[current]?.title}</Title>
        {steps[current]?.description && (
          <Text type="secondary">{steps[current].description}</Text>
        )}
      </div>
    </div>
  );
};

// ==================== 智能通知系统 ====================

export class NotificationManager {
  private static instance: NotificationManager;
  private notificationQueue: Array<{
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
    description?: string;
    duration?: number;
  }> = [];

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  success(message: string, description?: string, duration = 4.5) {
    notification.success({
      message,
      description,
      duration,
      placement: 'topRight',
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />
    });
  }

  error(message: string, description?: string, duration = 6) {
    notification.error({
      message,
      description,
      duration,
      placement: 'topRight',
      icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    });
  }

  warning(message: string, description?: string, duration = 4.5) {
    notification.warning({
      message,
      description,
      duration,
      placement: 'topRight',
      icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />
    });
  }

  info(message: string, description?: string, duration = 4.5) {
    notification.info({
      message,
      description,
      duration,
      placement: 'topRight',
      icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />
    });
  }

  // 批量通知处理
  batch(notifications: Array<{
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
    description?: string;
  }>) {
    notifications.forEach((notif, index) => {
      setTimeout(() => {
        this[notif.type](notif.message, notif.description);
      }, index * 500); // 间隔500ms显示
    });
  }
}

// ==================== 用户引导组件 ====================

interface UserGuideProps {
  steps: TourProps['steps'];
  open: boolean;
  onClose: () => void;
  onFinish?: () => void;
}

export const UserGuide: React.FC<UserGuideProps> = ({
  steps,
  open,
  onClose,
  onFinish
}) => {
  return (
    <Tour
      open={open}
      onClose={onClose}
      onFinish={onFinish}
      steps={steps}
      indicatorsRender={(current, total) => (
        <span>
          {current + 1} / {total}
        </span>
      )}
      type="primary"
    />
  );
};

// ==================== 快捷操作面板 ====================

interface QuickActionPanelProps {
  actions: Array<{
    key: string;
    icon: React.ReactNode;
    label: string;
    onClick: () => void;
    disabled?: boolean;
    tooltip?: string;
  }>;
  visible: boolean;
  onClose: () => void;
}

export const QuickActionPanel: React.FC<QuickActionPanelProps> = ({
  actions,
  visible,
  onClose
}) => {
  return (
    <Drawer
      title="快捷操作"
      placement="right"
      onClose={onClose}
      open={visible}
      width={300}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {actions.map((action) => (
          <Tooltip key={action.key} title={action.tooltip}>
            <Button
              type="text"
              icon={action.icon}
              disabled={action.disabled}
              onClick={action.onClick}
              style={{ width: '100%', textAlign: 'left' }}
            >
              {action.label}
            </Button>
          </Tooltip>
        ))}
      </Space>
    </Drawer>
  );
};

// ==================== 浮动操作按钮 ====================

interface FloatingActionsProps {
  showBackTop?: boolean;
  showHelp?: boolean;
  showFeedback?: boolean;
  onHelp?: () => void;
  onFeedback?: () => void;
  customActions?: Array<{
    icon: React.ReactNode;
    tooltip: string;
    onClick: () => void;
  }>;
}

export const FloatingActions: React.FC<FloatingActionsProps> = ({
  showBackTop = true,
  showHelp = true,
  showFeedback = true,
  onHelp,
  onFeedback,
  customActions = []
}) => {
  return (
    <FloatButton.Group
      trigger="hover"
      type="primary"
      style={{ right: 24 }}
      icon={<BulbOutlined />}
    >
      {showBackTop && <FloatButton.BackTop visibilityHeight={0} />}
      
      {showHelp && onHelp && (
        <FloatButton
          icon={<QuestionCircleOutlined />}
          tooltip="帮助"
          onClick={onHelp}
        />
      )}
      
      {showFeedback && onFeedback && (
        <FloatButton
          icon={<CustomerServiceOutlined />}
          tooltip="反馈"
          onClick={onFeedback}
        />
      )}
      
      {customActions.map((action, index) => (
        <FloatButton
          key={index}
          icon={action.icon}
          tooltip={action.tooltip}
          onClick={action.onClick}
        />
      ))}
    </FloatButton.Group>
  );
};

// ==================== 响应式容器 ====================

interface ResponsiveContainerProps {
  children: React.ReactNode;
  breakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  className?: string;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  breakpoint = 'lg',
  className = ''
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      const breakpoints = {
        xs: 480,
        sm: 576,
        md: 768,
        lg: 992,
        xl: 1200,
        xxl: 1600
      };
      
      setIsMobile(window.innerWidth < breakpoints[breakpoint]);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, [breakpoint]);

  return (
    <div 
      className={`${isMobile ? 'mobile-layout' : 'desktop-layout'} ${className}`}
      data-mobile={isMobile}
    >
      {children}
    </div>
  );
};

// ==================== 错误边界组件 ====================

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // 发送错误报告到监控系统
    // reportError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Result
          status="500"
          title="页面出错了"
          subTitle="抱歉，页面发生了错误，请刷新页面重试。"
          extra={
            <Button type="primary" onClick={() => window.location.reload()}>
              刷新页面
            </Button>
          }
        />
      );
    }

    return this.props.children;
  }
}

// ==================== 导出所有组件 ====================

export const UXComponents = {
  SmartLoading,
  ActionConfirm,
  ProgressIndicator,
  NotificationManager,
  UserGuide,
  QuickActionPanel,
  FloatingActions,
  ResponsiveContainer,
  ErrorBoundary
};

export default UXComponents;
