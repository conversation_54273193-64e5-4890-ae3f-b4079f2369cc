/**
 * 加载动画组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React from 'react'
import { Spin } from 'antd'

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large'
  tip?: string
  className?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  tip = '加载中...',
  className = '',
}) => {
  return (
    <div className={`flex items-center justify-center min-h-screen ${className}`}>
      <Spin size={size} tip={tip} />
    </div>
  )
}

export default LoadingSpinner
