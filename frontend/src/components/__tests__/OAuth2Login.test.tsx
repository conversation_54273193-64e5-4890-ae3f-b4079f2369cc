/**
 * OAuth2Login组件测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import OAuth2Login from '../OAuth2Login'
import OAuth2Service from '@/services/oauth2Service'

// Mock OAuth2Service
vi.mock('@/services/oauth2Service', () => ({
  default: {
    getProviders: vi.fn(),
    startAuthentication: vi.fn()
  }
}))

// Mock antd message
vi.mock('antd', async () => {
  const actual = await vi.importActual('antd')
  return {
    ...actual,
    message: {
      error: vi.fn(),
      success: vi.fn()
    }
  }
})

describe('OAuth2Login', () => {
  const mockProviders = {
    google: {
      clientName: 'Google',
      authorizationUri: '/oauth2/authorization/google'
    },
    github: {
      clientName: 'GitHub',
      authorizationUri: '/oauth2/authorization/github'
    },
    custom: {
      clientName: '自定义ID Provider',
      authorizationUri: '/oauth2/authorization/custom'
    }
  }

  const mockProvidersResponse = {
    providers: mockProviders,
    redirectUris: ['http://localhost:3000/oauth2/redirect']
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('应该正确渲染OAuth2登录组件', async () => {
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)

    render(<OAuth2Login />)

    // 等待加载完成
    await waitFor(() => {
      expect(screen.queryByText('加载登录选项...')).not.toBeInTheDocument()
    })

    // 验证分隔线文本
    expect(screen.getByText('或使用第三方账号登录')).toBeInTheDocument()

    // 验证所有提供商按钮
    expect(screen.getByText('使用 Google 登录')).toBeInTheDocument()
    expect(screen.getByText('使用 GitHub 登录')).toBeInTheDocument()
    expect(screen.getByText('使用 自定义ID Provider 登录')).toBeInTheDocument()
  })

  it('应该显示加载状态', () => {
    vi.mocked(OAuth2Service.getProviders).mockImplementation(() => new Promise(() => {}))

    render(<OAuth2Login />)

    expect(screen.getByText('加载登录选项...')).toBeInTheDocument()
    expect(screen.getByRole('img', { name: 'loading' })).toBeInTheDocument()
  })

  it('应该处理获取提供商失败的情况', async () => {
    const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.mocked(OAuth2Service.getProviders).mockRejectedValue(new Error('网络错误'))

    render(<OAuth2Login />)

    await waitFor(() => {
      expect(consoleError).toHaveBeenCalledWith('获取OAuth2提供商失败:', expect.any(Error))
    })

    consoleError.mockRestore()
  })

  it('应该在没有提供商时不渲染任何内容', async () => {
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue({
      providers: {},
      redirectUris: []
    })

    const { container } = render(<OAuth2Login />)

    await waitFor(() => {
      expect(container.firstChild).toBeNull()
    })
  })

  it('应该处理Google登录点击', async () => {
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)
    vi.mocked(OAuth2Service.startAuthentication).mockResolvedValue()

    render(<OAuth2Login />)

    await waitFor(() => {
      expect(screen.getByText('使用 Google 登录')).toBeInTheDocument()
    })

    const googleButton = screen.getByText('使用 Google 登录')
    fireEvent.click(googleButton)

    expect(OAuth2Service.startAuthentication).toHaveBeenCalledWith(
      'google',
      'http://localhost:3000/oauth2/redirect'
    )
  })

  it('应该处理GitHub登录点击', async () => {
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)
    vi.mocked(OAuth2Service.startAuthentication).mockResolvedValue()

    render(<OAuth2Login />)

    await waitFor(() => {
      expect(screen.getByText('使用 GitHub 登录')).toBeInTheDocument()
    })

    const githubButton = screen.getByText('使用 GitHub 登录')
    fireEvent.click(githubButton)

    expect(OAuth2Service.startAuthentication).toHaveBeenCalledWith(
      'github',
      'http://localhost:3000/oauth2/redirect'
    )
  })

  it('应该处理自定义重定向URI', async () => {
    const customRedirectUri = 'http://localhost:3000/custom/redirect'
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)
    vi.mocked(OAuth2Service.startAuthentication).mockResolvedValue()

    render(<OAuth2Login redirectUri={customRedirectUri} />)

    await waitFor(() => {
      expect(screen.getByText('使用 Google 登录')).toBeInTheDocument()
    })

    const googleButton = screen.getByText('使用 Google 登录')
    fireEvent.click(googleButton)

    expect(OAuth2Service.startAuthentication).toHaveBeenCalledWith('google', customRedirectUri)
  })

  it('应该处理登录失败', async () => {
    const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)
    vi.mocked(OAuth2Service.startAuthentication).mockRejectedValue(new Error('认证失败'))

    render(<OAuth2Login />)

    await waitFor(() => {
      expect(screen.getByText('使用 Google 登录')).toBeInTheDocument()
    })

    const googleButton = screen.getByText('使用 Google 登录')
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(consoleError).toHaveBeenCalledWith('google OAuth2登录失败:', expect.any(Error))
    })

    consoleError.mockRestore()
  })

  it('应该显示按钮加载状态', async () => {
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)
    vi.mocked(OAuth2Service.startAuthentication).mockImplementation(() => new Promise(() => {}))

    render(<OAuth2Login />)

    await waitFor(() => {
      expect(screen.getByText('使用 Google 登录')).toBeInTheDocument()
    })

    const googleButton = screen.getByText('使用 Google 登录')
    fireEvent.click(googleButton)

    // 验证按钮进入加载状态
    await waitFor(() => {
      expect(googleButton.closest('button')).toHaveClass('ant-btn-loading')
    })
  })

  it('应该调用成功回调', async () => {
    const onSuccess = vi.fn()
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)

    render(<OAuth2Login onSuccess={onSuccess} />)

    // 这里只是验证组件能正确接收回调函数
    // 实际的成功回调会在OAuth2认证完成后由父组件处理
    expect(onSuccess).not.toHaveBeenCalled()
  })

  it('应该调用错误回调', async () => {
    const onError = vi.fn()
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)

    render(<OAuth2Login onError={onError} />)

    // 这里只是验证组件能正确接收回调函数
    // 实际的错误回调会在OAuth2认证失败后由父组件处理
    expect(onError).not.toHaveBeenCalled()
  })

  it('应该应用自定义CSS类', async () => {
    const customClassName = 'custom-oauth2-login'
    vi.mocked(OAuth2Service.getProviders).mockResolvedValue(mockProvidersResponse)

    const { container } = render(<OAuth2Login className={customClassName} />)

    await waitFor(() => {
      expect(container.firstChild).toHaveClass(customClassName)
    })
  })
})
