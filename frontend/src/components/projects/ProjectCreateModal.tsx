/**
 * 项目创建模态框组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState } from 'react'
import { useDispatch } from 'react-redux'
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Switch,
  Upload,
  Button,
  message,
  Row,
  Col,
  Divider,
} from 'antd'
import { UploadOutlined, PlusOutlined } from '@ant-design/icons'
import type { UploadFile } from 'antd/es/upload/interface'

import { AppDispatch } from '@/store'
import { createProject } from '@/store/slices/projectSlice'
import { ProjectStatus, ProjectPriority, ProjectCreateRequest } from '@/types/project'

const { TextArea } = Input
const { Option } = Select
const { RangePicker } = DatePicker

interface ProjectCreateModalProps {
  visible: boolean
  onCancel: () => void
  onSuccess: () => void
}

const ProjectCreateModal: React.FC<ProjectCreateModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm()
  const dispatch = useDispatch<AppDispatch>()
  const [loading, setLoading] = useState(false)
  const [fileList, setFileList] = useState<UploadFile[]>([])

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)
      
      // 处理日期范围
      const [startDate, endDate] = values.dateRange || []
      
      // 构建创建请求
      const createRequest: ProjectCreateRequest = {
        name: values.name,
        description: values.description,
        status: values.status || ProjectStatus.PLANNING,
        priority: values.priority || ProjectPriority.MEDIUM,
        startDate: startDate?.format('YYYY-MM-DD'),
        endDate: endDate?.format('YYYY-MM-DD'),
        budget: values.budget,
        ownerId: 'current-user-id', // TODO: 从当前用户获取
        managerId: values.managerId,
        teamMemberIds: values.teamMemberIds || [],
        tags: values.tags || [],
        isPublic: values.isPublic || false,
        settings: {
          isTaskAutoAssignment: values.isTaskAutoAssignment || false,
          isTimeTracking: values.isTimeTracking || true,
          isPublicComments: values.isPublicComments || false,
          notificationSettings: {
            taskUpdates: true,
            deadlineReminders: true,
            teamUpdates: true,
          },
          workflowSettings: {
            requireApproval: values.requireApproval || false,
            allowSelfAssignment: values.allowSelfAssignment || true,
            autoCloseCompletedTasks: values.autoCloseCompletedTasks || false,
          },
        },
      }

      await dispatch(createProject(createRequest)).unwrap()
      
      message.success('项目创建成功')
      form.resetFields()
      setFileList([])
      onSuccess()
      
    } catch (error: any) {
      message.error(error.message || '项目创建失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    form.resetFields()
    setFileList([])
    onCancel()
  }

  return (
    <Modal
      title="创建新项目"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          status: ProjectStatus.PLANNING,
          priority: ProjectPriority.MEDIUM,
          isPublic: false,
          isTaskAutoAssignment: false,
          isTimeTracking: true,
          isPublicComments: false,
          requireApproval: false,
          allowSelfAssignment: true,
          autoCloseCompletedTasks: false,
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="项目名称"
              rules={[
                { required: true, message: '请输入项目名称' },
                { max: 100, message: '项目名称不能超过100个字符' },
              ]}
            >
              <Input placeholder="请输入项目名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="status"
              label="项目状态"
            >
              <Select>
                <Option value={ProjectStatus.PLANNING}>计划中</Option>
                <Option value={ProjectStatus.ACTIVE}>进行中</Option>
                <Option value={ProjectStatus.ON_HOLD}>暂停</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label="项目描述"
          rules={[
            { max: 500, message: '项目描述不能超过500个字符' },
          ]}
        >
          <TextArea
            rows={3}
            placeholder="请输入项目描述"
            showCount
            maxLength={500}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="priority"
              label="优先级"
            >
              <Select>
                <Option value={ProjectPriority.LOW}>低</Option>
                <Option value={ProjectPriority.MEDIUM}>中</Option>
                <Option value={ProjectPriority.HIGH}>高</Option>
                <Option value={ProjectPriority.URGENT}>紧急</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="budget"
              label="项目预算"
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入预算金额"
                min={0}
                precision={2}
                formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="isPublic"
              label="公开项目"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="dateRange"
          label="项目周期"
          rules={[
            { required: true, message: '请选择项目开始和结束日期' },
          ]}
        >
          <RangePicker
            style={{ width: '100%' }}
            placeholder={['开始日期', '结束日期']}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="managerId"
              label="项目经理"
            >
              <Select
                placeholder="选择项目经理"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              >
                {/* TODO: 从用户服务获取用户列表 */}
                <Option value="user1">张三</Option>
                <Option value="user2">李四</Option>
                <Option value="user3">王五</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="teamMemberIds"
              label="团队成员"
            >
              <Select
                mode="multiple"
                placeholder="选择团队成员"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              >
                {/* TODO: 从用户服务获取用户列表 */}
                <Option value="user1">张三</Option>
                <Option value="user2">李四</Option>
                <Option value="user3">王五</Option>
                <Option value="user4">赵六</Option>
                <Option value="user5">钱七</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="tags"
          label="项目标签"
        >
          <Select
            mode="tags"
            placeholder="添加项目标签"
            tokenSeparators={[',']}
          >
            <Option value="前端">前端</Option>
            <Option value="后端">后端</Option>
            <Option value="移动端">移动端</Option>
            <Option value="AI">AI</Option>
            <Option value="数据分析">数据分析</Option>
          </Select>
        </Form.Item>

        <Divider>项目设置</Divider>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="isTaskAutoAssignment"
              label="自动分配任务"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="isTimeTracking"
              label="启用时间跟踪"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="isPublicComments"
              label="公开评论"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="requireApproval"
              label="需要审批"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="allowSelfAssignment"
              label="允许自分配"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="autoCloseCompletedTasks"
              label="自动关闭已完成任务"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item className="mb-0 mt-6">
          <div className="flex justify-end space-x-2">
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建项目
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ProjectCreateModal
