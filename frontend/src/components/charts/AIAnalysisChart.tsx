/**
 * AI分析结果可视化组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react'
import * as echarts from 'echarts'

interface RiskData {
  name: string
  level: 'low' | 'medium' | 'high' | 'critical'
  probability: number
  impact: number
  description: string
}

interface PredictionData {
  date: string
  predicted: number
  actual?: number
  confidence: number
}

interface AIAnalysisChartProps {
  type: 'risk' | 'prediction' | 'recommendation'
  riskData?: RiskData[]
  predictionData?: PredictionData[]
  height?: number
}

const AIAnalysisChart: React.FC<AIAnalysisChartProps> = ({
  type,
  riskData = [
    { name: '进度延期', level: 'high', probability: 75, impact: 85, description: '项目可能延期2周' },
    { name: '资源不足', level: 'medium', probability: 60, impact: 70, description: '开发人员工作负载过重' },
    { name: '需求变更', level: 'medium', probability: 45, impact: 60, description: '客户可能提出新需求' },
    { name: '技术风险', level: 'low', probability: 30, impact: 50, description: '新技术栈学习成本' },
    { name: '质量问题', level: 'low', probability: 25, impact: 40, description: '测试覆盖率不足' },
  ],
  predictionData = [
    { date: '2025-08-16', predicted: 75, actual: 75, confidence: 95 },
    { date: '2025-08-17', predicted: 78, actual: 76, confidence: 92 },
    { date: '2025-08-18', predicted: 82, actual: 80, confidence: 90 },
    { date: '2025-08-19', predicted: 85, confidence: 88 },
    { date: '2025-08-20', predicted: 88, confidence: 85 },
    { date: '2025-08-21', predicted: 92, confidence: 82 },
    { date: '2025-08-22', predicted: 95, confidence: 80 },
  ],
  height = 400,
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current)

    let option: any = {}

    switch (type) {
      case 'risk':
        option = {
          title: {
            text: 'AI风险分析矩阵',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: (params: any) => {
              const data = params.data
              return `
                <div>
                  <strong>${data.name}</strong><br/>
                  风险等级: ${data.level}<br/>
                  发生概率: ${data.probability}%<br/>
                  影响程度: ${data.impact}%<br/>
                  描述: ${data.description}
                </div>
              `
            }
          },
          xAxis: {
            type: 'value',
            name: '发生概率 (%)',
            min: 0,
            max: 100,
            axisLabel: {
              formatter: '{value}%'
            }
          },
          yAxis: {
            type: 'value',
            name: '影响程度 (%)',
            min: 0,
            max: 100,
            axisLabel: {
              formatter: '{value}%'
            }
          },
          series: [
            {
              name: '风险分析',
              type: 'scatter',
              symbolSize: (data: any) => Math.sqrt(data[0] * data[1]) / 3,
              data: riskData.map(item => ({
                value: [item.probability, item.impact],
                name: item.name,
                level: item.level,
                description: item.description,
                itemStyle: {
                  color: item.level === 'critical' ? '#ff4d4f' :
                         item.level === 'high' ? '#fa8c16' :
                         item.level === 'medium' ? '#faad14' : '#52c41a'
                }
              })),
              markArea: {
                silent: true,
                itemStyle: {
                  color: 'transparent',
                  borderWidth: 1,
                  borderType: 'dashed'
                },
                data: [
                  [
                    { name: '高风险区域', x: '70%', y: '70%' },
                    { x: '100%', y: '100%' }
                  ],
                  [
                    { name: '中风险区域', x: '40%', y: '40%' },
                    { x: '70%', y: '70%' }
                  ],
                  [
                    { name: '低风险区域', x: '0%', y: '0%' },
                    { x: '40%', y: '40%' }
                  ]
                ]
              }
            }
          ]
        }
        break

      case 'prediction':
        option = {
          title: {
            text: 'AI项目进度预测',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'axis',
            formatter: (params: any) => {
              let result = `<div><strong>${params[0].axisValue}</strong><br/>`
              params.forEach((param: any) => {
                if (param.seriesName === '置信区间') return
                result += `${param.seriesName}: ${param.value}%<br/>`
              })
              const confidence = params.find((p: any) => p.seriesName === '预测进度')?.data?.confidence
              if (confidence) {
                result += `置信度: ${confidence}%`
              }
              result += '</div>'
              return result
            }
          },
          legend: {
            data: ['实际进度', '预测进度', '置信区间'],
            top: 30
          },
          xAxis: {
            type: 'category',
            data: predictionData.map(item => item.date.substring(5))
          },
          yAxis: {
            type: 'value',
            name: '完成进度 (%)',
            min: 0,
            max: 100,
            axisLabel: {
              formatter: '{value}%'
            }
          },
          series: [
            {
              name: '实际进度',
              type: 'line',
              data: predictionData.map(item => item.actual || null),
              lineStyle: {
                color: '#1890ff',
                width: 3
              },
              itemStyle: {
                color: '#1890ff'
              },
              symbol: 'circle',
              symbolSize: 6
            },
            {
              name: '预测进度',
              type: 'line',
              data: predictionData.map(item => ({
                value: item.predicted,
                confidence: item.confidence
              })),
              lineStyle: {
                color: '#52c41a',
                width: 2,
                type: 'dashed'
              },
              itemStyle: {
                color: '#52c41a'
              },
              symbol: 'diamond',
              symbolSize: 6
            },
            {
              name: '置信区间',
              type: 'line',
              data: predictionData.map(item => [
                Math.max(0, item.predicted - (100 - item.confidence) / 2),
                Math.min(100, item.predicted + (100 - item.confidence) / 2)
              ]),
              areaStyle: {
                color: 'rgba(82, 196, 26, 0.2)'
              },
              lineStyle: {
                opacity: 0
              },
              symbol: 'none',
              stack: 'confidence'
            }
          ]
        }
        break

      case 'recommendation':
        const recommendations = [
          { action: '增加开发人员', priority: 90, impact: 85, effort: 70 },
          { action: '优化工作流程', priority: 80, impact: 75, effort: 40 },
          { action: '提前测试', priority: 75, impact: 70, effort: 50 },
          { action: '需求冻结', priority: 70, impact: 80, effort: 30 },
          { action: '技术培训', priority: 60, impact: 60, effort: 60 },
        ]

        option = {
          title: {
            text: 'AI优化建议分析',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: (params: any) => {
              const data = params.data
              return `
                <div>
                  <strong>${data.name}</strong><br/>
                  优先级: ${data.value[0]}%<br/>
                  预期效果: ${data.value[1]}%<br/>
                  实施难度: ${data.value[2]}%
                </div>
              `
            }
          },
          radar: {
            indicator: [
              { name: '优先级', max: 100 },
              { name: '预期效果', max: 100 },
              { name: '实施难度', max: 100 }
            ],
            center: ['50%', '55%'],
            radius: '60%'
          },
          series: [
            {
              name: 'AI建议',
              type: 'radar',
              data: recommendations.map((item, index) => ({
                value: [item.priority, item.impact, item.effort],
                name: item.action,
                itemStyle: {
                  color: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'][index]
                },
                areaStyle: {
                  opacity: 0.3
                }
              }))
            }
          ]
        }
        break
    }

    chartInstance.current.setOption(option)

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [type, riskData, predictionData, height])

  return (
    <div
      ref={chartRef}
      style={{ width: '100%', height: `${height}px` }}
    />
  )
}

export default AIAnalysisChart
