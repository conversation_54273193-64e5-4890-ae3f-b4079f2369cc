/**
 * 项目进度图表组件
 * 使用ECharts显示项目进度和状态分布
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface ProjectData {
  name: string;
  progress: number;
  status: 'active' | 'completed' | 'paused' | 'planning';
  startDate: string;
  endDate: string;
}

interface ProjectChartProps {
  data?: ProjectData[];
  height?: number;
}

export const ProjectChart: React.FC<ProjectChartProps> = ({ 
  data, 
  height = 300 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 模拟数据
  const defaultData: ProjectData[] = [
    {
      name: 'AI推荐系统',
      progress: 85,
      status: 'active',
      startDate: '2025-07-01',
      endDate: '2025-08-30'
    },
    {
      name: '用户管理模块',
      progress: 100,
      status: 'completed',
      startDate: '2025-06-15',
      endDate: '2025-07-15'
    },
    {
      name: '数据分析平台',
      progress: 65,
      status: 'active',
      startDate: '2025-07-15',
      endDate: '2025-09-15'
    },
    {
      name: '移动端应用',
      progress: 30,
      status: 'active',
      startDate: '2025-08-01',
      endDate: '2025-10-01'
    },
    {
      name: '集成测试',
      progress: 0,
      status: 'planning',
      startDate: '2025-09-01',
      endDate: '2025-09-30'
    }
  ];

  const projectData = data || defaultData;

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    // 配置图表选项
    const option = {
      title: {
        text: '项目进度概览',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const data = params[0];
          const project = projectData[data.dataIndex];
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${project.name}</div>
              <div>进度: ${project.progress}%</div>
              <div>状态: ${getStatusText(project.status)}</div>
              <div>开始: ${project.startDate}</div>
              <div>结束: ${project.endDate}</div>
            </div>
          `;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: projectData.map(item => item.name),
        axisLabel: {
          rotate: 45,
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '项目进度',
          type: 'bar',
          data: projectData.map(item => ({
            value: item.progress,
            itemStyle: {
              color: getStatusColor(item.status)
            }
          })),
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%'
          }
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chartInstance.current?.dispose();
    };
  }, [projectData]);

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed':
        return '#52c41a';
      case 'active':
        return '#1890ff';
      case 'paused':
        return '#faad14';
      case 'planning':
        return '#d9d9d9';
      default:
        return '#1890ff';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'active':
        return '进行中';
      case 'paused':
        return '已暂停';
      case 'planning':
        return '规划中';
      default:
        return '未知';
    }
  };

  return (
    <div 
      ref={chartRef} 
      style={{ 
        width: '100%', 
        height: `${height}px`,
        minHeight: '200px'
      }} 
    />
  );
};
