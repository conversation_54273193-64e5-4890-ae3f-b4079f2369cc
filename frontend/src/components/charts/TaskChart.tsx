/**
 * 任务状态图表组件
 * 使用ECharts显示任务状态分布饼图
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface TaskStatusData {
  status: string;
  count: number;
  color: string;
}

interface TaskChartProps {
  data?: TaskStatusData[];
  height?: number;
}

export const TaskChart: React.FC<TaskChartProps> = ({ 
  data, 
  height = 300 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 模拟数据
  const defaultData: TaskStatusData[] = [
    { status: '待开始', count: 25, color: '#d9d9d9' },
    { status: '进行中', count: 45, color: '#1890ff' },
    { status: '已完成', count: 120, color: '#52c41a' },
    { status: '已逾期', count: 8, color: '#ff4d4f' },
    { status: '已暂停', count: 5, color: '#faad14' }
  ];

  const taskData = data || defaultData;

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    // 配置图表选项
    const option = {
      title: {
        text: '任务状态分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.name}</div>
              <div>数量: ${params.value}</div>
              <div>占比: ${params.percent}%</div>
            </div>
          `;
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
        textStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '任务状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: taskData.map(item => ({
            value: item.count,
            name: item.status,
            itemStyle: {
              color: item.color
            }
          }))
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chartInstance.current?.dispose();
    };
  }, [taskData]);

  return (
    <div 
      ref={chartRef} 
      style={{ 
        width: '100%', 
        height: `${height}px`,
        minHeight: '200px'
      }} 
    />
  );
};
