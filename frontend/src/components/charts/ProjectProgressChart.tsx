/**
 * 项目进度图表组件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react'
import * as echarts from 'echarts'

interface ProjectProgressData {
  name: string
  progress: number
  target: number
  status: string
}

interface ProjectProgressChartProps {
  data?: ProjectProgressData[]
  height?: number
}

const ProjectProgressChart: React.FC<ProjectProgressChartProps> = ({
  data = [
    { name: 'AI客服系统', progress: 75, target: 80, status: 'active' },
    { name: '移动端应用重构', progress: 45, target: 60, status: 'active' },
    { name: '数据分析平台', progress: 90, target: 85, status: 'review' },
    { name: '用户管理系统', progress: 30, target: 40, status: 'active' },
    { name: '报表系统', progress: 100, target: 100, status: 'completed' },
  ],
  height = 300,
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current)

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const data = params[0]
          const progressData = params[1]
          return `
            <div>
              <strong>${data.name}</strong><br/>
              目标进度: ${data.value}%<br/>
              实际进度: ${progressData.value}%<br/>
              完成度: ${((progressData.value / data.value) * 100).toFixed(1)}%
            </div>
          `
        }
      },
      legend: {
        data: ['目标进度', '实际进度'],
        top: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      yAxis: {
        type: 'category',
        data: data.map(item => item.name),
        axisLabel: {
          interval: 0,
          formatter: (value: string) => {
            return value.length > 8 ? value.substring(0, 8) + '...' : value
          }
        }
      },
      series: [
        {
          name: '目标进度',
          type: 'bar',
          data: data.map(item => item.target),
          itemStyle: {
            color: '#e6f7ff',
            borderColor: '#1890ff',
            borderWidth: 1
          },
          barWidth: 20,
          z: 1
        },
        {
          name: '实际进度',
          type: 'bar',
          data: data.map(item => ({
            value: item.progress,
            itemStyle: {
              color: item.progress >= item.target ? '#52c41a' :
                     item.progress >= item.target * 0.8 ? '#faad14' : '#ff4d4f'
            }
          })),
          barWidth: 20,
          z: 2
        }
      ]
    }

    chartInstance.current.setOption(option)

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, height])

  return (
    <div
      ref={chartRef}
      style={{ width: '100%', height: `${height}px` }}
    />
  )
}

export default ProjectProgressChart
