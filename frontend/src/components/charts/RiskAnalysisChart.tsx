/**
 * 风险分析图表组件
 * 使用ECharts显示项目风险分析和趋势
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface RiskData {
  date: string;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
}

interface RiskAnalysisChartProps {
  data?: RiskData[];
  height?: number;
}

export const RiskAnalysisChart: React.FC<RiskAnalysisChartProps> = ({ 
  data, 
  height = 300 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 模拟数据 - 最近7天的风险分析
  const defaultData: RiskData[] = [
    { date: '08-10', highRisk: 2, mediumRisk: 5, lowRisk: 8 },
    { date: '08-11', highRisk: 3, mediumRisk: 4, lowRisk: 9 },
    { date: '08-12', highRisk: 1, mediumRisk: 6, lowRisk: 7 },
    { date: '08-13', highRisk: 2, mediumRisk: 5, lowRisk: 10 },
    { date: '08-14', highRisk: 4, mediumRisk: 3, lowRisk: 8 },
    { date: '08-15', highRisk: 2, mediumRisk: 7, lowRisk: 6 },
    { date: '08-16', highRisk: 1, mediumRisk: 5, lowRisk: 9 }
  ];

  const riskData = data || defaultData;

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    // 配置图表选项
    const option = {
      title: {
        text: '项目风险趋势',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: (params: any) => {
          let result = `<div style="padding: 8px;">`;
          result += `<div style="font-weight: bold; margin-bottom: 4px;">日期: ${params[0].axisValue}</div>`;
          params.forEach((param: any) => {
            result += `<div style="color: ${param.color};">
              ${param.seriesName}: ${param.value} 个项目
            </div>`;
          });
          result += `</div>`;
          return result;
        }
      },
      legend: {
        data: ['高风险', '中风险', '低风险'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: riskData.map(item => item.date),
        axisLabel: {
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}',
          fontSize: 12
        }
      },
      series: [
        {
          name: '高风险',
          type: 'line',
          stack: 'Total',
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 77, 79, 0.8)' },
              { offset: 1, color: 'rgba(255, 77, 79, 0.1)' }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: riskData.map(item => item.highRisk),
          lineStyle: {
            color: '#ff4d4f',
            width: 2
          },
          itemStyle: {
            color: '#ff4d4f'
          }
        },
        {
          name: '中风险',
          type: 'line',
          stack: 'Total',
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(250, 173, 20, 0.8)' },
              { offset: 1, color: 'rgba(250, 173, 20, 0.1)' }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: riskData.map(item => item.mediumRisk),
          lineStyle: {
            color: '#faad14',
            width: 2
          },
          itemStyle: {
            color: '#faad14'
          }
        },
        {
          name: '低风险',
          type: 'line',
          stack: 'Total',
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(82, 196, 26, 0.8)' },
              { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: riskData.map(item => item.lowRisk),
          lineStyle: {
            color: '#52c41a',
            width: 2
          },
          itemStyle: {
            color: '#52c41a'
          }
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chartInstance.current?.dispose();
    };
  }, [riskData]);

  return (
    <div 
      ref={chartRef} 
      style={{ 
        width: '100%', 
        height: `${height}px`,
        minHeight: '200px'
      }} 
    />
  );
};
