/**
 * 任务状态图表组件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react'
import * as echarts from 'echarts'

interface TaskStatusData {
  name: string
  value: number
  color: string
}

interface TaskStatusChartProps {
  data?: TaskStatusData[]
  height?: number
}

const TaskStatusChart: React.FC<TaskStatusChartProps> = ({
  data = [
    { name: '待办', value: 25, color: '#d9d9d9' },
    { name: '进行中', value: 35, color: '#1890ff' },
    { name: '待审核', value: 15, color: '#faad14' },
    { name: '已完成', value: 45, color: '#52c41a' },
  ],
  height = 300,
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current)

    const total = data.reduce((sum, item) => sum + item.value, 0)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const percent = ((params.value / total) * 100).toFixed(1)
          return `${params.name}: ${params.value} (${percent}%)`
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center',
        data: data.map(item => item.name)
      },
      series: [
        {
          name: '任务状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['65%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
              formatter: (params: any) => {
                const percent = ((params.value / total) * 100).toFixed(1)
                return `${params.name}\n${percent}%`
              }
            }
          },
          labelLine: {
            show: false
          },
          data: data.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color
            }
          }))
        }
      ]
    }

    chartInstance.current.setOption(option)

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, height])

  return (
    <div
      ref={chartRef}
      style={{ width: '100%', height: `${height}px` }}
    />
  )
}

export default TaskStatusChart
