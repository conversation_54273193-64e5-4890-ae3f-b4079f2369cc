/**
 * 团队绩效图表组件
 * 使用ECharts显示团队成员绩效雷达图
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface PerformanceMetric {
  name: string;
  max: number;
}

interface TeamMemberPerformance {
  name: string;
  values: number[];
  color: string;
}

interface TeamPerformanceChartProps {
  data?: TeamMemberPerformance[];
  metrics?: PerformanceMetric[];
  height?: number;
}

export const TeamPerformanceChart: React.FC<TeamPerformanceChartProps> = ({ 
  data, 
  metrics,
  height = 300 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 默认绩效指标
  const defaultMetrics: PerformanceMetric[] = [
    { name: '任务完成率', max: 100 },
    { name: '代码质量', max: 100 },
    { name: '团队协作', max: 100 },
    { name: '创新能力', max: 100 },
    { name: '学习成长', max: 100 },
    { name: '沟通能力', max: 100 }
  ];

  // 模拟数据
  const defaultData: TeamMemberPerformance[] = [
    {
      name: '张三',
      values: [85, 90, 88, 75, 82, 85],
      color: '#1890ff'
    },
    {
      name: '李四',
      values: [78, 85, 92, 88, 85, 90],
      color: '#52c41a'
    },
    {
      name: '王五',
      values: [92, 88, 85, 90, 88, 85],
      color: '#722ed1'
    }
  ];

  const performanceMetrics = metrics || defaultMetrics;
  const teamData = data || defaultData;

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    // 配置图表选项
    const option = {
      title: {
        text: '团队绩效分析',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const member = teamData[params.seriesIndex];
          const metric = performanceMetrics[params.dataIndex];
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${member.name}</div>
              <div>${metric.name}: ${params.value}</div>
            </div>
          `;
        }
      },
      legend: {
        data: teamData.map(item => item.name),
        bottom: 10
      },
      radar: {
        indicator: performanceMetrics.map(metric => ({
          name: metric.name,
          max: metric.max
        })),
        center: ['50%', '50%'],
        radius: '60%',
        axisName: {
          fontSize: 12,
          color: '#666'
        },
        splitArea: {
          areaStyle: {
            color: ['rgba(114, 172, 209, 0.2)', 'rgba(114, 172, 209, 0.4)', 
                   'rgba(114, 172, 209, 0.6)', 'rgba(114, 172, 209, 0.8)', 
                   'rgba(114, 172, 209, 1)'].reverse()
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(114, 172, 209, 0.2)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(114, 172, 209, 0.2)'
          }
        }
      },
      series: teamData.map(member => ({
        name: member.name,
        type: 'radar',
        data: [
          {
            value: member.values,
            name: member.name,
            areaStyle: {
              color: member.color,
              opacity: 0.1
            },
            lineStyle: {
              color: member.color,
              width: 2
            },
            itemStyle: {
              color: member.color
            }
          }
        ]
      }))
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chartInstance.current?.dispose();
    };
  }, [teamData, performanceMetrics]);

  return (
    <div 
      ref={chartRef} 
      style={{ 
        width: '100%', 
        height: `${height}px`,
        minHeight: '200px'
      }} 
    />
  );
};
