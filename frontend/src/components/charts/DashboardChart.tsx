/**
 * 仪表板综合图表组件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react'
import * as echarts from 'echarts'

interface DashboardData {
  projectTrend: Array<{ date: string; projects: number; tasks: number }>
  taskDistribution: Array<{ name: string; value: number }>
  teamPerformance: Array<{ name: string; efficiency: number; workload: number }>
  timelineData: Array<{ date: string; events: number }>
}

interface DashboardChartProps {
  type: 'trend' | 'distribution' | 'performance' | 'timeline'
  data?: DashboardData
  height?: number
}

const DashboardChart: React.FC<DashboardChartProps> = ({
  type,
  data = {
    projectTrend: [
      { date: '2025-08-01', projects: 8, tasks: 120 },
      { date: '2025-08-05', projects: 10, tasks: 145 },
      { date: '2025-08-10', projects: 12, tasks: 180 },
      { date: '2025-08-15', projects: 12, tasks: 203 },
      { date: '2025-08-16', projects: 12, tasks: 210 },
    ],
    taskDistribution: [
      { name: '已完成', value: 156 },
      { name: '进行中', value: 35 },
      { name: '待开始', value: 25 },
      { name: '已暂停', value: 8 },
    ],
    teamPerformance: [
      { name: '前端团队', efficiency: 85, workload: 92 },
      { name: '后端团队', efficiency: 78, workload: 88 },
      { name: '测试团队', efficiency: 82, workload: 75 },
      { name: '产品团队', efficiency: 90, workload: 85 },
    ],
    timelineData: [
      { date: '2025-08-12', events: 5 },
      { date: '2025-08-13', events: 8 },
      { date: '2025-08-14', events: 12 },
      { date: '2025-08-15', events: 15 },
      { date: '2025-08-16', events: 18 },
    ],
  },
  height = 300,
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current)

    let option: any = {}

    switch (type) {
      case 'trend':
        option = {
          title: {
            text: '项目和任务趋势',
            textStyle: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          legend: {
            data: ['项目数量', '任务数量'],
            top: 25
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '20%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.projectTrend.map(item => item.date.substring(5))
          },
          yAxis: [
            {
              type: 'value',
              name: '项目数量',
              position: 'left',
              axisLabel: {
                formatter: '{value}'
              }
            },
            {
              type: 'value',
              name: '任务数量',
              position: 'right',
              axisLabel: {
                formatter: '{value}'
              }
            }
          ],
          series: [
            {
              name: '项目数量',
              type: 'line',
              data: data.projectTrend.map(item => item.projects),
              smooth: true,
              lineStyle: {
                color: '#1890ff',
                width: 3
              },
              itemStyle: {
                color: '#1890ff'
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                  { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
                ])
              }
            },
            {
              name: '任务数量',
              type: 'line',
              yAxisIndex: 1,
              data: data.projectTrend.map(item => item.tasks),
              smooth: true,
              lineStyle: {
                color: '#52c41a',
                width: 3
              },
              itemStyle: {
                color: '#52c41a'
              }
            }
          ]
        }
        break

      case 'distribution':
        option = {
          title: {
            text: '任务状态分布',
            left: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            top: 'center',
            data: data.taskDistribution.map(item => item.name)
          },
          series: [
            {
              name: '任务状态',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['60%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 8,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 16,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: data.taskDistribution.map((item, index) => ({
                value: item.value,
                name: item.name,
                itemStyle: {
                  color: ['#52c41a', '#1890ff', '#faad14', '#f5222d'][index]
                }
              }))
            }
          ]
        }
        break

      case 'performance':
        option = {
          title: {
            text: '团队绩效对比',
            textStyle: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['效率', '工作负载'],
            top: 25
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '20%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.teamPerformance.map(item => item.name)
          },
          yAxis: {
            type: 'value',
            max: 100,
            axisLabel: {
              formatter: '{value}%'
            }
          },
          series: [
            {
              name: '效率',
              type: 'bar',
              data: data.teamPerformance.map(item => item.efficiency),
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#83bff6' },
                  { offset: 0.5, color: '#188df0' },
                  { offset: 1, color: '#188df0' }
                ])
              },
              barWidth: '30%'
            },
            {
              name: '工作负载',
              type: 'bar',
              data: data.teamPerformance.map(item => item.workload),
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#ffd666' },
                  { offset: 0.5, color: '#faad14' },
                  { offset: 1, color: '#faad14' }
                ])
              },
              barWidth: '30%'
            }
          ]
        }
        break

      case 'timeline':
        option = {
          title: {
            text: '活动时间线',
            textStyle: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'axis',
            formatter: (params: any) => {
              const data = params[0]
              return `${data.name}: ${data.value} 个活动`
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.timelineData.map(item => item.date.substring(5))
          },
          yAxis: {
            type: 'value',
            name: '活动数量'
          },
          series: [
            {
              name: '活动',
              type: 'line',
              data: data.timelineData.map(item => item.events),
              smooth: true,
              lineStyle: {
                color: '#722ed1',
                width: 3
              },
              itemStyle: {
                color: '#722ed1'
              },
              symbol: 'circle',
              symbolSize: 8,
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(114, 46, 209, 0.3)' },
                  { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }
                ])
              }
            }
          ]
        }
        break
    }

    chartInstance.current.setOption(option)

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [type, data, height])

  return (
    <div
      ref={chartRef}
      style={{ width: '100%', height: `${height}px` }}
    />
  )
}

export default DashboardChart
