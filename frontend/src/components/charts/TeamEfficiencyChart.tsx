/**
 * 团队效率分析图表组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useRef } from 'react'
import * as echarts from 'echarts'

interface TeamMemberData {
  name: string
  tasksCompleted: number
  tasksInProgress: number
  productivity: number
  workHours: number
  efficiency: number
}

interface TeamEfficiencyChartProps {
  data?: TeamMemberData[]
  height?: number
  type?: 'productivity' | 'workload' | 'efficiency'
}

const TeamEfficiencyChart: React.FC<TeamEfficiencyChartProps> = ({
  data = [
    { name: '张三', tasksCompleted: 45, tasksInProgress: 3, productivity: 85, workHours: 160, efficiency: 92 },
    { name: '李四', tasksCompleted: 38, tasksInProgress: 5, productivity: 78, workHours: 155, efficiency: 85 },
    { name: '王五', tasksCompleted: 52, tasksInProgress: 2, productivity: 92, workHours: 165, efficiency: 95 },
    { name: '赵六', tasksCompleted: 28, tasksInProgress: 4, productivity: 65, workHours: 140, efficiency: 72 },
    { name: '钱七', tasksCompleted: 41, tasksInProgress: 6, productivity: 82, workHours: 158, efficiency: 88 },
  ],
  height = 400,
  type = 'productivity',
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current)

    let option: any = {}

    switch (type) {
      case 'productivity':
        option = {
          title: {
            text: '团队生产力分析',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            },
            formatter: (params: any) => {
              const data = params[0]
              const member = data.name
              const productivity = data.value
              const memberData = data.data
              return `
                <div>
                  <strong>${member}</strong><br/>
                  生产力: ${productivity}%<br/>
                  已完成任务: ${memberData.tasksCompleted}<br/>
                  进行中任务: ${memberData.tasksInProgress}<br/>
                  工作时长: ${memberData.workHours}h
                </div>
              `
            }
          },
          legend: {
            data: ['生产力', '效率'],
            top: 30
          },
          xAxis: [
            {
              type: 'category',
              data: data.map(item => item.name),
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '生产力(%)',
              min: 0,
              max: 100,
              interval: 20,
              axisLabel: {
                formatter: '{value}%'
              }
            },
            {
              type: 'value',
              name: '效率(%)',
              min: 0,
              max: 100,
              interval: 20,
              axisLabel: {
                formatter: '{value}%'
              }
            }
          ],
          series: [
            {
              name: '生产力',
              type: 'bar',
              data: data.map(item => ({
                value: item.productivity,
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#83bff6' },
                    { offset: 0.5, color: '#188df0' },
                    { offset: 1, color: '#188df0' }
                  ])
                },
                ...item
              })),
              barWidth: '40%'
            },
            {
              name: '效率',
              type: 'line',
              yAxisIndex: 1,
              data: data.map(item => item.efficiency),
              lineStyle: {
                color: '#52c41a',
                width: 3
              },
              itemStyle: {
                color: '#52c41a'
              },
              symbol: 'circle',
              symbolSize: 8
            }
          ]
        }
        break

      case 'workload':
        option = {
          title: {
            text: '团队工作负载分析',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'axis',
            formatter: (params: any) => {
              const completed = params[0]
              const inProgress = params[1]
              return `
                <div>
                  <strong>${completed.name}</strong><br/>
                  已完成: ${completed.value}<br/>
                  进行中: ${inProgress.value}<br/>
                  总计: ${completed.value + inProgress.value}
                </div>
              `
            }
          },
          legend: {
            data: ['已完成任务', '进行中任务'],
            top: 30
          },
          xAxis: {
            type: 'category',
            data: data.map(item => item.name)
          },
          yAxis: {
            type: 'value',
            name: '任务数量'
          },
          series: [
            {
              name: '已完成任务',
              type: 'bar',
              stack: 'total',
              data: data.map(item => item.tasksCompleted),
              itemStyle: {
                color: '#52c41a'
              }
            },
            {
              name: '进行中任务',
              type: 'bar',
              stack: 'total',
              data: data.map(item => item.tasksInProgress),
              itemStyle: {
                color: '#faad14'
              }
            }
          ]
        }
        break

      case 'efficiency':
        option = {
          title: {
            text: '团队效率雷达图',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            data: data.map(item => item.name),
            top: 30,
            type: 'scroll'
          },
          radar: {
            indicator: [
              { name: '任务完成率', max: 100 },
              { name: '工作效率', max: 100 },
              { name: '时间管理', max: 100 },
              { name: '质量评分', max: 100 },
              { name: '协作能力', max: 100 },
              { name: '创新能力', max: 100 }
            ],
            center: ['50%', '60%'],
            radius: '60%'
          },
          series: [
            {
              name: '团队效率',
              type: 'radar',
              data: data.slice(0, 3).map((item, index) => ({
                value: [
                  item.productivity,
                  item.efficiency,
                  Math.min(100, (item.workHours / 160) * 100),
                  85 + Math.random() * 10, // 模拟质量评分
                  80 + Math.random() * 15, // 模拟协作能力
                  75 + Math.random() * 20  // 模拟创新能力
                ],
                name: item.name,
                itemStyle: {
                  color: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'][index]
                },
                areaStyle: {
                  opacity: 0.3
                }
              }))
            }
          ]
        }
        break
    }

    chartInstance.current.setOption(option)

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, height, type])

  return (
    <div
      ref={chartRef}
      style={{ width: '100%', height: `${height}px` }}
    />
  )
}

export default TeamEfficiencyChart
