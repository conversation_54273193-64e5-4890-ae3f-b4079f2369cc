/**
 * Redux Store配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import { configureStore } from '@reduxjs/toolkit'
import { persistStore, persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import { combineReducers } from '@reduxjs/toolkit'

// 导入所有slice
import authSlice from './slices/authSlice'
import userSlice from './slices/userSlice'
import projectSlice from './slices/projectSlice'
import taskSlice from './slices/taskSlice'
import teamSlice from './slices/teamSlice'
import reportSlice from './slices/reportSlice'
import analyticsSlice from './slices/analyticsSlice'
import uiSlice from './slices/uiSlice'

// 持久化配置
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'user', 'ui'], // 只持久化这些slice
}

// 根reducer
const rootReducer = combineReducers({
  auth: authSlice,
  user: userSlice,
  project: projectSlice,
  task: taskSlice,
  team: teamSlice,
  report: reportSlice,
  analytics: analyticsSlice,
  ui: uiSlice,
})

// 持久化reducer
const persistedReducer = persistReducer(persistConfig, rootReducer)

// 配置store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
})

// 创建persistor
export const persistor = persistStore(store)

// 导出类型
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
