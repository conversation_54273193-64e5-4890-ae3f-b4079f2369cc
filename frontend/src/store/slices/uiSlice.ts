/**
 * UI状态管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// UI状态接口
interface UIState {
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
  language: string
  loading: {
    global: boolean
    [key: string]: boolean
  }
  notifications: {
    enabled: boolean
    sound: boolean
  }
  layout: {
    headerFixed: boolean
    sidebarFixed: boolean
  }
}

// 初始状态
const initialState: UIState = {
  sidebarCollapsed: false,
  theme: 'light',
  language: 'zh-CN',
  loading: {
    global: false,
  },
  notifications: {
    enabled: true,
    sound: true,
  },
  layout: {
    headerFixed: true,
    sidebarFixed: true,
  },
}

// 创建slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload
    },
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload
    },
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loading[action.payload.key] = action.payload.loading
    },
    toggleNotifications: (state) => {
      state.notifications.enabled = !state.notifications.enabled
    },
    setNotificationSound: (state, action: PayloadAction<boolean>) => {
      state.notifications.sound = action.payload
    },
    updateLayout: (state, action: PayloadAction<Partial<UIState['layout']>>) => {
      state.layout = { ...state.layout, ...action.payload }
    },
  },
})

export const {
  toggleSidebar,
  setSidebarCollapsed,
  setTheme,
  setLanguage,
  setGlobalLoading,
  setLoading,
  toggleNotifications,
  setNotificationSound,
  updateLayout,
} = uiSlice.actions

export default uiSlice.reducer
