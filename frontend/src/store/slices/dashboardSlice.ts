/**
 * 仪表板状态管理
 * 管理仪表板数据和状态
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { dashboardApi } from '../../services/api/dashboardApi';

// 类型定义
export interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedTasks: number;
  totalTasks: number;
  teamMembers: number;
  overdueTasks: number;
}

export interface ProjectProgress {
  id: string;
  name: string;
  progress: number;
  status: 'active' | 'completed' | 'paused' | 'planning';
  startDate: string;
  endDate: string;
}

export interface TaskDistribution {
  status: string;
  count: number;
  percentage: number;
}

export interface TeamPerformance {
  memberId: string;
  memberName: string;
  metrics: {
    taskCompletion: number;
    codeQuality: number;
    collaboration: number;
    innovation: number;
    learning: number;
    communication: number;
  };
}

export interface RiskAnalysis {
  date: string;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
}

export interface RecentActivity {
  id: string;
  type: 'project' | 'task' | 'team';
  title: string;
  description: string;
  timestamp: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
  };
}

export interface UpcomingDeadline {
  id: string;
  title: string;
  type: 'project' | 'task';
  deadline: string;
  priority: 'high' | 'medium' | 'low';
  progress: number;
  projectId?: string;
}

export interface DashboardData {
  stats: DashboardStats;
  projectProgress: ProjectProgress[];
  taskDistribution: TaskDistribution[];
  teamPerformance: TeamPerformance[];
  riskAnalysis: RiskAnalysis[];
  recentActivities: RecentActivity[];
  upcomingDeadlines: UpcomingDeadline[];
}

interface DashboardState {
  data: DashboardData | null;
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

// 初始状态
const initialState: DashboardState = {
  data: null,
  loading: false,
  error: null,
  lastUpdated: null,
};

// 异步操作
export const fetchDashboardData = createAsyncThunk(
  'dashboard/fetchData',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardApi.getDashboardData();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取仪表板数据失败');
    }
  }
);

export const refreshDashboardStats = createAsyncThunk(
  'dashboard/refreshStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardApi.getStats();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '刷新统计数据失败');
    }
  }
);

export const fetchProjectProgress = createAsyncThunk(
  'dashboard/fetchProjectProgress',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardApi.getProjectProgress();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取项目进度失败');
    }
  }
);

export const fetchTeamPerformance = createAsyncThunk(
  'dashboard/fetchTeamPerformance',
  async (timeRange: string = '30d', { rejectWithValue }) => {
    try {
      const response = await dashboardApi.getTeamPerformance(timeRange);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取团队绩效失败');
    }
  }
);

export const fetchRiskAnalysis = createAsyncThunk(
  'dashboard/fetchRiskAnalysis',
  async (days: number = 7, { rejectWithValue }) => {
    try {
      const response = await dashboardApi.getRiskAnalysis(days);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取风险分析失败');
    }
  }
);

// Slice
const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateStats: (state, action: PayloadAction<Partial<DashboardStats>>) => {
      if (state.data) {
        state.data.stats = { ...state.data.stats, ...action.payload };
      }
    },
    addRecentActivity: (state, action: PayloadAction<RecentActivity>) => {
      if (state.data) {
        state.data.recentActivities.unshift(action.payload);
        // 保持最新的10条活动
        if (state.data.recentActivities.length > 10) {
          state.data.recentActivities = state.data.recentActivities.slice(0, 10);
        }
      }
    },
    updateProjectProgress: (state, action: PayloadAction<{ projectId: string; progress: number }>) => {
      if (state.data) {
        const project = state.data.projectProgress.find(p => p.id === action.payload.projectId);
        if (project) {
          project.progress = action.payload.progress;
        }
      }
    },
    markDeadlineCompleted: (state, action: PayloadAction<string>) => {
      if (state.data) {
        state.data.upcomingDeadlines = state.data.upcomingDeadlines.filter(
          deadline => deadline.id !== action.payload
        );
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取仪表板数据
      .addCase(fetchDashboardData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDashboardData.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchDashboardData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 刷新统计数据
      .addCase(refreshDashboardStats.fulfilled, (state, action) => {
        if (state.data) {
          state.data.stats = action.payload;
          state.lastUpdated = new Date().toISOString();
        }
      })
      .addCase(refreshDashboardStats.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // 获取项目进度
      .addCase(fetchProjectProgress.fulfilled, (state, action) => {
        if (state.data) {
          state.data.projectProgress = action.payload;
        }
      })
      .addCase(fetchProjectProgress.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // 获取团队绩效
      .addCase(fetchTeamPerformance.fulfilled, (state, action) => {
        if (state.data) {
          state.data.teamPerformance = action.payload;
        }
      })
      .addCase(fetchTeamPerformance.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // 获取风险分析
      .addCase(fetchRiskAnalysis.fulfilled, (state, action) => {
        if (state.data) {
          state.data.riskAnalysis = action.payload;
        }
      })
      .addCase(fetchRiskAnalysis.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  updateStats,
  addRecentActivity,
  updateProjectProgress,
  markDeadlineCompleted,
} = dashboardSlice.actions;

export default dashboardSlice.reducer;

// 选择器
export const selectDashboardData = (state: { dashboard: DashboardState }) => state.dashboard.data;
export const selectDashboardLoading = (state: { dashboard: DashboardState }) => state.dashboard.loading;
export const selectDashboardError = (state: { dashboard: DashboardState }) => state.dashboard.error;
export const selectDashboardStats = (state: { dashboard: DashboardState }) => state.dashboard.data?.stats;
export const selectProjectProgress = (state: { dashboard: DashboardState }) => state.dashboard.data?.projectProgress;
export const selectTaskDistribution = (state: { dashboard: DashboardState }) => state.dashboard.data?.taskDistribution;
export const selectTeamPerformance = (state: { dashboard: DashboardState }) => state.dashboard.data?.teamPerformance;
export const selectRiskAnalysis = (state: { dashboard: DashboardState }) => state.dashboard.data?.riskAnalysis;
export const selectRecentActivities = (state: { dashboard: DashboardState }) => state.dashboard.data?.recentActivities;
export const selectUpcomingDeadlines = (state: { dashboard: DashboardState }) => state.dashboard.data?.upcomingDeadlines;
