/**
 * 项目状态管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { projectService } from '@/services/projectService'
import { Project, ProjectCreateRequest, ProjectUpdateRequest } from '@/types/project'

// 项目状态接口
interface ProjectState {
  projects: Project[]
  currentProject: Project | null
  isLoading: boolean
  error: string | null
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  filters: {
    status?: string
    priority?: string
    search?: string
  }
}

// 初始状态
const initialState: ProjectState = {
  projects: [],
  currentProject: null,
  isLoading: false,
  error: null,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },
  filters: {},
}

// 异步actions
export const fetchProjects = createAsyncThunk(
  'project/fetchProjects',
  async (params: { page?: number; size?: number; filters?: any }, { rejectWithValue }) => {
    try {
      const response = await projectService.getProjects(params)
      return response
    } catch (error: any) {
      return rejectWithValue(error.message || '获取项目列表失败')
    }
  }
)

export const fetchProjectById = createAsyncThunk(
  'project/fetchProjectById',
  async (projectId: string, { rejectWithValue }) => {
    try {
      const project = await projectService.getProjectById(projectId)
      return project
    } catch (error: any) {
      return rejectWithValue(error.message || '获取项目详情失败')
    }
  }
)

export const createProject = createAsyncThunk(
  'project/createProject',
  async (projectData: ProjectCreateRequest, { rejectWithValue }) => {
    try {
      const project = await projectService.createProject(projectData)
      return project
    } catch (error: any) {
      return rejectWithValue(error.message || '创建项目失败')
    }
  }
)

export const updateProject = createAsyncThunk(
  'project/updateProject',
  async ({ id, data }: { id: string; data: ProjectUpdateRequest }, { rejectWithValue }) => {
    try {
      const project = await projectService.updateProject(id, data)
      return project
    } catch (error: any) {
      return rejectWithValue(error.message || '更新项目失败')
    }
  }
)

export const deleteProject = createAsyncThunk(
  'project/deleteProject',
  async (projectId: string, { rejectWithValue }) => {
    try {
      await projectService.deleteProject(projectId)
      return projectId
    } catch (error: any) {
      return rejectWithValue(error.message || '删除项目失败')
    }
  }
)

// 创建slice
const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCurrentProject: (state, action: PayloadAction<Project | null>) => {
      state.currentProject = action.payload
    },
    updatePagination: (state, action: PayloadAction<Partial<typeof initialState.pagination>>) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },
    updateFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = {}
    },
    updateProjectInList: (state, action: PayloadAction<Project>) => {
      const index = state.projects.findIndex(p => p.id === action.payload.id)
      if (index !== -1) {
        state.projects[index] = action.payload
      }
    },
  },
  extraReducers: (builder) => {
    // 获取项目列表
    builder
      .addCase(fetchProjects.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchProjects.fulfilled, (state, action) => {
        state.isLoading = false
        state.projects = action.payload.content
        state.pagination = {
          current: action.payload.number + 1,
          pageSize: action.payload.size,
          total: action.payload.totalElements,
        }
      })
      .addCase(fetchProjects.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // 获取项目详情
    builder
      .addCase(fetchProjectById.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchProjectById.fulfilled, (state, action) => {
        state.isLoading = false
        state.currentProject = action.payload
      })
      .addCase(fetchProjectById.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // 创建项目
    builder
      .addCase(createProject.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(createProject.fulfilled, (state, action) => {
        state.isLoading = false
        state.projects.unshift(action.payload)
        state.pagination.total += 1
      })
      .addCase(createProject.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // 更新项目
    builder
      .addCase(updateProject.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(updateProject.fulfilled, (state, action) => {
        state.isLoading = false
        const index = state.projects.findIndex(p => p.id === action.payload.id)
        if (index !== -1) {
          state.projects[index] = action.payload
        }
        if (state.currentProject?.id === action.payload.id) {
          state.currentProject = action.payload
        }
      })
      .addCase(updateProject.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // 删除项目
    builder
      .addCase(deleteProject.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(deleteProject.fulfilled, (state, action) => {
        state.isLoading = false
        state.projects = state.projects.filter(p => p.id !== action.payload)
        state.pagination.total -= 1
        if (state.currentProject?.id === action.payload) {
          state.currentProject = null
        }
      })
      .addCase(deleteProject.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
  },
})

export const {
  clearError,
  setCurrentProject,
  updatePagination,
  updateFilters,
  clearFilters,
  updateProjectInList,
} = projectSlice.actions

export default projectSlice.reducer
