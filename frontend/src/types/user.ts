/**
 * 用户相关类型定义
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

export interface User {
  id: string
  username: string
  email: string
  firstName?: string
  lastName?: string
  avatar?: string
  phone?: string
  department?: string
  position?: string
  bio?: string
  isActive: boolean
  isEmailVerified: boolean
  roles: Role[]
  permissions: Permission[]
  preferences: UserPreferences
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
}

export interface Role {
  id: string
  name: string
  displayName: string
  description?: string
  permissions: Permission[]
}

export interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description?: string
}

export interface UserPreferences {
  language: string
  timezone: string
  theme: 'light' | 'dark' | 'auto'
  notifications: {
    email: boolean
    push: boolean
    desktop: boolean
  }
  dashboard: {
    layout: string
    widgets: string[]
  }
}

export interface UserProfile {
  firstName?: string
  lastName?: string
  avatar?: string
  phone?: string
  department?: string
  position?: string
  bio?: string
}

export interface UserCreateRequest {
  username: string
  email: string
  password: string
  firstName?: string
  lastName?: string
  phone?: string
  department?: string
  position?: string
  roleIds?: string[]
}

export interface UserUpdateRequest {
  username?: string
  email?: string
  firstName?: string
  lastName?: string
  phone?: string
  department?: string
  position?: string
  bio?: string
  isActive?: boolean
  roleIds?: string[]
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface ResetPasswordRequest {
  email: string
}

export interface UserListResponse {
  content: User[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

export interface UserStats {
  totalUsers: number
  activeUsers: number
  newUsersThisMonth: number
  usersByRole: Record<string, number>
  usersByDepartment: Record<string, number>
}
