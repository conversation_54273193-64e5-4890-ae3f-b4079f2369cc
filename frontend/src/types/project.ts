/**
 * 项目相关类型定义
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import { User } from './user'

export interface Project {
  id: string
  name: string
  description?: string
  status: ProjectStatus
  priority: ProjectPriority
  progress: number
  startDate: string
  endDate?: string
  budget?: number
  actualCost?: number
  owner: User
  manager?: User
  team: ProjectMember[]
  tags: string[]
  isPublic: boolean
  settings: ProjectSettings
  stats: ProjectStats
  createdAt: string
  updatedAt: string
}

export enum ProjectStatus {
  PLANNING = 'PLANNING',
  ACTIVE = 'ACTIVE',
  ON_HOLD = 'ON_HOLD',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export enum ProjectPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export interface ProjectMember {
  id: string
  user: User
  role: ProjectRole
  joinedAt: string
  permissions: string[]
}

export enum ProjectRole {
  OWNER = 'OWNER',
  MANAGER = 'MANAGER',
  DEVELOPER = 'DEVELOPER',
  TESTER = 'TESTER',
  DESIGNER = 'DESIGNER',
  ANALYST = 'ANALYST',
  VIEWER = 'VIEWER',
}

export interface ProjectSettings {
  isTaskAutoAssignment: boolean
  isTimeTracking: boolean
  isPublicComments: boolean
  notificationSettings: {
    taskUpdates: boolean
    deadlineReminders: boolean
    teamUpdates: boolean
  }
  workflowSettings: {
    requireApproval: boolean
    allowSelfAssignment: boolean
    autoCloseCompletedTasks: boolean
  }
}

export interface ProjectStats {
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  todoTasks: number
  blockedTasks: number
  overdueTasks: number
  teamSize: number
  activeMembers: number
  totalTimeSpent: number
  estimatedTimeRemaining: number
  bugCount: number
  fixedBugs: number
}

export interface ProjectCreateRequest {
  name: string
  description?: string
  status?: ProjectStatus
  priority?: ProjectPriority
  startDate: string
  endDate?: string
  budget?: number
  ownerId: string
  managerId?: string
  teamMemberIds?: string[]
  tags?: string[]
  isPublic?: boolean
  settings?: Partial<ProjectSettings>
}

export interface ProjectUpdateRequest {
  name?: string
  description?: string
  status?: ProjectStatus
  priority?: ProjectPriority
  startDate?: string
  endDate?: string
  budget?: number
  managerId?: string
  tags?: string[]
  isPublic?: boolean
  settings?: Partial<ProjectSettings>
}

export interface ProjectListResponse {
  content: Project[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

export interface ProjectDashboard {
  project: Project
  recentTasks: any[]
  recentActivities: any[]
  teamActivity: any[]
  progressChart: any
  burndownChart: any
  riskAnalysis: any
}

export interface ProjectTemplate {
  id: string
  name: string
  description?: string
  category: string
  tags: string[]
  structure: {
    phases: ProjectPhase[]
    taskTemplates: any[]
    milestones: any[]
  }
  isPublic: boolean
  usageCount: number
  createdBy: User
  createdAt: string
}

export interface ProjectPhase {
  id: string
  name: string
  description?: string
  order: number
  startDate?: string
  endDate?: string
  status: ProjectStatus
  progress: number
  tasks: any[]
  dependencies: string[]
}
