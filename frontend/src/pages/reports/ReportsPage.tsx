/**
 * 报表中心页面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Tabs,
  Button,
  Space,
  DatePicker,
  Select,
  Statistic,
  Table,
  Tag,
  Progress,
} from 'antd'
import {
  DownloadOutlined,
  PrinterOutlined,
  ShareAltOutlined,
  Bar<PERSON>hartOutlined,
  Pie<PERSON><PERSON>Outlined,
  Line<PERSON>hartOutlined,
} from '@ant-design/icons'

import ProjectProgress<PERSON><PERSON> from '@/components/charts/ProjectProgressChart'
import TaskStatus<PERSON>hart from '@/components/charts/TaskStatusChart'
import TeamEfficiencyChart from '@/components/charts/TeamEfficiencyChart'
import AIAnalysisChart from '@/components/charts/AIAnalysis<PERSON><PERSON>'
import Dashboard<PERSON>hart from '@/components/charts/DashboardChart'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { RangePicker } = DatePicker
const { Option } = Select

const ReportsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [dateRange, setDateRange] = useState<any>(null)
  const [reportType, setReportType] = useState('weekly')

  // 模拟报表数据
  const reportStats = {
    totalProjects: 12,
    completedProjects: 8,
    totalTasks: 203,
    completedTasks: 156,
    teamMembers: 24,
    activeMembers: 18,
    avgProjectDuration: 45,
    onTimeDelivery: 85,
  }

  const projectReportData = [
    {
      id: '1',
      name: 'AI客服系统',
      status: '进行中',
      progress: 75,
      startDate: '2025-07-01',
      endDate: '2025-09-15',
      budget: 500000,
      actualCost: 375000,
      teamSize: 8,
      tasksCompleted: 45,
      totalTasks: 60,
    },
    {
      id: '2',
      name: '移动端应用重构',
      status: '进行中',
      progress: 45,
      startDate: '2025-08-01',
      endDate: '2025-10-01',
      budget: 300000,
      actualCost: 135000,
      teamSize: 5,
      tasksCompleted: 18,
      totalTasks: 40,
    },
    {
      id: '3',
      name: '数据分析平台',
      status: '待审核',
      progress: 90,
      startDate: '2025-06-15',
      endDate: '2025-08-30',
      budget: 800000,
      actualCost: 720000,
      teamSize: 12,
      tasksCompleted: 54,
      totalTasks: 60,
    },
  ]

  const handleExport = (format: 'pdf' | 'excel') => {
    console.log(`导出${format}格式报表`)
    // TODO: 实现导出功能
  }

  const handlePrint = () => {
    window.print()
  }

  const projectColumns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === '进行中' ? 'processing' : status === '已完成' ? 'success' : 'warning'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '预算/实际',
      key: 'budget',
      width: 150,
      render: (record: any) => (
        <div>
          <div>预算: ¥{record.budget.toLocaleString()}</div>
          <div>实际: ¥{record.actualCost.toLocaleString()}</div>
        </div>
      ),
    },
    {
      title: '团队规模',
      dataIndex: 'teamSize',
      key: 'teamSize',
      width: 100,
    },
    {
      title: '任务完成',
      key: 'tasks',
      width: 120,
      render: (record: any) => (
        <div>
          {record.tasksCompleted}/{record.totalTasks}
          <Progress
            percent={(record.tasksCompleted / record.totalTasks) * 100}
            size="small"
            showInfo={false}
            className="mt-1"
          />
        </div>
      ),
    },
  ]

  return (
    <div className="reports-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">报表中心</Title>
          <Text type="secondary">
            查看项目、任务、团队的详细报表和分析数据
          </Text>
        </div>
        <Space>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            placeholder={['开始日期', '结束日期']}
          />
          <Select
            value={reportType}
            onChange={setReportType}
            style={{ width: 120 }}
          >
            <Option value="daily">日报</Option>
            <Option value="weekly">周报</Option>
            <Option value="monthly">月报</Option>
            <Option value="quarterly">季报</Option>
          </Select>
          <Button icon={<DownloadOutlined />} onClick={() => handleExport('excel')}>
            导出Excel
          </Button>
          <Button icon={<DownloadOutlined />} onClick={() => handleExport('pdf')}>
            导出PDF
          </Button>
          <Button icon={<PrinterOutlined />} onClick={handlePrint}>
            打印
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="项目总数"
              value={reportStats.totalProjects}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="完成项目"
              value={reportStats.completedProjects}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="任务完成率"
              value={(reportStats.completedTasks / reportStats.totalTasks * 100).toFixed(1)}
              suffix="%"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="按时交付率"
              value={reportStats.onTimeDelivery}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 报表内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="综合概览" key="overview">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="项目进度分析" size="small">
                  <ProjectProgressChart height={300} />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="任务状态分布" size="small">
                  <TaskStatusChart height={300} />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="团队效率分析" size="small">
                  <TeamEfficiencyChart type="productivity" height={300} />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="项目趋势分析" size="small">
                  <DashboardChart type="trend" height={300} />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="项目报表" key="projects">
            <div className="mb-4">
              <Space>
                <Button icon={<BarChartOutlined />}>柱状图</Button>
                <Button icon={<LineChartOutlined />}>折线图</Button>
                <Button icon={<PieChartOutlined />}>饼图</Button>
              </Space>
            </div>
            <Table
              columns={projectColumns}
              dataSource={projectReportData}
              rowKey="id"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </TabPane>

          <TabPane tab="团队报表" key="team">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={8}>
                <Card title="团队生产力" size="small">
                  <TeamEfficiencyChart type="productivity" height={250} />
                </Card>
              </Col>
              <Col xs={24} lg={8}>
                <Card title="工作负载分析" size="small">
                  <TeamEfficiencyChart type="workload" height={250} />
                </Card>
              </Col>
              <Col xs={24} lg={8}>
                <Card title="效率雷达图" size="small">
                  <TeamEfficiencyChart type="efficiency" height={250} />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="AI分析报表" key="ai">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="风险分析矩阵" size="small">
                  <AIAnalysisChart type="risk" height={300} />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="进度预测分析" size="small">
                  <AIAnalysisChart type="prediction" height={300} />
                </Card>
              </Col>
              <Col xs={24}>
                <Card title="AI优化建议" size="small">
                  <AIAnalysisChart type="recommendation" height={300} />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="自定义报表" key="custom">
            <div className="text-center py-20">
              <Title level={4}>自定义报表功能</Title>
              <Text type="secondary">
                拖拽式报表设计器，支持自定义图表类型、数据源和样式
              </Text>
              <div className="mt-4">
                <Button type="primary" size="large">
                  创建自定义报表
                </Button>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default ReportsPage
