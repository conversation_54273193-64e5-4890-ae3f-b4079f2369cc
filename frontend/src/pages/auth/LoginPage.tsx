/**
 * 登录页面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { Form, Input, Button, Checkbox, Alert, Divider, Space } from 'antd'
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons'

import { RootState, AppDispatch } from '@/store'
import { login, clearError } from '@/store/slices/authSlice'

interface LoginFormValues {
  email: string
  password: string
  rememberMe: boolean
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch<AppDispatch>()
  
  const { isLoading, error, isAuthenticated, loginAttempts } = useSelector(
    (state: RootState) => state.auth
  )

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/dashboard'

  useEffect(() => {
    // 如果已经登录，重定向到目标页面
    if (isAuthenticated) {
      navigate(from, { replace: true })
    }
  }, [isAuthenticated, navigate, from])

  useEffect(() => {
    // 清除错误信息
    return () => {
      dispatch(clearError())
    }
  }, [dispatch])

  // 处理表单提交
  const handleSubmit = async (values: LoginFormValues) => {
    try {
      await dispatch(login({
        email: values.email,
        password: values.password,
      })).unwrap()
      
      // 登录成功后会通过useEffect重定向
    } catch (error) {
      // 错误已经在store中处理
      console.error('登录失败:', error)
    }
  }

  // 处理表单失败
  const handleSubmitFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* 头部 */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">AI</span>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            登录您的账户
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            或者{' '}
            <Link
              to="/register"
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              创建新账户
            </Link>
          </p>
        </div>

        {/* 登录表单 */}
        <div className="bg-white py-8 px-6 shadow-lg rounded-lg">
          {/* 错误提示 */}
          {error && (
            <Alert
              message="登录失败"
              description={error}
              type="error"
              showIcon
              closable
              onClose={() => dispatch(clearError())}
              className="mb-6"
            />
          )}

          {/* 登录尝试次数警告 */}
          {loginAttempts >= 3 && (
            <Alert
              message="安全提醒"
              description={`您已尝试登录 ${loginAttempts} 次，请确认账户信息是否正确`}
              type="warning"
              showIcon
              className="mb-6"
            />
          )}

          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            onFinishFailed={handleSubmitFailed}
            layout="vertical"
            size="large"
            autoComplete="off"
          >
            {/* 邮箱 */}
            <Form.Item
              name="email"
              label="邮箱地址"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined className="text-gray-400" />}
                placeholder="请输入邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            {/* 密码 */}
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="text-gray-400" />}
                placeholder="请输入密码"
                autoComplete="current-password"
              />
            </Form.Item>

            {/* 记住我和忘记密码 */}
            <div className="flex items-center justify-between mb-6">
              <Form.Item name="rememberMe" valuePropName="checked" noStyle>
                <Checkbox>记住我</Checkbox>
              </Form.Item>
              <Link
                to="/forgot-password"
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                忘记密码？
              </Link>
            </div>

            {/* 登录按钮 */}
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                block
                size="large"
                className="h-12"
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          {/* 分割线 */}
          <Divider>或者</Divider>

          {/* 第三方登录 */}
          <Space direction="vertical" className="w-full">
            <Button
              block
              size="large"
              icon={<UserOutlined />}
              className="h-12"
              disabled
            >
              使用企业账户登录
            </Button>
          </Space>

          {/* 底部链接 */}
          <div className="mt-6 text-center text-sm text-gray-600">
            <p>
              还没有账户？{' '}
              <Link
                to="/register"
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                立即注册
              </Link>
            </p>
          </div>
        </div>

        {/* 帮助信息 */}
        <div className="text-center text-xs text-gray-500">
          <p>
            登录即表示您同意我们的{' '}
            <a href="/terms" className="text-blue-600 hover:text-blue-500">
              服务条款
            </a>{' '}
            和{' '}
            <a href="/privacy" className="text-blue-600 hover:text-blue-500">
              隐私政策
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
