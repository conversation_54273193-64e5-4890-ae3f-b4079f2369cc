/**
 * 任务管理页面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Button,
  Space,
  Tag,
  Avatar,
  Progress,
  Select,
  Input,
  DatePicker,
  Modal,
  Form,
  message,
  Tooltip,
  Dropdown,
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  CalendarOutlined,
  UserOutlined,
  FlagOutlined,
  MoreOutlined,
  DragOutlined,
} from '@ant-design/icons'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'

const { Title } = Typography
const { Search } = Input
const { Option } = Select

// 任务状态枚举
enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  IN_REVIEW = 'in_review',
  DONE = 'done',
}

// 任务优先级枚举
enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// 任务接口
interface Task {
  id: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  assignee?: {
    id: string
    name: string
    avatar?: string
  }
  project?: {
    id: string
    name: string
  }
  dueDate?: string
  createdAt: string
  updatedAt: string
  tags: string[]
  progress: number
}

// 模拟任务数据
const mockTasks: Task[] = [
  {
    id: '1',
    title: '用户登录功能开发',
    description: '实现用户登录、注册、忘记密码功能',
    status: TaskStatus.TODO,
    priority: TaskPriority.HIGH,
    assignee: { id: '1', name: '张三', avatar: '' },
    project: { id: '1', name: 'AI客服系统' },
    dueDate: '2025-08-25',
    createdAt: '2025-08-15',
    updatedAt: '2025-08-16',
    tags: ['前端', '认证'],
    progress: 0,
  },
  {
    id: '2',
    title: '数据库设计',
    description: '设计用户表、项目表、任务表等数据库结构',
    status: TaskStatus.IN_PROGRESS,
    priority: TaskPriority.MEDIUM,
    assignee: { id: '2', name: '李四', avatar: '' },
    project: { id: '1', name: 'AI客服系统' },
    dueDate: '2025-08-22',
    createdAt: '2025-08-14',
    updatedAt: '2025-08-16',
    tags: ['后端', '数据库'],
    progress: 60,
  },
  {
    id: '3',
    title: 'API接口文档编写',
    description: '编写RESTful API接口文档',
    status: TaskStatus.IN_REVIEW,
    priority: TaskPriority.LOW,
    assignee: { id: '3', name: '王五', avatar: '' },
    project: { id: '2', name: '移动端应用' },
    dueDate: '2025-08-20',
    createdAt: '2025-08-13',
    updatedAt: '2025-08-16',
    tags: ['文档', 'API'],
    progress: 90,
  },
  {
    id: '4',
    title: '单元测试编写',
    description: '为核心功能编写单元测试',
    status: TaskStatus.DONE,
    priority: TaskPriority.MEDIUM,
    assignee: { id: '4', name: '赵六', avatar: '' },
    project: { id: '1', name: 'AI客服系统' },
    dueDate: '2025-08-18',
    createdAt: '2025-08-12',
    updatedAt: '2025-08-16',
    tags: ['测试', '质量'],
    progress: 100,
  },
]

const TaskListPage: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>(mockTasks)
  const [filteredTasks, setFilteredTasks] = useState<Task[]>(mockTasks)
  const [viewMode, setViewMode] = useState<'kanban' | 'list'>('kanban')
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    priority: '',
    assignee: '',
    project: '',
  })

  // 任务状态配置
  const statusConfig = {
    [TaskStatus.TODO]: { title: '待办', color: 'default' },
    [TaskStatus.IN_PROGRESS]: { title: '进行中', color: 'processing' },
    [TaskStatus.IN_REVIEW]: { title: '待审核', color: 'warning' },
    [TaskStatus.DONE]: { title: '已完成', color: 'success' },
  }

  // 优先级配置
  const priorityConfig = {
    [TaskPriority.LOW]: { title: '低', color: 'green' },
    [TaskPriority.MEDIUM]: { title: '中', color: 'blue' },
    [TaskPriority.HIGH]: { title: '高', color: 'orange' },
    [TaskPriority.URGENT]: { title: '紧急', color: 'red' },
  }

  // 处理拖拽
  const handleDragEnd = (result: any) => {
    if (!result.destination) return

    const { source, destination } = result

    if (source.droppableId === destination.droppableId) {
      // 同一列内拖拽
      const columnTasks = tasks.filter(task => task.status === source.droppableId)
      const [reorderedTask] = columnTasks.splice(source.index, 1)
      columnTasks.splice(destination.index, 0, reorderedTask)

      // 更新任务列表
      const newTasks = tasks.map(task => {
        if (task.status === source.droppableId) {
          const index = columnTasks.findIndex(t => t.id === task.id)
          return index !== -1 ? columnTasks[index] : task
        }
        return task
      })

      setTasks(newTasks)
    } else {
      // 跨列拖拽
      const newTasks = tasks.map(task => {
        if (task.id === result.draggableId) {
          return { ...task, status: destination.droppableId as TaskStatus }
        }
        return task
      })

      setTasks(newTasks)
      message.success('任务状态已更新')
    }
  }

  // 应用筛选
  useEffect(() => {
    let filtered = tasks

    if (filters.search) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(filters.search.toLowerCase()) ||
        task.description?.toLowerCase().includes(filters.search.toLowerCase())
      )
    }

    if (filters.status) {
      filtered = filtered.filter(task => task.status === filters.status)
    }

    if (filters.priority) {
      filtered = filtered.filter(task => task.priority === filters.priority)
    }

    if (filters.assignee) {
      filtered = filtered.filter(task => task.assignee?.id === filters.assignee)
    }

    if (filters.project) {
      filtered = filtered.filter(task => task.project?.id === filters.project)
    }

    setFilteredTasks(filtered)
  }, [tasks, filters])

  // 任务卡片组件
  const TaskCard: React.FC<{ task: Task; index: number }> = ({ task, index }) => {
    const menuItems = [
      {
        key: 'edit',
        label: '编辑任务',
        onClick: () => message.info('编辑功能开发中'),
      },
      {
        key: 'duplicate',
        label: '复制任务',
        onClick: () => message.info('复制功能开发中'),
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'delete',
        label: '删除任务',
        danger: true,
        onClick: () => {
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除任务"${task.title}"吗？`,
            okText: '删除',
            okType: 'danger',
            cancelText: '取消',
            onOk: () => {
              setTasks(tasks.filter(t => t.id !== task.id))
              message.success('任务删除成功')
            },
          })
        },
      },
    ]

    return (
      <Draggable draggableId={task.id} index={index}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            className={`mb-3 ${snapshot.isDragging ? 'opacity-75' : ''}`}
          >
            <Card
              size="small"
              className="cursor-pointer hover:shadow-md transition-shadow"
              bodyStyle={{ padding: '12px' }}
              actions={[
                <Tooltip title="拖拽移动">
                  <div {...provided.dragHandleProps}>
                    <DragOutlined />
                  </div>
                </Tooltip>,
                <Dropdown menu={{ items: menuItems }} trigger={['click']}>
                  <MoreOutlined />
                </Dropdown>,
              ]}
            >
              <div className="mb-2">
                <div className="flex justify-between items-start">
                  <h4 className="text-sm font-medium mb-1 text-ellipsis">
                    {task.title}
                  </h4>
                  <Tag color={priorityConfig[task.priority].color} size="small">
                    {priorityConfig[task.priority].title}
                  </Tag>
                </div>
                {task.description && (
                  <p className="text-xs text-gray-500 mb-2 text-ellipsis-2">
                    {task.description}
                  </p>
                )}
              </div>

              {task.progress > 0 && (
                <Progress percent={task.progress} size="small" className="mb-2" />
              )}

              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  {task.assignee && (
                    <Tooltip title={task.assignee.name}>
                      <Avatar size="small" src={task.assignee.avatar}>
                        {task.assignee.name.charAt(0)}
                      </Avatar>
                    </Tooltip>
                  )}
                  {task.dueDate && (
                    <span className="text-xs text-gray-500">
                      <CalendarOutlined className="mr-1" />
                      {new Date(task.dueDate).toLocaleDateString('zh-CN')}
                    </span>
                  )}
                </div>
                <div className="flex flex-wrap gap-1">
                  {task.tags.slice(0, 2).map(tag => (
                    <Tag key={tag} size="small">{tag}</Tag>
                  ))}
                  {task.tags.length > 2 && (
                    <Tag size="small">+{task.tags.length - 2}</Tag>
                  )}
                </div>
              </div>
            </Card>
          </div>
        )}
      </Draggable>
    )
  }

  // 看板列组件
  const KanbanColumn: React.FC<{ status: TaskStatus; tasks: Task[] }> = ({ status, tasks }) => (
    <Col xs={24} sm={12} lg={6}>
      <Card
        title={
          <div className="flex justify-between items-center">
            <span>{statusConfig[status].title}</span>
            <Tag color={statusConfig[status].color}>{tasks.length}</Tag>
          </div>
        }
        className="h-full"
        bodyStyle={{ padding: '16px', minHeight: '500px' }}
      >
        <Droppable droppableId={status}>
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`min-h-full ${snapshot.isDraggingOver ? 'bg-blue-50' : ''}`}
            >
              {tasks.map((task, index) => (
                <TaskCard key={task.id} task={task} index={index} />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </Card>
    </Col>
  )

  return (
    <div className="task-list-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">任务管理</Title>
          <p className="text-gray-600 m-0">
            管理和跟踪项目任务，使用看板视图拖拽更新任务状态
          </p>
        </div>
        <Space>
          <Select
            value={viewMode}
            onChange={setViewMode}
            style={{ width: 120 }}
          >
            <Option value="kanban">看板视图</Option>
            <Option value="list">列表视图</Option>
          </Select>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建任务
          </Button>
        </Space>
      </div>

      {/* 筛选和搜索 */}
      <Card className="mb-4">
        <div className="flex flex-wrap gap-4 items-center">
          <Search
            placeholder="搜索任务标题或描述"
            allowClear
            style={{ width: 300 }}
            value={filters.search}
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            enterButton={<SearchOutlined />}
          />

          <Select
            placeholder="任务状态"
            allowClear
            style={{ width: 120 }}
            value={filters.status}
            onChange={(value) => setFilters({ ...filters, status: value })}
          >
            {Object.entries(statusConfig).map(([key, config]) => (
              <Option key={key} value={key}>{config.title}</Option>
            ))}
          </Select>

          <Select
            placeholder="优先级"
            allowClear
            style={{ width: 100 }}
            value={filters.priority}
            onChange={(value) => setFilters({ ...filters, priority: value })}
          >
            {Object.entries(priorityConfig).map(([key, config]) => (
              <Option key={key} value={key}>{config.title}</Option>
            ))}
          </Select>

          <Select
            placeholder="负责人"
            allowClear
            style={{ width: 120 }}
            value={filters.assignee}
            onChange={(value) => setFilters({ ...filters, assignee: value })}
          >
            <Option value="1">张三</Option>
            <Option value="2">李四</Option>
            <Option value="3">王五</Option>
            <Option value="4">赵六</Option>
          </Select>

          <Select
            placeholder="所属项目"
            allowClear
            style={{ width: 150 }}
            value={filters.project}
            onChange={(value) => setFilters({ ...filters, project: value })}
          >
            <Option value="1">AI客服系统</Option>
            <Option value="2">移动端应用</Option>
          </Select>

          <Button icon={<FilterOutlined />}>
            更多筛选
          </Button>
        </div>
      </Card>

      {/* 任务内容区域 */}
      {viewMode === 'kanban' ? (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Row gutter={[16, 16]}>
            {Object.values(TaskStatus).map(status => {
              const statusTasks = filteredTasks.filter(task => task.status === status)
              return (
                <KanbanColumn
                  key={status}
                  status={status}
                  tasks={statusTasks}
                />
              )
            })}
          </Row>
        </DragDropContext>
      ) : (
        <Card>
          <div className="text-center py-20 text-gray-500">
            列表视图开发中...
          </div>
        </Card>
      )}

      {/* 创建任务模态框 */}
      <Modal
        title="创建新任务"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout="vertical">
          <Form.Item
            name="title"
            label="任务标题"
            rules={[{ required: true, message: '请输入任务标题' }]}
          >
            <Input placeholder="请输入任务标题" />
          </Form.Item>

          <Form.Item name="description" label="任务描述">
            <Input.TextArea rows={3} placeholder="请输入任务描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="priority" label="优先级">
                <Select placeholder="选择优先级">
                  {Object.entries(priorityConfig).map(([key, config]) => (
                    <Option key={key} value={key}>{config.title}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="assignee" label="负责人">
                <Select placeholder="选择负责人">
                  <Option value="1">张三</Option>
                  <Option value="2">李四</Option>
                  <Option value="3">王五</Option>
                  <Option value="4">赵六</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="project" label="所属项目">
                <Select placeholder="选择项目">
                  <Option value="1">AI客服系统</Option>
                  <Option value="2">移动端应用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="dueDate" label="截止日期">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="tags" label="任务标签">
            <Select mode="tags" placeholder="添加标签">
              <Option value="前端">前端</Option>
              <Option value="后端">后端</Option>
              <Option value="测试">测试</Option>
              <Option value="文档">文档</Option>
            </Select>
          </Form.Item>

          <Form.Item className="mb-0 mt-6">
            <div className="flex justify-end space-x-2">
              <Button onClick={() => setCreateModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建任务
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TaskListPage
