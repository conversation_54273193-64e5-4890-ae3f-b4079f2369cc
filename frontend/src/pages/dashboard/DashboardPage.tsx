/**
 * 仪表板页面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  List,
  Avatar,
  Tag,
  Button,
  Space,
  Typography,
  Divider,
  Alert,
} from 'antd'
import {
  ProjectOutlined,
  CheckSquareOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  WarningOutlined,
  RightOutlined,
  CalendarOutlined,
} from '@ant-design/icons'

import { RootState } from '@/store'
import Dashboard<PERSON>hart from '@/components/charts/DashboardChart'
import ProjectProgress<PERSON>hart from '@/components/charts/ProjectProgressChart'
import TaskStatus<PERSON>hart from '@/components/charts/TaskStatusChart'
import TeamEfficiency<PERSON>hart from '@/components/charts/TeamEfficiencyChart'
import AIAnalysisChart from '@/components/charts/AIAnalysisChart'

const { Title, Text } = Typography

// 模拟数据
const mockStats = {
  totalProjects: 12,
  activeProjects: 8,
  completedTasks: 156,
  totalTasks: 203,
  teamMembers: 24,
  activeMembers: 18,
  hoursThisWeek: 142,
  targetHours: 160,
}

const mockRecentProjects = [
  {
    id: '1',
    name: 'AI客服系统',
    progress: 75,
    status: 'active',
    dueDate: '2025-09-15',
    team: ['张三', '李四', '王五'],
  },
  {
    id: '2',
    name: '移动端应用重构',
    progress: 45,
    status: 'active',
    dueDate: '2025-10-01',
    team: ['赵六', '钱七'],
  },
  {
    id: '3',
    name: '数据分析平台',
    progress: 90,
    status: 'review',
    dueDate: '2025-08-30',
    team: ['孙八', '周九', '吴十'],
  },
]

const mockRecentTasks = [
  {
    id: '1',
    title: '完成用户界面设计',
    project: 'AI客服系统',
    assignee: '张三',
    priority: 'high',
    dueDate: '2025-08-18',
    status: 'in_progress',
  },
  {
    id: '2',
    title: '数据库性能优化',
    project: '数据分析平台',
    assignee: '李四',
    priority: 'medium',
    dueDate: '2025-08-20',
    status: 'todo',
  },
  {
    id: '3',
    title: 'API接口测试',
    project: '移动端应用重构',
    assignee: '王五',
    priority: 'low',
    dueDate: '2025-08-22',
    status: 'completed',
  },
]

const mockAlerts = [
  {
    id: '1',
    type: 'warning',
    message: 'AI客服系统项目进度落后，建议关注',
    time: '2小时前',
  },
  {
    id: '2',
    type: 'info',
    message: '数据分析平台即将进入验收阶段',
    time: '4小时前',
  },
]

const DashboardPage: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 模拟数据加载
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'processing'
      case 'completed':
        return 'success'
      case 'review':
        return 'warning'
      case 'on_hold':
        return 'default'
      default:
        return 'default'
    }
  }

  // 获取优先级标签颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'red'
      case 'medium':
        return 'orange'
      case 'low':
        return 'green'
      default:
        return 'default'
    }
  }

  return (
    <div className="dashboard-page">
      {/* 欢迎信息 */}
      <div className="mb-6">
        <Title level={2}>
          欢迎回来，{user?.firstName || user?.username}！
        </Title>
        <Text type="secondary">
          今天是 {new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long',
          })}
        </Text>
      </div>

      {/* 预警信息 */}
      {mockAlerts.length > 0 && (
        <div className="mb-6">
          {mockAlerts.map((alert) => (
            <Alert
              key={alert.id}
              message={alert.message}
              type={alert.type as any}
              showIcon
              closable
              className="mb-2"
              action={
                <Button size="small" type="text">
                  查看详情
                </Button>
              }
            />
          ))}
        </div>
      )}

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="活跃项目"
              value={mockStats.activeProjects}
              suffix={`/ ${mockStats.totalProjects}`}
              prefix={<ProjectOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="任务完成率"
              value={(mockStats.completedTasks / mockStats.totalTasks * 100).toFixed(1)}
              suffix="%"
              prefix={<CheckSquareOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="团队成员"
              value={mockStats.activeMembers}
              suffix={`/ ${mockStats.teamMembers}`}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="本周工时"
              value={mockStats.hoursThisWeek}
              suffix={`/ ${mockStats.targetHours}h`}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="项目进度概览" loading={loading}>
            <ProjectProgressChart />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="任务状态分布" loading={loading}>
            <TaskStatusChart />
          </Card>
        </Col>
      </Row>

      {/* 更多图表区域 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={8}>
          <Card title="项目趋势" loading={loading}>
            <DashboardChart type="trend" height={250} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="团队绩效" loading={loading}>
            <DashboardChart type="performance" height={250} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="活动时间线" loading={loading}>
            <DashboardChart type="timeline" height={250} />
          </Card>
        </Col>
      </Row>

      {/* AI分析区域 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="AI风险分析" loading={loading}>
            <AIAnalysisChart type="risk" height={300} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="进度预测" loading={loading}>
            <AIAnalysisChart type="prediction" height={300} />
          </Card>
        </Col>
      </Row>

      {/* 团队效率分析 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={8}>
          <Card title="团队生产力" loading={loading}>
            <TeamEfficiencyChart type="productivity" height={300} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="工作负载分析" loading={loading}>
            <TeamEfficiencyChart type="workload" height={300} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="AI优化建议" loading={loading}>
            <AIAnalysisChart type="recommendation" height={300} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最近项目 */}
        <Col xs={24} lg={12}>
          <Card
            title="最近项目"
            loading={loading}
            extra={
              <Button type="link" icon={<RightOutlined />}>
                查看全部
              </Button>
            }
          >
            <List
              dataSource={mockRecentProjects}
              renderItem={(project) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<ProjectOutlined />} />}
                    title={
                      <Space>
                        <span>{project.name}</span>
                        <Tag color={getStatusColor(project.status)}>
                          {project.status === 'active' ? '进行中' : 
                           project.status === 'review' ? '待审核' : '已完成'}
                        </Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <div className="mb-2">
                          <Progress percent={project.progress} size="small" />
                        </div>
                        <Space>
                          <CalendarOutlined />
                          <Text type="secondary">{project.dueDate}</Text>
                          <Divider type="vertical" />
                          <Text type="secondary">
                            团队: {project.team.join(', ')}
                          </Text>
                        </Space>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 最近任务 */}
        <Col xs={24} lg={12}>
          <Card
            title="最近任务"
            loading={loading}
            extra={
              <Button type="link" icon={<RightOutlined />}>
                查看全部
              </Button>
            }
          >
            <List
              dataSource={mockRecentTasks}
              renderItem={(task) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<CheckSquareOutlined />} />}
                    title={
                      <Space>
                        <span>{task.title}</span>
                        <Tag color={getPriorityColor(task.priority)}>
                          {task.priority === 'high' ? '高' :
                           task.priority === 'medium' ? '中' : '低'}
                        </Tag>
                      </Space>
                    }
                    description={
                      <Space>
                        <Text type="secondary">{task.project}</Text>
                        <Divider type="vertical" />
                        <Text type="secondary">负责人: {task.assignee}</Text>
                        <Divider type="vertical" />
                        <Text type="secondary">{task.dueDate}</Text>
                      </Space>
                    }
                  />
                  <div>
                    <Tag color={
                      task.status === 'completed' ? 'success' :
                      task.status === 'in_progress' ? 'processing' : 'default'
                    }>
                      {task.status === 'completed' ? '已完成' :
                       task.status === 'in_progress' ? '进行中' : '待开始'}
                    </Tag>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default DashboardPage
