/**
 * OAuth2重定向处理页面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-19
 */

import React, { useEffect, useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Result, Spin, Button } from 'antd'
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'
import OAuth2Service from '@/services/oauth2Service'
import { useAuth } from '@/hooks/useAuth'

/**
 * OAuth2重定向处理页面
 */
const OAuth2Redirect: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { login } = useAuth()
  
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState<string>('')

  useEffect(() => {
    const handleOAuth2Callback = async () => {
      try {
        // 解析URL参数
        const urlParams = new URLSearchParams(location.search)
        
        // 处理OAuth2回调
        const result = OAuth2Service.handleAuthenticationCallback(urlParams)
        
        if (result.success && result.token && result.refreshToken) {
          // 认证成功，更新用户状态
          await login(result.token, result.refreshToken)
          
          setStatus('success')
          setMessage('登录成功！正在跳转...')
          
          // 获取保存的重定向URI或使用默认路径
          const savedRedirectUri = OAuth2Service.getSavedRedirectUri()
          const redirectPath = savedRedirectUri || '/dashboard'
          
          // 清除保存的重定向URI
          OAuth2Service.clearSavedRedirectUri()
          
          // 延迟跳转，让用户看到成功消息
          setTimeout(() => {
            navigate(redirectPath, { replace: true })
          }, 2000)
          
        } else {
          // 认证失败
          setStatus('error')
          setMessage(result.message || '认证失败，请重试')
        }
        
      } catch (error) {
        console.error('处理OAuth2回调失败:', error)
        setStatus('error')
        setMessage('处理认证回调时发生错误')
      }
    }

    handleOAuth2Callback()
  }, [location.search, login, navigate])

  /**
   * 返回登录页面
   */
  const handleBackToLogin = () => {
    navigate('/login', { replace: true })
  }

  /**
   * 重试认证
   */
  const handleRetry = () => {
    navigate('/login', { replace: true })
  }

  // 加载中状态
  if (status === 'loading') {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        justifyContent: 'center', 
        minHeight: '100vh',
        padding: '20px'
      }}>
        <Spin size="large" />
        <div style={{ marginTop: 24, fontSize: '16px', color: '#666' }}>
          正在处理认证信息...
        </div>
      </div>
    )
  }

  // 成功状态
  if (status === 'success') {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        minHeight: '100vh',
        padding: '20px'
      }}>
        <Result
          icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          title="登录成功"
          subTitle={message}
          extra={
            <Button type="primary" onClick={() => navigate('/dashboard')}>
              立即进入
            </Button>
          }
        />
      </div>
    )
  }

  // 错误状态
  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center', 
      minHeight: '100vh',
      padding: '20px'
    }}>
      <Result
        icon={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
        title="认证失败"
        subTitle={message}
        extra={[
          <Button key="retry" type="primary" onClick={handleRetry}>
            重新登录
          </Button>,
          <Button key="back" onClick={handleBackToLogin}>
            返回登录页
          </Button>
        ]}
      />
    </div>
  )
}

export default OAuth2Redirect
