/**
 * 仪表板页面
 * 显示项目概览、关键指标和数据可视化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useState } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  List,
  Avatar,
  Tag,
  Button,
  Space,
  Typography,
  Divider,
  Alert,
  Spin
} from 'antd';
import {
  ProjectOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  TrendingUpOutlined,
  WarningOutlined,
  RocketOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

import { ProjectChart } from '../components/charts/ProjectChart';
import { TaskChart } from '../components/charts/TaskChart';
import { TeamPerformanceChart } from '../components/charts/TeamPerformanceChart';
import { RiskAnalysisChart } from '../components/charts/RiskAnalysisChart';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchDashboardData } from '../store/slices/dashboardSlice';

const { Title, Text } = Typography;

interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedTasks: number;
  totalTasks: number;
  teamMembers: number;
  overdueTasks: number;
}

interface RecentActivity {
  id: string;
  type: 'project' | 'task' | 'team';
  title: string;
  description: string;
  timestamp: string;
  user: {
    name: string;
    avatar?: string;
  };
}

interface UpcomingDeadline {
  id: string;
  title: string;
  type: 'project' | 'task';
  deadline: string;
  priority: 'high' | 'medium' | 'low';
  progress: number;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { data, loading, error } = useAppSelector((state) => state.dashboard);
  
  const [stats, setStats] = useState<DashboardStats>({
    totalProjects: 0,
    activeProjects: 0,
    completedTasks: 0,
    totalTasks: 0,
    teamMembers: 0,
    overdueTasks: 0,
  });

  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [upcomingDeadlines, setUpcomingDeadlines] = useState<UpcomingDeadline[]>([]);

  useEffect(() => {
    dispatch(fetchDashboardData());
    loadMockData();
  }, [dispatch]);

  const loadMockData = () => {
    // 模拟数据加载
    setStats({
      totalProjects: 12,
      activeProjects: 8,
      completedTasks: 156,
      totalTasks: 203,
      teamMembers: 15,
      overdueTasks: 7,
    });

    setRecentActivities([
      {
        id: '1',
        type: 'project',
        title: '创建了新项目',
        description: 'AI推荐系统开发',
        timestamp: '2小时前',
        user: { name: '张三', avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1' }
      },
      {
        id: '2',
        type: 'task',
        title: '完成了任务',
        description: '用户界面设计',
        timestamp: '4小时前',
        user: { name: '李四', avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=2' }
      },
      {
        id: '3',
        type: 'team',
        title: '加入了团队',
        description: '前端开发团队',
        timestamp: '1天前',
        user: { name: '王五', avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=3' }
      }
    ]);

    setUpcomingDeadlines([
      {
        id: '1',
        title: 'API接口开发',
        type: 'task',
        deadline: '2025-08-20',
        priority: 'high',
        progress: 75
      },
      {
        id: '2',
        title: '用户测试',
        type: 'project',
        deadline: '2025-08-25',
        priority: 'medium',
        progress: 45
      },
      {
        id: '3',
        title: '文档编写',
        type: 'task',
        deadline: '2025-08-30',
        priority: 'low',
        progress: 20
      }
    ]);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'project':
        return <ProjectOutlined style={{ color: '#1890ff' }} />;
      case 'task':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'team':
        return <TeamOutlined style={{ color: '#722ed1' }} />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载仪表板数据...</div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <RocketOutlined style={{ marginRight: '8px' }} />
          项目仪表板
        </Title>
        <Text type="secondary">欢迎回来！这里是您的项目概览和关键指标。</Text>
      </div>

      {/* 关键指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总项目数"
              value={stats.totalProjects}
              prefix={<ProjectOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃项目"
              value={stats.activeProjects}
              prefix={<TrendingUpOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="任务完成率"
              value={(stats.completedTasks / stats.totalTasks * 100).toFixed(1)}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="逾期任务"
              value={stats.overdueTasks}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <Card title="项目进度概览" extra={<Button type="link">查看详情</Button>}>
            <ProjectChart />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="任务状态分布" extra={<Button type="link">查看详情</Button>}>
            <TaskChart />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <Card title="团队绩效分析" extra={<Button type="link">查看详情</Button>}>
            <TeamPerformanceChart />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="风险分析" extra={<Button type="link">查看详情</Button>}>
            <RiskAnalysisChart />
          </Card>
        </Col>
      </Row>

      {/* 活动和截止日期 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card
            title="最近活动"
            extra={
              <Button type="link" onClick={() => navigate('/notifications')}>
                查看全部
              </Button>
            }
          >
            <List
              itemLayout="horizontal"
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        src={item.user.avatar}
                        icon={getActivityIcon(item.type)}
                      />
                    }
                    title={
                      <Space>
                        <Text strong>{item.user.name}</Text>
                        <Text>{item.title}</Text>
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size={4}>
                        <Text type="secondary">{item.description}</Text>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {item.timestamp}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title="即将到期"
            extra={
              <Button type="link" onClick={() => navigate('/tasks')}>
                查看全部
              </Button>
            }
          >
            <List
              itemLayout="vertical"
              dataSource={upcomingDeadlines}
              renderItem={(item) => (
                <List.Item>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text strong>{item.title}</Text>
                      <Tag color={getPriorityColor(item.priority)}>
                        {item.priority === 'high' ? '高优先级' : 
                         item.priority === 'medium' ? '中优先级' : '低优先级'}
                      </Tag>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <CalendarOutlined />
                      <Text type="secondary">{item.deadline}</Text>
                    </div>
                    <Progress percent={item.progress} size="small" />
                  </Space>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
