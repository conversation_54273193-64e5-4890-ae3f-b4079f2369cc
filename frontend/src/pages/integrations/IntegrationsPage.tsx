/**
 * 第三方集成页面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Badge,
  Avatar,
  Tabs,
  List,
  Tag,
  Tooltip,
  Progress,
  Statistic,
} from 'antd'
import {
  ApiOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TestOutlined,
  LinkOutlined,
  DisconnectOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs

// 集成配置接口
interface IntegrationConfig {
  id: string
  name: string
  type: 'jira' | 'dingtalk' | 'slack' | 'jenkins' | 'gitlab-ci'
  status: 'connected' | 'disconnected' | 'error'
  config: any
  lastSync?: string
  createdAt: string
}

// 集成类型配置
const integrationTypes = {
  jira: {
    name: 'Jira',
    icon: '🎯',
    description: '项目管理和问题跟踪',
    color: '#0052CC',
  },
  dingtalk: {
    name: '钉钉',
    icon: '💬',
    description: '企业通讯和协作',
    color: '#00A6FB',
  },
  slack: {
    name: 'Slack',
    icon: '💬',
    description: '团队沟通和协作',
    color: '#4A154B',
  },
  jenkins: {
    name: 'Jenkins',
    icon: '🔧',
    description: 'CI/CD自动化构建',
    color: '#D33833',
  },
  'gitlab-ci': {
    name: 'GitLab CI',
    icon: '🦊',
    description: 'GitLab持续集成',
    color: '#FC6D26',
  },
}

const IntegrationsPage: React.FC = () => {
  const [integrations, setIntegrations] = useState<IntegrationConfig[]>([])
  const [loading, setLoading] = useState(false)
  const [configModalVisible, setConfigModalVisible] = useState(false)
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationConfig | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [form] = Form.useForm()

  // 模拟数据
  const mockIntegrations: IntegrationConfig[] = [
    {
      id: '1',
      name: 'Jira集成',
      type: 'jira',
      status: 'connected',
      config: {
        serverUrl: 'https://company.atlassian.net',
        username: '<EMAIL>',
      },
      lastSync: '2025-08-16T10:30:00Z',
      createdAt: '2025-08-15T09:00:00Z',
    },
    {
      id: '2',
      name: '钉钉通知',
      type: 'dingtalk',
      status: 'connected',
      config: {
        appKey: 'dingxxxxxxxx',
        appSecret: '***',
      },
      lastSync: '2025-08-16T10:25:00Z',
      createdAt: '2025-08-15T10:00:00Z',
    },
    {
      id: '3',
      name: 'Slack频道',
      type: 'slack',
      status: 'disconnected',
      config: {
        botToken: 'xoxb-***',
        channel: '#general',
      },
      createdAt: '2025-08-15T11:00:00Z',
    },
    {
      id: '4',
      name: 'Jenkins构建',
      type: 'jenkins',
      status: 'error',
      config: {
        serverUrl: 'https://jenkins.company.com',
        username: 'admin',
      },
      lastSync: '2025-08-16T09:00:00Z',
      createdAt: '2025-08-15T12:00:00Z',
    },
  ]

  useEffect(() => {
    loadIntegrations()
  }, [])

  // 加载集成配置
  const loadIntegrations = async () => {
    setLoading(true)
    try {
      // TODO: 调用API获取集成配置
      setTimeout(() => {
        setIntegrations(mockIntegrations)
        setLoading(false)
      }, 1000)
    } catch (error) {
      message.error('加载集成配置失败')
      setLoading(false)
    }
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge status="success" text="已连接" />
      case 'disconnected':
        return <Badge status="default" text="未连接" />
      case 'error':
        return <Badge status="error" text="连接错误" />
      default:
        return <Badge status="default" text="未知" />
    }
  }

  // 测试连接
  const testConnection = async (integration: IntegrationConfig) => {
    try {
      setLoading(true)
      // TODO: 调用API测试连接
      await new Promise(resolve => setTimeout(resolve, 2000))
      message.success('连接测试成功')
      
      // 更新状态
      setIntegrations(prev =>
        prev.map(item =>
          item.id === integration.id
            ? { ...item, status: 'connected' as const }
            : item
        )
      )
    } catch (error) {
      message.error('连接测试失败')
    } finally {
      setLoading(false)
    }
  }

  // 同步数据
  const syncData = async (integration: IntegrationConfig) => {
    try {
      setLoading(true)
      // TODO: 调用API同步数据
      await new Promise(resolve => setTimeout(resolve, 3000))
      message.success('数据同步成功')
      
      // 更新最后同步时间
      setIntegrations(prev =>
        prev.map(item =>
          item.id === integration.id
            ? { ...item, lastSync: new Date().toISOString() }
            : item
        )
      )
    } catch (error) {
      message.error('数据同步失败')
    } finally {
      setLoading(false)
    }
  }

  // 保存配置
  const handleSaveConfig = async (values: any) => {
    try {
      setLoading(true)
      // TODO: 调用API保存配置
      console.log('保存配置:', values)
      message.success('配置保存成功')
      setConfigModalVisible(false)
      form.resetFields()
      loadIntegrations()
    } catch (error) {
      message.error('配置保存失败')
    } finally {
      setLoading(false)
    }
  }

  // 删除集成
  const deleteIntegration = (integration: IntegrationConfig) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除集成"${integration.name}"吗？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // TODO: 调用API删除集成
          setIntegrations(prev => prev.filter(item => item.id !== integration.id))
          message.success('集成删除成功')
        } catch (error) {
          message.error('集成删除失败')
        }
      },
    })
  }

  // 统计数据
  const stats = {
    total: integrations.length,
    connected: integrations.filter(i => i.status === 'connected').length,
    disconnected: integrations.filter(i => i.status === 'disconnected').length,
    error: integrations.filter(i => i.status === 'error').length,
  }

  return (
    <div className="integrations-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">第三方集成</Title>
          <Text type="secondary">
            连接和管理第三方工具，实现数据同步和工作流自动化
          </Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedIntegration(null)
              setConfigModalVisible(true)
            }}
          >
            添加集成
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="集成总数"
              value={stats.total}
              prefix={<ApiOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="已连接"
              value={stats.connected}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="未连接"
              value={stats.disconnected}
              prefix={<DisconnectOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="连接错误"
              value={stats.error}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="集成概览" key="overview">
            <Row gutter={[16, 16]}>
              {integrations.map((integration) => {
                const typeConfig = integrationTypes[integration.type]
                return (
                  <Col xs={24} sm={12} lg={8} key={integration.id}>
                    <Card
                      size="small"
                      actions={[
                        <Tooltip title="测试连接">
                          <Button
                            type="text"
                            icon={<TestOutlined />}
                            onClick={() => testConnection(integration)}
                            loading={loading}
                          />
                        </Tooltip>,
                        <Tooltip title="同步数据">
                          <Button
                            type="text"
                            icon={<SyncOutlined />}
                            onClick={() => syncData(integration)}
                            loading={loading}
                            disabled={integration.status !== 'connected'}
                          />
                        </Tooltip>,
                        <Tooltip title="编辑配置">
                          <Button
                            type="text"
                            icon={<EditOutlined />}
                            onClick={() => {
                              setSelectedIntegration(integration)
                              setConfigModalVisible(true)
                            }}
                          />
                        </Tooltip>,
                        <Tooltip title="删除集成">
                          <Button
                            type="text"
                            icon={<DeleteOutlined />}
                            danger
                            onClick={() => deleteIntegration(integration)}
                          />
                        </Tooltip>,
                      ]}
                    >
                      <div className="flex items-center mb-3">
                        <Avatar
                          style={{ backgroundColor: typeConfig.color }}
                          size="large"
                          className="mr-3"
                        >
                          {typeConfig.icon}
                        </Avatar>
                        <div>
                          <div className="font-medium">{integration.name}</div>
                          <div className="text-gray-500 text-sm">
                            {typeConfig.description}
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span>状态:</span>
                          {getStatusBadge(integration.status)}
                        </div>
                        
                        {integration.lastSync && (
                          <div className="flex justify-between items-center">
                            <span>最后同步:</span>
                            <span className="text-sm text-gray-500">
                              {new Date(integration.lastSync).toLocaleString('zh-CN')}
                            </span>
                          </div>
                        )}
                        
                        <div className="flex justify-between items-center">
                          <span>创建时间:</span>
                          <span className="text-sm text-gray-500">
                            {new Date(integration.createdAt).toLocaleDateString('zh-CN')}
                          </span>
                        </div>
                      </div>
                    </Card>
                  </Col>
                )
              })}
            </Row>
          </TabPane>
          
          <TabPane tab="可用集成" key="available">
            <Row gutter={[16, 16]}>
              {Object.entries(integrationTypes).map(([type, config]) => (
                <Col xs={24} sm={12} lg={8} key={type}>
                  <Card
                    hoverable
                    onClick={() => {
                      setSelectedIntegration({
                        id: '',
                        name: '',
                        type: type as any,
                        status: 'disconnected',
                        config: {},
                        createdAt: new Date().toISOString(),
                      })
                      setConfigModalVisible(true)
                    }}
                  >
                    <div className="text-center">
                      <Avatar
                        style={{ backgroundColor: config.color }}
                        size={64}
                        className="mb-4"
                      >
                        <span style={{ fontSize: '24px' }}>{config.icon}</span>
                      </Avatar>
                      <Title level={4}>{config.name}</Title>
                      <Text type="secondary">{config.description}</Text>
                      <div className="mt-4">
                        <Button type="primary" icon={<PlusOutlined />}>
                          添加集成
                        </Button>
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </TabPane>
          
          <TabPane tab="同步日志" key="logs">
            <List
              dataSource={[
                { time: '2025-08-16 10:30', action: 'Jira项目同步', status: 'success', details: '同步了5个项目，20个问题' },
                { time: '2025-08-16 10:25', action: '钉钉消息发送', status: 'success', details: '发送项目通知到开发群' },
                { time: '2025-08-16 09:00', action: 'Jenkins构建触发', status: 'error', details: '连接超时，构建失败' },
              ]}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        style={{
                          backgroundColor: item.status === 'success' ? '#52c41a' : '#f5222d'
                        }}
                        icon={item.status === 'success' ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
                      />
                    }
                    title={item.action}
                    description={
                      <div>
                        <div>{item.details}</div>
                        <div className="text-gray-400 text-sm">{item.time}</div>
                      </div>
                    }
                  />
                  <Tag color={item.status === 'success' ? 'success' : 'error'}>
                    {item.status === 'success' ? '成功' : '失败'}
                  </Tag>
                </List.Item>
              )}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 配置模态框 */}
      <Modal
        title={selectedIntegration?.id ? '编辑集成配置' : '添加集成配置'}
        open={configModalVisible}
        onCancel={() => {
          setConfigModalVisible(false)
          setSelectedIntegration(null)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        {selectedIntegration && (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSaveConfig}
            initialValues={selectedIntegration.config}
          >
            <Form.Item
              name="name"
              label="集成名称"
              rules={[{ required: true, message: '请输入集成名称' }]}
              initialValue={selectedIntegration.name}
            >
              <Input placeholder="请输入集成名称" />
            </Form.Item>

            <Form.Item label="集成类型">
              <Select
                value={selectedIntegration.type}
                disabled={!!selectedIntegration.id}
                onChange={(value) => {
                  setSelectedIntegration(prev => prev ? { ...prev, type: value } : null)
                }}
              >
                {Object.entries(integrationTypes).map(([type, config]) => (
                  <Option key={type} value={type}>
                    <Space>
                      <span>{config.icon}</span>
                      {config.name}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {/* Jira配置 */}
            {selectedIntegration.type === 'jira' && (
              <>
                <Form.Item
                  name="serverUrl"
                  label="Jira服务器地址"
                  rules={[
                    { required: true, message: '请输入Jira服务器地址' },
                    { type: 'url', message: '请输入有效的URL' },
                  ]}
                >
                  <Input placeholder="https://your-domain.atlassian.net" />
                </Form.Item>

                <Form.Item
                  name="username"
                  label="用户名"
                  rules={[{ required: true, message: '请输入用户名' }]}
                >
                  <Input placeholder="请输入Jira用户名" />
                </Form.Item>

                <Form.Item
                  name="apiToken"
                  label="API Token"
                  rules={[{ required: true, message: '请输入API Token' }]}
                >
                  <Input.Password placeholder="请输入Jira API Token" />
                </Form.Item>
              </>
            )}

            {/* 钉钉配置 */}
            {selectedIntegration.type === 'dingtalk' && (
              <>
                <Form.Item
                  name="appKey"
                  label="应用Key"
                  rules={[{ required: true, message: '请输入应用Key' }]}
                >
                  <Input placeholder="请输入钉钉应用Key" />
                </Form.Item>

                <Form.Item
                  name="appSecret"
                  label="应用Secret"
                  rules={[{ required: true, message: '请输入应用Secret' }]}
                >
                  <Input.Password placeholder="请输入钉钉应用Secret" />
                </Form.Item>

                <Form.Item
                  name="agentId"
                  label="应用ID"
                  rules={[{ required: true, message: '请输入应用ID' }]}
                >
                  <Input placeholder="请输入钉钉应用ID" />
                </Form.Item>
              </>
            )}

            {/* Slack配置 */}
            {selectedIntegration.type === 'slack' && (
              <>
                <Form.Item
                  name="botToken"
                  label="Bot Token"
                  rules={[{ required: true, message: '请输入Bot Token' }]}
                >
                  <Input.Password placeholder="xoxb-your-bot-token" />
                </Form.Item>

                <Form.Item
                  name="channel"
                  label="默认频道"
                  rules={[{ required: true, message: '请输入默认频道' }]}
                >
                  <Input placeholder="#general" />
                </Form.Item>

                <Form.Item
                  name="webhookUrl"
                  label="Webhook URL"
                >
                  <Input placeholder="https://hooks.slack.com/services/..." />
                </Form.Item>
              </>
            )}

            {/* Jenkins配置 */}
            {selectedIntegration.type === 'jenkins' && (
              <>
                <Form.Item
                  name="serverUrl"
                  label="Jenkins服务器地址"
                  rules={[
                    { required: true, message: '请输入Jenkins服务器地址' },
                    { type: 'url', message: '请输入有效的URL' },
                  ]}
                >
                  <Input placeholder="https://jenkins.your-domain.com" />
                </Form.Item>

                <Form.Item
                  name="username"
                  label="用户名"
                  rules={[{ required: true, message: '请输入用户名' }]}
                >
                  <Input placeholder="请输入Jenkins用户名" />
                </Form.Item>

                <Form.Item
                  name="apiToken"
                  label="API Token"
                  rules={[{ required: true, message: '请输入API Token' }]}
                >
                  <Input.Password placeholder="请输入Jenkins API Token" />
                </Form.Item>
              </>
            )}

            {/* GitLab CI配置 */}
            {selectedIntegration.type === 'gitlab-ci' && (
              <>
                <Form.Item
                  name="serverUrl"
                  label="GitLab服务器地址"
                  rules={[
                    { required: true, message: '请输入GitLab服务器地址' },
                    { type: 'url', message: '请输入有效的URL' },
                  ]}
                >
                  <Input placeholder="https://gitlab.com" />
                </Form.Item>

                <Form.Item
                  name="accessToken"
                  label="访问令牌"
                  rules={[{ required: true, message: '请输入访问令牌' }]}
                >
                  <Input.Password placeholder="请输入GitLab访问令牌" />
                </Form.Item>

                <Form.Item
                  name="projectId"
                  label="项目ID"
                  rules={[{ required: true, message: '请输入项目ID' }]}
                >
                  <Input placeholder="请输入GitLab项目ID" />
                </Form.Item>
              </>
            )}

            <Form.Item
              name="enabled"
              label="启用集成"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>

            <Form.Item className="mb-0 mt-6">
              <div className="flex justify-end space-x-2">
                <Button onClick={() => setConfigModalVisible(false)}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  保存配置
                </Button>
              </div>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  )
}

export default IntegrationsPage
