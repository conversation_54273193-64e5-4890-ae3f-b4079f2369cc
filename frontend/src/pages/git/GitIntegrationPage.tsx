/**
 * Git集成管理页面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Table,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Tooltip,
  Dropdown,
  Progress,
  Statistic,
  Timeline,
  Tabs,
} from 'antd'
import {
  PlusOutlined,
  SyncOutlined,
  SettingOutlined,
  BranchesOutlined,
  GitlabOutlined,
  GithubOutlined,
  LinkOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  MoreOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs

// Git仓库接口
interface GitRepository {
  id: string
  name: string
  description?: string
  platform: 'github' | 'gitlab' | 'gitee'
  repositoryUrl: string
  defaultBranch: string
  isPrivate: boolean
  syncEnabled: boolean
  syncStatus: 'success' | 'failed' | 'syncing' | 'pending'
  lastSyncAt?: string
  branchCount: number
  commitCount: number
  pullRequestCount: number
  contributorCount: number
  latestCommitMessage?: string
  latestCommitAuthor?: string
  latestCommitAt?: string
  createdAt: string
}

// 模拟数据
const mockRepositories: GitRepository[] = [
  {
    id: '1',
    name: 'ai-pm-frontend',
    description: 'AI项目管理平台前端应用',
    platform: 'github',
    repositoryUrl: 'https://github.com/ai-pm/frontend',
    defaultBranch: 'main',
    isPrivate: false,
    syncEnabled: true,
    syncStatus: 'success',
    lastSyncAt: '2025-08-16T10:30:00Z',
    branchCount: 8,
    commitCount: 156,
    pullRequestCount: 12,
    contributorCount: 5,
    latestCommitMessage: 'feat: 完成数据可视化组件开发',
    latestCommitAuthor: '张三',
    latestCommitAt: '2025-08-16T09:45:00Z',
    createdAt: '2025-07-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'ai-pm-backend',
    description: 'AI项目管理平台后端服务',
    platform: 'github',
    repositoryUrl: 'https://github.com/ai-pm/backend',
    defaultBranch: 'main',
    isPrivate: true,
    syncEnabled: true,
    syncStatus: 'syncing',
    lastSyncAt: '2025-08-16T10:00:00Z',
    branchCount: 12,
    commitCount: 203,
    pullRequestCount: 8,
    contributorCount: 4,
    latestCommitMessage: 'fix: 修复用户认证服务bug',
    latestCommitAuthor: '李四',
    latestCommitAt: '2025-08-16T08:30:00Z',
    createdAt: '2025-07-01T00:00:00Z',
  },
  {
    id: '3',
    name: 'ai-analysis-service',
    description: 'AI分析服务',
    platform: 'gitlab',
    repositoryUrl: 'https://gitlab.com/ai-pm/analysis',
    defaultBranch: 'develop',
    isPrivate: true,
    syncEnabled: false,
    syncStatus: 'failed',
    lastSyncAt: '2025-08-15T15:20:00Z',
    branchCount: 6,
    commitCount: 89,
    pullRequestCount: 3,
    contributorCount: 3,
    latestCommitMessage: 'refactor: 重构预测算法',
    latestCommitAuthor: '王五',
    latestCommitAt: '2025-08-15T14:15:00Z',
    createdAt: '2025-07-15T00:00:00Z',
  },
]

const GitIntegrationPage: React.FC = () => {
  const [repositories, setRepositories] = useState<GitRepository[]>(mockRepositories)
  const [loading, setLoading] = useState(false)
  const [addModalVisible, setAddModalVisible] = useState(false)
  const [selectedRepository, setSelectedRepository] = useState<GitRepository | null>(null)
  const [activeTab, setActiveTab] = useState('repositories')
  const [form] = Form.useForm()

  // 获取平台图标
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'github':
        return <GithubOutlined style={{ color: '#24292e' }} />
      case 'gitlab':
        return <GitlabOutlined style={{ color: '#fc6d26' }} />
      case 'gitee':
        return <span style={{ color: '#c71d23' }}>码云</span>
      default:
        return <LinkOutlined />
    }
  }

  // 获取同步状态标签
  const getSyncStatusTag = (status: string) => {
    switch (status) {
      case 'success':
        return <Tag color="success" icon={<CheckCircleOutlined />}>同步成功</Tag>
      case 'failed':
        return <Tag color="error" icon={<ExclamationCircleOutlined />}>同步失败</Tag>
      case 'syncing':
        return <Tag color="processing" icon={<SyncOutlined spin />}>同步中</Tag>
      case 'pending':
        return <Tag color="default" icon={<ClockCircleOutlined />}>待同步</Tag>
      default:
        return <Tag color="default">未知</Tag>
    }
  }

  // 处理添加仓库
  const handleAddRepository = async (values: any) => {
    try {
      setLoading(true)
      // TODO: 调用API添加仓库
      console.log('添加仓库:', values)
      message.success('仓库添加成功')
      setAddModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('仓库添加失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理同步仓库
  const handleSyncRepository = async (repositoryId: string) => {
    try {
      setLoading(true)
      // TODO: 调用API同步仓库
      console.log('同步仓库:', repositoryId)
      message.success('同步任务已启动')
    } catch (error) {
      message.error('同步失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理删除仓库
  const handleDeleteRepository = (repositoryId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个Git仓库吗？此操作不可恢复。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // TODO: 调用API删除仓库
          console.log('删除仓库:', repositoryId)
          setRepositories(repositories.filter(repo => repo.id !== repositoryId))
          message.success('仓库删除成功')
        } catch (error) {
          message.error('仓库删除失败')
        }
      },
    })
  }

  // 表格列定义
  const columns: ColumnsType<GitRepository> = [
    {
      title: '仓库信息',
      key: 'repository',
      width: 300,
      render: (_, record) => (
        <div className="flex items-center">
          <Avatar icon={getPlatformIcon(record.platform)} className="mr-3" />
          <div>
            <div className="font-medium flex items-center">
              {record.name}
              {record.isPrivate && (
                <Tag size="small" className="ml-2">私有</Tag>
              )}
            </div>
            <div className="text-gray-500 text-sm">{record.description}</div>
            <div className="text-gray-400 text-xs">
              默认分支: {record.defaultBranch}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '同步状态',
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      width: 120,
      render: (status: string, record) => (
        <div>
          {getSyncStatusTag(status)}
          {record.lastSyncAt && (
            <div className="text-gray-400 text-xs mt-1">
              {new Date(record.lastSyncAt).toLocaleString('zh-CN')}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '统计信息',
      key: 'stats',
      width: 200,
      render: (_, record) => (
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <BranchesOutlined className="mr-1" />
            {record.branchCount} 分支
          </div>
          <div>
            <span className="mr-1">📝</span>
            {record.commitCount} 提交
          </div>
          <div>
            <span className="mr-1">🔀</span>
            {record.pullRequestCount} PR
          </div>
          <div>
            <span className="mr-1">👥</span>
            {record.contributorCount} 贡献者
          </div>
        </div>
      ),
    },
    {
      title: '最新提交',
      key: 'latestCommit',
      width: 250,
      render: (_, record) => (
        <div>
          {record.latestCommitMessage && (
            <div className="text-sm text-ellipsis">
              {record.latestCommitMessage}
            </div>
          )}
          {record.latestCommitAuthor && (
            <div className="text-gray-500 text-xs">
              {record.latestCommitAuthor} · {' '}
              {record.latestCommitAt && 
                new Date(record.latestCommitAt).toLocaleDateString('zh-CN')}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => {
        const menuItems = [
          {
            key: 'view',
            icon: <EyeOutlined />,
            label: '查看详情',
            onClick: () => setSelectedRepository(record),
          },
          {
            key: 'sync',
            icon: <SyncOutlined />,
            label: '立即同步',
            onClick: () => handleSyncRepository(record.id),
          },
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: '编辑配置',
            onClick: () => message.info('编辑功能开发中'),
          },
          {
            type: 'divider' as const,
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: '删除仓库',
            danger: true,
            onClick: () => handleDeleteRepository(record.id),
          },
        ]

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        )
      },
    },
  ]

  return (
    <div className="git-integration-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">Git集成管理</Title>
          <Text type="secondary">
            管理项目的Git仓库，同步代码提交数据和分析开发活动
          </Text>
        </div>
        <Space>
          <Button 
            icon={<SyncOutlined />}
            onClick={() => message.info('批量同步功能开发中')}
          >
            批量同步
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAddModalVisible(true)}
          >
            添加仓库
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="仓库总数"
              value={repositories.length}
              prefix={<LinkOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="同步成功"
              value={repositories.filter(r => r.syncStatus === 'success').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="总提交数"
              value={repositories.reduce((sum, r) => sum + r.commitCount, 0)}
              prefix={<span>📝</span>}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="总贡献者"
              value={repositories.reduce((sum, r) => sum + r.contributorCount, 0)}
              prefix={<span>👥</span>}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="仓库列表" key="repositories">
            <Table
              columns={columns}
              dataSource={repositories}
              loading={loading}
              rowKey="id"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </TabPane>

          <TabPane tab="同步日志" key="sync-logs">
            <Timeline>
              <Timeline.Item color="green" dot={<CheckCircleOutlined />}>
                <div>
                  <div className="font-medium">ai-pm-frontend 同步成功</div>
                  <div className="text-gray-500 text-sm">
                    同步了 5 个新提交，2 个新分支 · 2025-08-16 10:30
                  </div>
                </div>
              </Timeline.Item>
              <Timeline.Item color="blue" dot={<SyncOutlined spin />}>
                <div>
                  <div className="font-medium">ai-pm-backend 正在同步</div>
                  <div className="text-gray-500 text-sm">
                    正在获取最新的提交记录... · 2025-08-16 10:25
                  </div>
                </div>
              </Timeline.Item>
              <Timeline.Item color="red" dot={<ExclamationCircleOutlined />}>
                <div>
                  <div className="font-medium">ai-analysis-service 同步失败</div>
                  <div className="text-gray-500 text-sm">
                    访问令牌已过期，请更新令牌 · 2025-08-15 15:20
                  </div>
                </div>
              </Timeline.Item>
            </Timeline>
          </TabPane>

          <TabPane tab="Webhook配置" key="webhooks">
            <div className="text-center py-20">
              <Title level={4}>Webhook配置</Title>
              <Text type="secondary">
                配置Git平台的Webhook，实现实时数据同步
              </Text>
              <div className="mt-4">
                <Button type="primary" size="large">
                  配置Webhook
                </Button>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 添加仓库模态框 */}
      <Modal
        title="添加Git仓库"
        open={addModalVisible}
        onCancel={() => {
          setAddModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddRepository}
        >
          <Form.Item
            name="platform"
            label="Git平台"
            rules={[{ required: true, message: '请选择Git平台' }]}
          >
            <Select placeholder="选择Git平台">
              <Option value="github">
                <Space>
                  <GithubOutlined />
                  GitHub
                </Space>
              </Option>
              <Option value="gitlab">
                <Space>
                  <GitlabOutlined />
                  GitLab
                </Space>
              </Option>
              <Option value="gitee">
                <Space>
                  <span>码</span>
                  Gitee
                </Space>
              </Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="repositoryUrl"
            label="仓库URL"
            rules={[
              { required: true, message: '请输入仓库URL' },
              { type: 'url', message: '请输入有效的URL' },
            ]}
          >
            <Input placeholder="https://github.com/username/repository" />
          </Form.Item>

          <Form.Item
            name="accessToken"
            label="访问令牌"
            rules={[{ required: true, message: '请输入访问令牌' }]}
          >
            <Input.Password placeholder="请输入Personal Access Token" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="仓库描述（可选）" />
          </Form.Item>

          <Form.Item
            name="syncEnabled"
            label="启用自动同步"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item className="mb-0 mt-6">
            <div className="flex justify-end space-x-2">
              <Button onClick={() => setAddModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                添加仓库
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 仓库详情模态框 */}
      <Modal
        title="仓库详情"
        open={!!selectedRepository}
        onCancel={() => setSelectedRepository(null)}
        footer={null}
        width={800}
      >
        {selectedRepository && (
          <div>
            <div className="flex items-center mb-4">
              <Avatar icon={getPlatformIcon(selectedRepository.platform)} size="large" className="mr-3" />
              <div>
                <Title level={3} className="mb-1">{selectedRepository.name}</Title>
                <Text type="secondary">{selectedRepository.description}</Text>
              </div>
            </div>

            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="基本信息" size="small">
                  <div className="space-y-2">
                    <div>平台: {selectedRepository.platform}</div>
                    <div>默认分支: {selectedRepository.defaultBranch}</div>
                    <div>可见性: {selectedRepository.isPrivate ? '私有' : '公开'}</div>
                    <div>同步状态: {getSyncStatusTag(selectedRepository.syncStatus)}</div>
                  </div>
                </Card>
              </Col>

              <Col span={12}>
                <Card title="统计信息" size="small">
                  <div className="space-y-2">
                    <div>分支数: {selectedRepository.branchCount}</div>
                    <div>提交数: {selectedRepository.commitCount}</div>
                    <div>PR数: {selectedRepository.pullRequestCount}</div>
                    <div>贡献者: {selectedRepository.contributorCount}</div>
                  </div>
                </Card>
              </Col>
            </Row>

            {selectedRepository.latestCommitMessage && (
              <Card title="最新提交" size="small" className="mt-4">
                <div>
                  <div className="font-medium">{selectedRepository.latestCommitMessage}</div>
                  <div className="text-gray-500 text-sm mt-1">
                    {selectedRepository.latestCommitAuthor} · {' '}
                    {selectedRepository.latestCommitAt &&
                      new Date(selectedRepository.latestCommitAt).toLocaleString('zh-CN')}
                  </div>
                </div>
              </Card>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default GitIntegrationPage
