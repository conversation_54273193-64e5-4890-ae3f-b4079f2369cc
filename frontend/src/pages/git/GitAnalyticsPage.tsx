/**
 * Git代码分析页面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Tabs,
  Select,
  DatePicker,
  Statistic,
  Table,
  Tag,
  Avatar,
  Progress,
  Timeline,
  List,
} from 'antd'
import {
  CodeOutlined,
  UserOutlined,
  BranchesOutlined,
  FileTextOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  FireOutlined,
  BugOutlined,
} from '@ant-design/icons'

import DashboardChart from '@/components/charts/DashboardChart'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { Option } = Select
const { RangePicker } = DatePicker

const GitAnalyticsPage: React.FC = () => {
  const [selectedRepository, setSelectedRepository] = useState('all')
  const [dateRange, setDateRange] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('overview')

  // 模拟数据
  const commitStats = {
    totalCommits: 1248,
    totalAdditions: 45632,
    totalDeletions: 12456,
    totalFiles: 2341,
    avgCommitsPerDay: 8.5,
    activeContributors: 12,
  }

  const topContributors = [
    { name: '张三', commits: 156, additions: 8945, deletions: 2341, avatar: '' },
    { name: '李四', commits: 134, additions: 7632, deletions: 1987, avatar: '' },
    { name: '王五', commits: 98, additions: 5421, deletions: 1654, avatar: '' },
    { name: '赵六', commits: 87, additions: 4532, deletions: 1234, avatar: '' },
    { name: '钱七', commits: 76, additions: 3987, deletions: 987, avatar: '' },
  ]

  const hotFiles = [
    { path: 'src/components/Dashboard.tsx', changes: 45, contributors: 5, lastModified: '2025-08-16' },
    { path: 'src/services/api.ts', changes: 38, contributors: 4, lastModified: '2025-08-15' },
    { path: 'src/pages/ProjectList.tsx', changes: 32, contributors: 3, lastModified: '2025-08-14' },
    { path: 'src/utils/helpers.ts', changes: 28, contributors: 6, lastModified: '2025-08-13' },
    { path: 'src/store/slices/userSlice.ts', changes: 24, contributors: 3, lastModified: '2025-08-12' },
  ]

  const recentCommits = [
    {
      sha: 'a1b2c3d',
      message: 'feat: 完成数据可视化组件开发',
      author: '张三',
      date: '2025-08-16T10:30:00Z',
      additions: 234,
      deletions: 45,
    },
    {
      sha: 'e4f5g6h',
      message: 'fix: 修复用户认证服务bug',
      author: '李四',
      date: '2025-08-16T09:15:00Z',
      additions: 12,
      deletions: 8,
    },
    {
      sha: 'i7j8k9l',
      message: 'refactor: 重构项目管理模块',
      author: '王五',
      date: '2025-08-15T16:45:00Z',
      additions: 156,
      deletions: 89,
    },
  ]

  const contributorColumns = [
    {
      title: '贡献者',
      key: 'contributor',
      render: (record: any) => (
        <div className="flex items-center">
          <Avatar src={record.avatar} className="mr-2">
            {record.name.charAt(0)}
          </Avatar>
          <span>{record.name}</span>
        </div>
      ),
    },
    {
      title: '提交数',
      dataIndex: 'commits',
      key: 'commits',
      sorter: (a: any, b: any) => a.commits - b.commits,
    },
    {
      title: '新增行数',
      dataIndex: 'additions',
      key: 'additions',
      sorter: (a: any, b: any) => a.additions - b.additions,
      render: (value: number) => <span className="text-green-600">+{value}</span>,
    },
    {
      title: '删除行数',
      dataIndex: 'deletions',
      key: 'deletions',
      sorter: (a: any, b: any) => a.deletions - b.deletions,
      render: (value: number) => <span className="text-red-600">-{value}</span>,
    },
    {
      title: '活跃度',
      key: 'activity',
      render: (record: any) => {
        const total = record.additions + record.deletions
        const maxTotal = Math.max(...topContributors.map(c => c.additions + c.deletions))
        const percentage = Math.round((total / maxTotal) * 100)
        return <Progress percent={percentage} size="small" />
      },
    },
  ]

  return (
    <div className="git-analytics-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">代码分析</Title>
          <Text type="secondary">
            分析代码提交数据，了解开发活动和团队贡献
          </Text>
        </div>
        <div className="flex items-center space-x-4">
          <Select
            value={selectedRepository}
            onChange={setSelectedRepository}
            style={{ width: 200 }}
          >
            <Option value="all">所有仓库</Option>
            <Option value="frontend">前端仓库</Option>
            <Option value="backend">后端仓库</Option>
            <Option value="ai-service">AI服务</Option>
          </Select>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            placeholder={['开始日期', '结束日期']}
          />
        </div>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic
              title="总提交数"
              value={commitStats.totalCommits}
              prefix={<CodeOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic
              title="新增代码行"
              value={commitStats.totalAdditions}
              prefix={<span className="text-green-600">+</span>}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic
              title="删除代码行"
              value={commitStats.totalDeletions}
              prefix={<span className="text-red-600">-</span>}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic
              title="修改文件数"
              value={commitStats.totalFiles}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic
              title="日均提交"
              value={commitStats.avgCommitsPerDay}
              precision={1}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} lg={4}>
          <Card>
            <Statistic
              title="活跃贡献者"
              value={commitStats.activeContributors}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 分析内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="提交概览" key="overview">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="提交趋势" size="small">
                  <DashboardChart type="trend" height={300} />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="代码变更分布" size="small">
                  <DashboardChart type="distribution" height={300} />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="贡献者分析" key="contributors">
            <Table
              columns={contributorColumns}
              dataSource={topContributors}
              rowKey="name"
              pagination={false}
              size="small"
            />
          </TabPane>

          <TabPane tab="热点文件" key="hot-files">
            <List
              dataSource={hotFiles}
              renderItem={(file) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<FireOutlined />} style={{ backgroundColor: '#fa8c16' }} />}
                    title={file.path}
                    description={
                      <div className="flex items-center space-x-4 text-sm">
                        <span>{file.changes} 次修改</span>
                        <span>{file.contributors} 个贡献者</span>
                        <span>最后修改: {file.lastModified}</span>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </TabPane>

          <TabPane tab="最近提交" key="recent-commits">
            <Timeline>
              {recentCommits.map((commit) => (
                <Timeline.Item key={commit.sha}>
                  <div>
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-medium">{commit.message}</div>
                        <div className="text-gray-500 text-sm">
                          {commit.author} · {new Date(commit.date).toLocaleString('zh-CN')}
                        </div>
                      </div>
                      <div className="text-sm">
                        <Tag color="green">+{commit.additions}</Tag>
                        <Tag color="red">-{commit.deletions}</Tag>
                      </div>
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </TabPane>

          <TabPane tab="代码质量" key="quality">
            <div className="text-center py-20">
              <Title level={4}>代码质量分析</Title>
              <Text type="secondary">
                集成SonarQube、CodeClimate等代码质量分析工具
              </Text>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default GitAnalyticsPage
