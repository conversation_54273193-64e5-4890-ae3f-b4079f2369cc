/**
 * 团队管理页面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Table,
  Button,
  Space,
  Tag,
  Avatar,
  Input,
  Select,
  Modal,
  Form,
  message,
  Tooltip,
  Dropdown,
  Progress,
  Statistic,
  List,
  Badge,
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  UserOutlined,
  TeamOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  MailOutlined,
  PhoneOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select

// 团队成员接口
interface TeamMember {
  id: string
  username: string
  email: string
  firstName?: string
  lastName?: string
  avatar?: string
  phone?: string
  department?: string
  position?: string
  role: string
  status: 'active' | 'inactive' | 'pending'
  joinDate: string
  lastActiveAt?: string
  skills: string[]
  projects: Array<{
    id: string
    name: string
    role: string
  }>
  performance: {
    tasksCompleted: number
    tasksInProgress: number
    averageRating: number
    productivity: number
  }
}

// 模拟团队成员数据
const mockTeamMembers: TeamMember[] = [
  {
    id: '1',
    username: 'zhangsan',
    email: '<EMAIL>',
    firstName: '三',
    lastName: '张',
    avatar: '',
    phone: '13800138001',
    department: '技术部',
    position: '前端工程师',
    role: 'developer',
    status: 'active',
    joinDate: '2024-01-15',
    lastActiveAt: '2025-08-16T10:30:00Z',
    skills: ['React', 'TypeScript', 'Vue.js', 'CSS'],
    projects: [
      { id: '1', name: 'AI客服系统', role: '前端开发' },
      { id: '2', name: '移动端应用', role: '技术顾问' },
    ],
    performance: {
      tasksCompleted: 45,
      tasksInProgress: 3,
      averageRating: 4.5,
      productivity: 85,
    },
  },
  {
    id: '2',
    username: 'lisi',
    email: '<EMAIL>',
    firstName: '四',
    lastName: '李',
    avatar: '',
    phone: '13800138002',
    department: '技术部',
    position: '后端工程师',
    role: 'developer',
    status: 'active',
    joinDate: '2024-02-01',
    lastActiveAt: '2025-08-16T09:15:00Z',
    skills: ['Java', 'Spring Boot', 'MySQL', 'Redis'],
    projects: [
      { id: '1', name: 'AI客服系统', role: '后端开发' },
    ],
    performance: {
      tasksCompleted: 38,
      tasksInProgress: 5,
      averageRating: 4.2,
      productivity: 78,
    },
  },
  {
    id: '3',
    username: 'wangwu',
    email: '<EMAIL>',
    firstName: '五',
    lastName: '王',
    avatar: '',
    phone: '13800138003',
    department: '产品部',
    position: '产品经理',
    role: 'manager',
    status: 'active',
    joinDate: '2023-12-01',
    lastActiveAt: '2025-08-16T11:45:00Z',
    skills: ['产品设计', '需求分析', 'Axure', 'Figma'],
    projects: [
      { id: '1', name: 'AI客服系统', role: '产品经理' },
      { id: '2', name: '移动端应用', role: '产品经理' },
    ],
    performance: {
      tasksCompleted: 52,
      tasksInProgress: 2,
      averageRating: 4.8,
      productivity: 92,
    },
  },
  {
    id: '4',
    username: 'zhaoliu',
    email: '<EMAIL>',
    firstName: '六',
    lastName: '赵',
    avatar: '',
    phone: '13800138004',
    department: '技术部',
    position: '测试工程师',
    role: 'tester',
    status: 'pending',
    joinDate: '2025-08-01',
    skills: ['自动化测试', 'Selenium', 'Jest', 'Cypress'],
    projects: [
      { id: '1', name: 'AI客服系统', role: '测试工程师' },
    ],
    performance: {
      tasksCompleted: 8,
      tasksInProgress: 4,
      averageRating: 4.0,
      productivity: 65,
    },
  },
]

const TeamPage: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>(mockTeamMembers)
  const [filteredMembers, setFilteredMembers] = useState<TeamMember[]>(mockTeamMembers)
  const [loading, setLoading] = useState(false)
  const [inviteModalVisible, setInviteModalVisible] = useState(false)
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null)
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    status: '',
    department: '',
  })

  // 角色配置
  const roleConfig = {
    admin: { title: '管理员', color: 'red' },
    manager: { title: '项目经理', color: 'blue' },
    developer: { title: '开发工程师', color: 'green' },
    designer: { title: '设计师', color: 'purple' },
    tester: { title: '测试工程师', color: 'orange' },
    analyst: { title: '分析师', color: 'cyan' },
  }

  // 状态配置
  const statusConfig = {
    active: { title: '活跃', color: 'success' },
    inactive: { title: '离线', color: 'default' },
    pending: { title: '待激活', color: 'warning' },
  }

  // 应用筛选
  useEffect(() => {
    let filtered = teamMembers

    if (filters.search) {
      filtered = filtered.filter(member =>
        member.username.toLowerCase().includes(filters.search.toLowerCase()) ||
        member.email.toLowerCase().includes(filters.search.toLowerCase()) ||
        `${member.lastName}${member.firstName}`.includes(filters.search)
      )
    }

    if (filters.role) {
      filtered = filtered.filter(member => member.role === filters.role)
    }

    if (filters.status) {
      filtered = filtered.filter(member => member.status === filters.status)
    }

    if (filters.department) {
      filtered = filtered.filter(member => member.department === filters.department)
    }

    setFilteredMembers(filtered)
  }, [teamMembers, filters])

  // 处理删除成员
  const handleDeleteMember = (memberId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该团队成员吗？此操作不可恢复。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        setTeamMembers(teamMembers.filter(member => member.id !== memberId))
        message.success('成员删除成功')
      },
    })
  }

  // 表格列定义
  const columns: ColumnsType<TeamMember> = [
    {
      title: '成员信息',
      key: 'member',
      width: 250,
      render: (_, record) => (
        <div className="flex items-center">
          <Avatar src={record.avatar} size="large" className="mr-3">
            {record.lastName}{record.firstName}
          </Avatar>
          <div>
            <div className="font-medium">
              {record.lastName}{record.firstName}
            </div>
            <div className="text-gray-500 text-sm">@{record.username}</div>
            <div className="text-gray-500 text-sm">{record.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role: string) => (
        <Tag color={roleConfig[role as keyof typeof roleConfig]?.color}>
          {roleConfig[role as keyof typeof roleConfig]?.title || role}
        </Tag>
      ),
    },
    {
      title: '部门/职位',
      key: 'department',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.department}</div>
          <div className="text-gray-500 text-sm">{record.position}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Badge
          status={statusConfig[status as keyof typeof statusConfig]?.color as any}
          text={statusConfig[status as keyof typeof statusConfig]?.title}
        />
      ),
    },
    {
      title: '技能',
      dataIndex: 'skills',
      key: 'skills',
      width: 200,
      render: (skills: string[]) => (
        <div className="flex flex-wrap gap-1">
          {skills.slice(0, 3).map(skill => (
            <Tag key={skill} size="small">{skill}</Tag>
          ))}
          {skills.length > 3 && (
            <Tag size="small">+{skills.length - 3}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '绩效',
      key: 'performance',
      width: 120,
      render: (_, record) => (
        <div>
          <Progress
            percent={record.performance.productivity}
            size="small"
            format={(percent) => `${percent}%`}
          />
          <div className="text-xs text-gray-500 mt-1">
            完成: {record.performance.tasksCompleted}
          </div>
        </div>
      ),
    },
    {
      title: '加入时间',
      dataIndex: 'joinDate',
      key: 'joinDate',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => {
        const menuItems = [
          {
            key: 'view',
            label: '查看详情',
            onClick: () => setSelectedMember(record),
          },
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: '编辑信息',
            onClick: () => message.info('编辑功能开发中'),
          },
          {
            type: 'divider' as const,
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: '移除成员',
            danger: true,
            onClick: () => handleDeleteMember(record.id),
          },
        ]

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        )
      },
    },
  ]

  return (
    <div className="team-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">团队管理</Title>
          <p className="text-gray-600 m-0">
            管理团队成员，查看绩效和协作情况
          </p>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setInviteModalVisible(true)}
        >
          邀请成员
        </Button>
      </div>

      {/* 团队统计 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="团队成员"
              value={teamMembers.length}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃成员"
              value={teamMembers.filter(m => m.status === 'active').length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均绩效"
              value={Math.round(teamMembers.reduce((acc, m) => acc + m.performance.productivity, 0) / teamMembers.length)}
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总完成任务"
              value={teamMembers.reduce((acc, m) => acc + m.performance.tasksCompleted, 0)}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选和搜索 */}
      <Card className="mb-4">
        <div className="flex flex-wrap gap-4 items-center">
          <Search
            placeholder="搜索成员姓名、用户名或邮箱"
            allowClear
            style={{ width: 300 }}
            value={filters.search}
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            enterButton={<SearchOutlined />}
          />

          <Select
            placeholder="角色"
            allowClear
            style={{ width: 120 }}
            value={filters.role}
            onChange={(value) => setFilters({ ...filters, role: value })}
          >
            {Object.entries(roleConfig).map(([key, config]) => (
              <Option key={key} value={key}>{config.title}</Option>
            ))}
          </Select>

          <Select
            placeholder="状态"
            allowClear
            style={{ width: 100 }}
            value={filters.status}
            onChange={(value) => setFilters({ ...filters, status: value })}
          >
            {Object.entries(statusConfig).map(([key, config]) => (
              <Option key={key} value={key}>{config.title}</Option>
            ))}
          </Select>

          <Select
            placeholder="部门"
            allowClear
            style={{ width: 120 }}
            value={filters.department}
            onChange={(value) => setFilters({ ...filters, department: value })}
          >
            <Option value="技术部">技术部</Option>
            <Option value="产品部">产品部</Option>
            <Option value="设计部">设计部</Option>
            <Option value="运营部">运营部</Option>
          </Select>
        </div>
      </Card>

      {/* 成员表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredMembers}
          loading={loading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 邀请成员模态框 */}
      <Modal
        title="邀请团队成员"
        open={inviteModalVisible}
        onCancel={() => setInviteModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout="vertical">
          <Form.Item
            name="email"
            label="邮箱地址"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入邀请成员的邮箱地址" />
          </Form.Item>

          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="选择成员角色">
              {Object.entries(roleConfig).map(([key, config]) => (
                <Option key={key} value={key}>{config.title}</Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="department" label="部门">
                <Select placeholder="选择部门">
                  <Option value="技术部">技术部</Option>
                  <Option value="产品部">产品部</Option>
                  <Option value="设计部">设计部</Option>
                  <Option value="运营部">运营部</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="position" label="职位">
                <Input placeholder="请输入职位" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="message" label="邀请消息">
            <Input.TextArea
              rows={3}
              placeholder="可选：添加邀请消息"
            />
          </Form.Item>

          <Form.Item className="mb-0 mt-6">
            <div className="flex justify-end space-x-2">
              <Button onClick={() => setInviteModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                发送邀请
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 成员详情模态框 */}
      <Modal
        title="成员详情"
        open={!!selectedMember}
        onCancel={() => setSelectedMember(null)}
        footer={null}
        width={800}
      >
        {selectedMember && (
          <div>
            <div className="flex items-center mb-6">
              <Avatar src={selectedMember.avatar} size={80} className="mr-4">
                {selectedMember.lastName}{selectedMember.firstName}
              </Avatar>
              <div>
                <Title level={3} className="mb-1">
                  {selectedMember.lastName}{selectedMember.firstName}
                </Title>
                <Text type="secondary">@{selectedMember.username}</Text>
                <div className="mt-2">
                  <Tag color={roleConfig[selectedMember.role as keyof typeof roleConfig]?.color}>
                    {roleConfig[selectedMember.role as keyof typeof roleConfig]?.title}
                  </Tag>
                  <Badge
                    status={statusConfig[selectedMember.status]?.color as any}
                    text={statusConfig[selectedMember.status]?.title}
                  />
                </div>
              </div>
            </div>

            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="基本信息" size="small">
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <MailOutlined className="mr-2" />
                      {selectedMember.email}
                    </div>
                    {selectedMember.phone && (
                      <div className="flex items-center">
                        <PhoneOutlined className="mr-2" />
                        {selectedMember.phone}
                      </div>
                    )}
                    <div>部门: {selectedMember.department}</div>
                    <div>职位: {selectedMember.position}</div>
                    <div>加入时间: {new Date(selectedMember.joinDate).toLocaleDateString('zh-CN')}</div>
                  </div>
                </Card>
              </Col>

              <Col span={12}>
                <Card title="绩效统计" size="small">
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between">
                        <span>生产力</span>
                        <span>{selectedMember.performance.productivity}%</span>
                      </div>
                      <Progress percent={selectedMember.performance.productivity} size="small" />
                    </div>
                    <div>已完成任务: {selectedMember.performance.tasksCompleted}</div>
                    <div>进行中任务: {selectedMember.performance.tasksInProgress}</div>
                    <div>平均评分: {selectedMember.performance.averageRating}/5</div>
                  </div>
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} className="mt-4">
              <Col span={12}>
                <Card title="技能标签" size="small">
                  <div className="flex flex-wrap gap-2">
                    {selectedMember.skills.map(skill => (
                      <Tag key={skill}>{skill}</Tag>
                    ))}
                  </div>
                </Card>
              </Col>

              <Col span={12}>
                <Card title="参与项目" size="small">
                  <List
                    size="small"
                    dataSource={selectedMember.projects}
                    renderItem={(project) => (
                      <List.Item>
                        <div>
                          <div className="font-medium">{project.name}</div>
                          <div className="text-gray-500 text-sm">{project.role}</div>
                        </div>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default TeamPage
