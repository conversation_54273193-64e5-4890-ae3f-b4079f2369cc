/**
 * 项目列表页面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Avatar,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  message,
  Tooltip,
  Dropdown,
  Typography,
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  TeamOutlined,
  CalendarOutlined,
  MoreOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

import { RootState, AppDispatch } from '@/store'
import { fetchProjects, deleteProject } from '@/store/slices/projectSlice'
import { Project, ProjectStatus, ProjectPriority } from '@/types/project'
import ProjectCreateModal from '@/components/projects/ProjectCreateModal'

const { Title } = Typography
const { Search } = Input
const { Option } = Select
const { RangePicker } = DatePicker

const ProjectListPage: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch<AppDispatch>()

  const { projects, isLoading, pagination, filters } = useSelector(
    (state: RootState) => state.project
  )

  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  useEffect(() => {
    // 加载项目列表
    dispatch(fetchProjects({
      page: pagination.current - 1,
      size: pagination.pageSize,
      filters,
    }))
  }, [dispatch, pagination.current, pagination.pageSize, filters])

  // 处理搜索
  const handleSearch = (value: string) => {
    dispatch(fetchProjects({
      page: 0,
      size: pagination.pageSize,
      filters: { ...filters, search: value },
    }))
  }

  // 处理筛选
  const handleFilter = (key: string, value: any) => {
    dispatch(fetchProjects({
      page: 0,
      size: pagination.pageSize,
      filters: { ...filters, [key]: value },
    }))
  }

  // 处理删除项目
  const handleDeleteProject = async (projectId: string) => {
    try {
      await dispatch(deleteProject(projectId)).unwrap()
      message.success('项目删除成功')
    } catch (error) {
      message.error('项目删除失败')
    }
  }

  // 获取状态标签颜色
  const getStatusColor = (status: ProjectStatus) => {
    switch (status) {
      case ProjectStatus.ACTIVE:
        return 'processing'
      case ProjectStatus.COMPLETED:
        return 'success'
      case ProjectStatus.ON_HOLD:
        return 'warning'
      case ProjectStatus.CANCELLED:
        return 'error'
      default:
        return 'default'
    }
  }

  // 获取优先级标签颜色
  const getPriorityColor = (priority: ProjectPriority) => {
    switch (priority) {
      case ProjectPriority.URGENT:
        return 'red'
      case ProjectPriority.HIGH:
        return 'orange'
      case ProjectPriority.MEDIUM:
        return 'blue'
      case ProjectPriority.LOW:
        return 'green'
      default:
        return 'default'
    }
  }

  // 表格列定义
  const columns: ColumnsType<Project> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: Project) => (
        <div>
          <Button
            type="link"
            onClick={() => navigate(`/projects/${record.id}`)}
            className="p-0 h-auto font-medium"
          >
            {text}
          </Button>
          {record.description && (
            <div className="text-gray-500 text-sm mt-1 text-ellipsis">
              {record.description}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ProjectStatus) => (
        <Tag color={getStatusColor(status)}>
          {status === ProjectStatus.ACTIVE ? '进行中' :
           status === ProjectStatus.COMPLETED ? '已完成' :
           status === ProjectStatus.ON_HOLD ? '暂停' :
           status === ProjectStatus.CANCELLED ? '已取消' : '计划中'}
        </Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: ProjectPriority) => (
        <Tag color={getPriorityColor(priority)}>
          {priority === ProjectPriority.URGENT ? '紧急' :
           priority === ProjectPriority.HIGH ? '高' :
           priority === ProjectPriority.MEDIUM ? '中' : '低'}
        </Tag>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number) => (
        <Progress
          percent={progress}
          size="small"
          status={progress === 100 ? 'success' : 'active'}
        />
      ),
    },
    {
      title: '团队',
      dataIndex: 'team',
      key: 'team',
      width: 120,
      render: (team: any[]) => (
        <div className="flex items-center">
          <Avatar.Group maxCount={3} size="small">
            {team?.map((member, index) => (
              <Tooltip key={index} title={member.user?.username}>
                <Avatar src={member.user?.avatar} size="small">
                  {member.user?.username?.charAt(0)}
                </Avatar>
              </Tooltip>
            ))}
          </Avatar.Group>
          <span className="ml-2 text-gray-500">
            <TeamOutlined /> {team?.length || 0}
          </span>
        </div>
      ),
    },
    {
      title: '截止日期',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 120,
      render: (endDate: string) => (
        <div className="flex items-center text-gray-600">
          <CalendarOutlined className="mr-1" />
          {endDate ? new Date(endDate).toLocaleDateString('zh-CN') : '-'}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: Project) => {
        const menuItems = [
          {
            key: 'view',
            icon: <EyeOutlined />,
            label: '查看详情',
            onClick: () => navigate(`/projects/${record.id}`),
          },
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: '编辑项目',
            onClick: () => {
              // TODO: 打开编辑模态框
              message.info('编辑功能开发中')
            },
          },
          {
            type: 'divider' as const,
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: '删除项目',
            danger: true,
            onClick: () => {
              Modal.confirm({
                title: '确认删除',
                content: `确定要删除项目"${record.name}"吗？此操作不可恢复。`,
                okText: '删除',
                okType: 'danger',
                cancelText: '取消',
                onOk: () => handleDeleteProject(record.id),
              })
            },
          },
        ]

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        )
      },
    },
  ]

  return (
    <div className="project-list-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">项目管理</Title>
          <p className="text-gray-600 m-0">
            管理和跟踪您的所有项目，查看进度和团队协作情况
          </p>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          size="large"
          onClick={() => setCreateModalVisible(true)}
        >
          创建项目
        </Button>
      </div>

      {/* 筛选和搜索 */}
      <Card className="mb-4">
        <div className="flex flex-wrap gap-4 items-center">
          <Search
            placeholder="搜索项目名称或描述"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
            enterButton={<SearchOutlined />}
          />

          <Select
            placeholder="项目状态"
            allowClear
            style={{ width: 120 }}
            onChange={(value) => handleFilter('status', value)}
          >
            <Option value={ProjectStatus.PLANNING}>计划中</Option>
            <Option value={ProjectStatus.ACTIVE}>进行中</Option>
            <Option value={ProjectStatus.ON_HOLD}>暂停</Option>
            <Option value={ProjectStatus.COMPLETED}>已完成</Option>
            <Option value={ProjectStatus.CANCELLED}>已取消</Option>
          </Select>

          <Select
            placeholder="优先级"
            allowClear
            style={{ width: 100 }}
            onChange={(value) => handleFilter('priority', value)}
          >
            <Option value={ProjectPriority.URGENT}>紧急</Option>
            <Option value={ProjectPriority.HIGH}>高</Option>
            <Option value={ProjectPriority.MEDIUM}>中</Option>
            <Option value={ProjectPriority.LOW}>低</Option>
          </Select>

          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={(dates) => handleFilter('dateRange', dates)}
          />

          <Button icon={<FilterOutlined />}>
            更多筛选
          </Button>
        </div>
      </Card>

      {/* 项目表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={projects}
          loading={isLoading}
          rowKey="id"
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1000 }}
        />

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded">
            <Space>
              <span>已选择 {selectedRowKeys.length} 个项目</span>
              <Button size="small">批量导出</Button>
              <Button size="small" danger>批量删除</Button>
            </Space>
          </div>
        )}
      </Card>

      {/* 创建项目模态框 */}
      <ProjectCreateModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSuccess={() => {
          setCreateModalVisible(false)
          // 重新加载项目列表
          dispatch(fetchProjects({
            page: pagination.current - 1,
            size: pagination.pageSize,
            filters,
          }))
        }}
      />
    </div>
  )
}

export default ProjectListPage
