/**
 * 项目详情页面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import {
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Progress,
  Avatar,
  Button,
  Space,
  Tabs,
  Statistic,
  Timeline,
  List,
  Descriptions,
  Tooltip,
  Dropdown,
  Modal,
  message,
} from 'antd'
import {
  EditOutlined,
  ShareAltOutlined,
  MoreOutlined,
  TeamOutlined,
  CalendarOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons'

import { RootState, AppDispatch } from '@/store'
import { fetchProjectById } from '@/store/slices/projectSlice'
import { ProjectStatus, ProjectPriority } from '@/types/project'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs

const ProjectDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const dispatch = useDispatch<AppDispatch>()

  const { currentProject, isLoading } = useSelector((state: RootState) => state.project)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (id) {
      dispatch(fetchProjectById(id))
    }
  }, [dispatch, id])

  if (!currentProject && !isLoading) {
    return (
      <div className="text-center py-20">
        <Title level={3}>项目不存在</Title>
        <Button type="primary" onClick={() => navigate('/projects')}>
          返回项目列表
        </Button>
      </div>
    )
  }

  // 获取状态标签颜色
  const getStatusColor = (status: ProjectStatus) => {
    switch (status) {
      case ProjectStatus.ACTIVE:
        return 'processing'
      case ProjectStatus.COMPLETED:
        return 'success'
      case ProjectStatus.ON_HOLD:
        return 'warning'
      case ProjectStatus.CANCELLED:
        return 'error'
      default:
        return 'default'
    }
  }

  // 获取优先级标签颜色
  const getPriorityColor = (priority: ProjectPriority) => {
    switch (priority) {
      case ProjectPriority.URGENT:
        return 'red'
      case ProjectPriority.HIGH:
        return 'orange'
      case ProjectPriority.MEDIUM:
        return 'blue'
      case ProjectPriority.LOW:
        return 'green'
      default:
        return 'default'
    }
  }

  // 模拟数据
  const mockTasks = [
    {
      id: '1',
      title: '需求分析',
      status: 'completed',
      assignee: '张三',
      dueDate: '2025-08-20',
    },
    {
      id: '2',
      title: '系统设计',
      status: 'in_progress',
      assignee: '李四',
      dueDate: '2025-08-25',
    },
    {
      id: '3',
      title: '前端开发',
      status: 'todo',
      assignee: '王五',
      dueDate: '2025-09-01',
    },
  ]

  const mockActivities = [
    {
      id: '1',
      user: '张三',
      action: '完成了任务',
      target: '需求分析',
      time: '2小时前',
    },
    {
      id: '2',
      user: '李四',
      action: '更新了项目进度',
      target: '系统设计',
      time: '4小时前',
    },
    {
      id: '3',
      user: '王五',
      action: '加入了项目',
      target: '',
      time: '1天前',
    },
  ]

  const menuItems = [
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: '编辑项目',
      onClick: () => message.info('编辑功能开发中'),
    },
    {
      key: 'share',
      icon: <ShareAltOutlined />,
      label: '分享项目',
      onClick: () => message.info('分享功能开发中'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'archive',
      label: '归档项目',
      onClick: () => message.info('归档功能开发中'),
    },
  ]

  return (
    <div className="project-detail-page">
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/projects')}
            className="mr-2"
          >
            返回
          </Button>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div>
                <Title level={2} className="mb-2">
                  {currentProject?.name}
                </Title>
                <Space>
                  <Tag color={getStatusColor(currentProject?.status!)}>
                    {currentProject?.status === ProjectStatus.ACTIVE ? '进行中' :
                     currentProject?.status === ProjectStatus.COMPLETED ? '已完成' :
                     currentProject?.status === ProjectStatus.ON_HOLD ? '暂停' :
                     currentProject?.status === ProjectStatus.CANCELLED ? '已取消' : '计划中'}
                  </Tag>
                  <Tag color={getPriorityColor(currentProject?.priority!)}>
                    {currentProject?.priority === ProjectPriority.URGENT ? '紧急' :
                     currentProject?.priority === ProjectPriority.HIGH ? '高' :
                     currentProject?.priority === ProjectPriority.MEDIUM ? '中' : '低'}
                  </Tag>
                  {currentProject?.tags?.map((tag) => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </Space>
              </div>
              <Space>
                <Button type="primary" icon={<EditOutlined />}>
                  编辑项目
                </Button>
                <Dropdown menu={{ items: menuItems }} trigger={['click']}>
                  <Button icon={<MoreOutlined />} />
                </Dropdown>
              </Space>
            </div>
          </div>
        </div>

        {currentProject?.description && (
          <Paragraph className="text-gray-600 mb-0">
            {currentProject.description}
          </Paragraph>
        )}
      </div>

      {/* 项目统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="项目进度"
              value={currentProject?.progress || 0}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress
              percent={currentProject?.progress || 0}
              size="small"
              className="mt-2"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="团队成员"
              value={currentProject?.team?.length || 0}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="任务总数"
              value={currentProject?.stats?.totalTasks || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="项目预算"
              value={currentProject?.budget || 0}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card>
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane tab="项目概览" key="overview">
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="项目经理">
                    <div className="flex items-center">
                      <Avatar src={currentProject?.manager?.avatar} size="small" className="mr-2">
                        {currentProject?.manager?.username?.charAt(0)}
                      </Avatar>
                      {currentProject?.manager?.username || '未分配'}
                    </div>
                  </Descriptions.Item>
                  <Descriptions.Item label="项目负责人">
                    <div className="flex items-center">
                      <Avatar src={currentProject?.owner?.avatar} size="small" className="mr-2">
                        {currentProject?.owner?.username?.charAt(0)}
                      </Avatar>
                      {currentProject?.owner?.username}
                    </div>
                  </Descriptions.Item>
                  <Descriptions.Item label="开始日期">
                    {currentProject?.startDate ?
                      new Date(currentProject.startDate).toLocaleDateString('zh-CN') : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="结束日期">
                    {currentProject?.endDate ?
                      new Date(currentProject.endDate).toLocaleDateString('zh-CN') : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="项目预算">
                    ¥{currentProject?.budget?.toLocaleString() || 0}
                  </Descriptions.Item>
                  <Descriptions.Item label="实际成本">
                    ¥{currentProject?.actualCost?.toLocaleString() || 0}
                  </Descriptions.Item>
                  <Descriptions.Item label="是否公开">
                    {currentProject?.isPublic ? '是' : '否'}
                  </Descriptions.Item>
                  <Descriptions.Item label="创建时间">
                    {currentProject?.createdAt ?
                      new Date(currentProject.createdAt).toLocaleDateString('zh-CN') : '-'}
                  </Descriptions.Item>
                </Descriptions>
              </TabPane>

              <TabPane tab="任务列表" key="tasks">
                <List
                  dataSource={mockTasks}
                  renderItem={(task) => (
                    <List.Item
                      actions={[
                        <Button type="link" size="small">查看</Button>,
                        <Button type="link" size="small">编辑</Button>,
                      ]}
                    >
                      <List.Item.Meta
                        title={task.title}
                        description={
                          <Space>
                            <Tag color={
                              task.status === 'completed' ? 'success' :
                              task.status === 'in_progress' ? 'processing' : 'default'
                            }>
                              {task.status === 'completed' ? '已完成' :
                               task.status === 'in_progress' ? '进行中' : '待开始'}
                            </Tag>
                            <span>负责人: {task.assignee}</span>
                            <span>截止: {task.dueDate}</span>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </TabPane>

              <TabPane tab="项目文件" key="files">
                <div className="text-center py-8 text-gray-500">
                  文件管理功能开发中...
                </div>
              </TabPane>

              <TabPane tab="项目设置" key="settings">
                <Descriptions column={1} bordered>
                  <Descriptions.Item label="自动分配任务">
                    {currentProject?.settings?.isTaskAutoAssignment ? '启用' : '禁用'}
                  </Descriptions.Item>
                  <Descriptions.Item label="时间跟踪">
                    {currentProject?.settings?.isTimeTracking ? '启用' : '禁用'}
                  </Descriptions.Item>
                  <Descriptions.Item label="公开评论">
                    {currentProject?.settings?.isPublicComments ? '启用' : '禁用'}
                  </Descriptions.Item>
                  <Descriptions.Item label="需要审批">
                    {currentProject?.settings?.workflowSettings?.requireApproval ? '是' : '否'}
                  </Descriptions.Item>
                  <Descriptions.Item label="允许自分配">
                    {currentProject?.settings?.workflowSettings?.allowSelfAssignment ? '是' : '否'}
                  </Descriptions.Item>
                </Descriptions>
              </TabPane>
            </Tabs>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          {/* 团队成员 */}
          <Card title="团队成员" className="mb-4">
            <List
              dataSource={currentProject?.team || []}
              renderItem={(member) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar src={member.user?.avatar}>{member.user?.username?.charAt(0)}</Avatar>}
                    title={member.user?.username}
                    description={member.role}
                  />
                </List.Item>
              )}
            />
          </Card>

          {/* 最近活动 */}
          <Card title="最近活动">
            <Timeline
              items={mockActivities.map((activity) => ({
                children: (
                  <div>
                    <div className="font-medium">
                      {activity.user} {activity.action} {activity.target}
                    </div>
                    <div className="text-gray-500 text-sm">{activity.time}</div>
                  </div>
                ),
              }))}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default ProjectDetailPage
