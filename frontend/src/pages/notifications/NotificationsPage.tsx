/**
 * 通知管理页面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Table,
  Button,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Tooltip,
  Dropdown,
  Progress,
  Statistic,
  Timeline,
  Tabs,
  Badge,
  DatePicker,
} from 'antd'
import {
  BellOutlined,
  SendOutlined,
  SettingOutlined,
  DeleteOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  MoreOutlined,
  EyeOutlined,
  EditOutlined,
  PlusOutlined,
  FilterOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

import websocketService from '@/services/websocketService'

const { Title, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs
const { RangePicker } = DatePicker

// 通知接口
interface Notification {
  id: string
  userId: string
  type: 'info' | 'warning' | 'error' | 'success'
  channel: 'in_app' | 'email' | 'sms' | 'websocket'
  title: string
  content: string
  status: 'pending' | 'sent' | 'failed' | 'read'
  priority: number
  scheduledAt?: string
  sentAt?: string
  readAt?: string
  createdAt: string
  data?: any
}

// 模拟数据
const mockNotifications: Notification[] = [
  {
    id: '1',
    userId: 'user1',
    type: 'info',
    channel: 'in_app',
    title: '项目进度更新',
    content: 'AI客服系统项目进度已更新至75%',
    status: 'sent',
    priority: 1,
    sentAt: '2025-08-16T10:30:00Z',
    createdAt: '2025-08-16T10:30:00Z',
  },
  {
    id: '2',
    userId: 'user2',
    type: 'warning',
    channel: 'email',
    title: '任务即将到期',
    content: '您有3个任务将在24小时内到期',
    status: 'sent',
    priority: 2,
    sentAt: '2025-08-16T09:15:00Z',
    createdAt: '2025-08-16T09:15:00Z',
  },
  {
    id: '3',
    userId: 'user1',
    type: 'success',
    channel: 'websocket',
    title: '代码审查通过',
    content: '您提交的代码已通过审查',
    status: 'read',
    priority: 1,
    sentAt: '2025-08-16T08:45:00Z',
    readAt: '2025-08-16T09:00:00Z',
    createdAt: '2025-08-16T08:45:00Z',
  },
  {
    id: '4',
    userId: 'user3',
    type: 'error',
    channel: 'sms',
    title: '构建失败',
    content: '前端项目构建失败',
    status: 'failed',
    priority: 3,
    createdAt: '2025-08-16T08:00:00Z',
  },
]

const NotificationsPage: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications)
  const [loading, setLoading] = useState(false)
  const [sendModalVisible, setSendModalVisible] = useState(false)
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null)
  const [activeTab, setActiveTab] = useState('list')
  const [filters, setFilters] = useState({
    type: '',
    channel: '',
    status: '',
    dateRange: null as any,
  })
  const [form] = Form.useForm()

  useEffect(() => {
    // 连接WebSocket
    const userId = 'current-user-id' // 从认证状态获取
    websocketService.connect(userId).catch(console.error)

    // 监听新通知
    const handleNewNotification = (event: CustomEvent) => {
      const notification = event.detail
      message.info(`新通知: ${notification.title}`)
      // 刷新通知列表
      loadNotifications()
    }

    window.addEventListener('websocket-notification', handleNewNotification as EventListener)

    return () => {
      window.removeEventListener('websocket-notification', handleNewNotification as EventListener)
      websocketService.disconnect()
    }
  }, [])

  // 加载通知列表
  const loadNotifications = async () => {
    setLoading(true)
    try {
      // TODO: 调用API获取通知
      setTimeout(() => {
        setNotifications(mockNotifications)
        setLoading(false)
      }, 1000)
    } catch (error) {
      message.error('加载通知失败')
      setLoading(false)
    }
  }

  // 获取通知类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      default:
        return <InfoCircleOutlined />
    }
  }

  // 获取渠道标签
  const getChannelTag = (channel: string) => {
    const channelMap = {
      in_app: { color: 'blue', text: '应用内' },
      email: { color: 'green', text: '邮件' },
      sms: { color: 'orange', text: '短信' },
      websocket: { color: 'purple', text: '实时' },
    }
    const config = channelMap[channel as keyof typeof channelMap] || { color: 'default', text: channel }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'default', text: '待发送' },
      sent: { color: 'success', text: '已发送' },
      failed: { color: 'error', text: '发送失败' },
      read: { color: 'processing', text: '已读' },
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 发送通知
  const handleSendNotification = async (values: any) => {
    try {
      setLoading(true)
      // TODO: 调用API发送通知
      console.log('发送通知:', values)
      message.success('通知发送成功')
      setSendModalVisible(false)
      form.resetFields()
      loadNotifications()
    } catch (error) {
      message.error('发送通知失败')
    } finally {
      setLoading(false)
    }
  }

  // 删除通知
  const handleDeleteNotification = (notificationId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条通知吗？',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // TODO: 调用API删除通知
          setNotifications(prev => prev.filter(n => n.id !== notificationId))
          message.success('通知已删除')
        } catch (error) {
          message.error('删除通知失败')
        }
      },
    })
  }

  // 表格列定义
  const columns: ColumnsType<Notification> = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: string) => (
        <Avatar icon={getTypeIcon(type)} size="small" />
      ),
    },
    {
      title: '通知内容',
      key: 'content',
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.title}</div>
          <div className="text-gray-500 text-sm">{record.content}</div>
        </div>
      ),
    },
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      width: 100,
      render: (channel: string) => getChannelTag(channel),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: number) => (
        <Tag color={priority === 3 ? 'red' : priority === 2 ? 'orange' : 'blue'}>
          {priority === 3 ? '高' : priority === 2 ? '中' : '低'}
        </Tag>
      ),
    },
    {
      title: '发送时间',
      dataIndex: 'sentAt',
      key: 'sentAt',
      width: 150,
      render: (sentAt: string) => (
        sentAt ? new Date(sentAt).toLocaleString('zh-CN') : '-'
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => {
        const menuItems = [
          {
            key: 'view',
            icon: <EyeOutlined />,
            label: '查看详情',
            onClick: () => setSelectedNotification(record),
          },
          {
            key: 'resend',
            icon: <SendOutlined />,
            label: '重新发送',
            disabled: record.status === 'sent',
            onClick: () => message.info('重新发送功能开发中'),
          },
          {
            type: 'divider' as const,
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: '删除',
            danger: true,
            onClick: () => handleDeleteNotification(record.id),
          },
        ]

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        )
      },
    },
  ]

  // 统计数据
  const stats = {
    total: notifications.length,
    sent: notifications.filter(n => n.status === 'sent').length,
    failed: notifications.filter(n => n.status === 'failed').length,
    read: notifications.filter(n => n.status === 'read').length,
  }

  return (
    <div className="notifications-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">通知管理</Title>
          <Text type="secondary">
            管理系统通知，支持多渠道发送和实时推送
          </Text>
        </div>
        <Space>
          <Button icon={<FilterOutlined />}>
            筛选
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setSendModalVisible(true)}
          >
            发送通知
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="通知总数"
              value={stats.total}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="发送成功"
              value={stats.sent}
              prefix={<CheckOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="发送失败"
              value={stats.failed}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="已读数量"
              value={stats.read}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="通知列表" key="list">
            <Table
              columns={columns}
              dataSource={notifications}
              loading={loading}
              rowKey="id"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </TabPane>

          <TabPane tab="发送统计" key="stats">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="发送成功率" size="small">
                  <Progress
                    type="circle"
                    percent={Math.round((stats.sent / stats.total) * 100)}
                    format={(percent) => `${percent}%`}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="阅读率" size="small">
                  <Progress
                    type="circle"
                    percent={Math.round((stats.read / stats.sent) * 100)}
                    format={(percent) => `${percent}%`}
                    strokeColor="#52c41a"
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="通知模板" key="templates">
            <div className="text-center py-20">
              <Title level={4}>通知模板管理</Title>
              <Text type="secondary">
                创建和管理通知模板，提高发送效率
              </Text>
              <div className="mt-4">
                <Button type="primary" size="large">
                  创建模板
                </Button>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 发送通知模态框 */}
      <Modal
        title="发送通知"
        open={sendModalVisible}
        onCancel={() => {
          setSendModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSendNotification}
        >
          <Form.Item
            name="type"
            label="通知类型"
            rules={[{ required: true, message: '请选择通知类型' }]}
          >
            <Select placeholder="选择通知类型">
              <Option value="info">信息</Option>
              <Option value="warning">警告</Option>
              <Option value="error">错误</Option>
              <Option value="success">成功</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="channel"
            label="发送渠道"
            rules={[{ required: true, message: '请选择发送渠道' }]}
          >
            <Select placeholder="选择发送渠道">
              <Option value="in_app">应用内通知</Option>
              <Option value="email">邮件通知</Option>
              <Option value="sms">短信通知</Option>
              <Option value="websocket">实时通知</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="通知标题"
            rules={[{ required: true, message: '请输入通知标题' }]}
          >
            <Input placeholder="请输入通知标题" />
          </Form.Item>

          <Form.Item
            name="content"
            label="通知内容"
            rules={[{ required: true, message: '请输入通知内容' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入通知内容" />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            initialValue={1}
          >
            <Select>
              <Option value={1}>低</Option>
              <Option value={2}>中</Option>
              <Option value={3}>高</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="userIds"
            label="接收用户"
            rules={[{ required: true, message: '请选择接收用户' }]}
          >
            <Select mode="multiple" placeholder="选择接收用户">
              <Option value="user1">张三</Option>
              <Option value="user2">李四</Option>
              <Option value="user3">王五</Option>
              <Option value="all">所有用户</Option>
            </Select>
          </Form.Item>

          <Form.Item className="mb-0 mt-6">
            <div className="flex justify-end space-x-2">
              <Button onClick={() => setSendModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                发送通知
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 通知详情模态框 */}
      <Modal
        title="通知详情"
        open={!!selectedNotification}
        onCancel={() => setSelectedNotification(null)}
        footer={null}
        width={600}
      >
        {selectedNotification && (
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Avatar icon={getTypeIcon(selectedNotification.type)} />
              <div>
                <div className="font-medium">{selectedNotification.title}</div>
                <div className="text-gray-500 text-sm">
                  {getChannelTag(selectedNotification.channel)}
                  {getStatusTag(selectedNotification.status)}
                </div>
              </div>
            </div>

            <div>
              <Text strong>通知内容:</Text>
              <div className="mt-1 p-3 bg-gray-50 rounded">
                {selectedNotification.content}
              </div>
            </div>

            <Row gutter={16}>
              <Col span={12}>
                <Text strong>优先级:</Text>
                <div>
                  <Tag color={selectedNotification.priority === 3 ? 'red' :
                              selectedNotification.priority === 2 ? 'orange' : 'blue'}>
                    {selectedNotification.priority === 3 ? '高' :
                     selectedNotification.priority === 2 ? '中' : '低'}
                  </Tag>
                </div>
              </Col>
              <Col span={12}>
                <Text strong>创建时间:</Text>
                <div>{new Date(selectedNotification.createdAt).toLocaleString('zh-CN')}</div>
              </Col>
            </Row>

            {selectedNotification.sentAt && (
              <div>
                <Text strong>发送时间:</Text>
                <div>{new Date(selectedNotification.sentAt).toLocaleString('zh-CN')}</div>
              </div>
            )}

            {selectedNotification.readAt && (
              <div>
                <Text strong>阅读时间:</Text>
                <div>{new Date(selectedNotification.readAt).toLocaleString('zh-CN')}</div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default NotificationsPage
