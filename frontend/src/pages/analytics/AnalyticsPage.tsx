/**
 * AI分析页面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Tabs,
  Button,
  Space,
  Alert,
  Spin,
  Progress,
  Tag,
  List,
  Avatar,
  Statistic,
  Timeline,
} from 'antd'
import {
  RobotOutlined,
  BulbOutlined,
  WarningOutlined,
  TrendingUpOutlined,
  TargetOutlined,
  <PERSON>boltOutlined,
  EyeOutlined,
  ReloadOutlined,
} from '@ant-design/icons'

import AIAnalysisChart from '@/components/charts/AIAnalysisChart'
import TeamEfficiencyChart from '@/components/charts/TeamEfficiencyChart'
import DashboardChart from '@/components/charts/DashboardChart'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs

const AnalyticsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [analysisLoading, setAnalysisLoading] = useState(false)
  const [lastAnalysisTime, setLastAnalysisTime] = useState(new Date())

  // 模拟AI分析数据
  const aiInsights = {
    riskLevel: 'medium',
    projectHealth: 78,
    teamEfficiency: 85,
    predictedDelay: 3,
    recommendations: [
      {
        id: '1',
        type: 'optimization',
        priority: 'high',
        title: '增加前端开发人员',
        description: '当前前端开发任务积压较多，建议增加2名前端开发人员',
        impact: '可提升项目进度15%',
        effort: '中等',
        timeline: '1-2周',
      },
      {
        id: '2',
        type: 'process',
        priority: 'medium',
        title: '优化代码审查流程',
        description: '代码审查环节耗时较长，建议采用并行审查机制',
        impact: '可节省20%审查时间',
        effort: '低',
        timeline: '1周',
      },
      {
        id: '3',
        type: 'risk',
        priority: 'high',
        title: '关注技术债务',
        description: '检测到技术债务积累，建议安排重构任务',
        impact: '避免未来维护成本增加',
        effort: '高',
        timeline: '3-4周',
      },
    ],
    predictions: [
      {
        metric: '项目完成时间',
        current: '2025-09-15',
        predicted: '2025-09-18',
        confidence: 85,
        trend: 'delay',
      },
      {
        metric: '预算使用率',
        current: '75%',
        predicted: '82%',
        confidence: 92,
        trend: 'normal',
      },
      {
        metric: '团队满意度',
        current: '4.2/5',
        predicted: '4.0/5',
        confidence: 78,
        trend: 'decline',
      },
    ],
    anomalies: [
      {
        id: '1',
        type: 'performance',
        severity: 'warning',
        title: '任务完成速度下降',
        description: '过去一周任务完成速度比平均水平低15%',
        detectedAt: '2025-08-16 10:30',
        affectedArea: '前端开发团队',
      },
      {
        id: '2',
        type: 'quality',
        severity: 'info',
        title: '代码质量提升',
        description: '代码审查通过率提升至95%，质量指标良好',
        detectedAt: '2025-08-16 09:15',
        affectedArea: '整体项目',
      },
    ],
  }

  // 触发AI分析
  const triggerAnalysis = async () => {
    setAnalysisLoading(true)
    // 模拟AI分析过程
    setTimeout(() => {
      setAnalysisLoading(false)
      setLastAnalysisTime(new Date())
    }, 3000)
  }

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red'
      case 'medium': return 'orange'
      case 'low': return 'green'
      default: return 'default'
    }
  }

  // 获取趋势图标
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'delay': return <WarningOutlined style={{ color: '#fa8c16' }} />
      case 'normal': return <TrendingUpOutlined style={{ color: '#52c41a' }} />
      case 'decline': return <WarningOutlined style={{ color: '#f5222d' }} />
      default: return <TrendingUpOutlined />
    }
  }

  return (
    <div className="analytics-page">
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title level={2} className="mb-2">
            <RobotOutlined className="mr-2" />
            AI智能分析
          </Title>
          <Text type="secondary">
            基于机器学习的项目智能分析和预测
          </Text>
        </div>
        <Space>
          <Text type="secondary">
            上次分析: {lastAnalysisTime.toLocaleString('zh-CN')}
          </Text>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            loading={analysisLoading}
            onClick={triggerAnalysis}
          >
            重新分析
          </Button>
        </Space>
      </div>

      {/* AI分析状态 */}
      <Alert
        message="AI分析引擎运行正常"
        description={`已完成${aiInsights.recommendations.length}项优化建议分析，检测到${aiInsights.anomalies.length}个异常情况`}
        type="success"
        showIcon
        className="mb-6"
        action={
          <Button size="small" type="text" icon={<EyeOutlined />}>
            查看详情
          </Button>
        }
      />

      {/* 核心指标 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="项目健康度"
              value={aiInsights.projectHealth}
              suffix="%"
              valueStyle={{
                color: aiInsights.projectHealth >= 80 ? '#52c41a' :
                       aiInsights.projectHealth >= 60 ? '#faad14' : '#f5222d'
              }}
              prefix={<TargetOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="团队效率"
              value={aiInsights.teamEfficiency}
              suffix="%"
              valueStyle={{ color: '#1890ff' }}
              prefix={<ThunderboltOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="预测延期"
              value={aiInsights.predictedDelay}
              suffix="天"
              valueStyle={{ color: '#fa8c16' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="优化建议"
              value={aiInsights.recommendations.length}
              suffix="项"
              valueStyle={{ color: '#722ed1' }}
              prefix={<BulbOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* AI分析内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="智能概览" key="overview">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="风险分析矩阵" size="small">
                  <AIAnalysisChart type="risk" height={300} />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="进度预测分析" size="small">
                  <AIAnalysisChart type="prediction" height={300} />
                </Card>
              </Col>
              <Col xs={24}>
                <Card title="AI优化建议" size="small">
                  <AIAnalysisChart type="recommendation" height={300} />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="优化建议" key="recommendations">
            <List
              itemLayout="vertical"
              dataSource={aiInsights.recommendations}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button type="link" key="detail">查看详情</Button>,
                    <Button type="link" key="apply">应用建议</Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={<BulbOutlined />} />}
                    title={
                      <Space>
                        <span>{item.title}</span>
                        <Tag color={getPriorityColor(item.priority)}>
                          {item.priority === 'high' ? '高优先级' :
                           item.priority === 'medium' ? '中优先级' : '低优先级'}
                        </Tag>
                        <Tag color="blue">{item.type}</Tag>
                      </Space>
                    }
                    description={item.description}
                  />
                  <div className="mt-2">
                    <Row gutter={16}>
                      <Col span={8}>
                        <Text strong>预期影响: </Text>
                        <Text>{item.impact}</Text>
                      </Col>
                      <Col span={8}>
                        <Text strong>实施难度: </Text>
                        <Text>{item.effort}</Text>
                      </Col>
                      <Col span={8}>
                        <Text strong>预计时间: </Text>
                        <Text>{item.timeline}</Text>
                      </Col>
                    </Row>
                  </div>
                </List.Item>
              )}
            />
          </TabPane>

          <TabPane tab="预测分析" key="predictions">
            <Row gutter={[16, 16]}>
              {aiInsights.predictions.map((prediction, index) => (
                <Col xs={24} sm={8} key={index}>
                  <Card>
                    <div className="flex justify-between items-center mb-2">
                      <Text strong>{prediction.metric}</Text>
                      {getTrendIcon(prediction.trend)}
                    </div>
                    <div className="mb-2">
                      <Text type="secondary">当前值: </Text>
                      <Text>{prediction.current}</Text>
                    </div>
                    <div className="mb-2">
                      <Text type="secondary">预测值: </Text>
                      <Text>{prediction.predicted}</Text>
                    </div>
                    <div>
                      <Text type="secondary">置信度: </Text>
                      <Progress
                        percent={prediction.confidence}
                        size="small"
                        format={(percent) => `${percent}%`}
                      />
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </TabPane>

          <TabPane tab="异常检测" key="anomalies">
            <Timeline>
              {aiInsights.anomalies.map((anomaly) => (
                <Timeline.Item
                  key={anomaly.id}
                  color={anomaly.severity === 'warning' ? 'orange' : 'blue'}
                  dot={anomaly.severity === 'warning' ? <WarningOutlined /> : <EyeOutlined />}
                >
                  <div>
                    <div className="flex justify-between items-start mb-2">
                      <Text strong>{anomaly.title}</Text>
                      <Tag color={anomaly.severity === 'warning' ? 'orange' : 'blue'}>
                        {anomaly.severity === 'warning' ? '警告' : '信息'}
                      </Tag>
                    </div>
                    <Paragraph className="mb-2">{anomaly.description}</Paragraph>
                    <div className="text-gray-500 text-sm">
                      <Text type="secondary">检测时间: {anomaly.detectedAt}</Text>
                      <Text type="secondary" className="ml-4">影响范围: {anomaly.affectedArea}</Text>
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </TabPane>

          <TabPane tab="团队分析" key="team">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={8}>
                <Card title="团队效率分析" size="small">
                  <TeamEfficiencyChart type="productivity" height={250} />
                </Card>
              </Col>
              <Col xs={24} lg={8}>
                <Card title="工作负载分布" size="small">
                  <TeamEfficiencyChart type="workload" height={250} />
                </Card>
              </Col>
              <Col xs={24} lg={8}>
                <Card title="综合能力雷达" size="small">
                  <TeamEfficiencyChart type="efficiency" height={250} />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="趋势分析" key="trends">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="项目趋势" size="small">
                  <DashboardChart type="trend" height={300} />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="团队绩效趋势" size="small">
                  <DashboardChart type="performance" height={300} />
                </Card>
              </Col>
              <Col xs={24}>
                <Card title="活动时间线" size="small">
                  <DashboardChart type="timeline" height={250} />
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default AnalyticsPage
