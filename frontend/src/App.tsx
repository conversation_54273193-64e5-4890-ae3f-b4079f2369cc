/**
 * 主应用组件
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-16
 */

import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import { App as AntdApp } from 'antd'

import { RootState } from './store'
import { checkAuthStatus } from './store/slices/authSlice'
import { AppDispatch } from './store'

// 布局组件
import MainLayout from './components/layout/MainLayout'
import AuthLayout from './components/layout/AuthLayout'

// 页面组件
import LoginPage from './pages/auth/LoginPage'
import RegisterPage from './pages/auth/RegisterPage'
import DashboardPage from './pages/dashboard/DashboardPage'
import ProjectListPage from './pages/projects/ProjectListPage'
import ProjectDetailPage from './pages/projects/ProjectDetailPage'
import TaskListPage from './pages/tasks/TaskListPage'
import TeamPage from './pages/team/TeamPage'
import ReportsPage from './pages/reports/ReportsPage'
import AnalyticsPage from './pages/analytics/AnalyticsPage'
import GitIntegrationPage from './pages/git/GitIntegrationPage'
import GitAnalyticsPage from './pages/git/GitAnalyticsPage'
import NotificationsPage from './pages/notifications/NotificationsPage'
import IntegrationsPage from './pages/integrations/IntegrationsPage'
import SettingsPage from './pages/settings/SettingsPage'
import NotFoundPage from './pages/error/NotFoundPage'

// 路由守卫组件
import ProtectedRoute from './components/auth/ProtectedRoute'
import PublicRoute from './components/auth/PublicRoute'

const App: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    // 检查认证状态
    dispatch(checkAuthStatus())
  }, [dispatch])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <AntdApp>
      <Routes>
        {/* 公开路由 */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <AuthLayout>
                <LoginPage />
              </AuthLayout>
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <AuthLayout>
                <RegisterPage />
              </AuthLayout>
            </PublicRoute>
          }
        />

        {/* 受保护的路由 */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="projects" element={<ProjectListPage />} />
          <Route path="projects/:id" element={<ProjectDetailPage />} />
          <Route path="tasks" element={<TaskListPage />} />
          <Route path="team" element={<TeamPage />} />
          <Route path="git" element={<GitIntegrationPage />} />
          <Route path="git/analytics" element={<GitAnalyticsPage />} />
          <Route path="notifications" element={<NotificationsPage />} />
          <Route path="integrations" element={<IntegrationsPage />} />
          <Route path="reports" element={<ReportsPage />} />
          <Route path="analytics" element={<AnalyticsPage />} />
          <Route path="settings" element={<SettingsPage />} />
        </Route>

        {/* 404页面 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </AntdApp>
  )
}

export default App
