# AI项目管理平台技术架构设计文档

## 1. 架构概览

### 1.1 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Web前端<br/>React + TypeScript]
        B[移动端<br/>React Native]
        C[桌面端<br/>Electron]
    end
    
    subgraph "API网关层"
        D[API Gateway<br/>Kong/Nginx]
        E[负载均衡器<br/>HAProxy]
        F[认证服务<br/>OAuth 2.0]
    end
    
    subgraph "微服务层"
        G[项目管理服务<br/>Spring Boot]
        H[AI分析服务<br/>Python/FastAPI]
        I[集成服务<br/>Node.js]
        J[通知服务<br/>Go]
        K[用户服务<br/>Spring Boot]
    end
    
    subgraph "AI引擎层"
        L[机器学习平台<br/>MLflow]
        M[模型服务<br/>TensorFlow Serving]
        N[数据处理<br/>Apache Spark]
        O[特征存储<br/>Feast]
    end
    
    subgraph "数据层"
        P[关系数据库<br/>PostgreSQL]
        Q[文档数据库<br/>MongoDB]
        R[图数据库<br/>Neo4j]
        S[缓存<br/>Redis]
        T[搜索引擎<br/>Elasticsearch]
    end
    
    subgraph "基础设施层"
        U[容器编排<br/>Kubernetes]
        V[服务网格<br/>Istio]
        W[监控<br/>Prometheus]
        X[日志<br/>ELK Stack]
        Y[消息队列<br/>Apache Kafka]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    
    H --> L
    H --> M
    H --> N
    H --> O
    
    G --> P
    H --> Q
    I --> R
    J --> S
    K --> T
    
    G --> Y
    H --> Y
    I --> Y
    J --> Y
    
    U --> V
    V --> W
    W --> X
```

### 1.2 技术栈选择

#### 1.2.1 前端技术栈
- **Web前端**: React 18 + TypeScript + Ant Design
- **状态管理**: Redux Toolkit + RTK Query
- **构建工具**: Vite + ESBuild
- **测试框架**: Jest + React Testing Library
- **代码质量**: ESLint + Prettier + Husky

#### 1.2.2 后端技术栈
- **项目管理服务**: Spring Boot 3.0 + Java 17
- **AI分析服务**: Python 3.11 + FastAPI + Pydantic
- **集成服务**: Node.js 18 + Express + TypeScript
- **通知服务**: Go 1.20 + Gin + gRPC
- **用户服务**: Spring Boot 3.0 + Spring Security

#### 1.2.3 AI/ML技术栈
- **机器学习**: TensorFlow 2.13 + PyTorch 2.0
- **数据处理**: Apache Spark 3.4 + Pandas
- **模型管理**: MLflow + DVC
- **特征工程**: Feast + Apache Airflow
- **模型服务**: TensorFlow Serving + Triton

#### 1.2.4 数据存储技术栈
- **关系数据库**: PostgreSQL 15 + pgvector
- **文档数据库**: MongoDB 6.0
- **图数据库**: Neo4j 5.0
- **缓存**: Redis 7.0 + Redis Cluster
- **搜索**: Elasticsearch 8.0

#### 1.2.5 基础设施技术栈
- **容器化**: Docker + Kubernetes 1.27
- **服务网格**: Istio 1.18
- **监控**: Prometheus + Grafana + Jaeger
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **消息队列**: Apache Kafka 3.5

## 2. 微服务架构设计

### 2.1 服务拆分原则

#### 2.1.1 业务边界划分
- **项目管理服务**: 项目、任务、里程碑管理
- **AI分析服务**: 机器学习模型、预测分析
- **集成服务**: 第三方工具集成、数据同步
- **通知服务**: 消息推送、邮件通知
- **用户服务**: 用户认证、权限管理

#### 2.1.2 数据一致性策略
- **强一致性**: 用户认证、权限管理
- **最终一致性**: 项目数据、分析结果
- **事件驱动**: 跨服务数据同步
- **补偿机制**: 分布式事务处理

### 2.2 服务间通信

#### 2.2.1 同步通信
```yaml
# gRPC服务定义示例
syntax = "proto3";

package project.v1;

service ProjectService {
  rpc CreateProject(CreateProjectRequest) returns (CreateProjectResponse);
  rpc GetProject(GetProjectRequest) returns (GetProjectResponse);
  rpc UpdateProject(UpdateProjectRequest) returns (UpdateProjectResponse);
  rpc DeleteProject(DeleteProjectRequest) returns (DeleteProjectResponse);
  rpc ListProjects(ListProjectsRequest) returns (ListProjectsResponse);
}

message Project {
  string id = 1;
  string name = 2;
  string description = 3;
  string status = 4;
  int64 created_at = 5;
  int64 updated_at = 6;
}
```

#### 2.2.2 异步通信
```yaml
# Kafka事件定义示例
events:
  project_created:
    schema:
      type: object
      properties:
        project_id:
          type: string
        project_name:
          type: string
        created_by:
          type: string
        created_at:
          type: string
          format: date-time
    
  task_completed:
    schema:
      type: object
      properties:
        task_id:
          type: string
        project_id:
          type: string
        completed_by:
          type: string
        completed_at:
          type: string
          format: date-time
```

### 2.3 数据管理策略

#### 2.3.1 数据库选择原则
- **PostgreSQL**: 事务性数据、复杂查询
- **MongoDB**: 文档数据、灵活schema
- **Neo4j**: 关系数据、图分析
- **Redis**: 缓存数据、会话存储
- **Elasticsearch**: 全文搜索、日志分析

#### 2.3.2 数据分片策略
```sql
-- PostgreSQL分片示例
-- 按项目ID分片
CREATE TABLE projects_shard_1 (
    LIKE projects INCLUDING ALL
) INHERITS (projects);

CREATE TABLE projects_shard_2 (
    LIKE projects INCLUDING ALL
) INHERITS (projects);

-- 分片规则
ALTER TABLE projects_shard_1 ADD CONSTRAINT projects_shard_1_check 
CHECK (project_id % 2 = 0);

ALTER TABLE projects_shard_2 ADD CONSTRAINT projects_shard_2_check 
CHECK (project_id % 2 = 1);
```

## 3. AI引擎架构设计

### 3.1 机器学习管道

#### 3.1.1 数据处理管道
```python
# Apache Airflow DAG示例
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta

def extract_project_data():
    """提取项目数据"""
    # 从各个数据源提取数据
    pass

def transform_features():
    """特征工程"""
    # 数据清洗和特征提取
    pass

def train_model():
    """模型训练"""
    # 训练机器学习模型
    pass

def validate_model():
    """模型验证"""
    # 模型性能验证
    pass

def deploy_model():
    """模型部署"""
    # 部署到生产环境
    pass

# DAG定义
dag = DAG(
    'ml_pipeline',
    default_args={
        'owner': 'ai-team',
        'depends_on_past': False,
        'start_date': datetime(2025, 1, 1),
        'email_on_failure': True,
        'email_on_retry': False,
        'retries': 1,
        'retry_delay': timedelta(minutes=5)
    },
    description='机器学习训练管道',
    schedule_interval='@daily',
    catchup=False
)

# 任务定义
extract_task = PythonOperator(
    task_id='extract_data',
    python_callable=extract_project_data,
    dag=dag
)

transform_task = PythonOperator(
    task_id='transform_features',
    python_callable=transform_features,
    dag=dag
)

train_task = PythonOperator(
    task_id='train_model',
    python_callable=train_model,
    dag=dag
)

validate_task = PythonOperator(
    task_id='validate_model',
    python_callable=validate_model,
    dag=dag
)

deploy_task = PythonOperator(
    task_id='deploy_model',
    python_callable=deploy_model,
    dag=dag
)

# 任务依赖
extract_task >> transform_task >> train_task >> validate_task >> deploy_task
```

#### 3.1.2 模型服务架构
```python
# TensorFlow Serving配置示例
import tensorflow as tf
from tensorflow_serving.apis import predict_pb2
from tensorflow_serving.apis import prediction_service_pb2_grpc

class ModelServer:
    def __init__(self, model_name, model_version):
        self.model_name = model_name
        self.model_version = model_version
        self.channel = grpc.insecure_channel('localhost:8500')
        self.stub = prediction_service_pb2_grpc.PredictionServiceStub(self.channel)
    
    def predict(self, input_data):
        """模型预测"""
        request = predict_pb2.PredictRequest()
        request.model_spec.name = self.model_name
        request.model_spec.signature_name = 'serving_default'
        
        # 输入数据转换
        request.inputs['input'].CopyFrom(
            tf.make_tensor_proto(input_data, shape=input_data.shape)
        )
        
        # 执行预测
        result = self.stub.Predict(request, 10.0)
        
        # 结果解析
        output = tf.make_ndarray(result.outputs['output'])
        return output
```

### 3.2 特征工程

#### 3.2.1 特征存储设计
```python
# Feast特征定义示例
from feast import Entity, Feature, FeatureView, ValueType
from feast.data_source import BigQuerySource

# 实体定义
project = Entity(name="project_id", value_type=ValueType.STRING)
user = Entity(name="user_id", value_type=ValueType.STRING)

# 数据源定义
project_stats_source = BigQuerySource(
    table_ref="ai_pm.project_stats",
    event_timestamp_column="timestamp",
)

# 特征视图定义
project_features = FeatureView(
    name="project_stats",
    entities=["project_id"],
    features=[
        Feature(name="task_completion_rate", dtype=ValueType.FLOAT),
        Feature(name="team_velocity", dtype=ValueType.FLOAT),
        Feature(name="code_quality_score", dtype=ValueType.FLOAT),
        Feature(name="risk_score", dtype=ValueType.FLOAT),
    ],
    online=True,
    batch_source=project_stats_source,
    ttl=timedelta(days=1),
)
```

#### 3.2.2 实时特征计算
```python
# Apache Flink实时特征计算示例
from pyflink.datastream import StreamExecutionEnvironment
from pyflink.table import StreamTableEnvironment

def calculate_real_time_features():
    """实时特征计算"""
    env = StreamExecutionEnvironment.get_execution_environment()
    t_env = StreamTableEnvironment.create(env)
    
    # 定义数据源
    t_env.execute_sql("""
        CREATE TABLE project_events (
            project_id STRING,
            event_type STRING,
            event_data STRING,
            event_time TIMESTAMP(3),
            WATERMARK FOR event_time AS event_time - INTERVAL '5' SECOND
        ) WITH (
            'connector' = 'kafka',
            'topic' = 'project-events',
            'properties.bootstrap.servers' = 'localhost:9092',
            'format' = 'json'
        )
    """)
    
    # 实时特征计算
    t_env.execute_sql("""
        CREATE TABLE project_features AS
        SELECT 
            project_id,
            COUNT(*) as event_count,
            COUNT(CASE WHEN event_type = 'task_completed' THEN 1 END) as completed_tasks,
            TUMBLE_END(event_time, INTERVAL '1' HOUR) as window_end
        FROM project_events
        GROUP BY project_id, TUMBLE(event_time, INTERVAL '1' HOUR)
    """)
```

## 4. 安全架构设计

### 4.1 认证和授权

#### 4.1.1 OAuth 2.0 + JWT实现
```java
// Spring Security配置示例
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .requestMatchers("/api/projects/**").hasAnyRole("USER", "ADMIN")
                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt
                    .decoder(jwtDecoder())
                    .jwtAuthenticationConverter(jwtAuthenticationConverter())
                )
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            );
        
        return http.build();
    }
    
    @Bean
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri("https://auth.example.com/.well-known/jwks.json")
                .build();
    }
}
```

#### 4.1.2 基于角色的权限控制
```yaml
# RBAC权限配置示例
roles:
  admin:
    permissions:
      - project:create
      - project:read
      - project:update
      - project:delete
      - user:manage
      - system:configure
  
  project_manager:
    permissions:
      - project:create
      - project:read
      - project:update
      - team:manage
      - report:generate
  
  developer:
    permissions:
      - project:read
      - task:update
      - code:commit
      - issue:create
  
  viewer:
    permissions:
      - project:read
      - report:view
```

### 4.2 数据安全

#### 4.2.1 数据加密策略
```python
# 数据加密示例
from cryptography.fernet import Fernet
import base64
import os

class DataEncryption:
    def __init__(self):
        # 从环境变量获取加密密钥
        key = os.environ.get('ENCRYPTION_KEY')
        if not key:
            key = Fernet.generate_key()
        self.cipher_suite = Fernet(key)
    
    def encrypt_sensitive_data(self, data):
        """加密敏感数据"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        encrypted_data = self.cipher_suite.encrypt(data)
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def decrypt_sensitive_data(self, encrypted_data):
        """解密敏感数据"""
        encrypted_data = base64.b64decode(encrypted_data.encode('utf-8'))
        decrypted_data = self.cipher_suite.decrypt(encrypted_data)
        return decrypted_data.decode('utf-8')
```

#### 4.2.2 数据脱敏处理
```python
# 数据脱敏示例
import re
import hashlib

class DataMasking:
    @staticmethod
    def mask_email(email):
        """邮箱脱敏"""
        if '@' not in email:
            return email
        local, domain = email.split('@')
        if len(local) <= 2:
            return email
        return local[0] + '*' * (len(local) - 2) + local[-1] + '@' + domain
    
    @staticmethod
    def mask_phone(phone):
        """手机号脱敏"""
        if len(phone) != 11:
            return phone
        return phone[:3] + '****' + phone[7:]
    
    @staticmethod
    def hash_sensitive_id(sensitive_id):
        """敏感ID哈希化"""
        return hashlib.sha256(sensitive_id.encode()).hexdigest()[:16]
```

## 5. 监控和运维

### 5.1 监控体系

#### 5.1.1 应用监控
```yaml
# Prometheus监控配置示例
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'ai-pm-services'
    static_configs:
      - targets: ['project-service:8080', 'ai-service:8081', 'integration-service:8082']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s

  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 5.1.2 告警规则
```yaml
# 告警规则示例
groups:
  - name: ai-pm-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} for {{ $labels.instance }}"
      
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 90% for {{ $labels.instance }}"
      
      - alert: ModelPredictionLatency
        expr: histogram_quantile(0.95, rate(model_prediction_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High model prediction latency"
          description: "95th percentile latency is {{ $value }}s"
```

### 5.2 日志管理

#### 5.2.1 结构化日志
```python
# 结构化日志示例
import structlog
import logging

# 配置structlog
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# 使用示例
def process_project_data(project_id, user_id):
    logger.info(
        "Processing project data",
        project_id=project_id,
        user_id=user_id,
        action="data_processing"
    )
    
    try:
        # 处理逻辑
        result = perform_analysis(project_id)
        
        logger.info(
            "Project data processed successfully",
            project_id=project_id,
            user_id=user_id,
            result_count=len(result),
            action="data_processing_success"
        )
        
        return result
        
    except Exception as e:
        logger.error(
            "Failed to process project data",
            project_id=project_id,
            user_id=user_id,
            error=str(e),
            action="data_processing_error"
        )
        raise
```

### 5.3 性能优化

#### 5.3.1 数据库优化策略
```sql
-- PostgreSQL性能优化示例
-- 1. 索引优化
CREATE INDEX CONCURRENTLY idx_projects_status_created_at
ON projects(status, created_at)
WHERE status IN ('IN_PROGRESS', 'PLANNING');

CREATE INDEX CONCURRENTLY idx_tasks_project_assignee
ON tasks(project_id, assignee)
WHERE status != 'DONE';

-- 2. 分区表设计
CREATE TABLE project_events (
    id BIGSERIAL,
    project_id UUID NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- 按月分区
CREATE TABLE project_events_2025_01 PARTITION OF project_events
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 3. 查询优化
-- 使用CTE优化复杂查询
WITH project_stats AS (
    SELECT
        p.id,
        p.name,
        COUNT(t.id) as total_tasks,
        COUNT(CASE WHEN t.status = 'DONE' THEN 1 END) as completed_tasks,
        AVG(CASE WHEN t.status = 'DONE'
            THEN EXTRACT(EPOCH FROM (t.updated_at - t.created_at))/3600
            END) as avg_completion_hours
    FROM projects p
    LEFT JOIN tasks t ON p.id = t.project_id
    WHERE p.status = 'IN_PROGRESS'
    GROUP BY p.id, p.name
)
SELECT
    ps.*,
    CASE
        WHEN ps.total_tasks > 0
        THEN (ps.completed_tasks::FLOAT / ps.total_tasks) * 100
        ELSE 0
    END as completion_percentage
FROM project_stats ps
ORDER BY completion_percentage DESC;
```

#### 5.3.2 缓存策略
```python
# Redis缓存策略实现
import redis
import json
from typing import Optional, Any
from datetime import timedelta

class CacheManager:
    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
        self.default_ttl = 3600  # 1小时

    def get_project_cache_key(self, project_id: str, cache_type: str) -> str:
        """生成项目缓存键"""
        return f"project:{project_id}:{cache_type}"

    def cache_project_data(self, project_id: str, data: dict, ttl: Optional[int] = None) -> None:
        """缓存项目数据"""
        cache_key = self.get_project_cache_key(project_id, "data")
        ttl = ttl or self.default_ttl

        self.redis_client.setex(
            cache_key,
            ttl,
            json.dumps(data, default=str)
        )

    def get_cached_project_data(self, project_id: str) -> Optional[dict]:
        """获取缓存的项目数据"""
        cache_key = self.get_project_cache_key(project_id, "data")
        cached_data = self.redis_client.get(cache_key)

        if cached_data:
            return json.loads(cached_data)
        return None

    def cache_ai_analysis(self, project_id: str, analysis_type: str, result: dict) -> None:
        """缓存AI分析结果"""
        cache_key = self.get_project_cache_key(project_id, f"ai:{analysis_type}")
        # AI分析结果缓存时间较长，因为计算成本高
        self.redis_client.setex(cache_key, 7200, json.dumps(result, default=str))

    def invalidate_project_cache(self, project_id: str) -> None:
        """清除项目相关缓存"""
        pattern = f"project:{project_id}:*"
        keys = self.redis_client.keys(pattern)
        if keys:
            self.redis_client.delete(*keys)

# 缓存装饰器
from functools import wraps

def cache_result(cache_key_func, ttl=3600):
    """缓存结果装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_manager = CacheManager("redis://localhost:6379")
            cache_key = cache_key_func(*args, **kwargs)

            # 尝试从缓存获取
            cached_result = cache_manager.redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)

            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            cache_manager.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(result, default=str)
            )

            return result
        return wrapper
    return decorator

# 使用示例
@cache_result(
    lambda project_id: f"project:{project_id}:health",
    ttl=1800  # 30分钟
)
async def calculate_project_health(project_id: str) -> dict:
    """计算项目健康度（带缓存）"""
    # 复杂的健康度计算逻辑
    pass
```

#### 5.3.3 API性能优化
```python
# FastAPI性能优化示例
from fastapi import FastAPI, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import asyncio
from typing import List
import uvloop

# 使用uvloop提升性能
asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

app = FastAPI(
    title="AI Project Management API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 连接池配置
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

DATABASE_URL = "postgresql+asyncpg://user:password@localhost/aipm"

engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,  # 连接池大小
    max_overflow=30,  # 最大溢出连接
    pool_pre_ping=True,  # 连接预检
    pool_recycle=3600,  # 连接回收时间
    echo=False
)

AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

# 依赖注入
async def get_db():
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# 批量操作优化
@app.post("/api/v1/projects/{project_id}/tasks/batch")
async def create_tasks_batch(
    project_id: str,
    tasks: List[CreateTaskRequest],
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """批量创建任务"""
    # 批量插入优化
    task_objects = [
        Task(
            project_id=project_id,
            title=task.title,
            description=task.description,
            assignee=task.assignee,
            priority=task.priority
        )
        for task in tasks
    ]

    db.add_all(task_objects)
    await db.commit()

    # 异步处理后续任务
    background_tasks.add_task(
        process_new_tasks_async,
        project_id,
        [task.id for task in task_objects]
    )

    return {"created_count": len(task_objects)}

async def process_new_tasks_async(project_id: str, task_ids: List[str]):
    """异步处理新创建的任务"""
    # 发送通知、更新缓存等
    pass

# 分页优化
from fastapi import Query

@app.get("/api/v1/projects/{project_id}/tasks")
async def get_project_tasks(
    project_id: str,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = None,
    assignee: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取项目任务（分页）"""
    offset = (page - 1) * size

    # 构建查询
    query = select(Task).where(Task.project_id == project_id)

    if status:
        query = query.where(Task.status == status)
    if assignee:
        query = query.where(Task.assignee == assignee)

    # 获取总数和数据
    count_query = select(func.count(Task.id)).where(Task.project_id == project_id)
    if status:
        count_query = count_query.where(Task.status == status)
    if assignee:
        count_query = count_query.where(Task.assignee == assignee)

    total = await db.scalar(count_query)
    tasks = await db.execute(
        query.offset(offset).limit(size).order_by(Task.created_at.desc())
    )

    return {
        "tasks": [TaskDto.from_orm(task) for task in tasks.scalars()],
        "pagination": {
            "page": page,
            "size": size,
            "total": total,
            "pages": (total + size - 1) // size
        }
    }
```

## 6. 部署和运维

### 6.1 容器化部署

#### 6.1.1 Docker配置
```dockerfile
# AI分析服务Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

```dockerfile
# 项目管理服务Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

# 复制JAR文件
COPY target/project-management-service.jar app.jar

# 创建非root用户
RUN useradd -m -u 1000 appuser
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# JVM优化参数
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseContainerSupport"

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 6.1.2 Kubernetes部署配置
```yaml
# 项目管理服务部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: project-management-service
  namespace: ai-pm
  labels:
    app: project-management-service
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: project-management-service
  template:
    metadata:
      labels:
        app: project-management-service
        version: v1.0.0
    spec:
      containers:
      - name: project-management
        image: ai-pm/project-management-service:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: redis-config
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
      volumes:
      - name: config-volume
        configMap:
          name: project-management-config

---
apiVersion: v1
kind: Service
metadata:
  name: project-management-service
  namespace: ai-pm
spec:
  selector:
    app: project-management-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP

---
# AI分析服务部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-analysis-service
  namespace: ai-pm
  labels:
    app: ai-analysis-service
    version: v1.0.0
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai-analysis-service
  template:
    metadata:
      labels:
        app: ai-analysis-service
        version: v1.0.0
    spec:
      containers:
      - name: ai-analysis
        image: ai-pm/ai-analysis-service:1.0.0
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: MODEL_STORAGE_PATH
          value: "/models"
        - name: MONGODB_URL
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: url
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
            nvidia.com/gpu: 0
          limits:
            memory: "4Gi"
            cpu: "2000m"
            nvidia.com/gpu: 1
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 120
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 10
        volumeMounts:
        - name: model-storage
          mountPath: /models
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: model-storage-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: ai-analysis-service
  namespace: ai-pm
spec:
  selector:
    app: ai-analysis-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: ClusterIP
```

### 6.2 CI/CD管道

#### 6.2.1 GitHub Actions配置
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [project-management, ai-analysis, integration, notification]

    steps:
    - uses: actions/checkout@v4

    - name: Setup Java (for Java services)
      if: matrix.service == 'project-management'
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Python (for Python services)
      if: matrix.service == 'ai-analysis'
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Setup Node.js (for Node.js services)
      if: matrix.service == 'integration'
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Setup Go (for Go services)
      if: matrix.service == 'notification'
      uses: actions/setup-go@v4
      with:
        go-version: '1.20'

    - name: Run tests
      run: |
        cd services/${{ matrix.service }}
        make test

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./services/${{ matrix.service }}/coverage.xml

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    strategy:
      matrix:
        service: [project-management, ai-analysis, integration, notification]

    steps:
    - uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: ./services/${{ matrix.service }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.27.0'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Deploy to Kubernetes
      run: |
        export KUBECONFIG=kubeconfig
        kubectl apply -f k8s/
        kubectl rollout status deployment/project-management-service -n ai-pm
        kubectl rollout status deployment/ai-analysis-service -n ai-pm
        kubectl rollout status deployment/integration-service -n ai-pm
        kubectl rollout status deployment/notification-service -n ai-pm
```

---

**文档版本：** 1.0
**创建日期：** 2025-08-15
**最后更新：** 2025-08-15
**文档状态：** 技术架构设计完成
