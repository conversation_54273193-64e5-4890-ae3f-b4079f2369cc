-- 初始数据库架构迁移
-- <AUTHOR>
-- @version 1.0.0
-- @since 2025-08-16

-- 迁移版本信息
INSERT INTO schema_migrations (version, description, applied_at) 
VALUES ('001', '初始数据库架构', CURRENT_TIMESTAMP);

-- 创建架构版本管理表
CREATE TABLE IF NOT EXISTS schema_migrations (
    version VARCHAR(20) PRIMARY KEY,
    description TEXT NOT NULL,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 执行主架构
\i '../schemas/main_schema.sql'

-- 执行AI架构
\i '../schemas/ai_schema.sql'

-- 插入默认角色
INSERT INTO roles (name, display_name, description, permissions, is_system) VALUES
('super_admin', '超级管理员', '系统超级管理员，拥有所有权限', 
 '["*"]', TRUE),
('admin', '管理员', '系统管理员，拥有大部分管理权限', 
 '["user.manage", "project.manage", "system.config"]', TRUE),
('project_manager', '项目经理', '项目经理，可以管理项目和团队', 
 '["project.create", "project.manage", "team.manage", "task.manage"]', TRUE),
('developer', '开发人员', '开发人员，可以处理任务和参与项目', 
 '["task.create", "task.update", "project.view", "comment.create"]', TRUE),
('viewer', '查看者', '只读用户，只能查看项目信息', 
 '["project.view", "task.view"]', TRUE);

-- 插入默认AI模型记录
INSERT INTO ai_models (name, type, version, description, model_path, status) VALUES
('项目风险预测模型', 'risk_prediction', '1.0.0', '基于项目特征预测风险等级', '/models/risk_predictor.pkl', 'active'),
('任务推荐模型', 'task_recommendation', '1.0.0', '基于用户技能和偏好推荐任务', '/models/task_recommender.pkl', 'active'),
('团队绩效分析模型', 'performance_analysis', '1.0.0', '分析团队和个人绩效表现', '/models/performance_analyzer.pkl', 'active');

-- 创建默认标签
INSERT INTO tags (name, color, created_by) VALUES
('紧急', '#ff4d4f', (SELECT id FROM users WHERE username = 'admin' LIMIT 1)),
('重要', '#faad14', (SELECT id FROM users WHERE username = 'admin' LIMIT 1)),
('优化', '#52c41a', (SELECT id FROM users WHERE username = 'admin' LIMIT 1)),
('bug修复', '#f50', (SELECT id FROM users WHERE username = 'admin' LIMIT 1)),
('新功能', '#1890ff', (SELECT id FROM users WHERE username = 'admin' LIMIT 1)),
('文档', '#722ed1', (SELECT id FROM users WHERE username = 'admin' LIMIT 1));

-- 创建系统配置表
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认系统配置
INSERT INTO system_config (key, value, description, category, is_public) VALUES
('app.name', '"AI项目管理平台"', '应用名称', 'general', TRUE),
('app.version', '"1.0.0"', '应用版本', 'general', TRUE),
('app.description', '"基于AI的智能项目管理平台"', '应用描述', 'general', TRUE),
('notification.email.enabled', 'true', '是否启用邮件通知', 'notification', FALSE),
('notification.sms.enabled', 'false', '是否启用短信通知', 'notification', FALSE),
('ai.risk_prediction.enabled', 'true', '是否启用风险预测', 'ai', FALSE),
('ai.task_recommendation.enabled', 'true', '是否启用任务推荐', 'ai', FALSE),
('ai.performance_analysis.enabled', 'true', '是否启用绩效分析', 'ai', FALSE),
('security.password.min_length', '8', '密码最小长度', 'security', FALSE),
('security.session.timeout', '3600', '会话超时时间（秒）', 'security', FALSE),
('file.upload.max_size', '10485760', '文件上传最大大小（字节）', 'file', FALSE),
('file.upload.allowed_types', '["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "zip"]', '允许上传的文件类型', 'file', FALSE);

-- 创建数据库函数

-- 获取用户项目权限
CREATE OR REPLACE FUNCTION get_user_project_permissions(user_uuid UUID, project_uuid UUID)
RETURNS TEXT[] AS $$
DECLARE
    user_permissions TEXT[];
    role_permissions TEXT[];
BEGIN
    -- 获取用户在项目中的直接权限
    SELECT permissions INTO user_permissions
    FROM project_members 
    WHERE user_id = user_uuid AND project_id = project_uuid;
    
    -- 获取用户角色权限
    SELECT array_agg(DISTINCT perm)
    INTO role_permissions
    FROM (
        SELECT jsonb_array_elements_text(r.permissions) as perm
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = user_uuid
    ) perms;
    
    -- 合并权限
    RETURN COALESCE(user_permissions, ARRAY[]::TEXT[]) || COALESCE(role_permissions, ARRAY[]::TEXT[]);
END;
$$ LANGUAGE plpgsql;

-- 计算项目进度
CREATE OR REPLACE FUNCTION calculate_project_progress(project_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    total_tasks INTEGER;
    completed_tasks INTEGER;
    progress INTEGER;
BEGIN
    -- 获取项目总任务数
    SELECT COUNT(*) INTO total_tasks
    FROM tasks 
    WHERE project_id = project_uuid;
    
    -- 获取已完成任务数
    SELECT COUNT(*) INTO completed_tasks
    FROM tasks 
    WHERE project_id = project_uuid AND status = 'done';
    
    -- 计算进度
    IF total_tasks = 0 THEN
        progress := 0;
    ELSE
        progress := ROUND((completed_tasks::DECIMAL / total_tasks::DECIMAL) * 100);
    END IF;
    
    -- 更新项目进度
    UPDATE projects 
    SET progress = calculate_project_progress.progress, updated_at = CURRENT_TIMESTAMP
    WHERE id = project_uuid;
    
    RETURN progress;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器自动更新项目进度
CREATE OR REPLACE FUNCTION trigger_update_project_progress()
RETURNS TRIGGER AS $$
BEGIN
    -- 当任务状态改变时，重新计算项目进度
    IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        PERFORM calculate_project_progress(NEW.project_id);
    ELSIF TG_OP = 'INSERT' THEN
        PERFORM calculate_project_progress(NEW.project_id);
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM calculate_project_progress(OLD.project_id);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 为任务表添加触发器
CREATE TRIGGER trigger_tasks_update_project_progress
    AFTER INSERT OR UPDATE OR DELETE ON tasks
    FOR EACH ROW EXECUTE FUNCTION trigger_update_project_progress();

-- 创建视图

-- 项目概览视图
CREATE VIEW project_overview AS
SELECT 
    p.id,
    p.name,
    p.description,
    p.status,
    p.priority,
    p.start_date,
    p.end_date,
    p.budget,
    p.progress,
    p.created_at,
    p.updated_at,
    u1.username as owner_username,
    u1.first_name || ' ' || u1.last_name as owner_name,
    u2.username as manager_username,
    u2.first_name || ' ' || u2.last_name as manager_name,
    (SELECT COUNT(*) FROM tasks WHERE project_id = p.id) as total_tasks,
    (SELECT COUNT(*) FROM tasks WHERE project_id = p.id AND status = 'done') as completed_tasks,
    (SELECT COUNT(*) FROM project_members WHERE project_id = p.id) as team_size
FROM projects p
LEFT JOIN users u1 ON p.owner_id = u1.id
LEFT JOIN users u2 ON p.manager_id = u2.id;

-- 任务详情视图
CREATE VIEW task_details AS
SELECT 
    t.id,
    t.title,
    t.description,
    t.status,
    t.priority,
    t.estimated_hours,
    t.actual_hours,
    t.progress,
    t.start_date,
    t.due_date,
    t.completed_at,
    t.tags,
    t.created_at,
    t.updated_at,
    p.name as project_name,
    u1.username as assignee_username,
    u1.first_name || ' ' || u1.last_name as assignee_name,
    u2.username as reporter_username,
    u2.first_name || ' ' || u2.last_name as reporter_name,
    (SELECT COUNT(*) FROM task_comments WHERE task_id = t.id) as comment_count
FROM tasks t
LEFT JOIN projects p ON t.project_id = p.id
LEFT JOIN users u1 ON t.assignee_id = u1.id
LEFT JOIN users u2 ON t.reporter_id = u2.id;

-- 用户工作负载视图
CREATE VIEW user_workload AS
SELECT 
    u.id,
    u.username,
    u.first_name || ' ' || u.last_name as full_name,
    COUNT(t.id) as assigned_tasks,
    COUNT(CASE WHEN t.status IN ('todo', 'in_progress') THEN 1 END) as active_tasks,
    COUNT(CASE WHEN t.status = 'done' THEN 1 END) as completed_tasks,
    COALESCE(SUM(t.estimated_hours), 0) as total_estimated_hours,
    COALESCE(SUM(t.actual_hours), 0) as total_actual_hours,
    COUNT(CASE WHEN t.due_date < CURRENT_DATE AND t.status != 'done' THEN 1 END) as overdue_tasks
FROM users u
LEFT JOIN tasks t ON u.id = t.assignee_id
GROUP BY u.id, u.username, u.first_name, u.last_name;

-- 添加更新时间触发器
CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
