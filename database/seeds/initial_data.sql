-- AI项目管理平台初始数据
-- <AUTHOR>
-- @version 1.0.0
-- @since 2025-08-16

-- 插入管理员用户
INSERT INTO users (id, username, email, password_hash, first_name, last_name, status, email_verified) VALUES
('00000000-0000-0000-0000-000000000001', 'admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3Kcm', '系统', '管理员', 'active', TRUE),
('00000000-0000-0000-0000-000000000002', 'demo_manager', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3Kcm', '张', '经理', 'active', TRUE),
('00000000-0000-0000-0000-000000000003', 'demo_dev1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3Kcm', '李', '开发', 'active', TRUE),
('00000000-0000-0000-0000-000000000004', 'demo_dev2', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3Kcm', '王', '开发', 'active', TRUE),
('00000000-0000-0000-0000-000000000005', 'demo_tester', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3Kcm', '赵', '测试', 'active', TRUE);

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES
('00000000-0000-0000-0000-000000000001', (SELECT id FROM roles WHERE name = 'super_admin'), '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000002', (SELECT id FROM roles WHERE name = 'project_manager'), '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000003', (SELECT id FROM roles WHERE name = 'developer'), '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000004', (SELECT id FROM roles WHERE name = 'developer'), '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000005', (SELECT id FROM roles WHERE name = 'developer'), '00000000-0000-0000-0000-000000000001');

-- 插入示例项目
INSERT INTO projects (id, name, description, status, priority, start_date, end_date, budget, owner_id, manager_id) VALUES
('10000000-0000-0000-0000-000000000001', 'AI推荐系统开发', '基于机器学习的智能推荐系统，为用户提供个性化内容推荐', 'active', 'high', '2025-07-01', '2025-09-30', 500000.00, '00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002'),
('10000000-0000-0000-0000-000000000002', '用户管理系统', '完善的用户管理和权限控制系统', 'completed', 'medium', '2025-05-01', '2025-07-31', 300000.00, '00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002'),
('10000000-0000-0000-0000-000000000003', '数据分析平台', '实时数据分析和可视化平台', 'active', 'high', '2025-08-01', '2025-11-30', 800000.00, '00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002'),
('10000000-0000-0000-0000-000000000004', '移动端应用', 'iOS和Android移动应用开发', 'planning', 'medium', '2025-09-01', '2025-12-31', 600000.00, '00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002');

-- 添加项目成员
INSERT INTO project_members (project_id, user_id, role) VALUES
('10000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', 'manager'),
('10000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000003', 'member'),
('10000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000004', 'member'),
('10000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000005', 'member'),
('10000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000002', 'manager'),
('10000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000003', 'member'),
('10000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000004', 'member'),
('10000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000002', 'manager'),
('10000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000003', 'member'),
('10000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000004', 'member'),
('10000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000005', 'member');

-- 插入示例任务
INSERT INTO tasks (id, project_id, title, description, status, priority, assignee_id, reporter_id, estimated_hours, start_date, due_date, tags) VALUES
('20000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '需求分析', '分析推荐系统的功能需求和技术需求', 'done', 'high', '00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000002', 40, '2025-07-01', '2025-07-10', ARRAY['需求', '分析']),
('20000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', '系统架构设计', '设计推荐系统的整体架构', 'done', 'high', '00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000002', 32, '2025-07-11', '2025-07-20', ARRAY['架构', '设计']),
('20000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', '数据模型设计', '设计推荐算法所需的数据模型', 'in_progress', 'medium', '00000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000002', 24, '2025-07-21', '2025-08-05', ARRAY['数据', '模型']),
('20000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', '推荐算法实现', '实现核心推荐算法', 'todo', 'high', '00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000002', 80, '2025-08-06', '2025-08-30', ARRAY['算法', '核心']),
('20000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000001', 'API接口开发', '开发推荐系统的REST API接口', 'todo', 'medium', '00000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000002', 48, '2025-08-15', '2025-09-10', ARRAY['API', '接口']),
('20000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000001', '前端界面开发', '开发推荐系统的用户界面', 'todo', 'medium', '00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000002', 56, '2025-08-20', '2025-09-15', ARRAY['前端', '界面']),
('20000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000001', '系统测试', '进行系统集成测试和性能测试', 'todo', 'high', '00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000002', 40, '2025-09-16', '2025-09-25', ARRAY['测试', '集成']);

-- 插入任务依赖关系
INSERT INTO task_dependencies (task_id, depends_on_id, dependency_type) VALUES
('20000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000001', 'finish_to_start'),
('20000000-0000-0000-0000-000000000003', '20000000-0000-0000-0000-000000000002', 'finish_to_start'),
('20000000-0000-0000-0000-000000000004', '20000000-0000-0000-0000-000000000003', 'finish_to_start'),
('20000000-0000-0000-0000-000000000005', '20000000-0000-0000-0000-000000000004', 'finish_to_start'),
('20000000-0000-0000-0000-000000000006', '20000000-0000-0000-0000-000000000005', 'start_to_start'),
('20000000-0000-0000-0000-000000000007', '20000000-0000-0000-0000-000000000005', 'finish_to_start'),
('20000000-0000-0000-0000-000000000007', '20000000-0000-0000-0000-000000000006', 'finish_to_start');

-- 插入任务评论
INSERT INTO task_comments (task_id, user_id, content) VALUES
('20000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', '需求分析已完成，请查看附件中的详细文档。'),
('20000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000003', '文档已审阅，整体需求清晰，可以开始架构设计。'),
('20000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000003', '架构设计初稿已完成，采用微服务架构，支持水平扩展。'),
('20000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000004', '数据模型设计进行中，预计本周完成用户行为数据模型。');

-- 插入里程碑
INSERT INTO milestones (id, project_id, title, description, due_date, status, created_by) VALUES
('30000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '需求分析完成', '完成系统需求分析和技术调研', '2025-07-20', 'closed', '00000000-0000-0000-0000-000000000002'),
('30000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', '核心算法实现', '完成推荐算法的核心实现', '2025-08-30', 'open', '00000000-0000-0000-0000-000000000002'),
('30000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', '系统上线', '完成系统开发并正式上线', '2025-09-30', 'open', '00000000-0000-0000-0000-000000000002');

-- 插入通知示例
INSERT INTO notifications (user_id, type, title, content, priority, project_id, task_id) VALUES
('00000000-0000-0000-0000-000000000003', 'task_assigned', '新任务分配', '您被分配了新任务：推荐算法实现', 'high', '10000000-0000-0000-0000-000000000001', '20000000-0000-0000-0000-000000000004'),
('00000000-0000-0000-0000-000000000004', 'task_due_soon', '任务即将到期', '任务"数据模型设计"将在3天后到期', 'medium', '10000000-0000-0000-0000-000000000001', '20000000-0000-0000-0000-000000000003'),
('00000000-0000-0000-0000-000000000002', 'project_update', '项目进度更新', 'AI推荐系统项目进度已更新至45%', 'info', '10000000-0000-0000-0000-000000000001', NULL);

-- 插入用户偏好设置
INSERT INTO user_preferences (user_id, notification_settings, ui_settings) VALUES
('00000000-0000-0000-0000-000000000001', 
 '{"email": true, "web": true, "sms": false, "task_assigned": true, "task_completed": true, "project_update": true}',
 '{"theme": "light", "language": "zh-CN", "timezone": "Asia/Shanghai"}'),
('00000000-0000-0000-0000-000000000002', 
 '{"email": true, "web": true, "sms": true, "task_assigned": true, "task_completed": true, "project_update": true}',
 '{"theme": "light", "language": "zh-CN", "timezone": "Asia/Shanghai"}'),
('00000000-0000-0000-0000-000000000003', 
 '{"email": true, "web": true, "sms": false, "task_assigned": true, "task_completed": false, "project_update": false}',
 '{"theme": "dark", "language": "zh-CN", "timezone": "Asia/Shanghai"}');

-- 插入时间跟踪记录
INSERT INTO time_entries (user_id, task_id, project_id, description, start_time, end_time, duration) VALUES
('00000000-0000-0000-0000-000000000003', '20000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '需求文档编写', '2025-07-01 09:00:00+08', '2025-07-01 12:00:00+08', 10800),
('00000000-0000-0000-0000-000000000003', '20000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '需求评审会议', '2025-07-02 14:00:00+08', '2025-07-02 16:00:00+08', 7200),
('00000000-0000-0000-0000-000000000003', '20000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', '架构设计', '2025-07-11 09:00:00+08', '2025-07-11 17:00:00+08', 28800),
('00000000-0000-0000-0000-000000000004', '20000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', '数据模型设计', '2025-07-21 09:00:00+08', '2025-07-21 12:00:00+08', 10800);

-- 插入AI相关示例数据

-- 插入项目风险预测记录
INSERT INTO project_risk_predictions (project_id, model_id, risk_level, risk_score, confidence, risk_factors, recommendations, input_features) VALUES
('10000000-0000-0000-0000-000000000001', 
 (SELECT id FROM ai_models WHERE type = 'risk_prediction' LIMIT 1),
 1, 0.65, 0.85,
 '["项目复杂度较高", "团队经验有限", "技术风险中等"]',
 '["增加技术专家参与", "加强代码审查", "建立技术风险评估机制"]',
 '{"duration_days": 90, "team_size": 4, "complexity_score": 7.5, "budget_amount": 500000, "requirements_count": 25, "dependencies_count": 5, "experience_score": 6.5, "technology_risk": 6.0}'),
('10000000-0000-0000-0000-000000000003',
 (SELECT id FROM ai_models WHERE type = 'risk_prediction' LIMIT 1),
 2, 0.78, 0.82,
 '["项目周期较长", "预算较大", "技术复杂度很高"]',
 '["分阶段交付", "增加预算监控", "建立技术原型验证"]',
 '{"duration_days": 120, "team_size": 4, "complexity_score": 8.5, "budget_amount": 800000, "requirements_count": 40, "dependencies_count": 8, "experience_score": 7.0, "technology_risk": 8.0}');

-- 插入任务推荐记录
INSERT INTO task_recommendations (user_id, task_id, model_id, score, reason, context) VALUES
('00000000-0000-0000-0000-000000000003', '20000000-0000-0000-0000-000000000004',
 (SELECT id FROM ai_models WHERE type = 'task_recommendation' LIMIT 1),
 0.92, '匹配技能: Python, 机器学习; 偏好任务类型: 算法开发',
 '{"user_skills": ["Python", "机器学习", "数据分析"], "task_type": "算法开发", "difficulty": "高"}'),
('00000000-0000-0000-0000-000000000004', '20000000-0000-0000-0000-000000000005',
 (SELECT id FROM ai_models WHERE type = 'task_recommendation' LIMIT 1),
 0.88, '匹配技能: Python, FastAPI; 偏好任务类型: 后端开发',
 '{"user_skills": ["Python", "FastAPI", "数据库"], "task_type": "后端开发", "difficulty": "中"}');

-- 插入团队绩效分析记录
INSERT INTO team_performance_analysis (project_id, user_id, model_id, analysis_period_start, analysis_period_end, overall_score, metrics, strengths, weaknesses, recommendations) VALUES
('10000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000003',
 (SELECT id FROM ai_models WHERE type = 'performance_analysis' LIMIT 1),
 '2025-07-01', '2025-07-31', 0.85,
 '{"task_completion_rate": 0.90, "code_quality_score": 0.88, "collaboration_score": 0.82, "innovation_score": 0.80, "learning_growth_score": 0.85, "communication_score": 0.83}',
 '["任务完成率高", "代码质量优秀", "学习能力强"]',
 '["团队协作有待提升", "创新思维需要加强"]',
 '["参与更多团队活动", "尝试新技术和方法", "加强与团队成员的沟通"]');

-- 更新项目进度
SELECT calculate_project_progress('10000000-0000-0000-0000-000000000001');
SELECT calculate_project_progress('10000000-0000-0000-0000-000000000002');
SELECT calculate_project_progress('10000000-0000-0000-0000-000000000003');
