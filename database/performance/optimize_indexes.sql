-- AI项目管理平台数据库性能优化脚本
-- 创建高效索引以提升查询性能
-- 
-- <AUTHOR>
-- @version 1.0.0
-- @since 2025-08-28

-- ============================================================================
-- 用户表索引优化
-- ============================================================================

-- 用户登录查询优化（用户名和邮箱）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username_lower 
ON users (LOWER(username)) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_lower 
ON users (LOWER(email)) 
WHERE status = 'active';

-- 用户状态和角色查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status_role 
ON users (status, role, created_at);

-- 最后登录时间查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_login 
ON users (last_login_at DESC) 
WHERE last_login_at IS NOT NULL;

-- ============================================================================
-- 项目表索引优化
-- ============================================================================

-- 项目状态和负责人查询优化（最常用查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_status_owner_created 
ON projects (status, owner_id, created_at DESC) 
WHERE is_archived = false;

-- 项目搜索优化（名称模糊查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_name_gin 
ON projects USING gin (to_tsvector('simple', name)) 
WHERE is_archived = false;

-- 项目优先级和进度查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_priority_progress 
ON projects (priority, progress, updated_at DESC) 
WHERE status IN ('planning', 'active', 'paused');

-- 项目预算范围查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_budget_range 
ON projects (budget) 
WHERE budget IS NOT NULL AND is_archived = false;

-- 项目成员权限查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_project_members_user_active 
ON project_members (user_id, project_id, role) 
WHERE is_active = true;

-- ============================================================================
-- 任务表索引优化
-- ============================================================================

-- 任务分配和状态查询优化（最频繁查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_assignee_status_project 
ON tasks (assignee_id, status, project_id, due_date) 
WHERE is_deleted = false;

-- 任务项目和状态查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_project_status_priority 
ON tasks (project_id, status, priority, created_at DESC) 
WHERE is_deleted = false;

-- 任务截止日期查询优化（逾期任务检查）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_due_date_status 
ON tasks (due_date, status) 
WHERE due_date IS NOT NULL AND status NOT IN ('done', 'cancelled') AND is_deleted = false;

-- 任务层级关系查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_parent_hierarchy 
ON tasks (parent_id, project_id) 
WHERE parent_id IS NOT NULL AND is_deleted = false;

-- 任务搜索优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_title_description_gin 
ON tasks USING gin (to_tsvector('simple', title || ' ' || COALESCE(description, ''))) 
WHERE is_deleted = false;

-- ============================================================================
-- 活动日志表索引优化
-- ============================================================================

-- 活动日志项目和时间查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_project_created 
ON activity_logs (project_id, created_at DESC);

-- 活动日志用户和操作类型查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_user_action_created 
ON activity_logs (user_id, action_type, created_at DESC);

-- 活动日志目标实体查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_target_type_id 
ON activity_logs (target_type, target_id, created_at DESC);

-- ============================================================================
-- 通知表索引优化
-- ============================================================================

-- 用户通知查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_status_created 
ON notifications (user_id, status, created_at DESC);

-- 通知类型和优先级查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_type_priority_created 
ON notifications (type, priority, created_at DESC) 
WHERE status != 'deleted';

-- 未读通知统计优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread 
ON notifications (user_id) 
WHERE status = 'unread';

-- ============================================================================
-- 评论表索引优化
-- ============================================================================

-- 评论目标和层级查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_target_parent_created 
ON comments (target_type, target_id, parent_id, created_at DESC) 
WHERE is_deleted = false;

-- 评论用户查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_user_created 
ON comments (user_id, created_at DESC) 
WHERE is_deleted = false;

-- ============================================================================
-- 附件表索引优化
-- ============================================================================

-- 附件关联实体查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attachments_entity_type_id 
ON attachments (entity_type, entity_id, created_at DESC) 
WHERE is_deleted = false;

-- 附件文件类型和大小查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attachments_type_size 
ON attachments (file_type, file_size) 
WHERE is_deleted = false;

-- ============================================================================
-- AI相关表索引优化
-- ============================================================================

-- AI模型查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_models_type_status_version 
ON ai_models (type, status, version DESC);

-- 项目风险查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_project_risks_project_level_created 
ON project_risks (project_id, risk_level, created_at DESC) 
WHERE status = 'active';

-- 任务推荐查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_recommendations_user_score_created 
ON task_recommendations (user_id, confidence_score DESC, created_at DESC) 
WHERE status = 'pending';

-- ============================================================================
-- Git集成表索引优化
-- ============================================================================

-- Git仓库查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_git_repositories_project_provider 
ON git_repositories (project_id, provider, is_active) 
WHERE is_active = true;

-- Git提交查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_git_commits_repo_date 
ON git_commits (repository_id, commit_date DESC);

-- Git分支查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_git_branches_repo_active 
ON git_branches (repository_id, is_active) 
WHERE is_active = true;

-- ============================================================================
-- 复合查询索引优化
-- ============================================================================

-- 项目仪表板查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dashboard_projects_complex 
ON projects (owner_id, status, priority, updated_at DESC) 
WHERE is_archived = false;

-- 任务看板查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_kanban_tasks_complex 
ON tasks (project_id, status, assignee_id, priority, updated_at DESC) 
WHERE is_deleted = false;

-- 团队绩效查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_team_performance_complex 
ON tasks (assignee_id, status, completed_at) 
WHERE completed_at IS NOT NULL AND is_deleted = false;

-- ============================================================================
-- 分区表索引（针对大数据量表）
-- ============================================================================

-- 活动日志按月分区的索引
-- 注意：这需要先创建分区表结构
-- CREATE INDEX CONCURRENTLY idx_activity_logs_monthly_project_created 
-- ON activity_logs_y2025m08 (project_id, created_at DESC);

-- ============================================================================
-- 统计信息更新
-- ============================================================================

-- 更新表统计信息以优化查询计划
ANALYZE users;
ANALYZE projects;
ANALYZE project_members;
ANALYZE tasks;
ANALYZE activity_logs;
ANALYZE notifications;
ANALYZE comments;
ANALYZE attachments;
ANALYZE ai_models;
ANALYZE project_risks;
ANALYZE task_recommendations;
ANALYZE git_repositories;
ANALYZE git_commits;
ANALYZE git_branches;

-- ============================================================================
-- 索引使用情况监控查询
-- ============================================================================

-- 查看索引使用情况的监控查询（仅供参考，不执行）
/*
-- 查看未使用的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE idx_tup_read = 0 AND idx_tup_fetch = 0
ORDER BY schemaname, tablename, indexname;

-- 查看索引大小
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
ORDER BY pg_relation_size(indexrelid) DESC;

-- 查看表和索引的总大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(relid)) as total_size,
    pg_size_pretty(pg_relation_size(relid)) as table_size,
    pg_size_pretty(pg_total_relation_size(relid) - pg_relation_size(relid)) as index_size
FROM pg_stat_user_tables 
ORDER BY pg_total_relation_size(relid) DESC;
*/

-- ============================================================================
-- 性能优化建议
-- ============================================================================

-- 1. 定期执行 VACUUM ANALYZE 以维护表统计信息
-- 2. 监控慢查询日志，识别需要优化的查询
-- 3. 使用 EXPLAIN ANALYZE 分析查询执行计划
-- 4. 考虑对大表进行分区
-- 5. 定期检查索引使用情况，删除未使用的索引
-- 6. 根据实际查询模式调整索引策略

-- 执行完成提示
SELECT 'AI项目管理平台数据库索引优化完成！' as message;
