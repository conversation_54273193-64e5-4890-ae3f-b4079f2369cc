-- AI项目管理平台 - AI功能数据库架构
-- <AUTHOR>
-- @version 1.0.0
-- @since 2025-08-16

-- AI模型表
CREATE TABLE ai_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'risk_prediction', 'task_recommendation', 'performance_analysis'
    version VARCHAR(20) NOT NULL,
    description TEXT,
    model_path TEXT NOT NULL,
    config JSONB DEFAULT '{}',
    metrics JSONB DEFAULT '{}', -- 模型性能指标
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'training', 'deprecated')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 项目风险预测表
CREATE TABLE project_risk_predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ai_models(id),
    risk_level INTEGER NOT NULL CHECK (risk_level IN (0, 1, 2)), -- 0: 低风险, 1: 中风险, 2: 高风险
    risk_score DECIMAL(5,4) NOT NULL CHECK (risk_score >= 0 AND risk_score <= 1),
    confidence DECIMAL(5,4) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
    risk_factors JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '[]',
    input_features JSONB NOT NULL,
    prediction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 任务推荐表
CREATE TABLE task_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ai_models(id),
    score DECIMAL(5,4) NOT NULL CHECK (score >= 0 AND score <= 1),
    reason TEXT,
    context JSONB DEFAULT '{}',
    accepted BOOLEAN,
    feedback_score INTEGER CHECK (feedback_score >= 1 AND feedback_score <= 5),
    feedback_comment TEXT,
    recommended_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP WITH TIME ZONE
);

-- 团队绩效分析表
CREATE TABLE team_performance_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ai_models(id),
    analysis_period_start DATE NOT NULL,
    analysis_period_end DATE NOT NULL,
    overall_score DECIMAL(5,4) CHECK (overall_score >= 0 AND overall_score <= 1),
    metrics JSONB NOT NULL, -- 各项绩效指标
    strengths JSONB DEFAULT '[]',
    weaknesses JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '[]',
    trends JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- AI训练数据表
CREATE TABLE ai_training_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_type VARCHAR(50) NOT NULL,
    data_type VARCHAR(50) NOT NULL, -- 'feature', 'label', 'feedback'
    source_entity_type VARCHAR(50) NOT NULL, -- 'project', 'task', 'user'
    source_entity_id UUID NOT NULL,
    features JSONB NOT NULL,
    labels JSONB,
    quality_score DECIMAL(3,2) CHECK (quality_score >= 0 AND quality_score <= 1),
    is_validated BOOLEAN DEFAULT FALSE,
    validated_by UUID REFERENCES users(id),
    validated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 模型训练历史表
CREATE TABLE model_training_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_id UUID NOT NULL REFERENCES ai_models(id),
    training_data_count INTEGER NOT NULL,
    training_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    training_end_time TIMESTAMP WITH TIME ZONE,
    training_duration INTEGER, -- 秒数
    hyperparameters JSONB DEFAULT '{}',
    training_metrics JSONB DEFAULT '{}', -- 训练过程中的指标
    validation_metrics JSONB DEFAULT '{}', -- 验证集指标
    test_metrics JSONB DEFAULT '{}', -- 测试集指标
    status VARCHAR(20) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
    error_message TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- AI预测反馈表
CREATE TABLE ai_prediction_feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    prediction_type VARCHAR(50) NOT NULL, -- 'risk', 'recommendation', 'performance'
    prediction_id UUID NOT NULL, -- 对应具体预测表的ID
    user_id UUID NOT NULL REFERENCES users(id),
    feedback_type VARCHAR(20) NOT NULL CHECK (feedback_type IN ('accuracy', 'usefulness', 'relevance')),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 智能洞察表
CREATE TABLE ai_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(50) NOT NULL, -- 'project_health', 'team_productivity', 'risk_alert', 'optimization_suggestion'
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'critical')),
    confidence DECIMAL(5,4) CHECK (confidence >= 0 AND confidence <= 1),
    data JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    project_id UUID REFERENCES projects(id),
    user_id UUID REFERENCES users(id),
    model_id UUID REFERENCES ai_models(id),
    is_acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by UUID REFERENCES users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户行为分析表
CREATE TABLE user_behavior_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(100),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id UUID,
    context JSONB DEFAULT '{}',
    duration INTEGER, -- 操作持续时间（秒）
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 项目健康度评分表
CREATE TABLE project_health_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ai_models(id),
    overall_score DECIMAL(5,4) NOT NULL CHECK (overall_score >= 0 AND overall_score <= 1),
    dimension_scores JSONB NOT NULL, -- 各维度评分
    factors JSONB DEFAULT '{}', -- 影响因素
    trends JSONB DEFAULT '{}', -- 趋势分析
    alerts JSONB DEFAULT '[]', -- 预警信息
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 智能报告表
CREATE TABLE ai_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(50) NOT NULL, -- 'weekly_summary', 'monthly_analysis', 'project_review'
    title VARCHAR(255) NOT NULL,
    content JSONB NOT NULL,
    charts JSONB DEFAULT '[]', -- 图表配置
    recipients JSONB DEFAULT '[]', -- 接收人列表
    project_id UUID REFERENCES projects(id),
    generated_by UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_ai_models_type ON ai_models(type);
CREATE INDEX idx_ai_models_status ON ai_models(status);

CREATE INDEX idx_project_risk_predictions_project_id ON project_risk_predictions(project_id);
CREATE INDEX idx_project_risk_predictions_prediction_date ON project_risk_predictions(prediction_date);
CREATE INDEX idx_project_risk_predictions_risk_level ON project_risk_predictions(risk_level);

CREATE INDEX idx_task_recommendations_user_id ON task_recommendations(user_id);
CREATE INDEX idx_task_recommendations_task_id ON task_recommendations(task_id);
CREATE INDEX idx_task_recommendations_recommended_at ON task_recommendations(recommended_at);
CREATE INDEX idx_task_recommendations_score ON task_recommendations(score);

CREATE INDEX idx_team_performance_analysis_project_id ON team_performance_analysis(project_id);
CREATE INDEX idx_team_performance_analysis_user_id ON team_performance_analysis(user_id);
CREATE INDEX idx_team_performance_analysis_period ON team_performance_analysis(analysis_period_start, analysis_period_end);

CREATE INDEX idx_ai_training_data_model_type ON ai_training_data(model_type);
CREATE INDEX idx_ai_training_data_source ON ai_training_data(source_entity_type, source_entity_id);
CREATE INDEX idx_ai_training_data_created_at ON ai_training_data(created_at);

CREATE INDEX idx_model_training_history_model_id ON model_training_history(model_id);
CREATE INDEX idx_model_training_history_status ON model_training_history(status);
CREATE INDEX idx_model_training_history_created_at ON model_training_history(created_at);

CREATE INDEX idx_ai_prediction_feedback_prediction ON ai_prediction_feedback(prediction_type, prediction_id);
CREATE INDEX idx_ai_prediction_feedback_user_id ON ai_prediction_feedback(user_id);
CREATE INDEX idx_ai_prediction_feedback_created_at ON ai_prediction_feedback(created_at);

CREATE INDEX idx_ai_insights_type ON ai_insights(type);
CREATE INDEX idx_ai_insights_severity ON ai_insights(severity);
CREATE INDEX idx_ai_insights_project_id ON ai_insights(project_id);
CREATE INDEX idx_ai_insights_created_at ON ai_insights(created_at);
CREATE INDEX idx_ai_insights_expires_at ON ai_insights(expires_at);

CREATE INDEX idx_user_behavior_analytics_user_id ON user_behavior_analytics(user_id);
CREATE INDEX idx_user_behavior_analytics_action ON user_behavior_analytics(action);
CREATE INDEX idx_user_behavior_analytics_timestamp ON user_behavior_analytics(timestamp);

CREATE INDEX idx_project_health_scores_project_id ON project_health_scores(project_id);
CREATE INDEX idx_project_health_scores_calculated_at ON project_health_scores(calculated_at);

CREATE INDEX idx_ai_reports_type ON ai_reports(type);
CREATE INDEX idx_ai_reports_project_id ON ai_reports(project_id);
CREATE INDEX idx_ai_reports_status ON ai_reports(status);
CREATE INDEX idx_ai_reports_created_at ON ai_reports(created_at);

-- 添加更新时间触发器
CREATE TRIGGER update_ai_models_updated_at BEFORE UPDATE ON ai_models FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_reports_updated_at BEFORE UPDATE ON ai_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
