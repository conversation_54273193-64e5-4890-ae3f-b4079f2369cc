# 贡献指南

欢迎为AI项目管理平台贡献代码！本文档将指导您如何参与项目开发。

## 🚀 快速开始

### 开发环境准备

1. **克隆项目**
```bash
git clone https://github.com/your-org/ai-pm-platform.git
cd ai-pm-platform
```

2. **安装依赖**
```bash
# 复制环境变量配置文件
cp .env.example .env

# 启动开发环境
make dev-start
```

3. **验证环境**
```bash
# 运行测试
make test

# 检查代码质量
make lint
```

## 📝 开发规范

### 代码规范

#### Java代码规范
- 使用Google Java Style Guide
- 类名使用PascalCase，方法名使用camelCase
- 所有公共方法必须有完整的JavaDoc注释
- 使用中文注释说明业务逻辑

```java
/**
 * 项目管理服务
 * 
 * 提供项目的创建、更新、删除和查询功能
 * 支持项目生命周期管理和状态跟踪
 */
@Service
public class ProjectService {
    
    /**
     * 创建新项目
     * 
     * @param request 项目创建请求，包含项目基本信息
     * @return 创建成功的项目信息
     * @throws ProjectValidationException 当项目数据验证失败时抛出
     */
    public ProjectDto createProject(CreateProjectRequest request) {
        // 验证项目数据的完整性和有效性
        validateProjectData(request);
        
        // 创建项目实体并保存到数据库
        Project project = buildProjectEntity(request);
        Project savedProject = projectRepository.save(project);
        
        // 发布项目创建事件，通知其他服务
        eventPublisher.publishProjectCreated(savedProject);
        
        return ProjectDto.fromEntity(savedProject);
    }
}
```

#### Python代码规范
- 遵循PEP 8规范
- 使用类型提示（Type Hints）
- 函数和类必须有详细的docstring
- 使用中文注释说明复杂逻辑

```python
"""
AI分析服务模块

提供项目进度预测、风险评估、质量分析等AI功能
"""

from typing import Dict, List, Optional
from datetime import datetime

class ProjectAnalyzer:
    """项目分析器
    
    使用机器学习算法分析项目数据，提供智能化的分析结果
    """
    
    def __init__(self, model_manager: ModelManager):
        """初始化项目分析器
        
        Args:
            model_manager: 模型管理器，用于加载和管理ML模型
        """
        self.model_manager = model_manager
        self.logger = logging.getLogger(__name__)
    
    async def analyze_project_progress(
        self, 
        project_id: str, 
        analysis_config: Optional[Dict] = None
    ) -> AnalysisResult:
        """分析项目进度
        
        基于项目历史数据和当前状态，预测项目完成时间和风险
        
        Args:
            project_id: 项目唯一标识符
            analysis_config: 分析配置参数，可选
            
        Returns:
            包含进度预测和风险评估的分析结果
            
        Raises:
            ProjectNotFoundException: 项目不存在时抛出
            AnalysisException: 分析过程中发生错误时抛出
        """
        try:
            # 获取项目数据并进行预处理
            project_data = await self._fetch_project_data(project_id)
            processed_data = self._preprocess_data(project_data)
            
            # 使用机器学习模型进行分析
            model = self.model_manager.get_progress_model()
            prediction = await model.predict(processed_data)
            
            # 生成分析报告
            result = self._generate_analysis_result(prediction, project_data)
            
            self.logger.info(f"项目 {project_id} 分析完成，预测完成时间: {result.predicted_completion}")
            return result
            
        except Exception as e:
            self.logger.error(f"项目分析失败: {str(e)}")
            raise AnalysisException(f"分析项目 {project_id} 时发生错误") from e
```

#### TypeScript/React代码规范
- 使用严格的TypeScript配置
- 组件使用函数式组件和Hooks
- 所有组件必须有PropTypes或TypeScript接口定义
- 使用中文注释说明组件功能

```typescript
/**
 * 项目仪表板组件
 * 
 * 显示项目的关键指标、进度图表和AI分析结果
 * 支持实时数据更新和交互式操作
 */
interface ProjectDashboardProps {
  /** 项目ID */
  projectId: string;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 数据刷新间隔（毫秒） */
  refreshInterval?: number;
}

const ProjectDashboard: React.FC<ProjectDashboardProps> = ({
  projectId,
  showDetails = true,
  refreshInterval = 30000
}) => {
  // 使用Redux管理项目数据状态
  const dispatch = useAppDispatch();
  const project = useAppSelector(state => selectProjectById(state, projectId));
  const loading = useAppSelector(selectProjectLoading);
  
  // 定期刷新项目数据
  useEffect(() => {
    const fetchData = () => {
      dispatch(fetchProjectData(projectId));
    };
    
    // 立即获取数据
    fetchData();
    
    // 设置定时刷新
    const interval = setInterval(fetchData, refreshInterval);
    
    return () => clearInterval(interval);
  }, [dispatch, projectId, refreshInterval]);
  
  /**
   * 处理项目状态更新
   * 当用户点击状态按钮时触发
   */
  const handleStatusUpdate = useCallback(async (newStatus: ProjectStatus) => {
    try {
      await dispatch(updateProjectStatus({ projectId, status: newStatus }));
      message.success('项目状态更新成功');
    } catch (error) {
      message.error('更新项目状态失败，请重试');
    }
  }, [dispatch, projectId]);
  
  if (loading) {
    return <Spin size="large" tip="加载项目数据中..." />;
  }
  
  return (
    <div className="project-dashboard">
      {/* 项目基本信息卡片 */}
      <ProjectInfoCard project={project} onStatusUpdate={handleStatusUpdate} />
      
      {/* 进度图表 */}
      <ProgressChart projectId={projectId} />
      
      {/* AI分析结果面板 */}
      {showDetails && <AIInsightsPanel projectId={projectId} />}
    </div>
  );
};

export default ProjectDashboard;
```

### Git提交规范

使用约定式提交（Conventional Commits）格式：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

#### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```bash
feat(user-service): 添加用户注册功能

实现了用户注册的完整流程，包括：
- 用户信息验证
- 密码加密存储
- 邮箱验证
- 注册成功通知

Closes #123
```

### 分支管理

- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成新功能
- `feature/*`: 功能分支，用于开发新功能
- `hotfix/*`: 热修复分支，用于紧急修复
- `release/*`: 发布分支，用于版本发布

### 代码审查

所有代码必须通过Pull Request进行审查：

1. 创建功能分支
2. 完成开发并编写测试
3. 提交Pull Request
4. 代码审查和讨论
5. 修复审查意见
6. 合并到目标分支

## 🧪 测试规范

### 测试覆盖率要求
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖核心业务流程
- E2E测试覆盖主要用户场景

### 测试命名规范
```java
// Java测试方法命名：should_ExpectedBehavior_When_StateUnderTest
@Test
public void should_ReturnProject_When_ValidProjectIdProvided() {
    // 测试实现
}
```

```python
# Python测试方法命名：test_expected_behavior_when_state_under_test
def test_should_return_analysis_result_when_valid_project_data_provided():
    # 测试实现
    pass
```

## 📚 文档规范

### API文档
- 使用OpenAPI 3.0规范
- 所有API必须有完整的文档
- 包含请求/响应示例
- 错误码说明

### 代码文档
- 所有公共接口必须有文档注释
- 复杂算法需要详细说明
- 使用中文编写注释和文档

## 🐛 问题报告

提交Issue时请包含：

1. **问题描述**: 清晰描述遇到的问题
2. **复现步骤**: 详细的复现步骤
3. **期望行为**: 期望的正确行为
4. **环境信息**: 操作系统、浏览器、版本等
5. **错误日志**: 相关的错误信息和日志

## 📞 联系方式

如有任何问题，请通过以下方式联系：

- 提交Issue: [GitHub Issues](https://github.com/your-org/ai-pm-platform/issues)
- 邮件: <EMAIL>
- 微信群: 扫描二维码加入开发者群

感谢您的贡献！🎉
