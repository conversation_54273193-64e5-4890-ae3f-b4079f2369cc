# AI项目管理平台 - 开发环境 Docker Compose 配置
# 用于本地开发环境的容器编排配置

version: '3.8'

services:
  # ============================================================================
  # 数据库服务
  # ============================================================================
  
  # PostgreSQL 主数据库
  postgres-dev:
    image: postgres:15-alpine
    container_name: aipm-postgres-dev
    environment:
      POSTGRES_DB: aipm_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password_123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev_user -d aipm_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c log_statement=all
      -c log_destination=stderr
      -c log_min_duration_statement=0
      -c shared_preload_libraries=pg_stat_statements

  # MongoDB 文档数据库
  mongodb-dev:
    image: mongo:6.0
    container_name: aipm-mongodb-dev
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: mongo_password_123
      MONGO_INITDB_DATABASE: aipm_dev
    volumes:
      - mongodb_dev_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    ports:
      - "27017:27017"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存服务
  redis-dev:
    image: redis:7-alpine
    container_name: aipm-redis-dev
    command: redis-server --requirepass redis_password_123 --appendonly yes
    volumes:
      - redis_dev_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Elasticsearch 搜索引擎
  elasticsearch-dev:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: aipm-elasticsearch-dev
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - cluster.name=aipm-dev-cluster
      - node.name=aipm-dev-node
    volumes:
      - elasticsearch_dev_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # 消息队列服务
  # ============================================================================
  
  # Zookeeper (Kafka依赖)
  zookeeper-dev:
    image: confluentinc/cp-zookeeper:latest
    container_name: aipm-zookeeper-dev
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    ports:
      - "2181:2181"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Apache Kafka
  kafka-dev:
    image: confluentinc/cp-kafka:latest
    container_name: aipm-kafka-dev
    depends_on:
      zookeeper-dev:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-dev:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
    volumes:
      - kafka_dev_data:/var/lib/kafka/data
    ports:
      - "9092:9092"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # 开发工具服务
  # ============================================================================
  
  # Adminer 数据库管理工具
  adminer-dev:
    image: adminer:latest
    container_name: aipm-adminer-dev
    depends_on:
      - postgres-dev
      - mongodb-dev
    ports:
      - "8080:8080"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    environment:
      ADMINER_DEFAULT_SERVER: postgres-dev

  # Redis Commander Redis管理工具
  redis-commander-dev:
    image: rediscommander/redis-commander:latest
    container_name: aipm-redis-commander-dev
    depends_on:
      - redis-dev
    environment:
      REDIS_HOSTS: local:redis-dev:6379:0:redis_password_123
    ports:
      - "8081:8081"
    networks:
      - aipm-dev-network
    restart: unless-stopped

  # Mongo Express MongoDB管理工具
  mongo-express-dev:
    image: mongo-express:latest
    container_name: aipm-mongo-express-dev
    depends_on:
      - mongodb-dev
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: mongo_password_123
      ME_CONFIG_MONGODB_URL: ****************************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    ports:
      - "8082:8081"
    networks:
      - aipm-dev-network
    restart: unless-stopped

  # Kibana Elasticsearch可视化工具
  kibana-dev:
    image: docker.elastic.co/kibana/kibana:8.9.0
    container_name: aipm-kibana-dev
    depends_on:
      elasticsearch-dev:
        condition: service_healthy
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch-dev:9200
      XPACK_SECURITY_ENABLED: false
      XPACK_ENCRYPTEDSAVEDOBJECTS_ENCRYPTIONKEY: "a7a6311933d3503b89bc2dbc36572c33a6c10925682e591bffcab6911c06786d"
    ports:
      - "5601:5601"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka UI Kafka管理界面
  kafka-ui-dev:
    image: provectuslabs/kafka-ui:latest
    container_name: aipm-kafka-ui-dev
    depends_on:
      kafka-dev:
        condition: service_healthy
    environment:
      KAFKA_CLUSTERS_0_NAME: aipm-dev-cluster
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka-dev:9092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper-dev:2181
    ports:
      - "8083:8080"
    networks:
      - aipm-dev-network
    restart: unless-stopped

  # ============================================================================
  # 监控服务 (开发环境可选)
  # ============================================================================
  
  # Prometheus 监控系统
  prometheus-dev:
    image: prom/prometheus:latest
    container_name: aipm-prometheus-dev
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_dev_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana 可视化面板
  grafana-dev:
    image: grafana/grafana:latest
    container_name: aipm-grafana-dev
    depends_on:
      - prometheus-dev
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin_password_123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_dev_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    networks:
      - aipm-dev-network
    restart: unless-stopped
    profiles:
      - monitoring

# ============================================================================
# 网络配置
# ============================================================================
networks:
  aipm-dev-network:
    driver: bridge
    name: aipm-dev-network
    ipam:
      config:
        - subnet: **********/16

# ============================================================================
# 数据卷配置
# ============================================================================
volumes:
  postgres_dev_data:
    name: aipm-postgres-dev-data
  mongodb_dev_data:
    name: aipm-mongodb-dev-data
  redis_dev_data:
    name: aipm-redis-dev-data
  elasticsearch_dev_data:
    name: aipm-elasticsearch-dev-data
  kafka_dev_data:
    name: aipm-kafka-dev-data
  prometheus_dev_data:
    name: aipm-prometheus-dev-data
  grafana_dev_data:
    name: aipm-grafana-dev-data
