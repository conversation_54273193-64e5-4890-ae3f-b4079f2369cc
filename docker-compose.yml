# AI项目管理平台 - 生产环境 Docker Compose 配置
# 用于生产环境的容器编排配置

version: '3.8'

services:
  # ============================================================================
  # 数据库服务
  # ============================================================================
  
  # PostgreSQL 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: aipm-postgres
    environment:
      POSTGRES_DB: aipm_prod
      POSTGRES_USER: aipm_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U aipm_user -d aipm_prod"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB 文档数据库
  mongodb:
    image: mongo:6.0
    container_name: aipm-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: aipm_prod
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: aipm-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: aipm-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # 消息队列服务
  # ============================================================================
  
  # Apache Kafka
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: aipm-kafka
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"
    networks:
      - aipm-network
    restart: unless-stopped

  # Zookeeper (Kafka依赖)
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: aipm-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - aipm-network
    restart: unless-stopped

  # ============================================================================
  # 应用服务 (生产环境使用预构建镜像)
  # ============================================================================
  
  # 用户管理服务
  user-management:
    image: ${DOCKER_REGISTRY}/user-management:${VERSION}
    container_name: aipm-user-management
    depends_on:
      - postgres
      - redis
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DATABASE_URL: *****************************************
      DATABASE_USERNAME: aipm_user
      DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - "8080:8080"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 项目管理服务
  project-management:
    image: ${DOCKER_REGISTRY}/project-management:${VERSION}
    container_name: aipm-project-management
    depends_on:
      - postgres
      - redis
      - kafka
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DATABASE_URL: *****************************************
      DATABASE_USERNAME: aipm_user
      DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
    ports:
      - "8081:8080"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI分析服务
  ai-analysis:
    image: ${DOCKER_REGISTRY}/ai-analysis:${VERSION}
    container_name: aipm-ai-analysis
    depends_on:
      - mongodb
      - redis
      - kafka
    environment:
      ENVIRONMENT: production
      MONGODB_URL: mongodb://admin:${MONGO_PASSWORD}@mongodb:27017/aipm_prod?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
    ports:
      - "8082:8000"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 集成服务
  integration:
    image: ${DOCKER_REGISTRY}/integration:${VERSION}
    container_name: aipm-integration
    depends_on:
      - mongodb
      - redis
      - kafka
    environment:
      NODE_ENV: production
      MONGODB_URL: mongodb://admin:${MONGO_PASSWORD}@mongodb:27017/aipm_prod?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
    ports:
      - "8083:3000"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 通知服务
  notification:
    image: ${DOCKER_REGISTRY}/notification:${VERSION}
    container_name: aipm-notification
    depends_on:
      - redis
      - kafka
    environment:
      GIN_MODE: release
      REDIS_ADDR: redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      KAFKA_BROKERS: kafka:9092
    ports:
      - "8084:8080"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web前端
  frontend:
    image: ${DOCKER_REGISTRY}/frontend:${VERSION}
    container_name: aipm-frontend
    ports:
      - "80:80"
      - "443:443"
    networks:
      - aipm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

# ============================================================================
# 网络配置
# ============================================================================
networks:
  aipm-network:
    driver: bridge
    name: aipm-network

# ============================================================================
# 数据卷配置
# ============================================================================
volumes:
  postgres_data:
    name: aipm-postgres-data
  mongodb_data:
    name: aipm-mongodb-data
  redis_data:
    name: aipm-redis-data
  elasticsearch_data:
    name: aipm-elasticsearch-data
