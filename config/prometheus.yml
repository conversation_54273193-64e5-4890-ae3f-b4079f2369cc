# AI项目管理平台 Prometheus 配置文件
# 开发环境监控配置

global:
  # 数据采集间隔
  scrape_interval: 15s
  # 规则评估间隔
  evaluation_interval: 15s
  # 外部标签
  external_labels:
    monitor: 'aipm-dev-monitor'
    environment: 'development'

# 规则文件配置
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# 数据采集配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # 用户管理服务监控
  - job_name: 'user-management-service'
    static_configs:
      - targets: ['user-management:8080']
    metrics_path: /actuator/prometheus
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # 项目管理服务监控
  - job_name: 'project-management-service'
    static_configs:
      - targets: ['project-management:8080']
    metrics_path: /actuator/prometheus
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true

  # AI分析服务监控
  - job_name: 'ai-analysis-service'
    static_configs:
      - targets: ['ai-analysis:8000']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true

  # 集成服务监控
  - job_name: 'integration-service'
    static_configs:
      - targets: ['integration:3000']
    metrics_path: /metrics
    scrape_interval: 10s
    scrape_timeout: 5s

  # 通知服务监控
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification:8080']
    metrics_path: /metrics
    scrape_interval: 10s
    scrape_timeout: 5s

  # PostgreSQL数据库监控
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    scrape_timeout: 10s

  # MongoDB监控
  - job_name: 'mongodb-exporter'
    static_configs:
      - targets: ['mongodb-exporter:9216']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Elasticsearch监控
  - job_name: 'elasticsearch-exporter'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Kafka监控
  - job_name: 'kafka-exporter'
    static_configs:
      - targets: ['kafka-exporter:9308']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter (系统监控)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # cAdvisor (容器监控)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Docker监控
  - job_name: 'docker'
    static_configs:
      - targets: ['docker-exporter:9323']
    scrape_interval: 30s
    scrape_timeout: 10s

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
