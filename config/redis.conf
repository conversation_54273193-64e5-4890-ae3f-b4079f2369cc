# AI项目管理平台 Redis 配置文件
# 开发环境配置

# ============================================================================
# 网络配置
# ============================================================================

# 绑定地址
bind 0.0.0.0

# 端口
port 6379

# TCP监听队列长度
tcp-backlog 511

# 客户端超时时间（秒）
timeout 0

# TCP keepalive
tcp-keepalive 300

# ============================================================================
# 通用配置
# ============================================================================

# 守护进程模式
daemonize no

# 进程文件
pidfile /var/run/redis_6379.pid

# 日志级别 (debug, verbose, notice, warning)
loglevel notice

# 日志文件
logfile ""

# 数据库数量
databases 16

# ============================================================================
# 持久化配置
# ============================================================================

# RDB持久化配置
# 900秒内至少1个key发生变化时保存
save 900 1
# 300秒内至少10个key发生变化时保存
save 300 10
# 60秒内至少10000个key发生变化时保存
save 60 10000

# RDB文件压缩
rdbcompression yes

# RDB文件校验
rdbchecksum yes

# RDB文件名
dbfilename dump.rdb

# 工作目录
dir /data

# AOF持久化配置
appendonly yes
appendfilename "appendonly.aof"

# AOF同步策略
# always: 每次写操作都同步
# everysec: 每秒同步一次
# no: 由操作系统决定何时同步
appendfsync everysec

# 重写时是否同步
no-appendfsync-on-rewrite no

# AOF重写触发条件
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# AOF加载时是否忽略错误
aof-load-truncated yes

# ============================================================================
# 内存管理
# ============================================================================

# 最大内存限制（开发环境设置为512MB）
maxmemory 512mb

# 内存淘汰策略
# noeviction: 不淘汰，内存满时返回错误
# allkeys-lru: 在所有key中使用LRU算法淘汰
# volatile-lru: 在设置了过期时间的key中使用LRU算法淘汰
# allkeys-random: 在所有key中随机淘汰
# volatile-random: 在设置了过期时间的key中随机淘汰
# volatile-ttl: 淘汰即将过期的key
maxmemory-policy allkeys-lru

# 内存采样数量
maxmemory-samples 5

# ============================================================================
# 安全配置
# ============================================================================

# 密码认证
requirepass redis_password_123

# 重命名危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_a1b2c3d4e5f6"

# ============================================================================
# 客户端配置
# ============================================================================

# 最大客户端连接数
maxclients 10000

# ============================================================================
# 慢查询日志
# ============================================================================

# 慢查询阈值（微秒）
slowlog-log-slower-than 10000

# 慢查询日志最大长度
slowlog-max-len 128

# ============================================================================
# 延迟监控
# ============================================================================

# 延迟监控阈值（毫秒）
latency-monitor-threshold 100

# ============================================================================
# 事件通知
# ============================================================================

# 键空间通知配置
# K: 键空间通知，所有通知以__keyspace@<db>__为前缀
# E: 键事件通知，所有通知以__keyevent@<db>__为前缀
# g: DEL、EXPIRE、RENAME等类型无关的通用命令的通知
# $: 字符串命令的通知
# l: 列表命令的通知
# s: 集合命令的通知
# h: 哈希命令的通知
# z: 有序集合命令的通知
# x: 过期事件：每当有过期键被删除时发送
# e: 驱逐(evict)事件：每当有键因为maxmemory政策而被删除时发送
# A: 参数g$lshzxe的别名
notify-keyspace-events "Ex"

# ============================================================================
# 高级配置
# ============================================================================

# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# 流配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# 频率限制
hz 10

# 动态频率调整
dynamic-hz yes

# AOF重写增量同步
aof-rewrite-incremental-fsync yes

# RDB保存增量同步
rdb-save-incremental-fsync yes

# ============================================================================
# 模块配置
# ============================================================================

# 加载模块（如果需要）
# loadmodule /path/to/module.so

# ============================================================================
# 集群配置（开发环境暂不启用）
# ============================================================================

# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-replica-validity-factor 10
# cluster-migration-barrier 1
# cluster-require-full-coverage yes
