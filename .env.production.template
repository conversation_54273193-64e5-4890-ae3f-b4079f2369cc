# 生产环境配置模板
# 复制此文件为 .env.production 并填入实际的生产环境配置

# ============================================================================
# 基础应用配置
# ============================================================================

# 应用基础URL（必须是HTTPS）
APP_BASE_URL=https://api.your-domain.com

# 前端应用URL
FRONTEND_URL=https://your-domain.com

# 服务器端口
SERVER_PORT=8080

# 应用上下文路径（可选）
CONTEXT_PATH=

# ============================================================================
# 数据库配置
# ============================================================================

# PostgreSQL数据库连接
DATABASE_URL=*********************************************
DATABASE_USERNAME=aipm_user
DATABASE_PASSWORD=your-secure-database-password

# ============================================================================
# Redis配置
# ============================================================================

# Redis连接配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-redis-password

# ============================================================================
# JWT配置
# ============================================================================

# JWT密钥（必须是强密钥，至少32字符）
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters-long

# JWT令牌过期时间（毫秒）
JWT_ACCESS_TOKEN_EXPIRATION=3600000    # 1小时
JWT_REFRESH_TOKEN_EXPIRATION=604800000 # 7天

# ============================================================================
# Google OAuth2配置
# ============================================================================

# Google OAuth2客户端配置
# 在 https://console.developers.google.com/ 创建OAuth2客户端
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Google OAuth2重定向URI配置（在Google控制台中配置）:
# - https://api.your-domain.com/oauth2/callback/google
# - https://your-domain.com/oauth2/redirect

# ============================================================================
# GitHub OAuth2配置
# ============================================================================

# GitHub OAuth2应用配置
# 在 https://github.com/settings/applications/new 创建OAuth应用
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# GitHub OAuth2回调URL配置（在GitHub应用设置中配置）:
# - https://api.your-domain.com/oauth2/callback/github

# ============================================================================
# 自定义OAuth2提供商配置
# ============================================================================

# 自定义OAuth2提供商基本信息
CUSTOM_CLIENT_ID=your-custom-oauth2-client-id
CUSTOM_CLIENT_SECRET=your-custom-oauth2-client-secret
CUSTOM_PROVIDER_NAME=Your Custom ID Provider

# 自定义OAuth2提供商端点
CUSTOM_AUTHORIZATION_URI=https://your-oauth2-provider.com/oauth2/authorize
CUSTOM_TOKEN_URI=https://your-oauth2-provider.com/oauth2/token
CUSTOM_USER_INFO_URI=https://your-oauth2-provider.com/oauth2/userinfo
CUSTOM_JWK_SET_URI=https://your-oauth2-provider.com/.well-known/jwks.json

# 自定义OAuth2用户信息字段映射
CUSTOM_USER_NAME_ATTRIBUTE=sub
CUSTOM_ID_ATTRIBUTE=id
CUSTOM_NAME_ATTRIBUTE=name
CUSTOM_EMAIL_ATTRIBUTE=email
CUSTOM_PICTURE_ATTRIBUTE=avatar

# ============================================================================
# SSL/TLS配置（如果使用应用层SSL）
# ============================================================================

# SSL配置（通常由反向代理处理，如Nginx）
SSL_ENABLED=false
SSL_KEY_STORE=
SSL_KEY_STORE_PASSWORD=
SSL_KEY_STORE_TYPE=PKCS12
SSL_KEY_ALIAS=

# ============================================================================
# 日志配置
# ============================================================================

# 日志文件路径
LOG_FILE_PATH=/var/log/aipm/user-management.log

# ============================================================================
# CORS配置
# ============================================================================

# 允许的跨域源（多个用逗号分隔）
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://admin.your-domain.com

# ============================================================================
# 监控和健康检查配置
# ============================================================================

# 是否启用Prometheus指标
PROMETHEUS_ENABLED=true

# 健康检查端点访问控制
HEALTH_SHOW_DETAILS=when-authorized

# ============================================================================
# 部署相关配置
# ============================================================================

# Docker镜像标签
DOCKER_IMAGE_TAG=latest

# Kubernetes命名空间
K8S_NAMESPACE=aipm-prod

# 副本数量
REPLICA_COUNT=3

# 资源限制
MEMORY_LIMIT=1Gi
CPU_LIMIT=1000m
MEMORY_REQUEST=512Mi
CPU_REQUEST=500m

# ============================================================================
# 备份和恢复配置
# ============================================================================

# 数据库备份配置
DB_BACKUP_ENABLED=true
DB_BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
DB_BACKUP_RETENTION_DAYS=30

# Redis备份配置
REDIS_BACKUP_ENABLED=true
REDIS_BACKUP_SCHEDULE=0 3 * * *  # 每天凌晨3点

# ============================================================================
# 安全配置
# ============================================================================

# 安全头配置
SECURITY_HEADERS_ENABLED=true

# 速率限制配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# IP白名单（管理接口访问控制）
ADMIN_IP_WHITELIST=10.0.0.0/8,**********/12,***********/16

# ============================================================================
# 第三方服务配置
# ============================================================================

# 邮件服务配置（用于通知）
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_TLS_ENABLED=true

# 短信服务配置（可选）
SMS_PROVIDER=
SMS_API_KEY=
SMS_API_SECRET=

# 对象存储配置（用于头像等文件）
S3_BUCKET_NAME=aipm-prod-assets
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key
S3_REGION=us-west-2
S3_ENDPOINT=https://s3.us-west-2.amazonaws.com
