# AI项目管理平台后端服务 Kubernetes 部署配置
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-16

apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-pm-backend
  namespace: ai-pm
  labels:
    app: ai-pm-backend
    version: v1.0.0
    component: backend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: ai-pm-backend
  template:
    metadata:
      labels:
        app: ai-pm-backend
        version: v1.0.0
        component: backend
    spec:
      containers:
      - name: backend
        image: ai-pm/backend:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-pm-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: ai-pm-secrets
              key: redis-url
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: ai-pm-secrets
              key: jwt-secret
        - name: AI_SERVICE_URL
          value: "http://ai-pm-ai-service:8001"
        - name: NOTIFICATION_SERVICE_URL
          value: "http://ai-pm-notification:8002"
        - name: INTEGRATION_SERVICE_URL
          value: "http://ai-pm-integration:8003"
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: config-volume
        configMap:
          name: ai-pm-config
      - name: logs-volume
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30

---
apiVersion: v1
kind: Service
metadata:
  name: ai-pm-backend
  namespace: ai-pm
  labels:
    app: ai-pm-backend
    component: backend
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: ai-pm-backend

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-pm-backend-hpa
  namespace: ai-pm
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-pm-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
