# Kubernetes生产环境OAuth2部署配置

apiVersion: v1
kind: Namespace
metadata:
  name: aipm-prod
  labels:
    name: aipm-prod
    environment: production

---
# ConfigMap for OAuth2 configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: oauth2-config
  namespace: aipm-prod
data:
  application-prod.yml: |
    spring:
      profiles:
        active: prod
      security:
        oauth2:
          client:
            registration:
              google:
                scope: openid,profile,email
                redirect-uri: "${APP_BASE_URL}/oauth2/callback/{registrationId}"
                client-name: Google
              github:
                scope: user:email,read:user
                redirect-uri: "${APP_BASE_URL}/oauth2/callback/{registrationId}"
                client-name: GitHub
              custom:
                scope: openid,profile,email
                redirect-uri: "${APP_BASE_URL}/oauth2/callback/{registrationId}"
                authorization-grant-type: authorization_code
                client-authentication-method: client_secret_basic
    app:
      oauth2:
        authorized-redirect-uris:
          - "${FRONTEND_URL}/oauth2/redirect"
          - "${FRONTEND_URL}/login/oauth2/callback"
        user-info-mapping:
          google:
            id-attribute: sub
            name-attribute: name
            email-attribute: email
            picture-attribute: picture
          github:
            id-attribute: id
            name-attribute: name
            email-attribute: email
            picture-attribute: avatar_url
          custom:
            id-attribute: id
            name-attribute: name
            email-attribute: email
            picture-attribute: avatar

---
# Secret for OAuth2 credentials
apiVersion: v1
kind: Secret
metadata:
  name: oauth2-secrets
  namespace: aipm-prod
type: Opaque
stringData:
  google-client-id: ""  # 从环境变量或外部密钥管理系统获取
  google-client-secret: ""
  github-client-id: ""
  github-client-secret: ""
  custom-client-id: ""
  custom-client-secret: ""
  jwt-secret: ""
  database-password: ""
  redis-password: ""

---
# Deployment for User Management Service with OAuth2
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-management-oauth2
  namespace: aipm-prod
  labels:
    app: user-management
    component: oauth2
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: user-management
      component: oauth2
  template:
    metadata:
      labels:
        app: user-management
        component: oauth2
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      containers:
      - name: user-management
        image: aipm/user-management:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: APP_BASE_URL
          value: "https://api.your-domain.com"
        - name: FRONTEND_URL
          value: "https://your-domain.com"
        - name: DATABASE_URL
          value: "*************************************************"
        - name: DATABASE_USERNAME
          value: "aipm_user"
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: database-password
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: redis-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: jwt-secret
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: google-client-id
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: google-client-secret
        - name: GITHUB_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: github-client-id
        - name: GITHUB_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: github-client-secret
        - name: CUSTOM_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: custom-client-id
        - name: CUSTOM_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: oauth2-secrets
              key: custom-client-secret
        - name: CUSTOM_AUTHORIZATION_URI
          value: "https://your-oauth2-provider.com/oauth2/authorize"
        - name: CUSTOM_TOKEN_URI
          value: "https://your-oauth2-provider.com/oauth2/token"
        - name: CUSTOM_USER_INFO_URI
          value: "https://your-oauth2-provider.com/oauth2/userinfo"
        - name: CUSTOM_JWK_SET_URI
          value: "https://your-oauth2-provider.com/.well-known/jwks.json"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: logs-volume
          mountPath: /app/logs
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: config-volume
        configMap:
          name: oauth2-config
      - name: logs-volume
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30

---
# Service for User Management
apiVersion: v1
kind: Service
metadata:
  name: user-management-service
  namespace: aipm-prod
  labels:
    app: user-management
    component: oauth2
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: user-management
    component: oauth2

---
# Ingress for OAuth2 endpoints
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: oauth2-ingress
  namespace: aipm-prod
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.your-domain.com
    secretName: api-tls-secret
  rules:
  - host: api.your-domain.com
    http:
      paths:
      - path: /oauth2
        pathType: Prefix
        backend:
          service:
            name: user-management-service
            port:
              number: 8080
      - path: /api/v1/oauth2
        pathType: Prefix
        backend:
          service:
            name: user-management-service
            port:
              number: 8080
      - path: /api/v1/auth
        pathType: Prefix
        backend:
          service:
            name: user-management-service
            port:
              number: 8080

---
# HorizontalPodAutoscaler for OAuth2 service
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-management-hpa
  namespace: aipm-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-management-oauth2
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
