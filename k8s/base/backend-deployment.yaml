# AI项目管理平台后端部署配置
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-16

apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: ai-pm
  labels:
    app: backend
    component: api
    tier: backend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
        component: api
        tier: backend
    spec:
      containers:
      - name: backend
        image: ai-pm/backend:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secret
              key: secret-key
        - name: DEBUG
          value: "false"
        - name: CORS_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: cors-origins
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: backend-logs-pvc
      - name: uploads
        persistentVolumeClaim:
          claimName: backend-uploads-pvc
      restartPolicy: Always
      imagePullPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: ai-pm
  labels:
    app: backend
    component: api
    tier: backend
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: backend

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: ai-pm
data:
  cors-origins: "http://localhost:3000,https://ai-pm.example.com"
  log-level: "INFO"
  max-upload-size: "10MB"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-logs-pvc
  namespace: ai-pm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-uploads-pvc
  namespace: ai-pm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: standard
