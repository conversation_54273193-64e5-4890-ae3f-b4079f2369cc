# AI项目管理平台 Ingress 配置
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-16

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-pm-ingress
  namespace: ai-pm
  labels:
    app: ai-pm
    component: ingress
  annotations:
    # Nginx Ingress Controller 配置
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    
    # CORS 配置
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://ai-pm.example.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    
    # 速率限制
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # 缓存配置
    nginx.ingress.kubernetes.io/configuration-snippet: |
      location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
      }
    
    # WebSocket 支持
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/websocket-services: "notification-service"
    
    # 证书管理器
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - ai-pm.example.com
    - api.ai-pm.example.com
    secretName: ai-pm-tls
  rules:
  # 主域名 - 前端应用
  - host: ai-pm.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
      # WebSocket 路径
      - path: /ws
        pathType: Prefix
        backend:
          service:
            name: notification-service
            port:
              number: 8084
  
  # API 子域名 - 后端API
  - host: api.ai-pm.example.com
    http:
      paths:
      # 主API
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8000
      # AI服务API
      - path: /ai
        pathType: Prefix
        backend:
          service:
            name: ai-service
            port:
              number: 8001
      # 通知服务API
      - path: /notifications
        pathType: Prefix
        backend:
          service:
            name: notification-service
            port:
              number: 8084
      # 集成服务API
      - path: /integrations
        pathType: Prefix
        backend:
          service:
            name: integration-service
            port:
              number: 8085

---
# 开发环境 Ingress（可选）
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-pm-dev-ingress
  namespace: ai-pm
  labels:
    app: ai-pm
    component: ingress
    environment: development
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  ingressClassName: nginx
  rules:
  - host: ai-pm.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
  - host: api.ai-pm.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8000

---
# 证书颁发器配置
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx

---
# 开发环境证书颁发器
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
spec:
  acme:
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-staging
    solvers:
    - http01:
        ingress:
          class: nginx
