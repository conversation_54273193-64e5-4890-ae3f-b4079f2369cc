# AI项目管理平台密钥配置
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-16
# 注意：这些是示例密钥，生产环境中应使用实际的安全密钥

apiVersion: v1
kind: Secret
metadata:
  name: database-secret
  namespace: ai-pm
  labels:
    app: ai-pm
    component: database
type: Opaque
data:
  # 数据库连接信息（Base64编码）
  username: YWlfcG1fdXNlcg==  # ai_pm_user
  password: YWlfcG1fcGFzc3dvcmQ=  # ai_pm_password
  database: YWlfcG1fZGI=  # ai_pm_db
  url: ********************************************************************************************  # ************************************************************/ai_pm_db

---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: ai-pm
  labels:
    app: ai-pm
    component: cache
type: Opaque
data:
  # Redis连接信息（Base64编码）
  password: YWlfcG1fcmVkaXNfcGFzc3dvcmQ=  # ai_pm_redis_password
  url: cmVkaXM6Ly86YWlfcG1fcmVkaXNfcGFzc3dvcmRAcmVkaXMtc2VydmljZTo2Mzc5LzA=  # redis://:ai_pm_redis_password@redis-service:6379/0

---
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
  namespace: ai-pm
  labels:
    app: ai-pm
    component: application
type: Opaque
data:
  # 应用密钥（Base64编码）
  secret-key: eW91ci1zdXBlci1zZWNyZXQta2V5LWhlcmUtZm9yLWFpLXBtLXBsYXRmb3Jt  # your-super-secret-key-here-for-ai-pm-platform
  jwt-secret: and0LXNlY3JldC1rZXktZm9yLWFpLXBtLXBsYXRmb3Jt  # jwt-secret-key-for-ai-pm-platform

---
apiVersion: v1
kind: Secret
metadata:
  name: email-secret
  namespace: ai-pm
  labels:
    app: ai-pm
    component: notification
type: Opaque
data:
  # 邮件服务配置（Base64编码）
  smtp-host: c210cC5nbWFpbC5jb20=  # smtp.gmail.com
  smtp-port: NTg3  # 587
  smtp-username: ****************************  # <EMAIL>
  smtp-password: eW91ci1hcHAtcGFzc3dvcmQ=  # your-app-password

---
apiVersion: v1
kind: Secret
metadata:
  name: integration-secret
  namespace: ai-pm
  labels:
    app: ai-pm
    component: integration
type: Opaque
data:
  # 第三方集成密钥（Base64编码）
  jira-api-token: eW91ci1qaXJhLWFwaS10b2tlbg==  # your-jira-api-token
  slack-bot-token: eG94Yi15b3VyLXNsYWNrLWJvdC10b2tlbg==  # xoxb-your-slack-bot-token
  dingtalk-app-secret: eW91ci1kaW5ndGFsay1hcHAtc2VjcmV0  # your-dingtalk-app-secret
  github-token: Z2hwX3lvdXItZ2l0aHViLXRva2Vu  # ghp_your-github-token

---
apiVersion: v1
kind: Secret
metadata:
  name: monitoring-secret
  namespace: ai-pm
  labels:
    app: ai-pm
    component: monitoring
type: Opaque
data:
  # 监控系统密钥（Base64编码）
  grafana-admin-password: YWRtaW4=  # admin
  prometheus-basic-auth: cHJvbWV0aGV1czpwYXNzd29yZA==  # prometheus:password

---
# TLS证书密钥（由cert-manager自动管理）
apiVersion: v1
kind: Secret
metadata:
  name: ai-pm-tls
  namespace: ai-pm
  labels:
    app: ai-pm
    component: tls
type: kubernetes.io/tls
data:
  # 这些将由cert-manager自动填充
  tls.crt: ""
  tls.key: ""

---
# 镜像拉取密钥（如果使用私有镜像仓库）
apiVersion: v1
kind: Secret
metadata:
  name: registry-secret
  namespace: ai-pm
  labels:
    app: ai-pm
    component: registry
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************

---
# 服务账户
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ai-pm-service-account
  namespace: ai-pm
  labels:
    app: ai-pm
imagePullSecrets:
- name: registry-secret

---
# RBAC 角色
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ai-pm-role
  namespace: ai-pm
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
# RBAC 角色绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ai-pm-role-binding
  namespace: ai-pm
subjects:
- kind: ServiceAccount
  name: ai-pm-service-account
  namespace: ai-pm
roleRef:
  kind: Role
  name: ai-pm-role
  apiGroup: rbac.authorization.k8s.io
