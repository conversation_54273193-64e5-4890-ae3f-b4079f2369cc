# AI项目管理平台数据库部署配置
# <AUTHOR>
# @version 1.0.0
# @since 2025-08-16

apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: ai-pm
  labels:
    app: postgres
    component: database
    tier: data
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
        component: database
        tier: data
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: password
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: database
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: postgres-init
          mountPath: /docker-entrypoint-initdb.d
          readOnly: true
      volumes:
      - name: postgres-init
        configMap:
          name: postgres-init-config
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi
      storageClassName: standard

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: ai-pm
  labels:
    app: postgres
    component: database
    tier: data
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: postgres

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-config
  namespace: ai-pm
data:
  01-init-databases.sql: |
    -- 创建AI项目管理平台数据库
    CREATE DATABASE ai_pm_db;
    CREATE DATABASE ai_pm_notification;
    CREATE DATABASE ai_pm_integration;
    
    -- 创建用户和权限
    CREATE USER ai_pm_user WITH PASSWORD 'ai_pm_password';
    GRANT ALL PRIVILEGES ON DATABASE ai_pm_db TO ai_pm_user;
    GRANT ALL PRIVILEGES ON DATABASE ai_pm_notification TO ai_pm_user;
    GRANT ALL PRIVILEGES ON DATABASE ai_pm_integration TO ai_pm_user;
    
    -- 设置默认权限
    \c ai_pm_db;
    GRANT ALL ON SCHEMA public TO ai_pm_user;
    
    \c ai_pm_notification;
    GRANT ALL ON SCHEMA public TO ai_pm_user;
    
    \c ai_pm_integration;
    GRANT ALL ON SCHEMA public TO ai_pm_user;

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ai-pm
  labels:
    app: redis
    component: cache
    tier: data
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        component: cache
        tier: data
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-data-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: ai-pm
  labels:
    app: redis
    component: cache
    tier: data
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-data-pvc
  namespace: ai-pm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
