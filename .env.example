# AI项目管理平台环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# ============================================================================
# 基础配置
# ============================================================================
# 项目名称
PROJECT_NAME=ai-pm-platform

# 环境类型 (development, staging, production)
ENVIRONMENT=development

# 应用版本
VERSION=latest

# Docker镜像仓库地址
DOCKER_REGISTRY=ghcr.io/your-org

# ============================================================================
# 数据库配置
# ============================================================================
# PostgreSQL 配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=aipm_dev
POSTGRES_USER=dev_user
POSTGRES_PASSWORD=dev_password_123

# MongoDB 配置
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DB=aipm_dev
MONGO_USER=admin
MONGO_PASSWORD=mongo_password_123

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password_123
REDIS_DB=0

# ============================================================================
# 消息队列配置
# ============================================================================
# Kafka 配置
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=aipm-group

# ============================================================================
# 应用服务配置
# ============================================================================
# 用户管理服务
USER_SERVICE_PORT=8080
USER_SERVICE_HOST=localhost

# 项目管理服务
PROJECT_SERVICE_PORT=8081
PROJECT_SERVICE_HOST=localhost

# AI分析服务
AI_SERVICE_PORT=8082
AI_SERVICE_HOST=localhost

# 集成服务
INTEGRATION_SERVICE_PORT=8083
INTEGRATION_SERVICE_HOST=localhost

# 通知服务
NOTIFICATION_SERVICE_PORT=8084
NOTIFICATION_SERVICE_HOST=localhost

# 前端服务
FRONTEND_PORT=3000
FRONTEND_HOST=localhost

# ============================================================================
# 安全配置
# ============================================================================
# JWT 密钥 (生产环境请使用强密钥)
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRATION=86400

# OAuth 配置
OAUTH_CLIENT_ID=your-oauth-client-id
OAUTH_CLIENT_SECRET=your-oauth-client-secret

# 加密密钥
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# ============================================================================
# 第三方服务配置
# ============================================================================
# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>

# 短信服务配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key

# 对象存储配置
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_ACCESS_KEY=your-oss-access-key
OSS_SECRET_KEY=your-oss-secret-key
OSS_BUCKET=aipm-files

# ============================================================================
# AI/ML 配置
# ============================================================================
# OpenAI API 配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# 模型存储路径
MODEL_STORAGE_PATH=/app/models

# 特征存储配置
FEATURE_STORE_URL=http://localhost:6566

# ============================================================================
# 监控和日志配置
# ============================================================================
# Prometheus 配置
PROMETHEUS_PORT=9090

# Grafana 配置
GRAFANA_PORT=3001
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin_password_123

# Elasticsearch 配置
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200

# Kibana 配置
KIBANA_PORT=5601

# Jaeger 配置
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# ============================================================================
# 开发环境配置
# ============================================================================
# 调试模式
DEBUG=true

# 日志级别 (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# 热重载
HOT_RELOAD=true

# API 文档
ENABLE_SWAGGER=true

# ============================================================================
# 生产环境配置
# ============================================================================
# SSL 证书路径
SSL_CERT_PATH=/etc/ssl/certs/aipm.crt
SSL_KEY_PATH=/etc/ssl/private/aipm.key

# 域名配置
DOMAIN=aipm.yourdomain.com
API_DOMAIN=api.aipm.yourdomain.com

# CDN 配置
CDN_URL=https://cdn.aipm.yourdomain.com

# ============================================================================
# 集成配置
# ============================================================================
# GitHub 集成
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GITHUB_WEBHOOK_SECRET=your-github-webhook-secret

# GitLab 集成
GITLAB_CLIENT_ID=your-gitlab-client-id
GITLAB_CLIENT_SECRET=your-gitlab-client-secret
**********************your-gitlab-webhook-secret

# Jira 集成
JIRA_BASE_URL=https://your-company.atlassian.net
JIRA_USERNAME=your-jira-username
JIRA_API_TOKEN=your-jira-api-token

# 钉钉集成
DINGTALK_APP_KEY=your-dingtalk-app-key
DINGTALK_APP_SECRET=your-dingtalk-app-secret

# 企业微信集成
WEWORK_CORP_ID=your-wework-corp-id
WEWORK_CORP_SECRET=your-wework-corp-secret
